#!/bin/bash

# <PERSON><PERSON>t to deploy the fixed digest functionality with proper IAM permissions

echo "===== DAILY DIGEST FEATURE DEPLOYMENT ====="
echo "Starting deployment at $(date)"

# Ensure GROQ_API_KEY is set
if [ -z "$GROQ_API_KEY" ]; then
  echo "ERROR: GROQ_API_KEY environment variable is not set."
  echo "Please set it with: export GROQ_API_KEY=your_api_key"
  exit 1
fi

echo "✓ GROQ_API_KEY is set"

# Navigate to the infrastructure directory
cd "$(dirname "$0")"
echo "✓ Changed directory to: $(pwd)"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
  echo "✓ Dependencies installed"
else
  echo "✓ Dependencies already installed"
fi

# Compile TypeScript
echo "Compiling TypeScript..."
npm run build
echo "✓ TypeScript compiled"

# Deploy the CDK stack
echo "Deploying CDK stack with daily digest table and fixed IAM permissions..."
echo "This may take several minutes..."
cdk deploy --require-approval never

echo "✓ Deployment complete at $(date)!"
echo ""
echo "The daily digest functionality should now work correctly."
echo ""
echo "=== VERIFICATION STEPS ==="
echo "1. Check that the lotus-daily-digest table was created in DynamoDB"
echo "2. Verify that the DigestLambda has proper IAM permissions to access the table"
echo "3. In the app, navigate to the Digest screen and try to generate a digest"
echo "4. Check CloudWatch logs for any errors in the DigestLambda function"
echo ""
echo "If you encounter any issues:"
echo "- Check CloudWatch logs for the DigestLambda function"
echo "- Verify that the GROQ_API_KEY is correctly set in the Lambda environment variables"
echo "- Check that the response from Groq is properly structured JSON" 