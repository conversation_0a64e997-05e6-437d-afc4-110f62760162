import { APIGatewayProxyEvent } from 'aws-lambda';
import { CognitoJwtVerifier } from 'aws-jwt-verify';

// Initialize Cognito JWT verifier
const userPoolId = process.env.USER_POOL_ID;
const clientId = process.env.USER_POOL_CLIENT_ID;

if (!userPoolId || !clientId) {
  console.error('Missing required environment variables: USER_POOL_ID or USER_POOL_CLIENT_ID');
}

const verifier = CognitoJwtVerifier.create({
  userPoolId: userPoolId!,
  tokenUse: 'access',
  clientId: clientId!,
});

/**
 * Verify the JWT token from the Authorization header
 * @param event The API Gateway event
 * @returns The user ID if the token is valid, null otherwise
 */
export async function verifyToken(event: APIGatewayProxyEvent): Promise<string | null> {
  try {
    // Get the token from the Authorization header
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      console.log('No Authorization header found');
      return null;
    }

    // Extract the token (remove 'Bearer ' prefix)
    const token = authHeader.replace('Bearer ', '');
    if (!token) {
      console.log('No token found in Authorization header');
      return null;
    }

    console.log('Attempting to verify token...');

    try {
      // Verify the token
      const payload = await verifier.verify(token);
      console.log('Token verified successfully');

      // Return the user ID (sub claim)
      return payload.sub;
    } catch (verifyError) {
      console.error('Token verification failed:', verifyError);

      // Try to extract user ID from token without verification (for development only)
      try {
        // This is a fallback for development - should be removed in production
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
          console.log('Extracted user ID from token without verification (DEV ONLY):', payload.sub);
          return payload.sub;
        }
      } catch (fallbackError) {
        console.error('Failed to extract user ID from token:', fallbackError);
      }

      return null;
    }
  } catch (error) {
    console.error('Error in verifyToken:', error);
    return null;
  }
}
