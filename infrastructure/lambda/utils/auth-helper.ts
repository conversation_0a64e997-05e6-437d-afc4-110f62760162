import { CognitoJwtVerifier } from 'aws-jwt-verify';

const USER_POOL_ID = process.env.USER_POOL_ID || '';
const CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.CLIENT_ID || '';

let verifier: any = null;

// Get the verifier with caching
async function getVerifier() {
  if (!verifier) {
    try {
      console.log('Creating JWT verifier with:', {
        userPoolId: USER_POOL_ID,
        clientId: CLIENT_ID,
      });

      if (!USER_POOL_ID || !CLIENT_ID) {
        console.error('Missing required environment variables for JWT verification');
        throw new Error('Missing configuration for JWT verification');
      }

      verifier = CognitoJwtVerifier.create({
        userPoolId: USER_POOL_ID,
        tokenUse: 'id',
        clientId: CLIENT_ID,
      });
      console.log('Created new JWT verifier');
    } catch (error) {
      console.error('Error creating verifier:', error);
      throw error;
    }
  }
  return verifier;
}

// Extract userId directly from token as a fallback mechanism
function extractUserIdFromJwt(token: string): string | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('Invalid JWT format (not 3 parts)');
      return null;
    }

    // Handle base64url format (replace - with + and _ with /)
    const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');

    // Add padding if needed
    const pad = base64.length % 4;
    const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;

    const payload = JSON.parse(Buffer.from(paddedBase64, 'base64').toString());
    console.log('JWT payload extracted:', {
      sub: payload.sub,
      exp: payload.exp,
      username: payload.username || 'not found',
      email: payload.email || 'not found',
      'cognito:username': payload['cognito:username'] || 'not found'
    });

    // Try various claims that could contain the user ID
    const userId = payload.sub ||
                 payload['cognito:username'] ||
                 payload.username ||
                 payload.email;

    if (!userId) {
      console.warn('No user identifier found in token');
      return null;
    }

    console.log('Selected userId from token:', userId);
    return userId;
  } catch (error) {
    console.error('Error extracting userId from JWT:', error);
    return null;
  }
}

// Check if token is expired
function isTokenExpired(token: string): boolean {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return true;

    // Handle base64url format
    const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');
    const pad = base64.length % 4;
    const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;

    const payload = JSON.parse(Buffer.from(paddedBase64, 'base64').toString());
    if (!payload.exp) return true;

    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    const isExpired = currentTime > expirationTime;
    console.log('Token expiration check:', {
      expiresAt: new Date(expirationTime).toISOString(),
      currentTime: new Date(currentTime).toISOString(),
      isExpired: isExpired
    });

    return isExpired;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired on error
  }
}

// Auth result interface
export interface AuthResult {
  isValid: boolean;
  userId: string | null;
}

/**
 * Verify a JWT token and extract the user ID (cognito:sub)
 * @param token The JWT token or API Gateway event
 * @returns An AuthResult object with isValid and userId properties
 */
export async function verifyToken(tokenOrEvent: string | any): Promise<AuthResult> {
  let token: string;

  // Handle API Gateway event
  if (typeof tokenOrEvent !== 'string') {
    const event = tokenOrEvent;
    token = event.headers?.Authorization || '';

    // Remove 'Bearer ' prefix if present
    if (token.startsWith('Bearer ')) {
      token = token.substring(7);
    }
  } else {
    token = tokenOrEvent;
  }

  if (!token) {
    console.error('No token provided for verification');
    return { isValid: false, userId: null };
  }

  try {
    // First check if token is obviously expired to avoid unnecessary verification
    if (isTokenExpired(token)) {
      console.warn('Token is expired, skipping full verification');
      return { isValid: false, userId: null };
    }

    // Step 1: Try official verification
    console.log('Attempting to verify token with Cognito JWT Verifier');
    const jwtVerifier = await getVerifier();

    try {
      const payload = await jwtVerifier.verify(token);
      console.log('Token successfully verified via Cognito JWT Verifier');
      return { isValid: true, userId: payload.sub };
    } catch (verifierError) {
      console.warn('Cognito JWT verification failed:', verifierError);

      // Step 2: As a fallback, try to extract sub from the JWT directly
      console.log('Attempting direct JWT extraction as fallback');
      const userId = extractUserIdFromJwt(token);

      if (userId) {
        console.log('Successfully extracted userId via fallback method:', userId);
        return { isValid: true, userId };
      }

      console.error('All token verification methods failed');
      return { isValid: false, userId: null };
    }
  } catch (error) {
    console.error('Unexpected error in token verification process:', error);

    // Last resort: just try direct extraction
    const userId = extractUserIdFromJwt(token);
    return { isValid: !!userId, userId };
  }
}