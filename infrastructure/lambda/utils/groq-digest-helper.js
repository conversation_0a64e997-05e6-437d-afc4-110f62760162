/**
 * Helper module for generating activities using the Groq API and fallbacks.
 */

const { GroqAPI } = require('./groq-api'); // Import the Groq API client
const groqApiKey = process.env.GROQ_API_KEY || '';

/**
 * Generate activities using the Groq API with fallback mechanisms.
 */
async function generateActivitiesWithGroq(
  userId,
  date,
  userProfile = {},
  userContext = [],
  logHistory = [],
  adjustmentInstruction
) {
  console.log(`Attempting to generate digest activities using Groq for ${userId} on ${date} with Llama 4 Scout model`);
  
  try {
    if (!groqApiKey) {
      console.warn('No Groq API key found in environment. Using template-based generation instead.');
      const activities = generateBasicActivities(date, userProfile);
      console.log(`Generated ${activities.length} template activities (no API key)`);
      return activities;
    }

    // Initialize the Groq API client
    const groq = new GroqAPI(groqApiKey);
    
    // Prepare the context for Groq
    const context = prepareGroqContext(userId, date, userProfile, userContext, logHistory, adjustmentInstruction);
    
    // Call the Groq API
    console.log('Calling Groq API to generate activities...');
    
    let response;
    try {
      response = await groq.generateDigestActivities(context);
      console.log('Received response from Groq API');
    } catch (groqError) {
      console.error('Error calling Groq API, falling back to template generation:', groqError);
      const activities = generateBasicActivities(date, userProfile);
      console.log(`Generated ${activities.length} template activities after Groq API error`);
      return activities;
    }
    
    // Parse and validate the activities from the response
    if (response && response.activities && Array.isArray(response.activities)) {
      console.log(`Groq returned ${response.activities.length} activities for ${date}`);
      
      // Log a sample of the activities for debugging
      const sampleSize = Math.min(response.activities.length, 3);
      for (let i = 0; i < sampleSize; i++) {
        console.log(`Sample activity ${i}:`, JSON.stringify(response.activities[i]));
      }
      
      // Validate that the activities have the required fields
      const validActivities = response.activities.filter(activity => 
        activity && 
        activity.id && 
        activity.title && 
        activity.type && 
        activity.scheduledTime && 
        typeof activity.completed === 'boolean' &&
        typeof activity.isUserOverride === 'boolean'
      );
      
      console.log(`${validActivities.length} valid activities after filtering`);
      
      // Ensure we have a minimum number of activities
      if (validActivities.length >= 5) {
        return validActivities;
      } else if (validActivities.length > 0) {
        // We have some valid activities but not enough - add more from templates
        console.warn(`Groq returned only ${validActivities.length} valid activities. Adding more from templates.`);
        const templateActivities = generateBasicActivities(date, userProfile);
        
        // Get activity types we already have
        const existingTypes = validActivities.map(a => a.type);
        
        // Add template activities of types we're missing
        const additionalActivities = templateActivities.filter(a => 
          !existingTypes.includes(a.type) || 
          (a.type === 'meal' && existingTypes.filter(t => t === 'meal').length < 3) ||
          (a.type === 'water' && !existingTypes.includes('water'))
        );
        
        const combinedActivities = [...validActivities, ...additionalActivities];
        
        // Sort by scheduled time
        combinedActivities.sort((a, b) => 
          new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
        );
        
        console.log(`Combined ${validActivities.length} Groq activities with ${additionalActivities.length} template activities for a total of ${combinedActivities.length}`);
        return combinedActivities;
      } else {
        console.warn('Groq returned activities but none were valid. Using template-based generation instead.');
        const activities = generateBasicActivities(date, userProfile);
        console.log(`Generated ${activities.length} template activities (no valid activities from Groq)`);
        return activities;
      }
    } else {
      console.warn('Invalid response from Groq. Using template-based generation instead.');
      const activities = generateBasicActivities(date, userProfile);
      console.log(`Generated ${activities.length} template activities (invalid response from Groq)`);
      return activities;
    }
  } catch (error) {
    console.error('Error in generateActivitiesWithGroq:', error);
    const activities = generateBasicActivities(date, userProfile);
    console.log(`Generated ${activities.length} template activities after error`);
    return activities;
  }
}

/**
 * Prepare the context object for the Groq API.
 */
function prepareGroqContext(userId, date, userProfile, userContext, logHistory, adjustmentInstruction) {
  // Get the day of the week from the date
  const dateObj = new Date(date);
  const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dayName = dayNames[dayOfWeek];
  
  // Extract relevant preferences from the user context
  let dietPreferences = [];
  let fitnessGoals = [];
  let sleepPreferences = {
    wakeUpTime: userProfile.wakeUpTime || '07:00',
    bedTime: userProfile.bedTime || '22:30'
  };
  
  // Process user context to extract preferences
  if (userContext && userContext.length > 0) {
    userContext.forEach(ctx => {
      if (ctx.contextType === 'preference') {
        const value = ctx.value?.toLowerCase() || '';
        
        // Extract diet preferences
        if (value.includes('vegetarian') || value.includes('vegan') || 
            value.includes('gluten-free') || value.includes('dairy-free')) {
          dietPreferences.push(ctx.value);
        }
        
        // Extract fitness goals
        if (value.includes('lose weight') || value.includes('gain muscle') || 
            value.includes('improve fitness') || value.includes('training for')) {
          fitnessGoals.push(ctx.value);
        }
        
        // Extract sleep preferences
        if (value.includes('wake up') || value.includes('get up at')) {
          const timeMatch = value.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i);
          if (timeMatch) {
            sleepPreferences.wakeUpTime = extractTimeFromMatch(timeMatch);
          }
        } else if (value.includes('bed time') || value.includes('sleep at')) {
          const timeMatch = value.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i);
          if (timeMatch) {
            sleepPreferences.bedTime = extractTimeFromMatch(timeMatch);
          }
        }
      }
    });
  }
  
  // Extract workout habits from log history
  const workoutLog = (logHistory || []).filter(log => log.type === 'workout');
  let workoutHabits = {};
  
  if (workoutLog.length > 0) {
    // Analyze workout frequency
    workoutHabits.frequency = Math.min(Math.round(workoutLog.length / 7), 7); // workouts per week
    
    // Analyze favorite workout types
    const workoutTypes = {};
    workoutLog.forEach(workout => {
      const type = workout.workoutType || 'unknown';
      workoutTypes[type] = (workoutTypes[type] || 0) + 1;
    });
    
    // Sort workout types by frequency
    workoutHabits.favoriteTypes = Object.entries(workoutTypes)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(entry => entry[0]);
  }
  
  // Build the final context object
  const context = {
    userId,
    date,
    dayOfWeek: dayName,
    userProfile: {
      name: userProfile.name || 'User',
      age: userProfile.age,
      gender: userProfile.gender,
      height: userProfile.height,
      weight: userProfile.weight,
      dietPreferences,
      fitnessGoals,
      sleepPreferences
    },
    workoutHabits,
    adjustmentInstruction
  };
  
  return context;
}

/**
 * Extract time in HH:MM format from a regex match
 */
function extractTimeFromMatch(match) {
  let hours = parseInt(match[1]);
  const minutes = match[2] ? parseInt(match[2]) : 0;
  const period = match[3]?.toLowerCase();
  
  // Convert to 24-hour format
  if (period === 'pm' && hours < 12) {
    hours += 12;
  } else if (period === 'am' && hours === 12) {
    hours = 0;
  }
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

/**
 * Generate basic activities for a date using templates.
 * This is used as a fallback when the Groq API fails.
 */
function generateBasicActivities(date, userProfile = {}) {
  console.log(`Generating basic template activities for ${date}`);
  
  const activities = [];
  const dateObj = new Date(date);
  const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.
  
  // Extract user preferences or use defaults
  const wakeUpTime = userProfile.wakeUpTime || '07:00';
  const bedTime = userProfile.bedTime || '22:30';
  const preferredWorkoutTime = userProfile.preferredWorkoutTime || '17:30';
  
  // Generate 3 meals for the day
  addMealActivities(activities, date, wakeUpTime, bedTime);
  
  // Generate appropriate workout based on day of week
  addWorkoutActivity(activities, date, dayOfWeek, preferredWorkoutTime);
  
  // Add water reminders
  addWaterReminders(activities, date, wakeUpTime, bedTime);
  
  // Add sleep reminder
  addSleepReminder(activities, date, bedTime);
  
  // Add custom activities based on user profile if available
  if (userProfile.name) {
    addPersonalizedActivities(activities, date, userProfile);
  }
  
  // Ensure we have at least 5 activities
  if (activities.length < 5) {
    addFallbackActivities(activities, date);
  }
  
  // Sort activities by scheduled time
  activities.sort((a, b) => 
    new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
  );
  
  console.log(`Generated ${activities.length} basic template activities`);
  return activities;
}

/**
 * Add meal activities to the array
 */
function addMealActivities(activities, date, wakeUpTime, bedTime) {
  // Parse wake-up and bed times
  const [wakeHour, wakeMinute] = wakeUpTime.split(':').map(Number);
  const [bedHour, bedMinute] = bedTime.split(':').map(Number);
  
  // Calculate meal times
  // Breakfast: 30 minutes after waking up
  let breakfastHour = wakeHour;
  let breakfastMinute = wakeMinute + 30;
  if (breakfastMinute >= 60) {
    breakfastHour += 1;
    breakfastMinute -= 60;
  }
  
  // Calculate the total waking hours
  let wakingHours = bedHour - wakeHour;
  if (wakingHours <= 0) wakingHours += 24; // Handle overnight
  
  // Lunch: Around midday, at least 3 hours after breakfast
  const breakfastTime = breakfastHour * 60 + breakfastMinute;
  const lunchTime = Math.max(12 * 60 + 30, breakfastTime + 180);
  const lunchHour = Math.floor(lunchTime / 60);
  const lunchMinute = lunchTime % 60;
  
  // Dinner: Around 6:30pm, at least 5 hours after lunch
  const dinnerTime = Math.max(18 * 60 + 30, lunchTime + 300);
  const dinnerHour = Math.floor(dinnerTime / 60) % 24;
  const dinnerMinute = dinnerTime % 60;
  
  // Format times as HH:MM
  const breakfastTimeStr = `${breakfastHour.toString().padStart(2, '0')}:${breakfastMinute.toString().padStart(2, '0')}`;
  const lunchTimeStr = `${lunchHour.toString().padStart(2, '0')}:${lunchMinute.toString().padStart(2, '0')}`;
  const dinnerTimeStr = `${dinnerHour.toString().padStart(2, '0')}:${dinnerMinute.toString().padStart(2, '0')}`;
  
  // Add breakfast
  activities.push({
    id: generateId(),
    type: 'meal',
    title: 'Protein-Packed Breakfast Bowl',
    description: 'Greek yogurt with fresh berries, honey, and granola for a balanced start to your day',
    scheduledTime: `${date}T${breakfastTimeStr}:00`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        ingredients: ['Greek yogurt', 'Mixed berries', 'Honey', 'Granola', 'Chia seeds'],
        nutrition: { calories: 420, protein: 24, carbs: 52, fat: 14 }
      }
    }
  });
  
  // Add lunch
  activities.push({
    id: generateId(),
    type: 'meal',
    title: 'Mediterranean Chicken Salad',
    description: 'Grilled chicken with mixed greens, cherry tomatoes, cucumber, olives, and feta cheese with olive oil dressing',
    scheduledTime: `${date}T${lunchTimeStr}:00`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        ingredients: ['Grilled chicken breast', 'Mixed greens', 'Cherry tomatoes', 'Cucumber', 'Kalamata olives', 'Feta cheese', 'Olive oil', 'Lemon juice'],
        nutrition: { calories: 380, protein: 32, carbs: 14, fat: 22 }
      }
    }
  });
  
  // Add dinner
  activities.push({
    id: generateId(),
    type: 'meal',
    title: 'Baked Salmon with Roasted Vegetables',
    description: 'Herb-crusted salmon fillet with roasted sweet potatoes, broccoli, and bell peppers',
    scheduledTime: `${date}T${dinnerTimeStr}:00`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        ingredients: ['Salmon fillet', 'Sweet potato', 'Broccoli', 'Bell pepper', 'Olive oil', 'Herbs', 'Lemon'],
        nutrition: { calories: 450, protein: 35, carbs: 30, fat: 22 }
      }
    }
  });
}

/**
 * Add an appropriate workout activity based on day of week
 */
function addWorkoutActivity(activities, date, dayOfWeek, preferredTime) {
  // Skip Sunday as a rest day
  if (dayOfWeek === 0) {
    activities.push({
      id: generateId(),
      type: 'reminder',
      title: 'Active Recovery Day',
      description: 'Take a walk, do light stretching, or other gentle activity to aid recovery',
      scheduledTime: `${date}T10:00:00`,
      completed: false,
      isUserOverride: false
    });
    return;
  }
  
  // Different workout types based on day of week
  let workoutTitle = 'Daily Workout';
  let workoutDescription = 'Recommended workout for today';
  let exercises = [];
  
  if (dayOfWeek === 1 || dayOfWeek === 4) { // Monday and Thursday
    workoutTitle = 'Upper Body Workout';
    workoutDescription = 'Focus on chest, back, shoulders, and arms';
    exercises = [
      { name: 'Push-ups', sets: 3, reps: '10-15', notes: 'Keep core engaged' },
      { name: 'Dumbbell Rows', sets: 3, reps: '10-12 per arm', notes: 'Focus on squeezing shoulder blades' },
      { name: 'Shoulder Press', sets: 3, reps: '10-12', notes: 'Control the movement' },
      { name: 'Tricep Dips', sets: 3, reps: '10-15', notes: 'Use a chair or bench' }
    ];
  } else if (dayOfWeek === 2 || dayOfWeek === 5) { // Tuesday and Friday
    workoutTitle = 'Lower Body Workout';
    workoutDescription = 'Focus on legs, glutes, and core';
    exercises = [
      { name: 'Squats', sets: 4, reps: '12-15', notes: 'Keep weight in heels' },
      { name: 'Lunges', sets: 3, reps: '10 per leg', notes: 'Step forward or backward' },
      { name: 'Glute Bridges', sets: 3, reps: '15-20', notes: 'Squeeze glutes at top' },
      { name: 'Plank', sets: 3, reps: '30-60 seconds', notes: 'Keep body in straight line' }
    ];
  } else if (dayOfWeek === 3) { // Wednesday
    workoutTitle = 'Cardio Session';
    workoutDescription = '30 minutes of zone 2 cardio';
    exercises = [
      { name: 'Brisk Walking', sets: 1, reps: '30 minutes', notes: 'Maintain conversation pace' },
      { name: 'Jumping Jacks', sets: 3, reps: '60 seconds', notes: 'Active rest between walking segments' }
    ];
  } else if (dayOfWeek === 6) { // Saturday
    workoutTitle = 'Active Recovery';
    workoutDescription = 'Light activity and mobility work';
    exercises = [
      { name: 'Walking', sets: 1, reps: '20 minutes', notes: 'Easy pace' },
      { name: 'Dynamic Stretching', sets: 1, reps: '10 minutes', notes: 'Full body' },
      { name: 'Foam Rolling', sets: 1, reps: '10 minutes', notes: 'Focus on tight areas' }
    ];
  }
  
  activities.push({
    id: generateId(),
    type: 'workout',
    title: workoutTitle,
    description: workoutDescription,
    scheduledTime: `${date}T${preferredTime}:00`,
    completed: false,
    isUserOverride: false,
    metadata: {
      workoutDetails: {
        duration: 45,
        exercises
      }
    }
  });
}

/**
 * Add water reminder activities
 */
function addWaterReminders(activities, date, wakeUpTime, bedTime) {
  // Parse wake-up and bed times to calculate waking hours
  const [wakeHour, wakeMinute] = wakeUpTime.split(':').map(Number);
  const [bedHour, bedMinute] = bedTime.split(':').map(Number);
  
  // Calculate total waking hours
  let wakingHours = bedHour - wakeHour;
  if (wakingHours <= 0) wakingHours += 24; // Handle overnight
  
  // Add reminders every ~3 hours during waking hours
  const reminderCount = Math.max(2, Math.floor(wakingHours / 3));
  
  for (let i = 0; i < reminderCount; i++) {
    const hourOffset = Math.round((i + 1) * (wakingHours / (reminderCount + 1)));
    let reminderHour = (wakeHour + hourOffset) % 24;
    
    const reminderTime = `${reminderHour.toString().padStart(2, '0')}:00`;
    
    activities.push({
      id: generateId(),
      type: 'water',
      title: 'Hydration Reminder',
      description: 'Drink 16oz of water',
      scheduledTime: `${date}T${reminderTime}:00`,
      completed: false,
      isUserOverride: false
    });
  }
}

/**
 * Add sleep reminder
 */
function addSleepReminder(activities, date, bedTime) {
  // Add a reminder 30 minutes before bedtime
  const [bedHour, bedMinute] = bedTime.split(':').map(Number);
  
  let reminderHour = bedHour;
  let reminderMinute = bedMinute - 30;
  
  if (reminderMinute < 0) {
    reminderHour = (reminderHour - 1 + 24) % 24;
    reminderMinute += 60;
  }
  
  const reminderTime = `${reminderHour.toString().padStart(2, '0')}:${reminderMinute.toString().padStart(2, '0')}`;
  
  activities.push({
    id: generateId(),
    type: 'sleep',
    title: 'Sleep Reminder',
    description: 'Prepare for bed to get quality sleep',
    scheduledTime: `${date}T${reminderTime}:00`,
    completed: false,
    isUserOverride: false
  });
}

/**
 * Add personalized activities based on user profile
 */
function addPersonalizedActivities(activities, date, userProfile) {
  // Add personalized activities if we have enough information
  if (userProfile.fitnessGoals && userProfile.fitnessGoals.includes('lose weight')) {
    activities.push({
      id: generateId(),
      type: 'reminder',
      title: 'Weight Tracking',
      description: 'Track your weight and measurements to monitor progress',
      scheduledTime: `${date}T08:30:00`,
      completed: false,
      isUserOverride: false
    });
  }
  
  if (userProfile.dietPreferences && userProfile.dietPreferences.length > 0) {
    activities.push({
      id: generateId(),
      type: 'reminder',
      title: 'Meal Planning',
      description: 'Plan tomorrow\'s meals to stay aligned with your dietary preferences',
      scheduledTime: `${date}T20:00:00`,
      completed: false,
      isUserOverride: false
    });
  }
}

/**
 * Add fallback activities to ensure we have enough content
 */
function addFallbackActivities(activities, date) {
  const fallbackActivities = [
    {
      id: generateId(),
      type: 'reminder',
      title: 'Mindfulness Break',
      description: 'Take 5 minutes to practice deep breathing and mindfulness',
      scheduledTime: `${date}T12:00:00`,
      completed: false,
      isUserOverride: false
    },
    {
      id: generateId(),
      type: 'reminder',
      title: 'Stretch Break',
      description: 'Take a few minutes to stretch your body, especially if you\'ve been sitting',
      scheduledTime: `${date}T15:00:00`,
      completed: false,
      isUserOverride: false
    },
    {
      id: generateId(),
      type: 'reminder',
      title: 'Self-Care Time',
      description: 'Schedule some time for yourself to relax and recharge',
      scheduledTime: `${date}T19:30:00`,
      completed: false,
      isUserOverride: false
    }
  ];
  
  // Add fallback activities until we have at least 5
  for (let i = 0; activities.length < 5 && i < fallbackActivities.length; i++) {
    activities.push(fallbackActivities[i]);
  }
}

/**
 * Generate a unique ID for activities
 */
function generateId() {
  return 'act_' + Math.random().toString(36).substring(2, 15);
}

module.exports = {
  generateActivitiesWithGroq,
  generateBasicActivities
}; 