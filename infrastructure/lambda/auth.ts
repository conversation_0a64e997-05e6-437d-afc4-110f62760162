import { CognitoIdentityProviderClient, InitiateA<PERSON><PERSON>ommand, SignUpCommand, ConfirmSignUpCommand, ResendConfirmationCodeCommand, AdminDeleteUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, QueryCommand, DeleteCommand, BatchWriteCommand } from '@aws-sdk/lib-dynamodb';

const cognitoClient = new CognitoIdentityProviderClient({});
const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));

// Table names from environment variables
const USER_PROFILE_TABLE = process.env.USER_PROFILE_TABLE_NAME || '';
const LOG_TABLE = process.env.LOG_TABLE_NAME || '';
const CONVERSATION_TABLE = process.env.CONVERSATION_TABLE_NAME || '';
const MESSAGE_TABLE = process.env.MESSAGE_TABLE_NAME || '';
const USER_CONTEXT_TABLE = process.env.USER_CONTEXT_TABLE_NAME || '';
const USER_ACHIEVEMENTS_TABLE = process.env.USER_ACHIEVEMENTS_TABLE_NAME || '';
const USER_STATS_TABLE = process.env.USER_STATS_TABLE_NAME || '';
const TDEE_TREND_TABLE = process.env.TDEE_TREND_TABLE_NAME || '';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing request body' }),
      };
    }

    const { action, email, password, code, refreshToken } = JSON.parse(event.body);

    if (!action) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing required action field' }),
      };
    }

    switch (action) {
      case 'signUp':
        if (!password) {
          return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Password is required for sign up' }),
          };
        }
        return await handleSignUp(email, password);
      case 'signIn':
        if (!password) {
          return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Password is required for sign in' }),
          };
        }
        return await handleSignIn(email, password);
      case 'confirmSignUp':
        if (!code) {
          return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Verification code is required' }),
          };
        }
        return await handleConfirmSignUp(email, code);
      case 'resendConfirmationCode':
        return await handleResendConfirmationCode(email);
      case 'refreshToken':
        if (!refreshToken) {
          return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Refresh token is required' }),
          };
        }
        return await handleRefreshToken(refreshToken);
      case 'deleteAccount':
        return await handleDeleteAccount(email, event.headers?.Authorization);
      default:
        return {
          statusCode: 400,
          body: JSON.stringify({ message: 'Invalid action' }),
        };
    }
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
};

async function handleSignUp(email: string, password: string): Promise<APIGatewayProxyResult> {
  try {
    const command = new SignUpCommand({
      ClientId: process.env.USER_POOL_CLIENT_ID,
      Username: email,
      Password: password,
      UserAttributes: [
        {
          Name: 'email',
          Value: email,
        },
      ],
    });

    const response = await cognitoClient.send(command);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'User created successfully',
        userSub: response.UserSub,
        userConfirmed: response.UserConfirmed,
      }),
    };
  } catch (error: any) {
    console.error('SignUp error:', error);
    return {
      statusCode: error.name === 'UsernameExistsException' ? 409 : 500,
      body: JSON.stringify({
        message: error.message || 'Error creating user',
      }),
    };
  }
}

async function handleSignIn(email: string, password: string): Promise<APIGatewayProxyResult> {
  try {
    const command = new InitiateAuthCommand({
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: process.env.USER_POOL_CLIENT_ID,
      AuthParameters: {
        USERNAME: email,
        PASSWORD: password,
      },
    });

    const response = await cognitoClient.send(command);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Sign in successful',
        tokens: {
          accessToken: response.AuthenticationResult?.AccessToken,
          idToken: response.AuthenticationResult?.IdToken,
          refreshToken: response.AuthenticationResult?.RefreshToken,
        },
      }),
    };
  } catch (error: any) {
    console.error('SignIn error:', error);
    return {
      statusCode: error.name === 'NotAuthorizedException' ? 401 : 500,
      body: JSON.stringify({
        message: error.message || 'Error signing in',
      }),
    };
  }
}

async function handleConfirmSignUp(email: string, code: string): Promise<APIGatewayProxyResult> {
  try {
    const command = new ConfirmSignUpCommand({
      ClientId: process.env.USER_POOL_CLIENT_ID,
      Username: email,
      ConfirmationCode: code,
    });

    await cognitoClient.send(command);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Email verified successfully',
      }),
    };
  } catch (error: any) {
    console.error('ConfirmSignUp error:', error);
    return {
      statusCode: error.name === 'CodeMismatchException' ? 400 : 500,
      body: JSON.stringify({
        message: error.message || 'Error confirming sign up',
      }),
    };
  }
}

async function handleResendConfirmationCode(email: string): Promise<APIGatewayProxyResult> {
  try {
    const command = new ResendConfirmationCodeCommand({
      ClientId: process.env.USER_POOL_CLIENT_ID,
      Username: email,
    });

    await cognitoClient.send(command);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Verification code resent successfully',
      }),
    };
  } catch (error: any) {
    console.error('ResendConfirmationCode error:', error);
    return {
      statusCode: error.name === 'UserNotFoundException' ? 404 : 500,
      body: JSON.stringify({
        message: error.message || 'Error resending confirmation code',
      }),
    };
  }
}

async function handleRefreshToken(refreshToken: string): Promise<APIGatewayProxyResult> {
  try {
    console.log('Processing refresh token request');
    
    const command = new InitiateAuthCommand({
      AuthFlow: 'REFRESH_TOKEN_AUTH',
      ClientId: process.env.USER_POOL_CLIENT_ID!,
      AuthParameters: {
        REFRESH_TOKEN: refreshToken
      }
    });

    const response = await cognitoClient.send(command);

    console.log('Cognito refresh response received');

    // Validate that we got the required tokens
    if (!response.AuthenticationResult?.AccessToken || !response.AuthenticationResult?.IdToken) {
      console.error('Cognito refresh response missing required tokens');
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: 'Invalid response from authentication service'
        })
      };
    }

    // Use the new refresh token if provided, otherwise keep the original
    // Note: Cognito may or may not return a new refresh token depending on configuration
    const newRefreshToken = response.AuthenticationResult?.RefreshToken || refreshToken;

    console.log('Token refresh successful, returning new tokens');

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Token refresh successful',
        tokens: {
          accessToken: response.AuthenticationResult.AccessToken,
          idToken: response.AuthenticationResult.IdToken,
          refreshToken: newRefreshToken
        }
      })
    };
  } catch (error: any) {
    console.error('Error refreshing token:', {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode
    });

    // Provide more specific error handling
    let statusCode = 500;
    let message = 'Error refreshing token';

    if (error.name === 'NotAuthorizedException') {
      statusCode = 401;
      message = 'Refresh token is invalid or expired';
    } else if (error.name === 'UserNotFoundException') {
      statusCode = 404;
      message = 'User not found';
    } else if (error.name === 'TokenRefreshException') {
      statusCode = 401;
      message = 'Token refresh failed';
    }

    return {
      statusCode,
      body: JSON.stringify({
        message: message,
        error: error.name
      })
    };
  }
}

/**
 * Delete all user data from DynamoDB tables
 * @param userId The user ID to delete data for
 */
async function deleteAllUserData(userId: string): Promise<void> {
  console.log(`Deleting all data for user ID: ${userId}`);

  try {
    // 1. Delete user profile
    if (USER_PROFILE_TABLE) {
      console.log(`Deleting user profile from ${USER_PROFILE_TABLE}`);
      await dynamoDb.send(new DeleteCommand({
        TableName: USER_PROFILE_TABLE,
        Key: { userId }
      }));
    }

    // 2. Delete user stats
    if (USER_STATS_TABLE) {
      console.log(`Deleting user stats from ${USER_STATS_TABLE}`);
      await dynamoDb.send(new DeleteCommand({
        TableName: USER_STATS_TABLE,
        Key: { userId }
      }));
    }

    // 3. Delete user achievements
    if (USER_ACHIEVEMENTS_TABLE) {
      console.log(`Deleting user achievements from ${USER_ACHIEVEMENTS_TABLE}`);

      // First, query all achievements for this user
      const achievementsResponse = await dynamoDb.send(new QueryCommand({
        TableName: USER_ACHIEVEMENTS_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: { ':userId': userId }
      }));

      // Then delete each achievement
      if (achievementsResponse.Items && achievementsResponse.Items.length > 0) {
        console.log(`Found ${achievementsResponse.Items.length} achievements to delete`);

        // Use BatchWrite for efficiency if there are many items
        const batchSize = 25; // DynamoDB batch size limit
        for (let i = 0; i < achievementsResponse.Items.length; i += batchSize) {
          const batch = achievementsResponse.Items.slice(i, i + batchSize);

          const deleteRequests = batch.map(item => ({
            DeleteRequest: {
              Key: {
                userId: userId,
                achievementId: item.achievementId
              }
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [USER_ACHIEVEMENTS_TABLE]: deleteRequests
            }
          }));
        }
      }
    }

    // 4. Delete user context data
    if (USER_CONTEXT_TABLE) {
      console.log(`Deleting user context data from ${USER_CONTEXT_TABLE}`);

      // First, query all context entries for this user
      const contextResponse = await dynamoDb.send(new QueryCommand({
        TableName: USER_CONTEXT_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: { ':userId': userId }
      }));

      // Then delete each context entry
      if (contextResponse.Items && contextResponse.Items.length > 0) {
        console.log(`Found ${contextResponse.Items.length} context entries to delete`);

        // Use BatchWrite for efficiency if there are many items
        const batchSize = 25; // DynamoDB batch size limit
        for (let i = 0; i < contextResponse.Items.length; i += batchSize) {
          const batch = contextResponse.Items.slice(i, i + batchSize);

          const deleteRequests = batch.map(item => ({
            DeleteRequest: {
              Key: {
                userId: userId,
                contextKey: item.contextKey
              }
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [USER_CONTEXT_TABLE]: deleteRequests
            }
          }));
        }
      }
    }

    // 5. Delete TDEE trend data
    if (TDEE_TREND_TABLE) {
      console.log(`Deleting TDEE trend data from ${TDEE_TREND_TABLE}`);

      // First, query all TDEE entries for this user
      const tdeeResponse = await dynamoDb.send(new QueryCommand({
        TableName: TDEE_TREND_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: { ':userId': userId }
      }));

      // Then delete each TDEE entry
      if (tdeeResponse.Items && tdeeResponse.Items.length > 0) {
        console.log(`Found ${tdeeResponse.Items.length} TDEE entries to delete`);

        // Use BatchWrite for efficiency if there are many items
        const batchSize = 25; // DynamoDB batch size limit
        for (let i = 0; i < tdeeResponse.Items.length; i += batchSize) {
          const batch = tdeeResponse.Items.slice(i, i + batchSize);

          const deleteRequests = batch.map(item => ({
            DeleteRequest: {
              Key: {
                userId: userId,
                date: item.date
              }
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [TDEE_TREND_TABLE]: deleteRequests
            }
          }));
        }
      }
    }

    // 6. Delete log entries
    if (LOG_TABLE) {
      console.log(`Deleting log entries from ${LOG_TABLE}`);

      // First, query all log entries for this user
      const logResponse = await dynamoDb.send(new QueryCommand({
        TableName: LOG_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: { ':userId': userId }
      }));

      // Then delete each log entry
      if (logResponse.Items && logResponse.Items.length > 0) {
        console.log(`Found ${logResponse.Items.length} log entries to delete`);

        // Use BatchWrite for efficiency if there are many items
        const batchSize = 25; // DynamoDB batch size limit
        for (let i = 0; i < logResponse.Items.length; i += batchSize) {
          const batch = logResponse.Items.slice(i, i + batchSize);

          const deleteRequests = batch.map(item => ({
            DeleteRequest: {
              Key: {
                userId: userId,
                timestamp: item.timestamp
              }
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [LOG_TABLE]: deleteRequests
            }
          }));
        }
      }
    }

    // 7. Delete conversations
    if (CONVERSATION_TABLE) {
      console.log(`Deleting conversations from ${CONVERSATION_TABLE}`);

      // First, query all conversations for this user
      const conversationResponse = await dynamoDb.send(new QueryCommand({
        TableName: CONVERSATION_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: { ':userId': userId }
      }));

      // Then delete each conversation
      if (conversationResponse.Items && conversationResponse.Items.length > 0) {
        console.log(`Found ${conversationResponse.Items.length} conversations to delete`);

        // Use BatchWrite for efficiency if there are many items
        const batchSize = 25; // DynamoDB batch size limit
        for (let i = 0; i < conversationResponse.Items.length; i += batchSize) {
          const batch = conversationResponse.Items.slice(i, i + batchSize);

          const deleteRequests = batch.map(item => ({
            DeleteRequest: {
              Key: {
                userId: userId,
                conversationId: item.conversationId
              }
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [CONVERSATION_TABLE]: deleteRequests
            }
          }));

          // Also delete messages for each conversation
          if (MESSAGE_TABLE) {
            for (const conversation of batch) {
              await deleteMessagesForConversation(conversation.conversationId);
            }
          }
        }
      }
    }

    console.log(`Successfully deleted all data for user ID: ${userId}`);
  } catch (error) {
    console.error('Error deleting user data:', error);
    throw error;
  }
}

/**
 * Delete all messages for a conversation
 * @param conversationId The conversation ID to delete messages for
 */
async function deleteMessagesForConversation(conversationId: string): Promise<void> {
  if (!MESSAGE_TABLE) return;

  console.log(`Deleting messages for conversation: ${conversationId}`);

  try {
    // Query all messages for this conversation
    const messagesResponse = await dynamoDb.send(new QueryCommand({
      TableName: MESSAGE_TABLE,
      KeyConditionExpression: 'conversationId = :conversationId',
      ExpressionAttributeValues: { ':conversationId': conversationId }
    }));

    // Delete each message
    if (messagesResponse.Items && messagesResponse.Items.length > 0) {
      console.log(`Found ${messagesResponse.Items.length} messages to delete for conversation ${conversationId}`);

      // Use BatchWrite for efficiency if there are many items
      const batchSize = 25; // DynamoDB batch size limit
      for (let i = 0; i < messagesResponse.Items.length; i += batchSize) {
        const batch = messagesResponse.Items.slice(i, i + batchSize);

        const deleteRequests = batch.map(item => ({
          DeleteRequest: {
            Key: {
              conversationId: conversationId,
              timestamp: item.timestamp
            }
          }
        }));

        await dynamoDb.send(new BatchWriteCommand({
          RequestItems: {
            [MESSAGE_TABLE]: deleteRequests
          }
        }));
      }
    }
  } catch (error) {
    console.error(`Error deleting messages for conversation ${conversationId}:`, error);
    throw error;
  }
}

async function handleDeleteAccount(email: string, authHeader?: string): Promise<APIGatewayProxyResult> {
  try {
    if (!authHeader) {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Authentication required' }),
      };
    }

    if (!email) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Email is required' }),
      };
    }

    console.log(`Processing account deletion request for: ${email}`);

    // Verify the token first
    try {
      // Get username from the token (sub claim)
      const token = authHeader.replace('Bearer ', '');
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const payload = JSON.parse(jsonPayload);
      const userId = payload.sub;

      console.log(`Token verification successful, proceeding with account deletion for user ID: ${userId}`);

      // First, delete all user data from DynamoDB
      console.log('Deleting all user data from DynamoDB');
      await deleteAllUserData(userId);

      // Then, delete the user from Cognito
      console.log('Creating AdminDeleteUserCommand for Cognito User Pool');
      const deleteCommand = new AdminDeleteUserCommand({
        UserPoolId: process.env.USER_POOL_ID!,
        Username: email as string,  // Cognito can use email as username if that's how account was created
      });

      console.log('Sending AdminDeleteUserCommand to Cognito');
      await cognitoClient.send(deleteCommand);

      console.log(`User account deleted successfully: ${email}`);
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Account and all associated data deleted successfully' }),
      };
    } catch (verifyError: any) {
      console.error('Error verifying token or deleting user:', verifyError);

      // Check if it's a token verification error
      if (verifyError.name === 'JwtVerificationError') {
        return {
          statusCode: 401,
          body: JSON.stringify({ message: 'Invalid token' }),
        };
      }

      throw verifyError; // Re-throw for outer catch block
    }
  } catch (error: any) {
    console.error('DeleteAccount error:', error);

    return {
      statusCode: error.name === 'UserNotFoundException' ? 404 : 500,
      body: JSON.stringify({
        message: error.message || 'Error deleting account',
      }),
    };
  }
}