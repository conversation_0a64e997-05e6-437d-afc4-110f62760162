import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, PutCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from './utils/auth-helper';

const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const USER_PROFILE_TABLE = process.env.USER_PROFILE_TABLE_NAME || '';

// Log critical environment info at startup
console.log('Profile Lambda initialized with:', {
  USER_PROFILE_TABLE,
  USER_POOL_ID: process.env.USER_POOL_ID || 'NOT SET',
  CLIENT_ID: process.env.CLIENT_ID || 'NOT SET',
  USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID || 'NOT SET'
});

// Helper function to extract userId directly from token for debugging
function extractUserIdFromToken(token: string): string | null {
  try {
    // Basic JWT decode to extract userId as fallback
    const parts = token.split('.');
    if (parts.length === 3) {
      // Handle base64url format
      const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');
      const pad = base64.length % 4;
      const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;

      const payload = JSON.parse(Buffer.from(paddedBase64, 'base64').toString());

      // Try different possible user ID fields
      const userId = payload.sub ||
                     payload['cognito:username'] ||
                     payload.username ||
                     payload.email;

      return userId || null;
    }
    return null;
  } catch (error) {
    console.error('Error manually decoding token:', error);
    return null;
  }
}

// Enable CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type,Authorization',
  'Access-Control-Allow-Methods': 'OPTIONS,GET,POST,PUT,DELETE'
};

// Verify DynamoDB table exists
async function verifyDynamoTable(): Promise<boolean> {
  if (!USER_PROFILE_TABLE) {
    console.error('USER_PROFILE_TABLE_NAME environment variable is not set');
    return false;
  }

  try {
    // Try a simple operation to verify table access
    await dynamoDb.send(new GetCommand({
      TableName: USER_PROFILE_TABLE,
      Key: { userId: 'test-connection' }
    }));
    console.log('Successfully verified DynamoDB table access');
    return true;
  } catch (error) {
    console.error('Error verifying DynamoDB table access:', error);
    return false;
  }
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('Profile Lambda invoked with event:', {
    path: event.path,
    method: event.httpMethod,
    hasBody: !!event.body,
    hasAuth: !!event.headers.Authorization,
    queryParams: event.queryStringParameters
  });

  // Handle preflight requests for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Verify DynamoDB configuration
  const tableAccessOk = await verifyDynamoTable();
  if (!tableAccessOk) {
    console.error(`DynamoDB table access verification failed`);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Database configuration error' }),
    };
  }

  try {
    // Extract the authorization token
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      console.error('Missing authorization header in request');
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Missing authorization token' }),
      };
    }

    // Verify and extract user ID from token
    const token = authHeader.replace('Bearer ', '');
    console.log('Token received:', token.substring(0, 20) + '...');

    // Try helper first, if it fails try direct extraction
    const authResult = await verifyToken(token);
    let userId: string | null = null;

    if (authResult && authResult.isValid && authResult.userId) {
      userId = authResult.userId;
      console.log('Token verification successful, userId:', userId);
    } else {
      // If verification fails, try direct extraction as a fallback
      console.log('Token verification failed, trying direct extraction fallback');
      userId = extractUserIdFromToken(token);

      if (userId) {
        console.log('Successfully extracted userId directly from token:', userId);
      } else {
        console.error('Both token verification and direct extraction failed');
        return {
          statusCode: 401,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Invalid or expired token' }),
        };
      }
    }

    console.log(`Request authenticated for userId: ${userId}`);

    // Handle different HTTP methods
    switch (event.httpMethod) {
      case 'GET':
        // Make sure userId is a string
        if (userId) {
          return await getProfile(userId);
        } else {
          console.error('Invalid userId for GET request');
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ message: 'Invalid user ID' }),
          };
        }
      case 'POST':
      case 'PUT':
        if (!event.body) {
          console.error('Missing request body for profile creation/update');
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ message: 'Missing request body' }),
          };
        }
        try {
          console.log('Parsing request body');
          const profileData = JSON.parse(event.body);
          console.log('Processing profile update with body data:', {
            name: profileData.name,
            hasExistingCreatedAt: !!profileData.createdAt
          });

          // Make sure userId is a string
          if (userId) {
            return await updateProfile(userId, profileData);
          } else {
            console.error('Invalid userId for POST/PUT request');
            return {
              statusCode: 400,
              headers: corsHeaders,
              body: JSON.stringify({ message: 'Invalid user ID' }),
            };
          }
        } catch (parseError) {
          console.error('Error parsing request body:', parseError, 'Raw body:', event.body);
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ message: 'Invalid request body format' }),
          };
        }
      default:
        console.warn(`Unsupported HTTP method: ${event.httpMethod}`);
        return {
          statusCode: 405,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Method not allowed' }),
        };
    }
  } catch (error) {
    console.error('Error in profile lambda handler:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
};

async function getProfile(userId: string): Promise<APIGatewayProxyResult> {
  try {
    console.log(`Getting profile for userId: ${userId}`);
    if (!USER_PROFILE_TABLE) {
      console.error('USER_PROFILE_TABLE_NAME environment variable is not set');
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Database configuration error - table name missing' }),
      };
    }

    const params = {
      TableName: USER_PROFILE_TABLE,
      Key: { userId },
    };

    console.log(`Querying DynamoDB table: ${USER_PROFILE_TABLE}`);
    let result;

    try {
      result = await dynamoDb.send(new GetCommand(params));
      console.log('DynamoDB query completed successfully');
    } catch (dbError: any) {
      console.error(`DynamoDB error:`, dbError);

      if (dbError.name === 'ResourceNotFoundException') {
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({
            message: 'Database table not found',
            tableName: USER_PROFILE_TABLE
          }),
        };
      }

      throw dbError;
    }

    if (!result?.Item) {
      console.log(`No profile found for userId: ${userId}`);
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Profile not found' }),
      };
    }

    console.log(`Profile found for userId: ${userId}:`, {
      name: result.Item.name,
      createdAt: result.Item.createdAt,
      updatedAt: result.Item.updatedAt
    });

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(result.Item),
    };
  } catch (error) {
    console.error(`Error getting profile for userId ${userId}:`, error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Error retrieving profile' }),
    };
  }
}

async function updateProfile(userId: string, profileData: any): Promise<APIGatewayProxyResult> {
  try {
    // First check if there's existing data in the DB
    console.log(`Checking for existing profile for userId: ${userId}`);
    let existingProfile = null;

    try {
      const getParams = {
        TableName: USER_PROFILE_TABLE,
        Key: { userId },
      };

      const result = await dynamoDb.send(new GetCommand(getParams));
      existingProfile = result.Item;

      if (existingProfile) {
        console.log(`Found existing profile for userId: ${userId} created at ${existingProfile.createdAt}`);
      } else {
        console.log(`No existing profile found for userId: ${userId}`);
      }
    } catch (readError) {
      console.error(`Error checking for existing profile:`, readError);
      // Continue with create flow even if read fails
    }

    console.log(`Updating profile for userId: ${userId} with data:`, {
      name: profileData.name,
      birthday: profileData.birthday,
      fitnessGoal: profileData.fitnessGoal,
      weight: profileData.weight,
      height: profileData.height
    });

    // Start with clean profile data
    const cleanedProfileData = {
      name: profileData.name,
      birthday: profileData.birthday || '',
      weight: profileData.weight || '',
      height: profileData.height || '',
      fitnessGoal: profileData.fitnessGoal || ''
    };

    // Add timestamps and userId
    const now = new Date().toISOString();
    const profile: any = {
      ...cleanedProfileData,
      userId,
      updatedAt: now,
    };

    // Handle createdAt properly
    if (existingProfile?.createdAt) {
      // Use existing createAt timestamp from DB if available
      profile.createdAt = existingProfile.createdAt;
      console.log(`Using existing createAt from DB: ${profile.createdAt}`);
    } else if (profileData.createdAt) {
      // Use createdAt from request if provided and no existing DB record
      profile.createdAt = profileData.createdAt;
      console.log(`Using createdAt from request: ${profile.createdAt}`);
    } else {
      // No createdAt found, this is a new profile
      profile.createdAt = now;
      console.log(`New profile creation (adding createdAt: ${profile.createdAt})`);
    }

    const params = {
      TableName: USER_PROFILE_TABLE,
      Item: profile,
    };

    console.log(`Writing to DynamoDB table: ${USER_PROFILE_TABLE}`);
    try {
      await dynamoDb.send(new PutCommand(params));
      console.log(`Successfully saved profile to DynamoDB for userId: ${userId}`);
    } catch (dbError: any) {
      console.error(`DynamoDB write error:`, dbError);
      if (dbError.name === 'ResourceNotFoundException') {
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({
            message: 'Database table not found',
            tableName: USER_PROFILE_TABLE
          }),
        };
      }
      throw dbError;
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(profile),
    };
  } catch (error) {
    console.error(`Error updating profile for userId ${userId}:`, error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Error updating profile' }),
    };
  }
}