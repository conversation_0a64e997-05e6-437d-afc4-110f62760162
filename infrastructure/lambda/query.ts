import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoJwtVerifier } from 'aws-jwt-verify';

const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// NOTE: We no longer use direct JWT verification in this Lambda
// since API Gateway's Cognito authorizer already validates the tokens.
// We keep the code but comment it out for reference only.
/*
const cognitoJwtVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.USER_POOL_ID!,
  tokenUse: 'access', // This expected an accessToken, creating conflict with API Gateway's idToken validation
  clientId: process.env.USER_POOL_CLIENT_ID!,
});
*/

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing request body' }),
      };
    }

    // Get userId from the claims passed by the API Gateway authorizer
    // API Gateway Cognito authorizer has already verified the token
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    const { query, type, startDate, endDate } = JSON.parse(event.body);

    if (!query) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing query' }),
      };
    }

    // Build query parameters
    const queryParams: any = {
      TableName: process.env.LOG_TABLE_NAME,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
    };

    // Add type filter if specified
    if (type) {
      queryParams.IndexName = 'TypeIndex';
      queryParams.KeyConditionExpression += ' AND #type = :type';
      queryParams.ExpressionAttributeNames = {
        '#type': 'type',
      };
      queryParams.ExpressionAttributeValues[':type'] = type;
    }

    // Add date range if specified
    if (startDate && endDate) {
      queryParams.KeyConditionExpression += ' AND #timestamp BETWEEN :startDate AND :endDate';
      queryParams.ExpressionAttributeNames['#timestamp'] = 'timestamp';
      queryParams.ExpressionAttributeValues[':startDate'] = startDate;
      queryParams.ExpressionAttributeValues[':endDate'] = endDate;
    }

    // Execute query
    const command = new QueryCommand(queryParams);
    const response = await docClient.send(command);

    // Process results based on query type
    const results = processQueryResults(response.Items || [], query);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Query executed successfully',
        results,
      }),
    };
  } catch (error: any) {
    console.error('Error:', error);
    
    if (error.name === 'JwtExpiredError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Token expired' }),
      };
    }

    if (error.name === 'JwtInvalidSignatureError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Invalid token signature' }),
      };
    }

    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
};

function processQueryResults(items: any[], query: string): any {
  // Simple keyword-based query processing
  const queryLower = query.toLowerCase();

  if (queryLower.includes('last workout')) {
    const workouts = items.filter(item => item.type === 'workout');
    return workouts.length > 0 ? workouts[0] : null;
  }

  if (queryLower.includes('last meal')) {
    const meals = items.filter(item => item.type === 'meal');
    return meals.length > 0 ? meals[0] : null;
  }

  if (queryLower.includes('total workouts')) {
    return items.filter(item => item.type === 'workout').length;
  }

  if (queryLower.includes('total meals')) {
    return items.filter(item => item.type === 'meal').length;
  }

  // Default: return all matching items
  return items;
} 