import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDB } from 'aws-sdk';
import { verifyToken } from './utils/auth-helper';

const dynamoDB = new DynamoDB.DocumentClient();
const DAILY_DIGEST_TABLE = process.env.DAILY_DIGEST_TABLE_NAME || '';
const USER_CONTEXT_TABLE = process.env.USER_CONTEXT_TABLE_NAME || '';
const USER_PROFILE_TABLE = process.env.USER_PROFILE_TABLE_NAME || '';

// Interface for a digest activity
interface DigestActivity {
  id: string;
  type: string;
  title: string;
  description: string;
  scheduledTime: string; // ISO string
  completed: boolean;
  isUserOverride: boolean;
  originalActivity?: DigestActivity; // If this is an override, store the original
  metadata?: Record<string, any>;
}

// Interface for a full day's digest
interface DayDigest {
  userId: string;
  date: string; // ISO date string (YYYY-MM-DD)
  activities: DigestActivity[];
  lastUpdated: string; // ISO timestamp
  isAutoGenerated: boolean;
}

/**
 * Main handler for the digest Lambda
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Handle OPTIONS requests for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Retry-Attempt,X-Auth-Protocol-Version,Cache-Control,Pragma',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
        'Access-Control-Allow-Credentials': true,
      },
      body: '',
    };
  }
  
  // Check for protocol version flag - this indicates client using improved auth handling
  const isV2AuthProtocol = event.headers?.['X-Auth-Protocol-Version'] === '2';
  if (isV2AuthProtocol) {
    console.log('Client is using V2 Auth Protocol - lenient auth checking enabled');
  }
  
  // Check if this is a retry attempt
  const isRetryAttempt = event.headers?.['X-Retry-Attempt'] === 'true';
  if (isRetryAttempt) {
    console.log('This is a retry attempt from client - special handling enabled');
  }

  try {
    // Log the request details for debugging
    console.log('Digest API request details:', {
      path: event.path,
      method: event.httpMethod,
      hasAuth: !!event.headers?.Authorization,
      authHeader: event.headers?.Authorization ? 
        `${event.headers.Authorization.substring(0, 12)}...` : 'missing',
      pathParams: event.pathParameters || {},
      queryParams: event.queryStringParameters || {}
    });
    
    // Verify the user's token
    const authResult = await verifyToken(event);
    
    // Enhanced error handling and debugging for auth issues
    if (!authResult.isValid) {
      console.warn('WARNING: Token validation failed, but continuing for development');
      
      // Try to extract a userId directly from the token as fallback
      // This is a temporary development measure to help diagnose client issues
      let tempUserId = null;
      const auth = event.headers?.Authorization || '';
      if (auth.startsWith('Bearer ')) {
        const token = auth.substring(7);
        try {
          const parts = token.split('.');
          if (parts.length === 3) {
            const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');
            const pad = base64.length % 4;
            const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;
            const payload = JSON.parse(Buffer.from(paddedBase64, 'base64').toString());
            tempUserId = payload.sub || payload['cognito:username'] || payload.email;
            
            if (tempUserId) {
              console.log(`Extracted temporary userId from token payload: ${tempUserId}`);
            }
          }
        } catch (parseError) {
          console.error('Error extracting userId from token:', parseError);
        }
      }
      
      // In development, use the extracted userId as a fallback
      if (tempUserId) {
        console.warn(`DEVELOPMENT MODE: Proceeding with extracted userId: ${tempUserId}`);
        // Use the GLOBAL authResult to set this so it's available in the outside scope
        authResult.userId = tempUserId;
        authResult.isValid = true;
        console.log(`Request authorized for userId: ${tempUserId} (development fallback)`);
      } else {
        // In production, this would return 401
        console.error('ERROR: userId not found in authResult or token payload', authResult);
        return {
          statusCode: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
          },
          body: JSON.stringify({ 
            message: 'Unauthorized: Token validation failed',
            error: 'invalid_token'
          }),
        };
      }
    }
    
    const userId = authResult?.userId;
    
    if (!userId) {
      console.error('ERROR: userId not found in authResult', authResult);
      return {
        statusCode: 401,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          message: 'Unauthorized: User ID missing from token verification',
          error: 'missing_user_id'
        }),
      };
    }
    
    console.log(`Request authorized for userId: ${userId}`);

    // Handle different HTTP methods
    switch (event.httpMethod) {
      case 'GET':
        if (event.pathParameters?.date) {
          // Get digest for a specific date
          return await getDigestForDate(userId, event.pathParameters.date);
        } else {
          // Get digest for a date range (default to current week)
          const startDate = event.queryStringParameters?.startDate || getStartOfWeek();
          const endDate = event.queryStringParameters?.endDate || getEndOfWeek();
          return await getDigestRange(userId, startDate, endDate);
        }
      case 'POST':
        // Check if this is a regenerate request
        if (event.pathParameters?.date && event.path.includes('/regenerate')) {
          // Regenerate digest for a specific date using Groq
          return await regenerateDigestForDate(userId, event.pathParameters.date, event);
        } else {
          // Create a new digest (usually for future dates)
          return await createDigest(userId, event);
        }
      case 'PUT':
        if (event.pathParameters?.date) {
          // Update digest for a specific date
          return await updateDigestForDate(userId, event.pathParameters.date, event);
        } else {
          // Bulk update digests
          return await bulkUpdateDigests(userId, event);
        }
      case 'DELETE':
        if (event.pathParameters?.date) {
          // Delete digest for a specific date
          return await deleteDigestForDate(userId, event.pathParameters.date);
        } else {
          return {
            statusCode: 400,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Credentials': true,
            },
            body: JSON.stringify({ message: 'Date parameter is required for DELETE' }),
          };
        }
      default:
        return {
          statusCode: 405,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
          },
          body: JSON.stringify({ message: 'Method not allowed' }),
        };
    }
  } catch (error) {
    console.error('Error in digest handler:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Internal server error', error: (error as Error).message }),
    };
  }
};

/**
 * Get digest for a specific date
 */
async function getDigestForDate(userId: string, date: string): Promise<APIGatewayProxyResult> {
  try {
    // Check if digest exists in the database
    const params = {
      TableName: DAILY_DIGEST_TABLE,
      Key: {
        userId,
        date,
      },
    };

    const result = await dynamoDB.get(params).promise();

    if (result.Item) {
      // Return existing digest
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify(result.Item),
      };
    } else {
      // Check if this is a future date
      const requestedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (requestedDate >= today) {
        // For future dates, always generate a digest using Groq
        console.log(`No digest found for future date ${date}. Generating with Groq.`);
        
        // Fetch necessary context for Groq
        const userProfile = await getUserProfile(userId);
        const userContext = await getUserContext(userId);
        const logHistory = await getLogHistory(userId);

        if (!userProfile) {
          console.warn(`User profile not found for ${userId}. Proceeding with limited context for Groq.`);
        }

        const activities = await generateActivitiesWithGroq(
          userId,
          date,
          userProfile || {}, // Pass empty object if profile is null
          userContext || [],  // Pass empty array if context is null
          logHistory || []    // Pass empty array if history is null
        );

        const newDigest: DayDigest = {
          userId,
          date,
          activities,
          lastUpdated: new Date().toISOString(),
          isAutoGenerated: true, // Mark as auto-generated by Groq
        };

        // Save the generated digest
        await saveDigest(newDigest);

        return {
          statusCode: 200,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': true,
          },
          body: JSON.stringify(newDigest),
        };
      }

      // For past dates or dates more than a week in the future, return empty digest
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          userId,
          date,
          activities: [],
          lastUpdated: new Date().toISOString(),
          isAutoGenerated: false,
        }),
      };
    }
  } catch (error) {
    console.error('Error getting digest for date:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error getting digest', error: (error as Error).message }),
    };
  }
};

/**
 * Get digest for a date range
 */
async function getDigestRange(userId: string, startDate: string, endDate: string): Promise<APIGatewayProxyResult> {
  try {
    // Query the database for digests in the date range
    const params = {
      TableName: DAILY_DIGEST_TABLE,
      KeyConditionExpression: 'userId = :userId AND #date BETWEEN :startDate AND :endDate',
      ExpressionAttributeNames: {
        '#date': 'date',
      },
      ExpressionAttributeValues: {
        ':userId': userId,
        ':startDate': startDate,
        ':endDate': endDate,
      },
    };

    const result = await dynamoDB.query(params).promise();
    const existingDigests = result.Items as DayDigest[] || [];

    // Convert date strings to Date objects for comparison
    const start = new Date(startDate);
    const end = new Date(endDate);
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    // Create a map of existing digests by date
    const digestMap = new Map<string, DayDigest>();
    existingDigests.forEach(digest => {
      digestMap.set(digest.date, digest);
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const allDigests: DayDigest[] = [];

    // Fetch context once before the loop for efficiency
    let userProfile: any = null;
    let userContext: any[] = [];
    let logHistory: any[] = [];

    // Only fetch context if there's a possibility of generating new digests (i.e., range includes future dates)
    if (end >= today) {
      console.log('Fetching user context for potential Groq generation in getDigestRange.');
      userProfile = await getUserProfile(userId);
      userContext = await getUserContext(userId);
      logHistory = await getLogHistory(userId);
      if (!userProfile) {
        console.warn(`User profile not found for ${userId} in getDigestRange. Proceeding with limited context.`);
      }
    }

    // Iterate through each day in the range
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateString = d.toISOString().split('T')[0];

      if (digestMap.has(dateString)) {
        // Use existing digest
        allDigests.push(digestMap.get(dateString)!);
      } else if (d >= today) {
        // Generate new digest for all future dates using Groq
        console.log(`Generating new digest for future date ${dateString} using Groq in getDigestRange.`);
        
        const activities = await generateActivitiesWithGroq(
          userId,
          dateString,
          userProfile || {},
          userContext || [],
          logHistory || []
        );

        const newDigest: DayDigest = {
          userId,
          date: dateString,
          activities,
          lastUpdated: new Date().toISOString(),
          isAutoGenerated: true, // Mark as auto-generated by Groq
        };
        
        await saveDigest(newDigest);
        allDigests.push(newDigest);
      } else {
        // For past dates, add empty digest
        allDigests.push({
          userId,
          date: dateString,
          activities: [],
          lastUpdated: new Date().toISOString(),
          isAutoGenerated: false,
        });
      }
    }

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(allDigests),
    };
  } catch (error) {
    console.error('Error getting digest range:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error getting digest range', error: (error as Error).message }),
    };
  }
}

/**
 * Create a new digest
 */
async function createDigest(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  try {
    const body = JSON.parse(event.body || '{}');
    const { date, activities } = body;

    if (!date) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ message: 'Date is required' }),
      };
    }

    // Check if digest already exists
    const existingDigest = await getDigestFromDB(userId, date);
    if (existingDigest) {
      return {
        statusCode: 409,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ message: 'Digest already exists for this date' }),
      };
    }

    // Create new digest
    const newDigest: DayDigest = {
      userId,
      date,
      activities: activities || [],
      lastUpdated: new Date().toISOString(),
      isAutoGenerated: false,
    };

    // Save to database
    await saveDigest(newDigest);

    return {
      statusCode: 201,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(newDigest),
    };
  } catch (error) {
    console.error('Error creating digest:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error creating digest', error: (error as Error).message }),
    };
  }
}

/**
 * Update digest for a specific date
 */
async function updateDigestForDate(userId: string, date: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  try {
    const body = JSON.parse(event.body || '{}');
    const { activities } = body;

    // Check if digest exists
    const existingDigest = await getDigestFromDB(userId, date);
    if (!existingDigest) {
      return {
        statusCode: 404,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ message: 'Digest not found for this date' }),
      };
    }

    // Update digest
    const updatedDigest: DayDigest = {
      ...existingDigest,
      activities: activities || existingDigest.activities,
      lastUpdated: new Date().toISOString(),
      isAutoGenerated: false, // Mark as manually updated
    };

    // Save to database
    await saveDigest(updatedDigest);

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(updatedDigest),
    };
  } catch (error) {
    console.error('Error updating digest:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error updating digest', error: (error as Error).message }),
    };
  }
}

/**
 * Bulk update digests
 */
async function bulkUpdateDigests(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  try {
    const body = JSON.parse(event.body || '{}');
    const { digests } = body;

    if (!Array.isArray(digests)) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ message: 'Digests must be an array' }),
      };
    }

    // Process each digest
    const updatePromises = digests.map(async (digest: any) => {
      if (!digest.date) {
        throw new Error('Each digest must have a date');
      }

      // Check if digest exists
      const existingDigest = await getDigestFromDB(userId, digest.date);

      // Create or update digest
      const updatedDigest: DayDigest = {
        userId,
        date: digest.date,
        activities: digest.activities || (existingDigest ? existingDigest.activities : []),
        lastUpdated: new Date().toISOString(),
        isAutoGenerated: false, // Mark as manually updated
      };

      // Save to database
      await saveDigest(updatedDigest);

      return updatedDigest;
    });

    const updatedDigests = await Promise.all(updatePromises);

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(updatedDigests),
    };
  } catch (error) {
    console.error('Error bulk updating digests:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error bulk updating digests', error: (error as Error).message }),
    };
  }
}

/**
 * Delete digest for a specific date
 */
async function deleteDigestForDate(userId: string, date: string): Promise<APIGatewayProxyResult> {
  try {
    // Delete from database
    const params = {
      TableName: DAILY_DIGEST_TABLE,
      Key: {
        userId,
        date,
      },
    };

    await dynamoDB.delete(params).promise();

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Digest deleted successfully' }),
    };
  } catch (error) {
    console.error('Error deleting digest:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ message: 'Error deleting digest', error: (error as Error).message }),
    };
  }
}

/**
 * Generate a digest for a specific date
 */
async function generateDigestForDate(userId: string, date: string): Promise<DayDigest> {
  console.log(`Generating digest for user ${userId} on ${date}`);

  try {
    // Get user profile
    const userProfile = await getUserProfile(userId);
    console.log('Retrieved user profile:', { 
      hasProfile: !!userProfile,
      wakeUpTime: userProfile?.wakeUpTime,
      fitnessGoal: userProfile?.fitnessGoal 
    });

    // Get ALL user context (not filtered by type)
    const userContext = await getUserContext(userId);
    console.log(`Retrieved ${userContext?.length || 0} context items for comprehensive analysis`);
    
         // Log sample context items for debugging
     if (userContext && userContext.length > 0) {
       console.log('Sample context items:');
       userContext.slice(0, 3).forEach((ctx: any, index: number) => {
         console.log(`Context ${index + 1}:`, {
           type: ctx.contextType || 'unknown',
           value: typeof ctx.value === 'string' ? ctx.value.substring(0, 100) : ctx.value,
           timestamp: ctx.timestamp,
           source: ctx.source
         });
       });
     } else {
       console.log('No context data found for user');
     }

    // Get log history for variety and progression
    const logHistory = await getLogHistory(userId);
    console.log(`Retrieved ${logHistory?.length || 0} log history items`);

    // Get previous digests for variety
    const previousDigests = await getPreviousDigests(userId, date, 5);
    console.log(`Retrieved ${previousDigests?.length || 0} previous digests for variety`);

    // Get nutrition targets
    const nutritionTargets = await getNutritionTargets(userId);
    console.log('Retrieved nutrition targets:', nutritionTargets ? 'Yes' : 'No');

    let activities: DigestActivity[] = [];

    try {
             // Use the new comprehensive LLM-first approach
       console.log('Attempting comprehensive LLM generation with all user context...');
       activities = await generateActivitiesWithGroq(
         userId,
         date,
         userProfile,
         userContext, // Pass ALL context to LLM
         logHistory,
         undefined, // No specific adjustment instruction
         previousDigests,
         nutritionTargets
       );
      
      console.log(`Successfully generated ${activities.length} activities using comprehensive LLM approach`);
    } catch (groqError) {
      console.error('Comprehensive LLM generation failed:', groqError);
      
             // Fallback to template-based generation
       console.log('Falling back to template-based generation...');
       try {
         activities = await generateActivitiesWithTemplate(userId, date, userProfile, userContext, true);
         console.log(`Generated ${activities.length} activities using template fallback`);
       } catch (fallbackError) {
         console.error('Template fallback also failed:', fallbackError);
         console.log('Will use minimal default activities...');
       }
    }

    // Ensure we have at least some activities
    if (!activities || activities.length === 0) {
      console.warn('Still no activities after all fallbacks. Creating minimal default activities.');
      
      // Use calculated meal times from user profile if available, otherwise use context-aware defaults
      let breakfastTime = '08:00';
      let lunchTime = '12:30';
      let dinnerTime = '19:00';
      let wakeUpTime = '07:00';
      
      // Try to extract timing from user context
      if (userContext && userContext.length > 0) {
                 // Look for recent preferences or daily preferences
         const recentPrefs = userContext
           .filter((ctx: any) => 
             ctx.contextType === 'preference' || 
             ctx.metadata?.category === 'daily_preferences'
           )
           .sort((a: any, b: any) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());
        
        if (recentPrefs.length > 0) {
          const latestPref = recentPrefs[0];
          
          // Try to extract wake-up time from context
          if (latestPref.value && typeof latestPref.value === 'string') {
            const wakeUpMatch = latestPref.value.match(/(?:i|I)\s*(?:wake|get)\s*(?:up|at)?\s*(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
            if (wakeUpMatch) {
              let hours = parseInt(wakeUpMatch[1]);
              const minutes = wakeUpMatch[2] ? parseInt(wakeUpMatch[2]) : 0;
              const period = wakeUpMatch[3]?.toLowerCase();

              // Convert to 24-hour format
              if (period === 'pm' && hours < 12) {
                hours += 12;
              } else if (period === 'am' && hours === 12) {
                hours = 0;
              }

              wakeUpTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
              
              // Recalculate meal times based on wake-up time
              const wakeMinutes = hours * 60 + minutes;
              
              // Breakfast: 45 minutes after wake-up
              const breakfastMinutes = wakeMinutes + 45;
              breakfastTime = `${Math.floor(breakfastMinutes / 60).toString().padStart(2, '0')}:${(breakfastMinutes % 60).toString().padStart(2, '0')}`;
              
              // Lunch: 4.5 hours after breakfast
              const lunchMinutes = breakfastMinutes + (4.5 * 60);
              lunchTime = `${Math.floor(lunchMinutes / 60).toString().padStart(2, '0')}:${(lunchMinutes % 60).toString().padStart(2, '0')}`;
              
              // Dinner: 6.5 hours after lunch
              const dinnerMinutes = lunchMinutes + (6.5 * 60);
              dinnerTime = `${Math.floor(dinnerMinutes / 60).toString().padStart(2, '0')}:${(dinnerMinutes % 60).toString().padStart(2, '0')}`;
              
              console.log(`Extracted wake-up time from context: ${wakeUpTime}, calculated meal times: breakfast=${breakfastTime}, lunch=${lunchTime}, dinner=${dinnerTime}`);
            }
          }
        }
      }
      
      // Use user profile meal times if available, otherwise use calculated times
      if (userProfile?.preferredMealTimes) {
        breakfastTime = userProfile.preferredMealTimes.breakfast || breakfastTime;
        lunchTime = userProfile.preferredMealTimes.lunch || lunchTime;
        dinnerTime = userProfile.preferredMealTimes.dinner || dinnerTime;
      }
      
      console.log(`Using meal times for minimal fallback: breakfast=${breakfastTime}, lunch=${lunchTime}, dinner=${dinnerTime}, wakeUp=${wakeUpTime}`);

      // Generate minimal activities with context-aware timing
      activities = [
        {
          id: generateId(),
          type: 'meal',
          title: 'Energizing Morning Breakfast',
          description: 'Start your day with a nutritious breakfast to fuel your morning.',
          scheduledTime: `${date}T${breakfastTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Oats', 'Banana', 'Almond milk', 'Honey'],
              nutrition: { calories: 350, protein: 12, carbs: 55, fat: 8 },
              steps: ['Cook oats', 'Add banana and honey', 'Serve with almond milk']
            }
          }
        },
        {
          id: generateId(),
          type: 'meal',
          title: 'Balanced Lunch',
          description: 'A well-balanced midday meal to maintain your energy levels.',
          scheduledTime: `${date}T${lunchTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Chicken breast', 'Mixed vegetables', 'Brown rice'],
              nutrition: { calories: 450, protein: 35, carbs: 40, fat: 12 },
              steps: ['Grill chicken', 'Steam vegetables', 'Cook rice', 'Combine and serve']
            }
          }
        },
        {
          id: generateId(),
          type: 'meal',
          title: 'Nutritious Dinner',
          description: 'End your day with a satisfying and healthy dinner.',
          scheduledTime: `${date}T${dinnerTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Salmon fillet', 'Broccoli', 'Sweet potato'],
              nutrition: { calories: 480, protein: 38, carbs: 35, fat: 18 },
              steps: ['Bake salmon', 'Steam broccoli', 'Roast sweet potato', 'Plate and serve']
            }
          }
        }
      ];
    }

    // Create the digest
    const digest: DayDigest = {
      userId,
      date,
      activities,
      lastUpdated: new Date().toISOString(),
      isAutoGenerated: true,
    };

    // Save the digest
    await saveDigest(digest);
    console.log(`Successfully saved digest for ${userId} on ${date} with ${activities.length} activities`);

    return digest;
  } catch (error) {
    console.error(`Error generating digest for ${userId} on ${date}:`, error);
    throw error;
  }
}

/**
 * Regenerate a digest for a specific date using Groq, potentially with adjustment instructions.
 */
async function regenerateDigestForDate(userId: string, date: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  try {
    // Extra checks for auth debugging
    console.log('Authentication details for regenerate digest call:', {
      userId: userId,
      authHeaderPresent: !!event.headers?.Authorization,
      authHeader: event.headers?.Authorization ? `${event.headers.Authorization.substring(0, 10)}...` : 'none',
      isV2Protocol: event.headers?.['X-Auth-Protocol-Version'] === '2',
      isRetryAttempt: event.headers?.['X-Retry-Attempt'] === 'true'
    });
    
    // Special bypass token for testing (only development)
    const isSpecialBypass = event.headers?.['X-Retry-Attempt'] === 'true' &&
                          event.headers?.['X-Auth-Protocol-Version'] === '2';
    
    if (isSpecialBypass) {
      console.log('Using special bypass auth for retry attempt - allowing operation to proceed');
    }
    
    const body = JSON.parse(event.body || '{}');
    // Extract adjustmentInstruction from the body, sent by chatService
    const { 
      forceRegenerate = true, 
      useGroq = true, 
      adjustmentInstruction,
      bypassCache = false,  // New flag to bypass any caching
      contextData  // Context data sent from client
    } = body;

    console.log(`Regenerating digest for user ${userId} on date ${date}`);
    console.log(`Options: forceRegenerate=${forceRegenerate}, useGroq=${useGroq}, bypassCache=${bypassCache}`);
    if (adjustmentInstruction) {
      console.log(`Adjustment Instruction: "${adjustmentInstruction}"`);
    }
    if (contextData) {
      console.log(`Client sent context data:`, JSON.stringify(contextData, null, 2));
    }

    // Get user profile data
    console.log('Fetching user profile data...');
    let userProfile = await getUserProfile(userId);

    // Get ALL user context data - this is important for personalization
    console.log('Fetching ALL user context data...');
    let userContext = await getUserContext(userId);

    // Merge client-sent context data with database context
    if (contextData) {
      console.log('Merging client-sent context data with database context...');
      
      // Process daily preferences from client
      if (contextData.dailyPreferences) {
        console.log('Processing daily preferences from client:', contextData.dailyPreferences);
        
        // Add the daily preferences to the context array with highest priority
        if (!userContext) userContext = [];
        
        // Remove any existing daily preferences to avoid conflicts
        userContext = userContext.filter((ctx: any) => 
          ctx.metadata?.category !== 'daily_preferences' && 
          ctx.contextType !== 'daily_preferences'
        );
        
        // Add new daily preferences at the beginning for highest priority
        userContext.unshift(contextData.dailyPreferences);
        
        // Extract wake-up time from daily preferences and update user profile
        let extractedWakeUpTime = null;
        
        // First check metadata
        if (contextData.dailyPreferences.metadata?.wakeUpTime) {
          extractedWakeUpTime = contextData.dailyPreferences.metadata.wakeUpTime;
          console.log(`Found wake-up time in metadata: ${extractedWakeUpTime}`);
        }
        // Then check text value
        else if (contextData.dailyPreferences.value && typeof contextData.dailyPreferences.value === 'string') {
          const wakeUpTimeMatch = contextData.dailyPreferences.value.match(/(?:i|I)\s*(?:wake|get)\s*(?:up|at)\s*(?:at)?\s*(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
          
          if (wakeUpTimeMatch) {
            let hours = parseInt(wakeUpTimeMatch[1]);
            const minutes = wakeUpTimeMatch[2] ? parseInt(wakeUpTimeMatch[2]) : 0;
            const period = wakeUpTimeMatch[3]?.toLowerCase();

            // Convert to 24-hour format
            if (period === 'pm' && hours < 12) {
              hours += 12;
            } else if (period === 'am' && hours === 12) {
              hours = 0;
            }

            extractedWakeUpTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
            console.log(`Extracted wake-up time from text: ${extractedWakeUpTime}`);
          }
        }
        
        if (extractedWakeUpTime) {
          console.log(`Updating user profile wake-up time to: ${extractedWakeUpTime}`);
          if (!userProfile) userProfile = {};
          userProfile.wakeUpTime = extractedWakeUpTime;
          
          // Calculate meal times based on wake-up time
          const [wakeHour, wakeMinute] = extractedWakeUpTime.split(':').map(Number);
          
          // Breakfast: 30-60 minutes after wake-up
          let breakfastTotalMinutes = wakeHour * 60 + wakeMinute + 45; // 45 minutes after wake-up
          const breakfastHour = Math.floor(breakfastTotalMinutes / 60);
          const breakfastMinute = breakfastTotalMinutes % 60;
          
          // Lunch: 4-5 hours after breakfast
          let lunchTotalMinutes = breakfastTotalMinutes + (4.5 * 60); // 4.5 hours after breakfast
          const lunchHour = Math.floor(lunchTotalMinutes / 60);
          const lunchMinute = lunchTotalMinutes % 60;
          
          // Dinner: 6-7 hours after lunch
          let dinnerTotalMinutes = lunchTotalMinutes + (6.5 * 60); // 6.5 hours after lunch
          const dinnerHour = Math.floor(dinnerTotalMinutes / 60);
          const dinnerMinute = dinnerTotalMinutes % 60;
          
          userProfile.preferredMealTimes = {
            breakfast: `${breakfastHour.toString().padStart(2, '0')}:${breakfastMinute.toString().padStart(2, '0')}`,
            lunch: `${lunchHour.toString().padStart(2, '0')}:${lunchMinute.toString().padStart(2, '0')}`,
            dinner: `${dinnerHour.toString().padStart(2, '0')}:${dinnerMinute.toString().padStart(2, '0')}`
          };
          
          console.log('Calculated meal times based on wake-up time:', userProfile.preferredMealTimes);
        }
      }
      
      // Add other context data if present with priority handling
      if (contextData.allPreferences && Array.isArray(contextData.allPreferences)) {
        console.log(`Adding ${contextData.allPreferences.length} preference context items from client`);
        
        // Process each preference and remove older conflicting ones
        contextData.allPreferences.forEach((newPref: any) => {
          if (newPref.metadata?.category) {
            // Remove existing context of the same category
            userContext = userContext.filter((ctx: any) => 
              ctx.metadata?.category !== newPref.metadata.category
            );
          }
          
          // Add new preference at the beginning for priority
          userContext.unshift(newPref);
        });
      }
      
      if (contextData.allCustom && Array.isArray(contextData.allCustom)) {
        console.log(`Adding ${contextData.allCustom.length} custom context items from client`);
        
        // Process each custom context and remove older conflicting ones
        contextData.allCustom.forEach((newCustom: any) => {
          if (newCustom.contextType) {
            // Remove existing context of the same type
            userContext = userContext.filter((ctx: any) => 
              ctx.contextType !== newCustom.contextType
            );
          }
          
          // Add new custom context at the beginning for priority
          userContext.unshift(newCustom);
        });
      }
    }

    console.log(`Retrieved ${userContext?.length || 0} context items for user ${userId} (including client data)`);

    // Get detailed workout, meal, and weight history from the log table
    console.log('Fetching detailed log history...');
    const logHistory = await getLogHistory(userId);
    console.log(`Retrieved ${logHistory?.length || 0} log history items for user ${userId}`);

    // Get previous days' digests for variety (last 5 days)
    console.log('Fetching previous days\' digests for variety...');
    const previousDigests = await getPreviousDigests(userId, date, 5);
    console.log(`Retrieved ${previousDigests?.length || 0} previous digests for variety`);

    // Log some context information for debugging
    if (userContext && Array.isArray(userContext)) {
      const contextTypes = new Set(userContext.map(ctx => ctx.contextType));
      console.log(`Context types found: ${Array.from(contextTypes).join(', ')}`);
    }

    // Get nutrition targets for the user
    console.log('Fetching nutrition targets...');
    const nutritionTargets = await getNutritionTargets(userId);

    // Generate new activities with Groq integration, passing the adjustment instruction
    console.log('Generating activities with Groq integration...');
    
    let activities: DigestActivity[] = []; // Explicitly typed
    try {
      // Try to use Groq first
      const { generateActivitiesWithGroq } = require('./utils/groq-digest-helper');
      activities = await generateActivitiesWithGroq(userId, date, userProfile, userContext, logHistory, adjustmentInstruction, previousDigests, nutritionTargets);
      
      // Verify we got comprehensive activities back
      if (!activities || !Array.isArray(activities) || activities.length < 4) {
        console.warn(`Groq integration returned insufficient activities (${activities?.length || 0}). Enhancing with template generation.`);
        
        // If Groq returned some activities but not enough, supplement with templates
        if (activities && activities.length > 0) {
          console.log('Supplementing existing Groq activities with template activities');
          const { generateBasicActivities } = require('./utils/groq-digest-helper');
          const nutritionTargets = await getNutritionTargets(userId);
          const templateActivities = await generateBasicActivities(date, userProfile, userContext, logHistory, 'Supplementing Groq activities', nutritionTargets);
          
          // Merge activities, avoiding duplicates by type and time
          const existingTypes = new Set(activities.map(a => `${a.type}-${a.scheduledTime}`));
          const supplementalActivities = templateActivities.filter((ta: DigestActivity) => 
            !existingTypes.has(`${ta.type}-${ta.scheduledTime}`)
          );
          
          activities = [...activities, ...supplementalActivities];
          console.log(`Enhanced to ${activities.length} total activities`);
        } else {
          // If Groq returned nothing, fall back to templates
          const { generateBasicActivities } = require('./utils/groq-digest-helper');
          const nutritionTargets = await getNutritionTargets(userId);
          activities = await generateBasicActivities(date, userProfile, userContext, logHistory, 'Groq returned no activities', nutritionTargets);
          console.log(`Generated ${activities.length} fallback template activities`);
        }
      } else {
        console.log(`Groq successfully generated ${activities.length} comprehensive activities`);
      }
    } catch (groqError) {
      console.error('Error calling generateActivitiesWithGroq:', groqError);
      // Fall back to template generation
      const { generateBasicActivities } = require('./utils/groq-digest-helper');
      const nutritionTargets = await getNutritionTargets(userId);
      activities = await generateBasicActivities(date, userProfile, userContext, logHistory, 'Groq error fallback', nutritionTargets);
      console.log(`Generated ${activities.length} fallback template activities after Groq error`);
    }

    // Ensure we have at least some activities
    if (!activities || activities.length === 0) {
      console.warn('Still no activities after template fallback. Creating comprehensive default activities.');
      // Generate comprehensive activities as absolute fallback
      
      // Use calculated meal times from user profile if available
      const breakfastTime = userProfile?.preferredMealTimes?.breakfast || '08:00';
      const lunchTime = userProfile?.preferredMealTimes?.lunch || '12:30';
      const dinnerTime = userProfile?.preferredMealTimes?.dinner || '19:00';
      const wakeUpTime = userProfile?.wakeUpTime || '07:00';
      
      console.log(`Using meal times for fallback: breakfast=${breakfastTime}, lunch=${lunchTime}, dinner=${dinnerTime}, wakeUp=${wakeUpTime}`);
      
      activities = [
        {
          id: generateId(),
          type: 'meal',
          title: 'Protein-Rich Breakfast Bowl',
          description: 'Greek yogurt with fresh berries, granola, and nuts for sustained energy.',
          scheduledTime: `${date}T${breakfastTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Greek yogurt', 'Mixed berries', 'Granola', 'Almonds', 'Honey'],
              nutrition: { calories: 380, protein: 25, carbs: 42, fat: 12 },
              steps: ['Add yogurt to bowl', 'Top with berries and granola', 'Drizzle with honey']
            }
          }
        },
        {
          id: generateId(),
          type: 'water',
          title: 'Morning Hydration',
          description: 'Start your day with a large glass of water to kickstart your metabolism.',
          scheduledTime: `${date}T${wakeUpTime.split(':')[0].padStart(2, '0')}:30:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            waterDetails: {
              amount: '16 oz',
              temperature: 'room temperature',
              benefits: ['Hydration', 'Metabolism boost', 'Mental clarity']
            }
          }
        },
        {
          id: generateId(),
          type: 'meal',
          title: 'Mediterranean Lunch Salad',
          description: 'Grilled chicken with mixed greens, vegetables, and olive oil dressing.',
          scheduledTime: `${date}T${lunchTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Grilled chicken', 'Mixed greens', 'Cherry tomatoes', 'Cucumber', 'Feta cheese', 'Olive oil'],
              nutrition: { calories: 420, protein: 35, carbs: 15, fat: 22 },
              steps: ['Grill chicken breast', 'Prepare salad base', 'Add toppings', 'Dress with olive oil']
            }
          }
        },
        {
          id: generateId(),
          type: 'water',
          title: 'Midday Hydration Check',
          description: 'Stay hydrated throughout the day with regular water intake.',
          scheduledTime: `${date}T14:00:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            waterDetails: {
              amount: '12 oz',
              reminder: 'Check your hydration levels',
              benefits: ['Sustained energy', 'Better focus', 'Healthy skin']
            }
          }
        },
        {
          id: generateId(),
          type: 'workout',
          title: 'Full Body Strength Training',
          description: 'Balanced workout targeting all major muscle groups for overall fitness.',
          scheduledTime: `${date}T17:00:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            workoutDetails: {
              duration: 45,
              exercises: [
                { name: 'Squats', sets: 3, reps: '12-15', notes: 'Focus on proper form' },
                { name: 'Push-ups', sets: 3, reps: '10-12', notes: 'Modify as needed' },
                { name: 'Lunges', sets: 3, reps: '10 per leg', notes: 'Alternate legs' },
                { name: 'Plank', sets: 3, reps: '30-60 seconds', notes: 'Keep core tight' },
                { name: 'Dumbbell Rows', sets: 3, reps: '10-12', notes: 'Squeeze shoulder blades' }
              ],
              warmup: '5 minutes dynamic stretching',
              cooldown: '5 minutes static stretching'
            }
          }
        },
        {
          id: generateId(),
          type: 'meal',
          title: 'Balanced Dinner Plate',
          description: 'Baked salmon with roasted vegetables and quinoa for complete nutrition.',
          scheduledTime: `${date}T${dinnerTime}:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            mealDetails: {
              ingredients: ['Salmon fillet', 'Broccoli', 'Sweet potato', 'Quinoa', 'Olive oil', 'Herbs'],
              nutrition: { calories: 480, protein: 38, carbs: 35, fat: 18 },
              steps: ['Season and bake salmon', 'Roast vegetables', 'Cook quinoa', 'Plate and serve']
            }
          }
        },
        {
          id: generateId(),
          type: 'water',
          title: 'Evening Hydration',
          description: 'Final hydration reminder to meet your daily water intake goals.',
          scheduledTime: `${date}T20:00:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            waterDetails: {
              amount: '8 oz',
              timing: 'At least 2 hours before bed',
              benefits: ['Recovery', 'Detoxification', 'Better sleep preparation']
            }
          }
        },
        {
          id: generateId(),
          type: 'sleep',
          title: 'Wind Down Routine',
          description: 'Prepare your body and mind for quality sleep and recovery.',
          scheduledTime: `${date}T21:30:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            sleepDetails: {
              duration: '30 minutes',
              activities: ['Dim lights', 'Light stretching', 'Deep breathing', 'No screens'],
              benefits: ['Better sleep quality', 'Faster recovery', 'Reduced stress']
            }
          }
        },
        {
          id: generateId(),
          type: 'reminder',
          title: 'Daily Reflection',
          description: 'Take a moment to reflect on your day and plan for tomorrow.',
          scheduledTime: `${date}T22:00:00`,
          completed: false,
          isUserOverride: false,
          metadata: {
            reflectionDetails: {
              questions: [
                'What went well today?',
                'What could I improve tomorrow?',
                'How did I feel during my workout?',
                'Did I meet my nutrition goals?'
              ],
              duration: '5-10 minutes'
            }
          }
        }
      ];
    }

    // Create the new digest
    const newDigest: DayDigest = {
      userId,
      date,
      activities,
      lastUpdated: new Date().toISOString(),
      isAutoGenerated: false, // Mark as manually regenerated
    };

    // Save to database
    console.log(`Saving digest with ${activities.length} activities to database...`);
    await saveDigest(newDigest);

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(newDigest),
    };
  } catch (error) {
    console.error('Error regenerating digest:', error);
    
    try {
      // Last resort fallback - if everything fails, return a minimal valid digest
      const minimalDigest: DayDigest = {
        userId,
        date,
        activities: [
          {
            id: generateId(),
            type: 'reminder',
            title: 'Plan Your Day',
            description: 'Take some time to plan your meals and workouts for today.',
            scheduledTime: `${date}T09:00:00`,
            completed: false,
            isUserOverride: false
          }
        ],
        lastUpdated: new Date().toISOString(),
        isAutoGenerated: true,
      };
      
      // Try to save minimal digest
      try {
        await saveDigest(minimalDigest);
      } catch (saveError) {
        console.error('Failed to save minimal digest:', saveError);
      }
      
      console.log('Returning minimal fallback digest due to regeneration error');
      
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify(minimalDigest),
      };
    } catch (fallbackError) {
      console.error('Error creating minimal fallback digest:', fallbackError);
      return {
        statusCode: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ message: 'Error regenerating digest', error: (error as Error).message }),
      };
    }
  }
}

/**
 * Get nutrition targets for the user
 */
async function getNutritionTargets(userId: string): Promise<any> {
  try {
    console.log(`Fetching nutrition targets for user ${userId}`);
    
    // Try to get nutrition targets from the nutrition service
    // This would typically be a call to the nutrition service API
    // For now, we'll calculate basic targets based on user profile
    
    const userProfile = await getUserProfile(userId);
    
    if (!userProfile) {
      console.warn(`No user profile found for ${userId}, using default nutrition targets`);
      return {
        calories: 2000,
        protein: 150,
        carbs: 200,
        fat: 65
      };
    }

    // Extract user data for nutrition calculation
    const weight = parseFloat(userProfile.weight) || 150; // Default weight in lbs
    const height = parseFloat(userProfile.height) || 67; // Default height in inches
    const age = userProfile.birthday ? calculateAge(userProfile.birthday) : 30;
    const gender = userProfile.gender || 'male';
    const activityLevel = userProfile.activityLevel || 'moderatelyActive';
    const goal = userProfile.fitnessGoal || 'maintain';

    console.log('Nutrition calculation inputs:', {
      weight,
      height,
      age,
      gender,
      activityLevel,
      goal
    });

    // Calculate BMR using Mifflin-St Jeor Equation
    const weightKg = weight * 0.453592; // Convert lbs to kg
    const heightCm = height * 2.54; // Convert inches to cm
    
    let bmr: number;
    if (gender.toLowerCase() === 'male') {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) + 5;
    } else {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) - 161;
    }

    // Activity multipliers
    const activityMultipliers = {
      sedentary: 1.2,
      lightlyActive: 1.375,
      moderatelyActive: 1.55,
      veryActive: 1.725,
      extraActive: 1.9
    };

    const multiplier = activityMultipliers[activityLevel as keyof typeof activityMultipliers] || 1.55;
    const tdee = bmr * multiplier;

    // Adjust calories based on goal
    let targetCalories = tdee;
    if (goal.toLowerCase().includes('lose') || goal.toLowerCase().includes('cut')) {
      targetCalories = tdee - 500; // 500 calorie deficit for weight loss
    } else if (goal.toLowerCase().includes('gain') || goal.toLowerCase().includes('bulk')) {
      targetCalories = tdee + 300; // 300 calorie surplus for weight gain
    }

    // Calculate macros
    // Protein: 1g per lb of body weight for active individuals
    const protein = Math.round(weight * 1.0);
    
    // Fat: 25-30% of total calories
    const fatCalories = targetCalories * 0.275; // 27.5%
    const fat = Math.round(fatCalories / 9); // 9 calories per gram of fat
    
    // Carbs: remaining calories
    const proteinCalories = protein * 4; // 4 calories per gram
    const remainingCalories = targetCalories - proteinCalories - fatCalories;
    const carbs = Math.round(remainingCalories / 4); // 4 calories per gram

    const nutritionTargets = {
      calories: Math.round(targetCalories),
      protein,
      carbs,
      fat,
      bmr: Math.round(bmr),
      tdee: Math.round(tdee)
    };

    console.log('Calculated nutrition targets:', nutritionTargets);
    return nutritionTargets;

  } catch (error) {
    console.error('Error calculating nutrition targets:', error);
    // Return default targets on error
    return {
      calories: 2000,
      protein: 150,
      carbs: 200,
      fat: 65
    };
  }
}

/**
 * Calculate age from birthday string
 */
function calculateAge(birthday: string): number {
  try {
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return 30; // Default age
  }
}

/**
 * Generate activities for a digest with Groq API integration, optionally using an adjustment instruction.
 */
async function generateActivitiesWithGroq(
  userId: string,
  date: string,
  userProfile: any,
  userContext: any,
  logHistory: any[], // Add logHistory parameter
  adjustmentInstruction?: string, // Optional adjustment instruction from user chat
  previousDigests: DayDigest[] = [], // Add previousDigests parameter
  nutritionTargets?: any // Add nutrition targets parameter
): Promise<DigestActivity[]> {
  try {
    // Import the Groq helper
    const { generateActivitiesWithGroq } = require('./utils/groq-digest-helper');

    // First try to use the new Groq API integration
    console.log(`Attempting to generate activities using Groq API for user ${userId} on date ${date}`);

    // Use the nutrition targets passed to this function, or get them if not provided
    const finalNutritionTargets = nutritionTargets || await getNutritionTargets(userId);
    console.log('Retrieved nutrition targets for digest generation:', finalNutritionTargets);

    // Pass all parameters including nutrition targets to the Groq helper function
    return await generateActivitiesWithGroq(
      userId,
      date,
      userProfile,
      userContext,
      logHistory,
      adjustmentInstruction,
      previousDigests,
      finalNutritionTargets // Add nutrition targets parameter
    );
  } catch (error) {
    console.error('Error generating activities with Groq API in generateActivitiesWithGroq:', error);
    // Do not fall back to template-based generation.
    // Throw the error so the caller (regenerateDigestForDate or getDigestForDate) can handle it.
    // Or, return an empty array if that's the desired behavior on Groq failure.
    // For now, let's re-throw to make the failure explicit to the calling function.
    throw error;
  }
}

/**
 * Generate activities for a digest using templates
 */
async function generateActivitiesWithTemplate(
  userId: string,
  date: string,
  userProfile: any,
  userContext: any,
  includeContext: boolean = true
): Promise<DigestActivity[]> {
  // This is the fallback method that uses templates
  const activities: DigestActivity[] = [];
  const dateObj = new Date(date);
  const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.

  // Extract key user preferences from context
  let wakeUpTime = '08:00'; // Default wake-up time
  let bedTime = '22:00'; // Default bed time
  let preferredWorkoutTime = '17:30'; // Default workout time
  let preferredMealTimes = {
    breakfast: '08:30',
    lunch: '12:30',
    dinner: '19:00'
  };

  // First check user profile for basic preferences
  if (userProfile) {
    if (userProfile.wakeUpTime) {
      wakeUpTime = userProfile.wakeUpTime;
      console.log(`Using wake-up time from user profile: ${wakeUpTime}`);
    }
    if (userProfile.bedTime) {
      bedTime = userProfile.bedTime;
      console.log(`Using bed time from user profile: ${bedTime}`);
    }
    if (userProfile.preferredWorkoutTime) {
      preferredWorkoutTime = userProfile.preferredWorkoutTime;
    }
  }

  // Then check context for more specific or updated preferences
  if (userContext && Array.isArray(userContext)) {
    // Look for wake-up time in context
    const wakeUpContexts = userContext.filter(ctx =>
      ctx.contextType === 'preference' &&
      (ctx.value?.toLowerCase().includes('wake up') ||
       ctx.value?.toLowerCase().includes('wakeup') ||
       ctx.value?.toLowerCase().includes('wake at') ||
       ctx.value?.toLowerCase().includes('get up at'))
    );

    if (wakeUpContexts.length > 0) {
      // Sort by timestamp to get the most recent
      wakeUpContexts.sort((a, b) =>
        new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime()
      );

      const mostRecentWakeUpContext = wakeUpContexts[0];

      // Try to extract time from context
      const timeMatch = mostRecentWakeUpContext.value.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i);
      if (timeMatch) {
        let hours = parseInt(timeMatch[1]);
        const minutes = timeMatch[2] ? parseInt(timeMatch[2]) : 0;
        const period = timeMatch[3]?.toLowerCase();

        // Convert to 24-hour format
        if (period === 'pm' && hours < 12) {
          hours += 12;
        } else if (period === 'am' && hours === 12) {
          hours = 0;
        }

        wakeUpTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        console.log(`Found wake-up time in context: ${wakeUpTime}`);
      }
    }

    // Look for bed time in context
    const bedTimeContexts = userContext.filter(ctx =>
      ctx.contextType === 'preference' &&
      (ctx.value?.toLowerCase().includes('bed time') ||
       ctx.value?.toLowerCase().includes('bedtime') ||
       ctx.value?.toLowerCase().includes('sleep at'))
    );

    if (bedTimeContexts.length > 0) {
      // Sort by timestamp to get the most recent
      bedTimeContexts.sort((a, b) =>
        new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime()
      );

      const mostRecentBedTimeContext = bedTimeContexts[0];

      // Try to extract time from context
      const timeMatch = mostRecentBedTimeContext.value.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i);
      if (timeMatch) {
        let hours = parseInt(timeMatch[1]);
        const minutes = timeMatch[2] ? parseInt(timeMatch[2]) : 0;
        const period = timeMatch[3]?.toLowerCase();

        // Convert to 24-hour format
        if (period === 'pm' && hours < 12) {
          hours += 12;
        } else if (period === 'am' && hours === 12) {
          hours = 0;
        }

        bedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        console.log(`Found bed time in context: ${bedTime}`);
      }
    }
  }

  // Calculate meal times based on wake-up time
  // Breakfast is 30 minutes after waking up
  const wakeUpHour = parseInt(wakeUpTime.split(':')[0]);
  const wakeUpMinute = parseInt(wakeUpTime.split(':')[1]);

  let breakfastHour = wakeUpHour;
  let breakfastMinute = wakeUpMinute + 30;

  // Adjust if minutes overflow
  if (breakfastMinute >= 60) {
    breakfastHour += 1;
    breakfastMinute -= 60;
  }

  const breakfastTime = `${breakfastHour.toString().padStart(2, '0')}:${breakfastMinute.toString().padStart(2, '0')}`;

  // Lunch is around midday, but at least 3 hours after breakfast
  const breakfastTimeInMinutes = breakfastHour * 60 + breakfastMinute;
  const midDayInMinutes = 12 * 60 + 30; // 12:30 PM

  let lunchTimeInMinutes = Math.max(midDayInMinutes, breakfastTimeInMinutes + 180);
  const lunchHour = Math.floor(lunchTimeInMinutes / 60);
  const lunchMinute = lunchTimeInMinutes % 60;

  const lunchTime = `${lunchHour.toString().padStart(2, '0')}:${lunchMinute.toString().padStart(2, '0')}`;

  // Dinner is around 6-7 PM, but at least 5 hours after lunch
  const dinnerTimeInMinutes = Math.max(19 * 60, lunchTimeInMinutes + 300);
  const dinnerHour = Math.floor(dinnerTimeInMinutes / 60);
  const dinnerMinute = dinnerTimeInMinutes % 60;

  const dinnerTime = `${dinnerHour.toString().padStart(2, '0')}:${dinnerMinute.toString().padStart(2, '0')}`;

  console.log(`Adjusted meal times based on wake-up time ${wakeUpTime}:`);
  console.log(`Breakfast: ${breakfastTime}, Lunch: ${lunchTime}, Dinner: ${dinnerTime}`);

  // Add workout based on day of week
  if (dayOfWeek !== 0) { // Skip Sunday
    // Different workout types based on day of week
    let workoutTitle = 'Daily Workout';
    let workoutDescription = 'Recommended workout for today';

    if (dayOfWeek === 1 || dayOfWeek === 4) { // Monday and Thursday
      workoutTitle = 'Upper Body Workout';
      workoutDescription = 'Focus on chest, back, shoulders, and arms';
    } else if (dayOfWeek === 2 || dayOfWeek === 5) { // Tuesday and Friday
      workoutTitle = 'Lower Body Workout';
      workoutDescription = 'Focus on legs, glutes, and core';
    } else if (dayOfWeek === 3) { // Wednesday
      workoutTitle = 'Cardio Session';
      workoutDescription = '30 minutes of zone 2 cardio';
    } else if (dayOfWeek === 6) { // Saturday
      workoutTitle = 'Active Recovery';
      workoutDescription = 'Light activity and mobility work';
    }

    // Use the user's preferred workout time
    const workoutTime = userProfile?.preferredWorkoutTime || '17:30'; // Default to 5:30 PM

    activities.push({
      id: generateId(),
      type: 'workout',
      title: workoutTitle,
      description: workoutDescription,
      scheduledTime: `${date}T${workoutTime}:00.000Z`,
      completed: false,
      isUserOverride: false,
      metadata: {
        workoutDetails: {
          title: workoutTitle,
          description: workoutDescription,
          duration: 45,
          notes: 'Focus on proper form and controlled movements. Rest 60-90 seconds between sets.',
          exercises: [
            {
              name: 'Barbell Squat',
              sets: 4,
              reps: '8-10',
              weight: 'moderate',
              notes: 'Keep chest up, push through heels'
            },
            {
              name: 'Dumbbell Bench Press',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Full range of motion'
            },
            {
              name: 'Bent-Over Row',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Squeeze shoulder blades together'
            },
            {
              name: 'Romanian Deadlift',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Hinge at hips, keep back flat'
            },
            {
              name: 'Overhead Press',
              sets: 3,
              reps: '8-10',
              weight: 'moderate',
              notes: 'Brace core throughout'
            },
            {
              name: 'Plank',
              sets: 3,
              reps: '30-60 seconds',
              notes: 'Keep body in straight line'
            }
          ]
        }
      }
    });
  }

  // Check for meal preferences in context
  let breakfastPreference = null;
  let lunchPreference = null;
  let dinnerPreference = null;

  if (userContext && Array.isArray(userContext)) {
    console.log('Searching for meal preferences in user context...');

    // Look for breakfast preferences
    for (const ctx of userContext) {
      if (ctx.contextType === 'preference') {
        const value = ctx.value?.toLowerCase() || '';

        // Check for breakfast preferences
        if (value.includes('breakfast') ||
            (value.includes('morning') && value.includes('eat')) ||
            (value.includes('morning') && value.includes('food'))) {
          console.log(`Found breakfast preference: ${ctx.value}`);
          breakfastPreference = ctx.value;
        }

        // Check for lunch preferences
        if (value.includes('lunch') ||
            (value.includes('afternoon') && value.includes('eat')) ||
            (value.includes('midday') && value.includes('meal'))) {
          console.log(`Found lunch preference: ${ctx.value}`);
          lunchPreference = ctx.value;
        }

        // Check for dinner preferences
        if (value.includes('dinner') ||
            (value.includes('evening') && value.includes('eat')) ||
            (value.includes('night') && value.includes('meal'))) {
          console.log(`Found dinner preference: ${ctx.value}`);
          dinnerPreference = ctx.value;
        }
      }
    }
  }

  // Add meals with detailed information - customize based on preferences if available
  let breakfastTitle = 'Protein-Packed Breakfast Bowl';
  let breakfastDescription = 'Greek yogurt with fresh berries, honey, and granola for a balanced start to your day';
  let breakfastDetails = {
    title: 'Protein-Packed Breakfast Bowl',
    description: 'A nutritious breakfast bowl featuring Greek yogurt, fresh berries, honey, and homemade granola. This balanced meal provides protein, healthy fats, and complex carbohydrates to fuel your morning.',
    nutrition: {
      calories: 420,
      protein: 24,
      carbs: 52,
      fat: 14
    },
    ingredients: [
      "1 cup Greek yogurt (plain, 2% fat)",
      "1/2 cup mixed berries (strawberries, blueberries, raspberries)",
      "1 tablespoon honey or maple syrup",
      "1/4 cup low-sugar granola",
      "1 tablespoon chia seeds",
      "1/2 tablespoon almond butter"
    ],
    instructions: [
      "Add Greek yogurt to a bowl",
      "Top with mixed berries",
      "Drizzle with honey or maple syrup",
      "Sprinkle granola and chia seeds on top",
      "Add a dollop of almond butter",
      "Enjoy immediately for best texture"
    ],
    tags: ["high-protein", "quick", "vegetarian", "breakfast"]
  };

  // In the template-based approach, we use default meals without pattern matching
  console.log('Using default breakfast template for template-based generation');

  activities.push({
    id: generateId(),
    type: 'meal',
    title: breakfastTitle,
    description: breakfastDescription,
    scheduledTime: `${date}T${breakfastTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: breakfastDetails
    }
  });

  // Customize lunch based on preferences
  let lunchTitle = 'Mediterranean Chicken Salad';
  let lunchDescription = 'Grilled chicken with mixed greens, cherry tomatoes, cucumber, olives, and feta cheese with olive oil dressing';
  let lunchDetails = {
    title: 'Mediterranean Chicken Salad',
    description: 'A refreshing and protein-rich salad featuring grilled chicken, fresh vegetables, olives, and feta cheese, dressed with olive oil and lemon. This Mediterranean-inspired dish provides lean protein and healthy fats.',
    nutrition: {
      calories: 380,
      protein: 32,
      carbs: 14,
      fat: 22
    },
    ingredients: [
      "4 oz grilled chicken breast, sliced",
      "2 cups mixed greens",
      "1/2 cup cherry tomatoes, halved",
      "1/4 cucumber, sliced",
      "10 kalamata olives, pitted",
      "2 tablespoons feta cheese, crumbled",
      "1 tablespoon extra virgin olive oil",
      "1/2 tablespoon lemon juice",
      "1/4 teaspoon dried oregano",
      "Salt and pepper to taste"
    ],
    instructions: [
      "Grill chicken breast until fully cooked, then slice",
      "In a large bowl, combine mixed greens, cherry tomatoes, cucumber, and olives",
      "Add sliced chicken on top",
      "Sprinkle with crumbled feta cheese",
      "In a small bowl, whisk together olive oil, lemon juice, oregano, salt, and pepper",
      "Drizzle dressing over the salad just before serving"
    ],
    tags: ["high-protein", "low-carb", "mediterranean", "lunch"]
  };

  // In the template-based approach, we use default meals without pattern matching
  console.log('Using default lunch template for template-based generation');

  activities.push({
    id: generateId(),
    type: 'meal',
    title: lunchTitle,
    description: lunchDescription,
    scheduledTime: `${date}T${lunchTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: lunchDetails
    }
  });

  // Customize dinner based on preferences
  let dinnerTitle = 'Baked Salmon with Roasted Vegetables';
  let dinnerDescription = 'Herb-crusted salmon fillet with roasted sweet potatoes, broccoli, and bell peppers';
  let dinnerDetails = {
    title: 'Baked Salmon with Roasted Vegetables',
    description: 'A nutritious dinner featuring herb-crusted salmon and colorful roasted vegetables. This meal provides omega-3 fatty acids, quality protein, and a variety of vitamins and minerals from the vegetables.',
    nutrition: {
      calories: 450,
      protein: 35,
      carbs: 30,
      fat: 22
    },
    ingredients: [
      "5 oz salmon fillet",
      "1 tablespoon olive oil, divided",
      "1 teaspoon dried herbs (thyme, rosemary, parsley)",
      "1 small sweet potato, cubed",
      "1 cup broccoli florets",
      "1/2 bell pepper, sliced",
      "1/2 small red onion, sliced",
      "1 clove garlic, minced",
      "1/2 lemon",
      "Salt and pepper to taste"
    ],
    instructions: [
      "Preheat oven to 400°F (200°C)",
      "Toss sweet potato, broccoli, bell pepper, and onion with half the olive oil, garlic, salt, and pepper",
      "Spread vegetables on a baking sheet and roast for 10 minutes",
      "Meanwhile, pat salmon dry and season with salt, pepper, and dried herbs",
      "Push vegetables to one side of the baking sheet and add salmon",
      "Drizzle salmon with remaining olive oil",
      "Return to oven and bake for 12-15 minutes until salmon is cooked through",
      "Squeeze fresh lemon over the salmon before serving"
    ],
    tags: ["high-protein", "omega-3", "gluten-free", "dinner"]
  };

  // Customize dinner based on preferences
  if (dinnerPreference) {
    if (dinnerPreference.toLowerCase().includes('pasta') || dinnerPreference.toLowerCase().includes('spaghetti')) {
      dinnerTitle = 'Whole Grain Pasta with Turkey Bolognese';
      dinnerDescription = 'Whole grain spaghetti topped with lean turkey bolognese sauce and fresh basil';
      dinnerDetails = {
        title: 'Whole Grain Pasta with Turkey Bolognese',
        description: 'A healthier take on a classic Italian dish featuring whole grain pasta and a flavorful sauce made with lean ground turkey. This balanced dinner provides complex carbohydrates, protein, and vegetables in one satisfying meal.',
        nutrition: {
          calories: 420,
          protein: 28,
          carbs: 58,
          fat: 12
        },
        ingredients: [
          "2 oz dry whole grain spaghetti",
          "3 oz lean ground turkey",
          "1/4 cup diced onion",
          "1 small garlic clove, minced",
          "1/2 cup crushed tomatoes",
          "1 tablespoon tomato paste",
          "1/4 teaspoon dried oregano",
          "1/4 teaspoon dried basil",
          "1 teaspoon olive oil",
          "1 teaspoon grated Parmesan cheese",
          "Fresh basil leaves for garnish",
          "Salt and pepper to taste"
        ],
        instructions: [
          "Cook pasta according to package directions",
          "Meanwhile, heat olive oil in a pan over medium heat",
          "Add onion and cook until softened, about 3 minutes",
          "Add garlic and cook for 30 seconds until fragrant",
          "Add ground turkey and cook until browned, breaking it up with a spoon",
          "Stir in tomato paste, crushed tomatoes, oregano, basil, salt, and pepper",
          "Simmer for 10 minutes until sauce thickens",
          "Drain pasta and top with sauce",
          "Garnish with Parmesan cheese and fresh basil"
        ],
        tags: ["high-protein", "whole-grain", "italian", "dinner"]
      };
    } else if (dinnerPreference.toLowerCase().includes('stir fry') || dinnerPreference.toLowerCase().includes('asian')) {
      dinnerTitle = 'Chicken and Vegetable Stir Fry with Brown Rice';
      dinnerDescription = 'Lean chicken and colorful vegetables in a light ginger-soy sauce, served over brown rice';
      dinnerDetails = {
        title: 'Chicken and Vegetable Stir Fry with Brown Rice',
        description: 'A quick and flavorful stir fry featuring lean chicken breast, crisp vegetables, and a light ginger-soy sauce. Served over brown rice for a balanced meal rich in protein, fiber, and essential nutrients.',
        nutrition: {
          calories: 410,
          protein: 30,
          carbs: 45,
          fat: 12
        },
        ingredients: [
          "3 oz chicken breast, sliced into thin strips",
          "1/2 cup mixed vegetables (bell peppers, broccoli, snap peas, carrots)",
          "1/3 cup cooked brown rice",
          "1 teaspoon sesame oil",
          "1 teaspoon low-sodium soy sauce",
          "1/2 teaspoon grated fresh ginger",
          "1 small garlic clove, minced",
          "1 teaspoon cornstarch mixed with 1 tablespoon water",
          "1 green onion, sliced (for garnish)",
          "1/2 teaspoon sesame seeds (for garnish)"
        ],
        instructions: [
          "Cook brown rice according to package directions",
          "Heat sesame oil in a wok or large pan over high heat",
          "Add chicken and stir fry until nearly cooked through, about 3 minutes",
          "Add vegetables, ginger, and garlic, and stir fry for 2-3 minutes until crisp-tender",
          "Add soy sauce and cornstarch slurry",
          "Cook for another minute until sauce thickens",
          "Serve over brown rice",
          "Garnish with sliced green onions and sesame seeds"
        ],
        tags: ["high-protein", "quick", "asian-inspired", "dinner"]
      };
    }
  }

  activities.push({
    id: generateId(),
    type: 'meal',
    title: dinnerTitle,
    description: dinnerDescription,
    scheduledTime: `${date}T${dinnerTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: dinnerDetails
    }
  });

  // Add water reminders based on user's schedule
  // Morning hydration - 2 hours after waking up
  const morningHydrationHour = Math.min(wakeUpHour + 2, 11);
  const morningHydrationTime = `${morningHydrationHour.toString().padStart(2, '0')}:${wakeUpMinute.toString().padStart(2, '0')}`;

  activities.push({
    id: generateId(),
    type: 'water',
    title: 'Morning Hydration',
    description: 'Drink 16oz of water',
    scheduledTime: `${date}T${morningHydrationTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
  });

  // Afternoon hydration - midway between lunch and dinner
  const afternoonHydrationTimeInMinutes = Math.floor((lunchTimeInMinutes + dinnerTimeInMinutes) / 2);
  const afternoonHydrationHour = Math.floor(afternoonHydrationTimeInMinutes / 60);
  const afternoonHydrationMinute = afternoonHydrationTimeInMinutes % 60;

  const afternoonHydrationTime = `${afternoonHydrationHour.toString().padStart(2, '0')}:${afternoonHydrationMinute.toString().padStart(2, '0')}`;

  activities.push({
    id: generateId(),
    type: 'water',
    title: 'Afternoon Hydration',
    description: 'Drink 16oz of water',
    scheduledTime: `${date}T${afternoonHydrationTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
  });

  // Add sleep reminder - 1 hour before bedtime
  const bedTimeHour = parseInt(bedTime.split(':')[0]);
  const bedTimeMinute = parseInt(bedTime.split(':')[1]);

  let sleepReminderHour = bedTimeHour;
  let sleepReminderMinute = bedTimeMinute;

  // 1 hour before bedtime
  sleepReminderHour = sleepReminderHour - 1;
  if (sleepReminderHour < 0) {
    sleepReminderHour = 23;
  }

  const sleepReminderTime = `${sleepReminderHour.toString().padStart(2, '0')}:${sleepReminderMinute.toString().padStart(2, '0')}`;

  activities.push({
    id: generateId(),
    type: 'sleep',
    title: 'Sleep Reminder',
    description: 'Prepare for bed to get 8 hours of sleep',
    scheduledTime: `${date}T${sleepReminderTime}:00.000Z`,
    completed: false,
    isUserOverride: false,
  });

  // Sort activities by scheduled time
  return activities.sort((a, b) =>
    new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
  );
}

/**
 * Save a digest to the database
 */
async function saveDigest(digest: DayDigest): Promise<void> {
  const params = {
    TableName: DAILY_DIGEST_TABLE,
    Item: digest,
  };

  await dynamoDB.put(params).promise();
}

/**
 * Get a digest from the database
 */
async function getDigestFromDB(userId: string, date: string): Promise<DayDigest | null> {
  const params = {
    TableName: DAILY_DIGEST_TABLE,
    Key: {
      userId,
      date,
    },
  };

  const result = await dynamoDB.get(params).promise();

  return result.Item as DayDigest || null;
}

/**
 * Get user profile from the database
 */
async function getUserProfile(userId: string): Promise<any> {
  const params = {
    TableName: USER_PROFILE_TABLE,
    Key: {
      userId,
    },
  };

  const result = await dynamoDB.get(params).promise();

  return result.Item || {};
}

/**
 * Get user context from the database
 */
async function getUserContext(userId: string): Promise<any> {
  const params = {
    TableName: USER_CONTEXT_TABLE,
    KeyConditionExpression: 'userId = :userId',
    ExpressionAttributeValues: {
      ':userId': userId,
    },
  };

  const result = await dynamoDB.query(params).promise();

  return result.Items || [];
}

/**
 * Get detailed workout, meal, and weight history from the database
 */
async function getLogHistory(userId: string): Promise<any[]> {
  try {
    const params = {
      TableName: 'LogTable',
      IndexName: 'UserIdIndex',
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
      ScanIndexForward: false, // Sort by timestamp descending
      Limit: 50, // Get last 50 log entries
    };

    const result = await dynamoDB.query(params).promise();
    return result.Items || [];
  } catch (error) {
    console.error('Error fetching log history:', error);
    return [];
  }
}

/**
 * Generate a unique ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

/**
 * Get the start of the current week (Sunday)
 */
function getStartOfWeek(): string {
  const now = new Date();
  const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const diff = now.getDate() - day;
  const startOfWeek = new Date(now.setDate(diff));
  startOfWeek.setHours(0, 0, 0, 0);
  return startOfWeek.toISOString().split('T')[0];
}

/**
 * Get the end of the current week (Saturday)
 */
function getEndOfWeek(): string {
  const now = new Date();
  const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const diff = now.getDate() + (6 - day);
  const endOfWeek = new Date(now.setDate(diff));
  endOfWeek.setHours(0, 0, 0, 0);
  return endOfWeek.toISOString().split('T')[0];
}

async function getPreviousDigests(userId: string, currentDate: string, daysBack: number = 5): Promise<DayDigest[]> {
  try {
    const digests: DayDigest[] = [];
    const currentDateObj = new Date(currentDate);
    
    for (let i = 1; i <= daysBack; i++) {
      const previousDate = new Date(currentDateObj);
      previousDate.setDate(previousDate.getDate() - i);
      const dateString = previousDate.toISOString().split('T')[0];
      
      const digest = await getDigestFromDB(userId, dateString);
      if (digest && digest.activities && digest.activities.length > 0) {
        digests.push(digest);
      }
    }
    
    return digests;
  } catch (error) {
    console.error('Error fetching previous digests:', error);
    return [];
  }
}
