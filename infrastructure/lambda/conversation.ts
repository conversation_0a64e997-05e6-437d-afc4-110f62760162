import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  QueryCommand,
  GetCommand,
  DeleteCommand,
  BatchWriteCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoJwtVerifier } from 'aws-jwt-verify';

// DynamoDB client setup
const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// NOTE: We no longer use direct JWT verification in this Lambda
// since API Gateway's Cognito authorizer already validates the tokens.
// We keep the code but comment it out for reference only.
/*
const cognitoJwtVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.USER_POOL_ID!,
  tokenUse: 'access', // This expected an accessToken, creating conflict with API Gateway's idToken validation
  clientId: process.env.USER_POOL_CLIENT_ID!,
});
*/

// Constants
const CONVERSATION_TABLE = process.env.CONVERSATION_TABLE_NAME!;
const MESSAGE_TABLE = process.env.MESSAGE_TABLE_NAME!;

// Lambda handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Get userId from the claims passed by the API Gateway authorizer
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401, // Or 500, as this shouldn't happen if authorizer is set correctly
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);
    console.log(`*** CONVERSATION LAMBDA V3 RUNNING for user: ${userId} ***`);

    // Route based on HTTP method and path
    const path = event.path.toLowerCase();
    const method = event.httpMethod;

    // Handle conversations
    if (path.endsWith('/conversation')) {
      switch (method) {
        case 'GET':
          return await getConversations(userId);
        case 'POST':
          return await createOrUpdateConversation(userId, event);
        case 'DELETE':
          return await deleteConversation(userId, event);
        default:
          return {
            statusCode: 405,
            body: JSON.stringify({ message: 'Method not allowed' }),
          };
      }
    }

    // Handle bulk conversation deletion
    if (path.endsWith('/conversation/all')) {
      switch (method) {
        case 'DELETE':
          return await deleteAllConversations(userId);
        default:
          return {
            statusCode: 405,
            body: JSON.stringify({ message: 'Method not allowed' }),
          };
      }
    }

    // Handle messages
    if (path.includes('/conversation/messages')) {
      switch (method) {
        case 'GET':
          return await getMessages(userId, event);
        case 'POST':
          return await createMessage(userId, event);
        default:
          return {
            statusCode: 405,
            body: JSON.stringify({ message: 'Method not allowed' }),
          };
      }
    }

    return {
      statusCode: 404,
      body: JSON.stringify({ message: 'Not found' }),
    };
  } catch (error: any) {
    console.error('Error:', error);
    
    // Generic error for other issues
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error', error: error.message }),
    };
  }
};

// Get all conversations for a user
async function getConversations(userId: string): Promise<APIGatewayProxyResult> {
  const command = new QueryCommand({
    TableName: CONVERSATION_TABLE,
    KeyConditionExpression: 'userId = :userId',
    ExpressionAttributeValues: {
      ':userId': userId,
    },
  });

  const response = await docClient.send(command);

  return {
    statusCode: 200,
    body: JSON.stringify({
      conversations: response.Items || [],
    }),
  };
}

// Create or update a conversation
async function createOrUpdateConversation(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  if (!event.body) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing request body' }),
    };
  }

  const { conversationId, description, timestamp } = JSON.parse(event.body);

  if (!conversationId || !timestamp) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing required fields' }),
    };
  }

  const conversation = {
    userId,
    conversationId,
    description: description || 'New conversation',
    timestamp,
    updatedAt: new Date().toISOString(),
  };

  const command = new PutCommand({
    TableName: CONVERSATION_TABLE,
    Item: conversation,
  });

  await docClient.send(command);

  return {
    statusCode: 201,
    body: JSON.stringify({
      message: 'Conversation created/updated successfully',
      conversation,
    }),
  };
}

// Delete a conversation and its messages
async function deleteConversation(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  if (!event.body) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing request body' }),
    };
  }

  const { conversationId } = JSON.parse(event.body);

  if (!conversationId) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing conversationId' }),
    };
  }

  // First, verify the conversation belongs to the user
  const getCommand = new GetCommand({
    TableName: CONVERSATION_TABLE,
    Key: {
      userId,
      conversationId,
    },
  });

  const conversationResult = await docClient.send(getCommand);
  if (!conversationResult.Item) {
    return {
      statusCode: 404,
      body: JSON.stringify({ message: 'Conversation not found' }),
    };
  }

  // Delete the conversation
  const deleteCommand = new DeleteCommand({
    TableName: CONVERSATION_TABLE,
    Key: {
      userId,
      conversationId,
    },
  });

  await docClient.send(deleteCommand);

  // Query for all messages in the conversation
  const messagesCommand = new QueryCommand({
    TableName: MESSAGE_TABLE,
    KeyConditionExpression: 'conversationId = :conversationId',
    ExpressionAttributeValues: {
      ':conversationId': conversationId,
    },
  });

  const messagesResult = await docClient.send(messagesCommand);
  const messages = messagesResult.Items || [];

  // If messages exist, delete them in a batch
  if (messages.length > 0) {
    // DynamoDB batch write can only handle 25 items at a time
    const batches = [];
    for (let i = 0; i < messages.length; i += 25) {
      const batch = messages.slice(i, i + 25);
      const deleteRequests = batch.map(message => ({
        DeleteRequest: {
          Key: {
            conversationId,
            timestamp: message.timestamp,
          },
        },
      }));

      batches.push(deleteRequests);
    }

    // Execute each batch
    for (const batchRequests of batches) {
      const batchWriteCommand = new BatchWriteCommand({
        RequestItems: {
          [MESSAGE_TABLE]: batchRequests,
        },
      });

      await docClient.send(batchWriteCommand);
    }
  }

  return {
    statusCode: 200,
    body: JSON.stringify({
      message: 'Conversation and associated messages deleted successfully',
    }),
  };
}

// Get messages for a specific conversation
async function getMessages(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  const conversationId = event.queryStringParameters?.conversationId;

  if (!conversationId) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing conversationId parameter' }),
    };
  }

  // First, verify the conversation belongs to the user
  const getCommand = new GetCommand({
    TableName: CONVERSATION_TABLE,
    Key: {
      userId,
      conversationId,
    },
  });

  const conversationResult = await docClient.send(getCommand);
  if (!conversationResult.Item) {
    return {
      statusCode: 404,
      body: JSON.stringify({ message: 'Conversation not found' }),
    };
  }

  // Query all messages for the conversation
  const command = new QueryCommand({
    TableName: MESSAGE_TABLE,
    KeyConditionExpression: 'conversationId = :conversationId',
    ExpressionAttributeValues: {
      ':conversationId': conversationId,
    },
    ScanIndexForward: true, // Sort in ascending order (oldest first)
  });

  const response = await docClient.send(command);

  return {
    statusCode: 200,
    body: JSON.stringify({
      messages: response.Items || [],
    }),
  };
}

// Create a new message in a conversation
async function createMessage(userId: string, event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  if (!event.body) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing request body' }),
    };
  }

  const { conversationId, content, role, timestamp, detectedWorkout, detectedMeal } = JSON.parse(event.body);

  if (!conversationId || !content || !role || !timestamp) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing required fields' }),
    };
  }

  // First, verify the conversation exists and belongs to the user
  const getCommand = new GetCommand({
    TableName: CONVERSATION_TABLE,
    Key: {
      userId,
      conversationId,
    },
  });

  const conversationResult = await docClient.send(getCommand);
  if (!conversationResult.Item) {
    return {
      statusCode: 404,
      body: JSON.stringify({ message: 'Conversation not found' }),
    };
  }

  // Create the message
  const message = {
    conversationId,
    timestamp,
    userId, // Store userId for GSI queries
    content,
    role,
    detectedWorkout,
    detectedMeal,
  };

  const command = new PutCommand({
    TableName: MESSAGE_TABLE,
    Item: message,
  });

  await docClient.send(command);

  // Update the conversation's updatedAt timestamp
  const updateConversationCommand = new PutCommand({
    TableName: CONVERSATION_TABLE,
    Item: {
      ...conversationResult.Item,
      updatedAt: new Date().toISOString(),
    },
  });

  await docClient.send(updateConversationCommand);

  return {
    statusCode: 201,
    body: JSON.stringify({
      message: 'Message created successfully',
      data: message,
    }),
  };
}

// Delete all conversations for a user
async function deleteAllConversations(userId: string): Promise<APIGatewayProxyResult> {
  try {
    console.log(`Starting bulk deletion of all conversations for user: ${userId}`);

    // First, get all conversations for the user
    const getConversationsCommand = new QueryCommand({
      TableName: CONVERSATION_TABLE,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
    });

    const conversationsResult = await docClient.send(getConversationsCommand);
    const conversations = conversationsResult.Items || [];

    if (conversations.length === 0) {
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: 'No conversations found to delete',
          deletedCount: 0,
        }),
      };
    }

    console.log(`Found ${conversations.length} conversations to delete`);

    let deletedConversations = 0;
    let deletedMessages = 0;

    // Delete each conversation and its messages
    for (const conversation of conversations) {
      const conversationId = conversation.conversationId;
      
      try {
        // Query for all messages in this conversation
        const messagesCommand = new QueryCommand({
          TableName: MESSAGE_TABLE,
          KeyConditionExpression: 'conversationId = :conversationId',
          ExpressionAttributeValues: {
            ':conversationId': conversationId,
          },
        });

        const messagesResult = await docClient.send(messagesCommand);
        const messages = messagesResult.Items || [];

        // Delete messages in batches if they exist
        if (messages.length > 0) {
          console.log(`Deleting ${messages.length} messages for conversation ${conversationId}`);
          
          // DynamoDB batch write can only handle 25 items at a time
          const batches = [];
          for (let i = 0; i < messages.length; i += 25) {
            const batch = messages.slice(i, i + 25);
            const deleteRequests = batch.map(message => ({
              DeleteRequest: {
                Key: {
                  conversationId,
                  timestamp: message.timestamp,
                },
              },
            }));

            batches.push(deleteRequests);
          }

          // Execute each batch
          for (const batchRequests of batches) {
            const batchWriteCommand = new BatchWriteCommand({
              RequestItems: {
                [MESSAGE_TABLE]: batchRequests,
              },
            });

            await docClient.send(batchWriteCommand);
          }

          deletedMessages += messages.length;
        }

        // Delete the conversation itself
        const deleteConversationCommand = new DeleteCommand({
          TableName: CONVERSATION_TABLE,
          Key: {
            userId,
            conversationId,
          },
        });

        await docClient.send(deleteConversationCommand);
        deletedConversations++;
        
        console.log(`Successfully deleted conversation ${conversationId} and its ${messages.length} messages`);

      } catch (error) {
        console.error(`Error deleting conversation ${conversationId}:`, error);
        // Continue with other conversations even if one fails
      }
    }

    console.log(`Bulk deletion completed. Deleted ${deletedConversations} conversations and ${deletedMessages} messages`);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'All conversations deleted successfully',
        deletedConversations,
        deletedMessages,
        totalFound: conversations.length,
      }),
    };

  } catch (error: any) {
    console.error('Error in bulk conversation deletion:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Failed to delete all conversations',
        error: error.message,
      }),
    };
  }
} 