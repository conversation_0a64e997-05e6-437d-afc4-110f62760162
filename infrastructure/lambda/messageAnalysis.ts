import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from './utils/auth-helper';
import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, PutCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import { Groq } from 'groq-sdk';

const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const LOG_TABLE = process.env.LOG_TABLE_NAME || '';

// Initialize Groq client
let groq: Groq;
try {
    const apiKey = process.env.GROQ_API_KEY;
    if (!apiKey) {
        throw new Error('GROQ_API_KEY is not set in environment variables');
    }
    groq = new Groq({ apiKey });
    console.log('Groq client initialized successfully');
} catch (error) {
    console.error('Failed to initialize Groq client:', error);
    // Create a dummy client that will throw an error if used
    groq = {
        chat: {
            completions: {
                create: async () => {
                    throw new Error('Groq client not properly initialized');
                }
            }
        }
    } as any;
}

// CORS headers for all responses
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
    'Content-Type': 'application/json'
};

// Interface for the analysis response
interface AnalysisResult {
    preferences: {
        value: string;
        confidence: number;
        sentiment: 'positive' | 'negative' | 'neutral';
        category?: string;
        reasoning?: string;
    }[];
    dietaryRestrictions: {
        value: string;
        confidence: number;
        reason?: string;
    }[];
    injuries: {
        value: string;
        confidence: number;
        bodyPart?: string;
        severity?: string;
    }[];
    lifeUpdates: {
        value: string;
        confidence: number;
        timeframe?: string;
    }[];
    goals: {
        value: string;
        confidence: number;
        timeframe?: string;
    }[];
    weight?: number;
}

/**
 * Analyze a message to extract contextual information using an LLM
 *
 * @param message The message to analyze
 * @returns Structured analysis result
 */
async function analyzeMessageForContext(message: string): Promise<AnalysisResult> {
    const systemPrompt = `You are an expert at analyzing user messages to extract key information for a personalized fitness and nutrition app. Your task is to identify preferences, dietary restrictions, injuries, life updates, and goals from the user's message.

Return the information in a structured JSON format. For each item, provide a confidence score from 0 to 1.

CRITICAL INSTRUCTIONS:
1.  **Sentiment Analysis:** For preferences, you MUST correctly identify the sentiment.
    *   If a user says "I don't like quinoa", the sentiment is "negative" and the value is "quinoa".
    *   If a user says "I love running", the sentiment is "positive" and the value is "running".
    *   If a user says "I love dumbbells", the sentiment is "positive" and the value is "dumbbells".
    *   If a user says "I don't like dumbbells", the sentiment is "negative" and the value is "dumbbells".
2.  **Equipment and Exercise Preferences:** Pay special attention to fitness equipment and exercise preferences:
    *   "I love dumbbells" → preference: "dumbbells", sentiment: "positive"
    *   "I don't like dumbbells" → preference: "dumbbells", sentiment: "negative"
    *   "I prefer barbells" → preference: "barbells", sentiment: "positive"
    *   "I hate cardio" → preference: "cardio", sentiment: "negative"
    *   "I dislike machines" → preference: "machines", sentiment: "negative"
3.  **Negative Preference Detection:** Be extremely careful to detect negative preferences:
    *   "I don't like X" → preference: "X", sentiment: "negative"
    *   "I hate X" → preference: "X", sentiment: "negative"
    *   "I dislike X" → preference: "X", sentiment: "negative"
    *   "I'm not a fan of X" → preference: "X", sentiment: "negative"
    *   "X is not for me" → preference: "X", sentiment: "negative"
4.  **Specificity:** Do NOT extract generic terms like "dinner", "lunch", "breakfast", "food", "a meal", or "a workout" as preferences. Focus on specific, tangible items or activities.
5.  **Empty Arrays:** If no specific preferences, restrictions, etc. are mentioned, you MUST return an empty array for that key. Do not invent information.

Here is the JSON structure you must follow:
{
  "preferences": [{ "value": "...", "confidence": 0.9, "sentiment": "positive/negative/neutral", "category": "...", "reasoning": "..." }],
  "dietaryRestrictions": [{ "value": "...", "confidence": 0.9, "reason": "allergy/diet/preference" }],
  "injuries": [{ "value": "...", "confidence": 0.8, "bodyPart": "...", "severity": "mild/moderate/severe" }],
  "lifeUpdates": [{ "value": "...", "confidence": 0.8, "timeframe": "past/present/future" }],
  "goals": [{ "value": "...", "confidence": 0.9, "timeframe": "short-term/long-term" }],
  "weight": 150
}`;

    const userPrompt = `Analyze the following message and extract the relevant information:

"${message}"`;

    try {
        const completion = await groq.chat.completions.create({
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ],
            model: 'meta-llama/llama-4-scout-17b-16e-instruct',
            temperature: 0.2,
            max_tokens: 2000,
            top_p: 0.8,
            response_format: { type: 'json_object' }
        });

        const responseContent = completion.choices?.[0]?.message?.content;
        if (!responseContent) {
            throw new Error('Empty response from Groq API');
        }

        return JSON.parse(responseContent) as AnalysisResult;
    } catch (error) {
        console.error('Error calling Groq API for message analysis:', error);
        throw new Error('Failed to analyze message with LLM');
    }
}

/**
 * Handler for message analysis requests
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Message Analysis Lambda invoked with event:', {
        path: event.path,
        method: event.httpMethod,
        hasBody: !!event.body,
        hasAuth: !!event.headers.Authorization
    });

    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: ''
        };
    }

    try {
        const authorizationHeader = event.headers.Authorization || event.headers.authorization;
        if (!authorizationHeader) {
            return {
                statusCode: 401,
                headers: corsHeaders,
                body: JSON.stringify({ message: 'Unauthorized: Missing authorization header' })
            };
        }

        let userId;
        try {
            const authResult = await verifyToken(authorizationHeader);
            userId = authResult.userId;

            if (!userId) {
                return {
                    statusCode: 401,
                    headers: corsHeaders,
                    body: JSON.stringify({ message: 'Unauthorized: Invalid token payload' })
                };
            }
        } catch (error) {
            console.error('Error verifying token:', error);
            return {
                statusCode: 401,
                headers: corsHeaders,
                body: JSON.stringify({ message: 'Unauthorized: Invalid token' })
            };
        }

        if (!event.body) {
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({ message: 'Missing request body' })
            };
        }

        const requestData = JSON.parse(event.body);
        const { message, messageId, conversationId } = requestData;

        if (!message) {
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({ message: 'Missing required field: message' })
            };
        }

        console.log(`Analyzing message for context: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`);
        const analysisResult = await analyzeMessageForContext(message);

        try {
            const analysisId = messageId || `analysis_${Date.now()}_${uuidv4()}`;

            await dynamoDb.send(new PutCommand({
                TableName: LOG_TABLE,
                Item: {
                    id: analysisId,
                    userId,
                    type: 'message_analysis',
                    timestamp: new Date().toISOString(),
                    message: message.substring(0, 1000),
                    analysis: analysisResult,
                    messageId,
                    conversationId
                }
            }));

            console.log(`Successfully logged analysis for message ${messageId || 'unknown'}`);
        } catch (logError) {
            console.error('Error logging analysis result:', logError);
        }

        return {
            statusCode: 200,
            headers: corsHeaders,
            body: JSON.stringify({
                message: 'Message analyzed successfully',
                analysis: analysisResult
            })
        };
    } catch (error: any) {
        console.error('Error in message analysis lambda:', error);

        return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({
                message: 'Internal server error',
                error: error.message
            })
        };
    }
};
