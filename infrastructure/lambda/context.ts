import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, PutCommand, GetCommand, QueryCommand, DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from './utils/auth-helper';
import { Groq } from 'groq-sdk';

const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const groq = new Groq({ apiKey: process.env.GROQ_API_KEY || '' });
const USER_CONTEXT_TABLE = process.env.USER_CONTEXT_TABLE_NAME || '';
const USER_PROFILE_TABLE = process.env.USER_PROFILE_TABLE_NAME || '';
const LOG_TABLE = process.env.LOG_TABLE_NAME || '';

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json'
};

// Log critical environment info at startup
console.log('Context Lambda initialized with:', {
  USER_CONTEXT_TABLE,
  USER_PROFILE_TABLE,
  LOG_TABLE,
  USER_POOL_ID: process.env.USER_POOL_ID || 'NOT SET',
  USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID || 'NOT SET'
});

// Context types
enum ContextType {
  DIETARY_RESTRICTION = 'dietary_restriction',
  INJURY = 'injury',
  LIFE_UPDATE = 'life_update',
  PREFERENCE = 'preference',
  GOAL = 'goal',
  WORKOUT_HISTORY = 'workout_history',
  MEAL_HISTORY = 'meal_history',
  WEIGHT_HISTORY = 'weight_history',
  CHAT_SUMMARY = 'chat_summary',
  CUSTOM = 'custom'
}

// Context data interface
interface ContextData {
  userId: string;
  contextKey: string; // Composite key: contextType#timestamp
  contextType: ContextType;
  timestamp: string;
  value: any;
  source?: string;
  ttl?: number;
  metadata?: Record<string, any>;
}

// Verify DynamoDB table exists
async function verifyDynamoTable(): Promise<boolean> {
  if (!USER_CONTEXT_TABLE) {
    console.error('USER_CONTEXT_TABLE_NAME environment variable is not set');
    return false;
  }

  try {
    // Try a simple operation to verify table access
    await dynamoDb.send(new GetCommand({
      TableName: USER_CONTEXT_TABLE,
      Key: { userId: 'test-connection', contextKey: 'test' }
    }));
    console.log('Successfully verified DynamoDB table access');
    return true;
  } catch (error) {
    console.error('Error verifying DynamoDB table access:', error);
    return false;
  }
}

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('Context Lambda invoked with event:', {
    path: event.path,
    method: event.httpMethod,
    hasBody: !!event.body,
    hasAuth: !!event.headers.Authorization,
    queryParams: event.queryStringParameters
  });

  // Handle preflight requests for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Verify DynamoDB configuration
  const tableAccessOk = await verifyDynamoTable();
  if (!tableAccessOk) {
    console.error(`DynamoDB table access verification failed`);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Database configuration error' }),
    };
  }

  try {
    // Get userId from the claims passed by the API Gateway authorizer
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    // Route based on HTTP method and path
    const path = event.path.toLowerCase();

    if (path.endsWith('/context/summary')) {
      return await getContextSummary(userId);
    } else if (event.httpMethod === 'GET') {
      return await getContextData(userId, event.queryStringParameters);
    } else if (event.httpMethod === 'POST') {
      return await saveContextData(userId, event.body);
    } else if (event.httpMethod === 'DELETE') {
      return await deleteContextData(userId, event.queryStringParameters);
    } else {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Invalid request method' }),
      };
    }
  } catch (error: any) {
    console.error('Error in context lambda:', error);

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        message: 'Internal server error',
        error: error.message
      }),
    };
  }
};

// Get context data for a user
async function getContextData(userId: string, queryParams: any): Promise<APIGatewayProxyResult> {
  try {
    const contextType = queryParams?.type;

    if (contextType) {
      // Query by context type
      console.log(`Getting context data for userId: ${userId}, type: ${contextType}`);

      const params = {
        TableName: USER_CONTEXT_TABLE,
        KeyConditionExpression: 'userId = :userId and begins_with(contextKey, :contextType)',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':contextType': `${contextType}#`
        }
      };

      const result = await dynamoDb.send(new QueryCommand(params));

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: 'Context data retrieved successfully',
          data: result.Items || []
        }),
      };
    } else {
      // Get all context data for the user
      console.log(`Getting all context data for userId: ${userId}`);

      const params = {
        TableName: USER_CONTEXT_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId
        }
      };

      const result = await dynamoDb.send(new QueryCommand(params));

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: 'Context data retrieved successfully',
          data: result.Items || []
        }),
      };
    }
  } catch (error) {
    console.error('Error getting context data:', error);
    throw error;
  }
}

// Save context data for a user
async function saveContextData(userId: string, body: string | null): Promise<APIGatewayProxyResult> {
  try {
    if (!body) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Missing request body' }),
      };
    }

    const data = JSON.parse(body);

    if (!data.contextType || !data.value) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          message: 'Missing required fields',
          required: ['contextType', 'value']
        }),
      };
    }

    // Validate context type
    if (!Object.values(ContextType).includes(data.contextType)) {
      console.warn(`Unknown context type: ${data.contextType}, treating as custom`);
      data.contextType = ContextType.CUSTOM;
    }

    const timestamp = data.timestamp || new Date().toISOString();
    const contextKey = `${data.contextType}#${timestamp}`;

    // Set TTL if provided or default to 1 year
    const ttl = data.ttl || Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60);

    const contextData: ContextData = {
      userId,
      contextKey,
      contextType: data.contextType,
      timestamp,
      value: data.value,
      source: data.source || 'user_input',
      ttl,
      metadata: data.metadata || {}
    };

    console.log(`Saving context data for userId: ${userId}, type: ${data.contextType}`);

    const params = {
      TableName: USER_CONTEXT_TABLE,
      Item: contextData
    };

    await dynamoDb.send(new PutCommand(params));

    return {
      statusCode: 201,
      headers: corsHeaders,
      body: JSON.stringify({
        message: 'Context data saved successfully',
        data: contextData
      }),
    };
  } catch (error) {
    console.error('Error saving context data:', error);
    throw error;
  }
}

// Delete context data for a user
async function deleteContextData(userId: string, queryParams: any): Promise<APIGatewayProxyResult> {
  try {
    // Check if this is a "clear all" request
    if (queryParams?.action === 'clearAll') {
      console.log(`Clearing ALL context data for userId: ${userId}`);

      // Query all context items for this user
      const queryParams = {
        TableName: USER_CONTEXT_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId
        }
      };

      // DynamoDB can only return 1MB of data at a time, so we might need to paginate
      let items: any[] = [];
      let lastEvaluatedKey: any = undefined;
      
      // Paginate through results until we get all items
      do {
        const queryCommand: QueryCommand = new QueryCommand({
          ...queryParams,
          ExclusiveStartKey: lastEvaluatedKey,
        });
        
        const result: any = await dynamoDb.send(queryCommand);
        items = items.concat(result.Items || []);
        lastEvaluatedKey = result.LastEvaluatedKey;
      } while (lastEvaluatedKey);
      
      console.log(`Found ${items.length} context items to delete for user ${userId}`);

      // If we have a large number of items, delete in batches to avoid timeouts
      const BATCH_SIZE = 25;
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);
        console.log(`Processing deletion batch ${i/BATCH_SIZE + 1} of ${Math.ceil(items.length/BATCH_SIZE)}, size: ${batch.length}`);
        
        const batchPromises = batch.map(item => {
          return dynamoDb.send(new DeleteCommand({
            TableName: USER_CONTEXT_TABLE,
            Key: {
              userId: item.userId,
              contextKey: item.contextKey
            }
          }));
        });

        // Wait for current batch to complete before starting next batch
        await Promise.all(batchPromises);
        console.log(`Successfully deleted batch ${i/BATCH_SIZE + 1}`);
      }

      // Add special log marker for debugging clear operations
      console.log(`[CONTEXT_CLEAR_COMPLETED] Successfully cleared all context data (${items.length} items) for user ${userId}`);

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: `Successfully cleared all context data (${items.length} items)`,
          cleared: true,
          itemCount: items.length
        }),
      };
    }
    
    // Check if this is a "delete by type" request
    if (queryParams?.action === 'deleteByType' && queryParams?.type) {
      const contextTypeToDelete = queryParams.type;
      console.log(`Deleting all context data of type: ${contextTypeToDelete} for userId: ${userId}`);
      
      // Query all context items of this type for this user
      const typeQueryParams = {
        TableName: USER_CONTEXT_TABLE,
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'begins_with(contextKey, :contextType)',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':contextType': `${contextTypeToDelete}#`
        }
      };
      
      // DynamoDB can only return 1MB of data at a time, so we might need to paginate
      let items: any[] = [];
      let lastEvaluatedKey: any = undefined;
      
      // Paginate through results until we get all items
      do {
        const queryCommand: QueryCommand = new QueryCommand({
          ...typeQueryParams,
          ExclusiveStartKey: lastEvaluatedKey,
        });
        
        const result: any = await dynamoDb.send(queryCommand);
        items = items.concat(result.Items || []);
        lastEvaluatedKey = result.LastEvaluatedKey;
      } while (lastEvaluatedKey);
      
      console.log(`Found ${items.length} context items of type ${contextTypeToDelete} to delete for user ${userId}`);
      
      // If we have a large number of items, delete in batches to avoid timeouts
      const BATCH_SIZE = 25;
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);
        console.log(`Processing deletion batch ${i/BATCH_SIZE + 1} of ${Math.ceil(items.length/BATCH_SIZE)}, size: ${batch.length}`);
        
        const batchPromises = batch.map(item => {
          return dynamoDb.send(new DeleteCommand({
            TableName: USER_CONTEXT_TABLE,
            Key: {
              userId: item.userId,
              contextKey: item.contextKey
            }
          }));
        });
        
        // Wait for current batch to complete before starting next batch
        await Promise.all(batchPromises);
        console.log(`Successfully deleted batch ${i/BATCH_SIZE + 1}`);
      }
      
      // Add special log marker for debugging delete operations
      console.log(`[CONTEXT_DELETE_TYPE_COMPLETED] Successfully deleted ${items.length} context items of type ${contextTypeToDelete} for user ${userId}`);
      
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: `Successfully deleted ${items.length} context items of type ${contextTypeToDelete}`,
          deleted: true,
          itemCount: items.length,
          contextType: contextTypeToDelete
        }),
      };
    }
    
    // Handle regular single item deletion
    const contextKey = queryParams?.key;

    if (!contextKey) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Missing required parameter: key or action' }),
      };
    }

    console.log(`Deleting context data for userId: ${userId}, key: ${contextKey}`);

    const params = {
      TableName: USER_CONTEXT_TABLE,
      Key: {
        userId,
        contextKey
      }
    };

    await dynamoDb.send(new DeleteCommand(params));

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        message: 'Context data deleted successfully'
      }),
    };
  } catch (error) {
    console.error('Error deleting context data:', error);
    throw error;
  }
}

// Get a summary of context data for LLM prompts
async function getContextSummary(userId: string): Promise<APIGatewayProxyResult> {
  try {
    console.log(`Generating context summary for userId: ${userId}`);

    // Get user profile
    let userProfile = null;
    try {
      const profileResult = await dynamoDb.send(new GetCommand({
        TableName: USER_PROFILE_TABLE,
        Key: { userId }
      }));

      userProfile = profileResult.Item;
    } catch (error) {
      console.error('Error getting user profile:', error);
    }

    // Get context data
    const contextResult = await dynamoDb.send(new QueryCommand({
      TableName: USER_CONTEXT_TABLE,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId
      },
      Limit: 100 // Limit to most recent 100 items
    }));

    const contextItems = contextResult.Items || [];

    // Get recent weight logs
    let weightLogs: any[] = [];
    try {
      const logsResult = await dynamoDb.send(new QueryCommand({
        TableName: LOG_TABLE,
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: '#type = :type',
        ExpressionAttributeNames: {
          '#type': 'type'
        },
        ExpressionAttributeValues: {
          ':userId': userId,
          ':type': 'weight'
        },
        Limit: 10, // Get most recent 10 weight logs
        ScanIndexForward: false // Sort by timestamp descending
      }));

      weightLogs = logsResult.Items || [];
    } catch (error) {
      console.error('Error getting weight logs:', error);
    }

    // Generate summary
    const summary = await generateNarrativeContextSummary(userProfile, contextItems, weightLogs);

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        message: 'Context summary generated successfully',
        summary
      }),
    };
  } catch (error) {
    console.error('Error generating context summary:', error);
    throw error;
  }
}

// Generate a narrative summary of user context using an LLM
async function generateNarrativeContextSummary(
    userProfile: any,
    contextItems: any[],
    weightLogs: any[]
): Promise<string> {
    const rawSummary = generateUserContextSummary(userProfile, contextItems, weightLogs);

    const systemPrompt = `You are an expert at creating a coherent, narrative-style summary of a user's context for a personalized fitness and nutrition app. Your task is to synthesize the provided raw data into a human-readable summary that highlights the most important information for personalization.

Focus on creating a summary that flows well and is easy to understand. Start with the most critical information, like allergies and injuries, then move on to goals and preferences.`;

    const userPrompt = `Based on the following raw data, please generate a narrative summary of the user's context:

${rawSummary}`;

    try {
        const completion = await groq.chat.completions.create({
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ],
            model: 'meta-llama/llama-4-scout-17b-16e-instruct',
            temperature: 0.3,
            max_tokens: 1500,
            top_p: 0.9
        });

        const responseContent = completion.choices?.[0]?.message?.content;
        if (!responseContent) {
            throw new Error('Empty response from Groq API for narrative summary');
        }

        return responseContent;
    } catch (error) {
        console.error('Error calling Groq API for narrative summary:', error);
        // Fallback to raw summary if LLM fails
        return rawSummary;
    }
}

// Generate a summary of user context for LLM prompts
function generateUserContextSummary(
  userProfile: any,
  contextItems: any[],
  weightLogs: any[]
): string {
  let summary = "USER CONTEXT SUMMARY:\n\n";

  // Add user profile information
  if (userProfile) {
    summary += "USER PROFILE:\n";
    summary += `Name: ${userProfile.name || 'Unknown'}\n`;

    if (userProfile.birthday) {
      const birthYear = new Date(userProfile.birthday).getFullYear();
      const age = new Date().getFullYear() - birthYear;
      summary += `Age: ~${age} years\n`;
    }

    if (userProfile.height) {
      summary += `Height: ${userProfile.height} inches\n`;
    }

    if (userProfile.fitnessGoal) {
      summary += `Fitness Goal: ${userProfile.fitnessGoal}\n`;
    }

    summary += "\n";
  }

  // Add weight history
  if (weightLogs.length > 0) {
    summary += "WEIGHT HISTORY:\n";

    // Sort by timestamp descending
    const sortedLogs = [...weightLogs].sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Get current weight
    const currentWeight = sortedLogs[0]?.data?.metrics?.weight?.value;
    if (currentWeight) {
      summary += `Current Weight: ${currentWeight} lbs\n`;
    }

    // Calculate weight change if we have multiple entries
    if (sortedLogs.length > 1) {
      const oldestWeight = sortedLogs[sortedLogs.length - 1]?.data?.metrics?.weight?.value;
      const weightChange = currentWeight - oldestWeight;

      if (currentWeight && oldestWeight) {
        const changeDirection = weightChange > 0 ? 'gained' : 'lost';
        summary += `Weight Change: ${changeDirection} ${Math.abs(weightChange).toFixed(1)} lbs\n`;
      }
    }

    summary += "\n";
  }

  // Add dietary restrictions
  const dietaryRestrictions = contextItems.filter(item =>
    item.contextType === ContextType.DIETARY_RESTRICTION
  );

  if (dietaryRestrictions.length > 0) {
    summary += "DIETARY RESTRICTIONS:\n";
    dietaryRestrictions.forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add injuries
  const injuries = contextItems.filter(item =>
    item.contextType === ContextType.INJURY
  );

  if (injuries.length > 0) {
    summary += "INJURIES:\n";
    injuries.forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add goals
  const goals = contextItems.filter(item =>
    item.contextType === ContextType.GOAL
  );

  if (goals.length > 0) {
    summary += "GOALS:\n";
    goals.forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add preferences
  const preferences = contextItems.filter(item =>
    item.contextType === ContextType.PREFERENCE
  );

  if (preferences.length > 0) {
    summary += "PREFERENCES:\n";
    preferences.forEach(item => {
      summary += `- ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add life updates
  const lifeUpdates = contextItems.filter(item =>
    item.contextType === ContextType.LIFE_UPDATE
  );

  if (lifeUpdates.length > 0) {
    summary += "RECENT LIFE UPDATES:\n";

    // Sort by timestamp descending and take the 5 most recent
    const recentUpdates = [...lifeUpdates]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5);

    recentUpdates.forEach(item => {
      const date = new Date(item.timestamp).toLocaleDateString();
      summary += `- ${date}: ${item.value}\n`;
    });
    summary += "\n";
  }

  // Add chat summaries
  const chatSummaries = contextItems.filter(item =>
    item.contextType === ContextType.CHAT_SUMMARY
  );

  if (chatSummaries.length > 0) {
    summary += "RECENT CONVERSATION SUMMARIES:\n";

    // Sort by timestamp descending and take the 3 most recent
    const recentSummaries = [...chatSummaries]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 3);

    recentSummaries.forEach(item => {
      const date = new Date(item.timestamp).toLocaleDateString();
      summary += `- ${date}: ${item.value}\n`;
    });
    summary += "\n";
  }

  return summary;
}
