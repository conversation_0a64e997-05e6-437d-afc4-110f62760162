import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, PutCommand, GetCommand, QueryCommand, UpdateCommand, BatchWriteCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from './utils/auth-helper';

const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const USER_ACHIEVEMENTS_TABLE = process.env.USER_ACHIEVEMENTS_TABLE_NAME || '';

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json'
};

// Log critical environment info at startup
console.log('Achievements Lambda initialized with:', {
  USER_ACHIEVEMENTS_TABLE,
  USER_POOL_ID: process.env.USER_POOL_ID || 'NOT SET',
  USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID || 'NOT SET'
});

// Achievement status enum
enum AchievementStatus {
  LOCKED = 'locked',
  IN_PROGRESS = 'in_progress',
  UNLOCKED = 'unlocked'
}

// Achievement category enum
enum AchievementCategory {
  WORKOUT = 'workout',
  NUTRITION = 'nutrition',
  WEIGHT = 'weight',
  STREAK = 'streak',
  GENERAL = 'general'
}

// Achievement interface
interface Achievement {
  id: string;
  userId: string;
  title: string;
  description: string;
  category: AchievementCategory;
  icon: string;
  status: AchievementStatus;
  progress: number;
  currentValue?: number;
  targetValue?: number;
  unlockedAt?: string;
  achievementId: string;
  createdAt: string;
  updatedAt: string;
}

// Verify DynamoDB table exists
async function verifyDynamoTable(): Promise<boolean> {
  if (!USER_ACHIEVEMENTS_TABLE) {
    console.error('USER_ACHIEVEMENTS_TABLE_NAME environment variable is not set');
    return false;
  }

  // Instead of trying to query the table, which might fail if the table doesn't exist yet,
  // just check if the environment variable is set and return true
  console.log(`Using DynamoDB table: ${USER_ACHIEVEMENTS_TABLE}`);
  return true;
}

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    console.log('Achievements Lambda invoked with event:', {
      path: event.path,
      method: event.httpMethod,
      hasBody: !!event.body,
      hasAuth: !!event.headers?.Authorization,
      queryParams: event.queryStringParameters
    });

    // Handle preflight requests for CORS
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: ''
      };
    }

    // Verify DynamoDB configuration
    const tableAccessOk = await verifyDynamoTable();
    if (!tableAccessOk) {
      console.error(`DynamoDB table access verification failed`);
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Database configuration error' }),
      };
    }

    // Verify token and get user ID
    try {
      const authResult = await verifyToken(event);
      if (!authResult.isValid) {
        return {
          statusCode: 401,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Unauthorized' }),
        };
      }

      const userId = authResult.userId;
      if (!userId) {
        return {
          statusCode: 401,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'User ID not found in token' }),
        };
      }

    // Handle different routes
    const path = event.path;
    const method = event.httpMethod;

    // GET /achievements - Get all achievements for a user
    if (path === '/achievements' && method === 'GET') {
      return await getAllAchievements(userId);
    }

    // GET /achievements/{id} - Get a specific achievement
    if (path.match(/^\/achievements\/[a-zA-Z0-9-_]+$/) && method === 'GET') {
      const achievementId = path.split('/').pop() || '';
      return await getAchievement(userId, achievementId);
    }

    // PUT /achievements/{id}/progress - Update achievement progress
    if (path.match(/^\/achievements\/[a-zA-Z0-9-_]+\/progress$/) && method === 'PUT') {
      const achievementId = path.split('/')[2] || '';
      try {
        const body = JSON.parse(event.body || '{}');
        return await updateAchievementProgress(userId, achievementId, body);
      } catch (parseError) {
        console.error('Error parsing request body:', parseError);
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Invalid request body' }),
        };
      }
    }

    // Route not found
    return {
      statusCode: 404,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Route not found' }),
    };

    } catch (authError) {
      console.error('Error during authentication:', authError);
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Authentication error' }),
      };
    }
  } catch (error) {
    console.error('Error processing request:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
};

// Get default achievements for a user
function getDefaultAchievements(userId: string): Achievement[] {
  const now = new Date().toISOString();

  // Create default achievements with validated progress values
  const defaultAchievements = [
    // Workout Achievements
    {
      id: 'first-workout',
      userId,
      title: 'First Workout',
      description: 'Complete your first workout',
      category: AchievementCategory.WORKOUT,
      icon: 'fitness-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'first-workout',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-3',
      userId,
      title: 'Workout Streak: 3 Days',
      description: 'Complete workouts for 3 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 3,
      achievementId: 'workout-streak-3',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-7',
      userId,
      title: 'Workout Streak: 7 Days',
      description: 'Complete workouts for 7 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'workout-streak-7',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-14',
      userId,
      title: 'Workout Streak: 2 Weeks',
      description: 'Complete workouts for 14 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 14,
      achievementId: 'workout-streak-14',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-streak-30',
      userId,
      title: 'Workout Streak: 1 Month',
      description: 'Complete workouts for 30 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'flame-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      achievementId: 'workout-streak-30',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-5',
      userId,
      title: 'Workout Warrior: Beginner',
      description: 'Complete 5 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      achievementId: 'workout-count-5',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-20',
      userId,
      title: 'Workout Warrior: Intermediate',
      description: 'Complete 20 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 20,
      achievementId: 'workout-count-20',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-50',
      userId,
      title: 'Workout Warrior: Advanced',
      description: 'Complete 50 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      achievementId: 'workout-count-50',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'workout-count-100',
      userId,
      title: 'Workout Warrior: Elite',
      description: 'Complete 100 workouts',
      category: AchievementCategory.WORKOUT,
      icon: 'barbell-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 100,
      achievementId: 'workout-count-100',
      createdAt: now,
      updatedAt: now
    },

    // Nutrition Achievements
    {
      id: 'first-meal',
      userId,
      title: 'First Meal Logged',
      description: 'Log your first meal',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'first-meal',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-3',
      userId,
      title: 'Nutrition Tracker: 3 Days',
      description: 'Log your meals for 3 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 3,
      achievementId: 'meal-logging-3',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-7',
      userId,
      title: 'Nutrition Tracker: 7 Days',
      description: 'Log your meals for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'meal-logging-7',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-14',
      userId,
      title: 'Nutrition Tracker: 2 Weeks',
      description: 'Log your meals for 14 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 14,
      achievementId: 'meal-logging-14',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-logging-30',
      userId,
      title: 'Nutrition Tracker: 1 Month',
      description: 'Log your meals for 30 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'restaurant-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      achievementId: 'meal-logging-30',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-10',
      userId,
      title: 'Nutrition Master: Beginner',
      description: 'Log 10 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      achievementId: 'meal-count-10',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-50',
      userId,
      title: 'Nutrition Master: Intermediate',
      description: 'Log 50 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      achievementId: 'meal-count-50',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'meal-count-100',
      userId,
      title: 'Nutrition Master: Advanced',
      description: 'Log 100 meals',
      category: AchievementCategory.NUTRITION,
      icon: 'nutrition-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 100,
      achievementId: 'meal-count-100',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'protein-goal-7',
      userId,
      title: 'Protein Champion: 1 Week',
      description: 'Meet your protein goal for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'egg-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'protein-goal-7',
      createdAt: now,
      updatedAt: now
    },

    // Weight Tracking Achievements
    {
      id: 'first-weight',
      userId,
      title: 'Weight Tracking Started',
      description: 'Log your weight for the first time',
      category: AchievementCategory.WEIGHT,
      icon: 'scale-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'first-weight',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-tracking-7',
      userId,
      title: 'Weight Tracker: 1 Week',
      description: 'Track your weight for 7 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'weight-tracking-7',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-tracking-30',
      userId,
      title: 'Weight Tracker: 1 Month',
      description: 'Track your weight for 30 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      achievementId: 'weight-tracking-30',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-goal',
      userId,
      title: 'Weight Goal Achieved',
      description: 'Reach your target weight goal',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      achievementId: 'weight-goal',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-loss-5',
      userId,
      title: 'Weight Loss: 5 lbs',
      description: 'Lose 5 pounds from your starting weight',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      achievementId: 'weight-loss-5',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weight-loss-10',
      userId,
      title: 'Weight Loss: 10 lbs',
      description: 'Lose 10 pounds from your starting weight',
      category: AchievementCategory.WEIGHT,
      icon: 'trending-down-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      achievementId: 'weight-loss-10',
      createdAt: now,
      updatedAt: now
    },

    // General Achievements
    {
      id: 'profile-complete',
      userId,
      title: 'Profile Complete',
      description: 'Fill out all your profile information',
      category: AchievementCategory.GENERAL,
      icon: 'person-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      achievementId: 'profile-complete',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'first-chat',
      userId,
      title: 'First Conversation',
      description: 'Have your first conversation with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'first-chat',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'chat-count-10',
      userId,
      title: 'Conversation Starter',
      description: 'Have 10 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 10,
      achievementId: 'chat-count-10',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'chat-count-50',
      userId,
      title: 'Conversation Master',
      description: 'Have 50 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 50,
      achievementId: 'chat-count-50',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'app-usage-7',
      userId,
      title: 'Daily User: 1 Week',
      description: 'Use the app for 7 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'app-usage-7',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'app-usage-30',
      userId,
      title: 'Daily User: 1 Month',
      description: 'Use the app for 30 consecutive days',
      category: AchievementCategory.STREAK,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      achievementId: 'app-usage-30',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'full-health-day',
      userId,
      title: 'Full Health Day',
      description: 'Log a workout, meal, and weight all in one day',
      category: AchievementCategory.GENERAL,
      icon: 'ribbon-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'full-health-day',
      createdAt: now,
      updatedAt: now
    },


    // Add missing achievements to match frontend (examples shown, add all missing ones)
    {
      id: 'early-bird',
      userId,
      title: 'Early Bird',
      description: 'Log a workout before 7 AM',
      category: AchievementCategory.WORKOUT,
      icon: 'sunny-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'early-bird',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'night-owl',
      userId,
      title: 'Night Owl',
      description: 'Log a workout after 9 PM',
      category: AchievementCategory.WORKOUT,
      icon: 'moon-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 1,
      achievementId: 'night-owl',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'weekend-warrior',
      userId,
      title: 'Weekend Warrior',
      description: 'Complete workouts on both Saturday and Sunday',
      category: AchievementCategory.WORKOUT,
      icon: 'calendar-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 2,
      achievementId: 'weekend-warrior',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'protein-perfect',
      userId,
      title: 'Protein Perfect',
      description: 'Meet your protein goal for 5 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'egg-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      achievementId: 'protein-perfect',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'hydration-hero',
      userId,
      title: 'Hydration Hero',
      description: 'Log 8 glasses of water for 7 consecutive days',
      category: AchievementCategory.NUTRITION,
      icon: 'water-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'hydration-hero',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'veggie-victory',
      userId,
      title: 'Veggie Victory',
      description: 'Log 5 servings of vegetables in a single day',
      category: AchievementCategory.NUTRITION,
      icon: 'leaf-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 5,
      achievementId: 'veggie-victory',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'consistent-tracker',
      userId,
      title: 'Consistent Tracker',
      description: 'Log your weight for 14 consecutive days',
      category: AchievementCategory.WEIGHT,
      icon: 'analytics-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 14,
      achievementId: 'consistent-tracker',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'goal-setter',
      userId,
      title: 'Goal Setter',
      description: 'Set a weight goal and track progress for 30 days',
      category: AchievementCategory.WEIGHT,
      icon: 'flag-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 30,
      achievementId: 'goal-setter',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'lotus-master',
      userId,
      title: 'Lotus Master',
      description: 'Have 100 conversations with Lotus',
      category: AchievementCategory.GENERAL,
      icon: 'chatbubbles-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 100,
      achievementId: 'lotus-master',
      createdAt: now,
      updatedAt: now
    },
    {
      id: 'perfect-week',
      userId,
      title: 'Perfect Week',
      description: 'Log workouts, meals, and weight every day for a week',
      category: AchievementCategory.STREAK,
      icon: 'trophy-outline',
      status: AchievementStatus.LOCKED,
      progress: 0,
      currentValue: 0,
      targetValue: 7,
      achievementId: 'perfect-week',
      createdAt: now,
      updatedAt: now
    }
  ];

  // Validate all progress values before returning
  return defaultAchievements.map(achievement => ({
    ...achievement,
    progress: validateProgress(achievement.progress)
  }));
}

// Create default achievements for a user
async function createDefaultAchievements(userId: string): Promise<boolean> {
  try {
    // Get default achievements and validate all progress values
    const defaultAchievements = getDefaultAchievements(userId).map(achievement => ({
      ...achievement,
      progress: validateProgress(achievement.progress)
    }));

    // Use BatchWriteItem to create all achievements at once
    const batchSize = 25; // DynamoDB limit
    for (let i = 0; i < defaultAchievements.length; i += batchSize) {
      const batch = defaultAchievements.slice(i, i + batchSize);

      const putRequests = batch.map(achievement => ({
        PutRequest: {
          Item: achievement
        }
      }));

      await dynamoDb.send(new BatchWriteCommand({
        RequestItems: {
          [USER_ACHIEVEMENTS_TABLE]: putRequests
        }
      }));
    }

    return true;
  } catch (error) {
    console.error('Error creating default achievements:', error);
    return false;
  }
}

// Get all achievements for a user
async function getAllAchievements(userId: string): Promise<APIGatewayProxyResult> {
  try {
    // First, check if the table exists by trying to query it
    try {
      const params = {
        TableName: USER_ACHIEVEMENTS_TABLE,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        Limit: 1 // Just check if we can query, don't need all items yet
      };

      await dynamoDb.send(new QueryCommand(params));
    } catch (tableError) {
      console.error('Error accessing achievements table:', tableError);

      // Return default achievements as a fallback
      const defaultAchievements = getDefaultAchievements(userId);
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify(defaultAchievements),
      };
    }

    // If we get here, the table exists and we can query it
    const params = {
      TableName: USER_ACHIEVEMENTS_TABLE,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
      // Remove any limit to get all achievements
      ScanIndexForward: true
    };

    const result = await dynamoDb.send(new QueryCommand(params));

    // Get the default achievements to ensure we have all of them
    const defaultAchievements = getDefaultAchievements(userId);

    // If no achievements found, create default ones
    if (!result.Items || result.Items.length === 0) {
      console.log(`No achievements found for user ${userId}, creating defaults`);
      const created = await createDefaultAchievements(userId);

      if (created) {
        // Query again to get the newly created achievements
        const newResult = await dynamoDb.send(new QueryCommand(params));
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify(newResult.Items || []),
        };
      } else {
        // If creation failed, return default achievements
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify(defaultAchievements),
        };
      }
    }

    // If we have some achievements but not all of them, merge with defaults
    if (result.Items && result.Items.length > 0 && result.Items.length < defaultAchievements.length) {
      console.log(`Found ${result.Items.length} achievements for user ${userId}, but expected ${defaultAchievements.length}. Merging with defaults.`);

      // Create a map of existing achievements by ID
      const existingAchievementsMap = new Map();
      result.Items.forEach((item: any) => {
        existingAchievementsMap.set(item.id, item);
      });

      // Create a list of achievements that don't exist yet
      const missingAchievements = defaultAchievements.filter(
        achievement => !existingAchievementsMap.has(achievement.id)
      );

      if (missingAchievements.length > 0) {
        console.log(`Creating ${missingAchievements.length} missing achievements for user ${userId}`);

        // Use BatchWriteItem to create missing achievements
        const batchSize = 25; // DynamoDB limit
        for (let i = 0; i < missingAchievements.length; i += batchSize) {
          const batch = missingAchievements.slice(i, i + batchSize);

          const putRequests = batch.map(achievement => ({
            PutRequest: {
              Item: achievement
            }
          }));

          await dynamoDb.send(new BatchWriteCommand({
            RequestItems: {
              [USER_ACHIEVEMENTS_TABLE]: putRequests
            }
          }));
        }

        // Query again to get all achievements including the newly created ones
        const newResult = await dynamoDb.send(new QueryCommand(params));

        // Validate all progress values before returning
        const validatedItems = (newResult.Items || []).map((item: any) => ({
          ...item,
          progress: validateProgress(item.progress)
        }));

        console.log(`Returning ${validatedItems.length} achievements for user ${userId} after creating missing ones`);

        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify(validatedItems),
        };
      }
    }

    // Validate all progress values before returning
    const validatedItems = (result.Items || []).map((item: any) => ({
      ...item,
      progress: validateProgress(item.progress)
    }));

    // Log the number of achievements being returned
    console.log(`Returning ${validatedItems.length} achievements for user ${userId}`);

    // If we still don't have all achievements, return the default ones
    if (validatedItems.length < defaultAchievements.length) {
      console.log(`Still only have ${validatedItems.length} achievements, returning all ${defaultAchievements.length} default achievements`);
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify(defaultAchievements),
      };
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(validatedItems),
    };
  } catch (error) {
    console.error('Error getting achievements:', error);

    // Return default achievements as a fallback
    const defaultAchievements = getDefaultAchievements(userId);
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(defaultAchievements),
    };
  }
}

// Get a specific achievement
async function getAchievement(userId: string, achievementId: string): Promise<APIGatewayProxyResult> {
  try {
    const params = {
      TableName: USER_ACHIEVEMENTS_TABLE,
      Key: {
        userId,
        achievementId,
      },
    };

    const result = await dynamoDb.send(new GetCommand(params));

    if (!result.Item) {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Achievement not found' }),
      };
    }

    // Validate progress value before returning
    const validatedItem = {
      ...result.Item,
      progress: validateProgress((result.Item as any).progress)
    };

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(validatedItem),
    };
  } catch (error) {
    console.error('Error getting achievement:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Error getting achievement' }),
    };
  }
}

// Helper function to validate progress value (ensure it's between 0 and 1)
function validateProgress(progress: number): number {
  return Math.max(0, Math.min(1, progress));
}

// Update achievement progress
async function updateAchievementProgress(
  userId: string,
  achievementId: string,
  body: { progress: number; currentValue?: number }
): Promise<APIGatewayProxyResult> {
  try {
    // Validate user ID
    if (!userId) {
      console.error('Missing userId in updateAchievementProgress');
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'User ID is required.' }),
      };
    }

    // Validate achievement ID
    if (!achievementId) {
      console.error('Missing achievementId in updateAchievementProgress');
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Achievement ID is required.' }),
      };
    }

    // Handle the renamed achievement ID
    // If the client sends 'all-in-one-day', map it to 'full-health-day'
    let normalizedAchievementId = achievementId;
    if (achievementId === 'all-in-one-day') {
      console.log('Mapping legacy achievement ID "all-in-one-day" to "full-health-day"');
      normalizedAchievementId = 'full-health-day';
    }

    // Log the request
    console.log(`Updating achievement ${normalizedAchievementId} (original: ${achievementId}) for user ${userId} with body:`, body);

    const { progress: rawProgress, currentValue } = body;

    // Validate progress value
    if (rawProgress === undefined) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Progress value is required.' }),
      };
    }

    // Automatically fix invalid progress values instead of returning an error
    const progress = validateProgress(rawProgress);

    // Log if we had to adjust the progress value
    if (progress !== rawProgress) {
      console.warn(`Adjusted progress value for ${achievementId} from ${rawProgress} to ${progress}`);
    }

    // Get the current achievement
    // First try with achievementId as the key
    const getParams = {
      TableName: USER_ACHIEVEMENTS_TABLE,
      Key: {
        userId,
        achievementId: normalizedAchievementId,
      },
    };

    console.log(`Checking if achievement ${normalizedAchievementId} exists for user ${userId}`);
    let result;
    try {
      result = await dynamoDb.send(new GetCommand(getParams));

      // If not found, try with id as the key
      if (!result.Item) {
        console.log(`Achievement not found with achievementId=${normalizedAchievementId}, trying with id=${normalizedAchievementId}`);

        // Query using id instead of achievementId
        const queryParams = {
          TableName: USER_ACHIEVEMENTS_TABLE,
          KeyConditionExpression: 'userId = :userId',
          FilterExpression: 'id = :id',
          ExpressionAttributeValues: {
            ':userId': userId,
            ':id': normalizedAchievementId
          }
        };

        // Also try with the original achievementId if it was mapped
        if (normalizedAchievementId !== achievementId) {
          console.log(`Also trying with original id=${achievementId}`);
          queryParams.FilterExpression = 'id = :id OR id = :originalId';
          // Use type assertion to fix TypeScript error
          (queryParams.ExpressionAttributeValues as Record<string, any>)[':originalId'] = achievementId;
        }

        const queryResult = await dynamoDb.send(new QueryCommand(queryParams));

        if (queryResult.Items && queryResult.Items.length > 0) {
          console.log(`Found achievement with id=${queryResult.Items[0].id}`);
          result = { Item: queryResult.Items[0] };
        }
      }
    } catch (error) {
      console.error(`Error getting achievement ${normalizedAchievementId} for user ${userId}:`, error);
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Error retrieving achievement' }),
      };
    }

    // If achievement doesn't exist, create it
    if (!result.Item) {
      console.log(`Achievement ${normalizedAchievementId} not found for user ${userId}, creating it`);

      // Get default achievements to find the template for this achievement
      const defaultAchievements = getDefaultAchievements(userId);
      const defaultAchievement = defaultAchievements.find(a =>
        a.achievementId === normalizedAchievementId ||
        a.id === normalizedAchievementId ||
        (normalizedAchievementId !== achievementId && (a.achievementId === achievementId || a.id === achievementId))
      );

      if (!defaultAchievement) {
        console.error(`No default template found for achievement ${normalizedAchievementId} (original: ${achievementId})`);
        return {
          statusCode: 404,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Achievement not found and no template available' }),
        };
      }

      // Create the achievement
      const now = new Date().toISOString();
      const newAchievement = {
        ...defaultAchievement,
        progress: progress,
        currentValue: currentValue !== undefined ? currentValue : 0,
        updatedAt: now
      };

      // If we're using the new ID, make sure the achievement uses it
      if (normalizedAchievementId !== achievementId && normalizedAchievementId === 'full-health-day') {
        newAchievement.id = 'full-health-day';
        newAchievement.achievementId = 'full-health-day';
      }

      console.log(`Creating new achievement for ${normalizedAchievementId}:`, newAchievement);

      const putParams = {
        TableName: USER_ACHIEVEMENTS_TABLE,
        Item: newAchievement
      };

      try {
        await dynamoDb.send(new PutCommand(putParams));
      } catch (error) {
        console.error(`Error creating achievement ${normalizedAchievementId} for user ${userId}:`, error);
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Error creating achievement' }),
        };
      }

      // Return the newly created achievement
      return {
        statusCode: 201,
        headers: corsHeaders,
        body: JSON.stringify(newAchievement),
      };
    }

    const achievement = result.Item as Achievement;
    const now = new Date().toISOString();

    // Determine if the achievement should be unlocked
    let status = achievement.status;
    let unlockedAt = achievement.unlockedAt;

    if (progress >= 1 && status !== AchievementStatus.UNLOCKED) {
      status = AchievementStatus.UNLOCKED;
      unlockedAt = now;
      console.log(`Achievement ${normalizedAchievementId} unlocked for user ${userId}`);
    } else if (progress > 0 && progress < 1) {
      status = AchievementStatus.IN_PROGRESS;
      console.log(`Achievement ${normalizedAchievementId} in progress (${progress * 100}%) for user ${userId}`);
    }

    // Update the achievement
    // Check if we need to use id or achievementId as the key
    const actualAchievement = result.Item as Achievement;
    const useIdAsKey = actualAchievement.id === normalizedAchievementId && actualAchievement.achievementId !== normalizedAchievementId;

    // If we found the achievement by id but not by achievementId, we need to use a different approach
    if (useIdAsKey) {
      console.log(`Using id=${normalizedAchievementId} as key instead of achievementId`);

      // We need to use a different approach since we can't update using a non-key attribute
      // First, create a copy of the achievement with the updated values
      const updatedAchievement = {
        ...actualAchievement,
        progress: progress,
        status: status,
        updatedAt: now,
        ...(unlockedAt && { unlockedAt }),
        ...(currentValue !== undefined && { currentValue }),
      };

      // Then, put the updated achievement back
      const putParams = {
        TableName: USER_ACHIEVEMENTS_TABLE,
        Item: updatedAchievement,
      };

      console.log(`Putting updated achievement with id=${normalizedAchievementId}:`, JSON.stringify(putParams, null, 2));

      try {
        await dynamoDb.send(new PutCommand(putParams));
        console.log(`Successfully updated achievement with id=${normalizedAchievementId} for user ${userId}`);

        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify(updatedAchievement),
        };
      } catch (error) {
        console.error(`Error updating achievement with id=${normalizedAchievementId} for user ${userId}:`, error);
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ message: 'Error updating achievement in database' }),
        };
      }
    }

    // Standard update using achievementId as the key
    const updateParams = {
      TableName: USER_ACHIEVEMENTS_TABLE,
      Key: {
        userId,
        achievementId: normalizedAchievementId,
      },
      UpdateExpression: 'SET progress = :progress, #status = :status, updatedAt = :updatedAt' +
        (unlockedAt ? ', unlockedAt = :unlockedAt' : '') +
        (currentValue !== undefined ? ', currentValue = :currentValue' : ''),
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':progress': progress,
        ':status': status,
        ':updatedAt': now,
        ...(unlockedAt && { ':unlockedAt': unlockedAt }),
        ...(currentValue !== undefined && { ':currentValue': currentValue }),
      },
      ReturnValues: 'ALL_NEW' as const, // Type assertion to fix TypeScript error
    };

    console.log(`Updating achievement ${normalizedAchievementId} for user ${userId} with params:`, JSON.stringify(updateParams, null, 2));

    try {
      const updateResult = await dynamoDb.send(new UpdateCommand(updateParams));
      console.log(`Successfully updated achievement ${normalizedAchievementId} for user ${userId}`);

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify(updateResult.Attributes),
      };
    } catch (error) {
      console.error(`Error updating achievement ${normalizedAchievementId} for user ${userId}:`, error);
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Error updating achievement in database' }),
      };
    }
  } catch (error) {
    console.error(`Error in updateAchievementProgress for achievement ${achievementId} and user ${userId}:`, error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
}
