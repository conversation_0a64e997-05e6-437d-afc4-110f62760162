import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Get userId from the claims passed by the API Gateway authorizer
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const logType = queryParams.type;

    if (!logType) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing required parameter: type' }),
      };
    }

    // Validate log type
    if (!['workout', 'meal', 'conversation', 'weight'].includes(logType)) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Invalid log type' }),
      };
    }

    console.log(`Fetching logs of type ${logType} for user ${userId}`);

    // Query logs by userId and type
    const queryCommand = new QueryCommand({
      TableName: process.env.LOG_TABLE_NAME,
      KeyConditionExpression: 'userId = :userId',
      FilterExpression: '#type = :type',
      ExpressionAttributeNames: {
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':userId': userId,
        ':type': logType,
      },
    });

    const response = await docClient.send(queryCommand);
    const logs = response.Items || [];

    console.log(`Found ${logs.length} logs of type ${logType} for user ${userId}`);

    // Format the response to match the expected client structure
    const formattedLogs = logs.map(item => ({
      type: item.type,
      data: item.data,
      timestamp: item.timestamp
    }));

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Logs retrieved successfully',
        logs: formattedLogs,
      }),
    };
  } catch (error: any) {
    console.error('Error fetching logs:', error);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Internal server error',
        error: error.message
      }),
    };
  }
};
