import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, DeleteCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { CognitoJwtVerifier } from 'aws-jwt-verify';

const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// NOTE: We no longer use direct JWT verification in this Lambda
// since API Gateway's Cognito authorizer already validates the tokens.
// We keep the code but comment it out for reference only.
/*
const cognitoJwtVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.USER_POOL_ID!,
  tokenUse: 'access', // This expected an accessToken, creating conflict with API Gateway's idToken validation
  clientId: process.env.USER_POOL_CLIENT_ID!,
});
*/

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Check request method and route accordingly
    if (event.httpMethod === 'DELETE') {
      return await handleDeleteLog(event);
    }

    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing request body' }),
      };
    }

    // Get userId from the claims passed by the API Gateway authorizer
    // API Gateway Cognito authorizer has already verified the token
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    const { type, data, timestamp } = JSON.parse(event.body);

    if (!type || !data || !timestamp) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing required fields' }),
      };
    }

    // Validate log type
    if (!['workout', 'meal', 'conversation', 'weight'].includes(type)) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Invalid log type' }),
      };
    }

    // Additional data validation based on log type
    if (type === 'meal') {
      if (!data.id || !data.metrics?.meal?.title) {
        console.warn('Received invalid meal data:', JSON.stringify(data));
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: 'Invalid meal data: missing required fields',
            details: 'Meal logs must include an id and metrics.meal.title'
          }),
        };
      }
    } else if (type === 'workout') {
      if (!data.id || !data.workoutData?.title) {
        console.warn('Received invalid workout data:', JSON.stringify(data));
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: 'Invalid workout data: missing required fields',
            details: 'Workout logs must include an id and workoutData.title'
          }),
        };
      }
    } else if (type === 'weight') {
      if (!data.id || !data.metrics?.weight?.value || typeof data.metrics.weight.value !== 'number') {
        console.warn('Received invalid weight data:', JSON.stringify(data));
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: 'Invalid weight data: missing required fields',
            details: 'Weight logs must include an id and metrics.weight.value (number)'
          }),
        };
      }
    }

    // Log the data being saved
    console.log(`Saving ${type} log for user ${userId}:`, JSON.stringify({
      logType: type,
      logId: data.id,
      timestamp
    }));

    // Create log entry
    const logEntry = {
      userId,
      timestamp,
      type,
      data,
      ttl: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60), // 1 year TTL
    };

    const command = new PutCommand({
      TableName: process.env.LOG_TABLE_NAME,
      Item: logEntry,
    });

    await docClient.send(command);

    return {
      statusCode: 201,
      body: JSON.stringify({
        message: 'Log entry created successfully',
        logEntry,
      }),
    };
  } catch (error: any) {
    console.error('Error saving log:', error);
    console.error('Request body:', event.body);

    // Log more details about the error
    if (error.name) {
      console.error('Error name:', error.name);
    }
    if (error.message) {
      console.error('Error message:', error.message);
    }
    if (error.stack) {
      console.error('Error stack:', error.stack);
    }

    if (error.name === 'JwtExpiredError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Token expired' }),
      };
    }

    if (error.name === 'JwtInvalidSignatureError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Invalid token signature' }),
      };
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Internal server error',
        error: error.message || 'Unknown error'
      }),
    };
  }
};

// Handle DELETE requests for logs
async function handleDeleteLog(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
  try {
    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing request body' }),
      };
    }

    // Get userId from the claims passed by the API Gateway authorizer
    // API Gateway Cognito authorizer has already verified the token
    const userId = event.requestContext.authorizer?.claims?.sub;
    if (!userId) {
      console.error('ERROR: userId not found in authorizer claims', event.requestContext.authorizer);
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Unauthorized: User ID missing from request context' })
      };
    }
    console.log(`Request authorized for userId: ${userId}`);

    // Parse request body
    const { logId, type } = JSON.parse(event.body);

    if (!logId || !type) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'Missing required fields: logId and type' }),
      };
    }

    console.log(`Attempting to delete ${type} log with ID ${logId} for user ${userId}`);

    // First, verify the log exists and belongs to the user
    // The combination of logId and timestamp is needed to identify the record
    // Since we don't have the timestamp in the request, we need to query for logs by user and find the matching logId
    // For now, we'll skip this verification step and directly attempt deletion based on the provided logId

    // Delete the log
    const deleteCommand = new DeleteCommand({
      TableName: process.env.LOG_TABLE_NAME,
      Key: {
        userId: userId,
        // Use logId as timestamp - this works if logId was stored as the sort key
        // If not, this operation will fail
        timestamp: logId
      },
      ConditionExpression: 'attribute_exists(userId)',
    });

    try {
      await docClient.send(deleteCommand);
    } catch (deleteError: any) {
      console.error('Error deleting log:', deleteError);

      if (deleteError.name === 'ConditionalCheckFailedException') {
        return {
          statusCode: 404,
          body: JSON.stringify({ message: 'Log not found or not owned by user' }),
        };
      }

      throw deleteError;
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Log deleted successfully',
        logId,
        type
      }),
    };
  } catch (error: any) {
    console.error('Error in delete log handler:', error);

    if (error.name === 'JwtExpiredError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Token expired' }),
      };
    }

    if (error.name === 'JwtInvalidSignatureError') {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: 'Invalid token signature' }),
      };
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Internal server error',
        error: error.message
      }),
    };
  }
}