import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocument, PutCommand, GetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from './utils/auth-helper';

const dynamoDb = DynamoDBDocument.from(new DynamoDB({}));
const TDEE_TREND_TABLE = process.env.TDEE_TREND_TABLE_NAME || '';
const LOG_TABLE = process.env.LOG_TABLE_NAME || '';
const USER_PROFILE_TABLE = process.env.USER_PROFILE_TABLE_NAME || '';

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json'
};

// Log critical environment info at startup
console.log('TD<PERSON> Lambda initialized with:', {
  TDEE_TREND_TABLE,
  LOG_TABLE,
  USER_PROFILE_TABLE,
  USER_POOL_ID: process.env.USER_POOL_ID || 'NOT SET',
  USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID || 'NOT SET'
});

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('Received event:', JSON.stringify(event, null, 2));

  // OPTIONS handling is already taken care of by API Gateway's CORS configuration

  try {
    // Log the authorization header for debugging
    console.log('Authorization header:', event.headers?.Authorization || 'None provided');

    // Verify token and get user ID
    const authResult = await verifyToken(event);
    if (!authResult.isValid) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Unauthorized' }),
      };
    }

    const userId = authResult.userId;
    if (!userId) {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'User ID not found in token' }),
      };
    }

    console.log(`Request authorized for userId: ${userId}`);

    // Handle different routes
    const method = event.httpMethod;

    // GET /tdee - Get TDEE data
    if (method === 'GET') {
      return await getTDEEData(userId, event.queryStringParameters);
    }

    // Method not supported
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Method not allowed' }),
    };
  } catch (error) {
    console.error('Error processing request:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'Internal server error' }),
    };
  }
};

// Get TDEE data for a user
async function getTDEEData(userId: string, queryParams: any): Promise<APIGatewayProxyResult> {
  try {
    // Default to 14 days if not specified
    const days = queryParams?.days ? parseInt(queryParams.days, 10) : 14;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Format dates for query
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    console.log(`Getting TDEE data for userId: ${userId} from ${startDateStr} to ${endDateStr}`);

    // Get weight logs
    const weightLogs = await getWeightLogs(userId, startDateStr, endDateStr);
    console.log(`Retrieved ${weightLogs.length} weight logs`);

    // Get calorie logs
    const calorieLogs = await getCalorieLogs(userId, startDateStr, endDateStr);
    console.log(`Retrieved ${calorieLogs.length} calorie logs`);

    // Get TDEE trend data
    const tdeeTrend = await getTDEETrend(userId, startDateStr, endDateStr);
    console.log(`Retrieved ${tdeeTrend.length} TDEE trend entries`);

    // Calculate current TDEE if we have enough data
    let currentTDEE = await calculateAndStoreTDEE(userId, weightLogs, calorieLogs);

    // If we couldn't calculate TDEE, use the most recent value or fall back to BMR-based estimate
    if (!currentTDEE && tdeeTrend.length > 0) {
      currentTDEE = tdeeTrend[tdeeTrend.length - 1].tdee;
    }

    if (!currentTDEE) {
      // Fall back to BMR-based estimate
      const profile = await getUserProfile(userId);
      if (profile) {
        currentTDEE = await calculateStaticTDEE(profile);
      } else {
        currentTDEE = 2000; // Default fallback
      }
    }

    // Prepare response data
    const response = {
      maintenance: currentTDEE,
      intake: calorieLogs.map(log => ({
        date: log.date,
        calories: log.calories
      })),
      burn: calorieLogs.map(log => ({
        date: log.date,
        calories: currentTDEE // Use the calculated TDEE as the burn rate
      })),
      trend: tdeeTrend.map(entry => ({
        date: entry.date,
        tdee: entry.tdee
      }))
    };

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Error getting TDEE data:', error);
    throw error;
  }
}

// Get weight logs for a user within a date range
async function getWeightLogs(userId: string, startDate: string, endDate: string): Promise<{date: string, weightKg: number}[]> {
  try {
    // Query the log table for weight entries
    const params = {
      TableName: LOG_TABLE,
      KeyConditionExpression: 'userId = :userId',
      FilterExpression: 'begins_with(#type, :type) AND #timestamp BETWEEN :startDate AND :endDate',
      ExpressionAttributeNames: {
        '#type': 'type',
        '#timestamp': 'timestamp'
      },
      ExpressionAttributeValues: {
        ':userId': userId,
        ':type': 'weight',
        ':startDate': startDate,
        ':endDate': endDate + 'T23:59:59.999Z'
      }
    };

    const result = await dynamoDb.send(new QueryCommand(params));

    // Transform the data
    return (result.Items || []).map(item => {
      // Extract weight value from the log
      const weightLbs = item.metrics?.weight?.value || 0;
      // Convert pounds to kg
      const weightKg = weightLbs * 0.453592;
      // Extract date from timestamp
      const date = item.timestamp.split('T')[0];

      return { date, weightKg };
    }).sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting weight logs:', error);
    return [];
  }
}

// Get calorie logs for a user within a date range
async function getCalorieLogs(userId: string, startDate: string, endDate: string): Promise<{date: string, calories: number}[]> {
  try {
    // Query the log table for meal entries
    const params = {
      TableName: LOG_TABLE,
      KeyConditionExpression: 'userId = :userId',
      FilterExpression: 'begins_with(#type, :type) AND #timestamp BETWEEN :startDate AND :endDate',
      ExpressionAttributeNames: {
        '#type': 'type',
        '#timestamp': 'timestamp'
      },
      ExpressionAttributeValues: {
        ':userId': userId,
        ':type': 'meal',
        ':startDate': startDate,
        ':endDate': endDate + 'T23:59:59.999Z'
      }
    };

    const result = await dynamoDb.send(new QueryCommand(params));

    // Group meals by date and sum calories
    const caloriesByDate: {[date: string]: number} = {};

    (result.Items || []).forEach(item => {
      const date = item.timestamp.split('T')[0];
      const calories = item.metrics?.meal?.calories || 0;

      if (!caloriesByDate[date]) {
        caloriesByDate[date] = 0;
      }

      caloriesByDate[date] += calories;
    });

    // Convert to array format
    return Object.entries(caloriesByDate).map(([date, calories]) => ({
      date,
      calories
    })).sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting calorie logs:', error);
    return [];
  }
}

// Get TDEE trend data for a user within a date range
async function getTDEETrend(userId: string, startDate: string, endDate: string): Promise<{date: string, tdee: number}[]> {
  try {
    // Query the TDEE trend table
    const params = {
      TableName: TDEE_TREND_TABLE,
      KeyConditionExpression: 'userId = :userId AND #date BETWEEN :startDate AND :endDate',
      ExpressionAttributeNames: {
        '#date': 'date'
      },
      ExpressionAttributeValues: {
        ':userId': userId,
        ':startDate': startDate,
        ':endDate': endDate
      }
    };

    const result = await dynamoDb.send(new QueryCommand(params));

    // Transform the data
    return (result.Items || []).map(item => ({
      date: item.date,
      tdee: item.tdee
    })).sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting TDEE trend data:', error);
    return [];
  }
}

// Calculate and store TDEE based on weight and calorie data
async function calculateAndStoreTDEE(
  userId: string,
  weightLogs: {date: string, weightKg: number}[],
  calorieLogs: {date: string, calories: number}[]
): Promise<number | null> {
  try {
    // Need at least 7 days of data for meaningful calculation
    if (weightLogs.length < 7 || calorieLogs.length < 7) {
      console.log('Not enough data to calculate TDEE');
      return null;
    }

    // Calculate weight trend using EMA (Exponential Moving Average)
    const smoothingFactor = 0.1; // Alpha value for EMA
    let ema = weightLogs[0].weightKg;

    // Calculate starting EMA (for the first data point)
    const trendStart = ema;

    // Calculate ending EMA (for the last data point)
    for (let i = 1; i < weightLogs.length; i++) {
      ema = ema + smoothingFactor * (weightLogs[i].weightKg - ema);
    }
    const trendEnd = ema;

    // Calculate weight change per day
    const windowDays = weightLogs.length;
    const deltaKgPerDay = (trendEnd - trendStart) / windowDays;

    // Convert weight change to calories (1 kg of body weight ≈ 7700 kcal)
    const deltaKcalPerDay = deltaKgPerDay * 7700;

    // Calculate average calorie intake
    const totalCalories = calorieLogs.reduce((sum, log) => sum + log.calories, 0);
    const avgIntake = totalCalories / calorieLogs.length;

    // Calculate TDEE: Average intake - daily calorie surplus/deficit
    let tdee = avgIntake - deltaKcalPerDay;

    // Round to nearest 5 kcal for cleaner numbers
    tdee = Math.round(tdee / 5) * 5;

    // Clamp to reasonable range (1200-6000 kcal)
    tdee = Math.max(1200, Math.min(6000, tdee));

    console.log('TDEE calculation:', {
      trendStart,
      trendEnd,
      windowDays,
      deltaKgPerDay,
      deltaKcalPerDay,
      avgIntake,
      calculatedTDEE: tdee
    });

    // Store the calculated TDEE
    const today = new Date().toISOString().split('T')[0];
    await storeTDEE(userId, today, tdee);

    return tdee;
  } catch (error) {
    console.error('Error calculating TDEE:', error);
    return null;
  }
}

// Store TDEE value in the database
async function storeTDEE(userId: string, date: string, tdee: number): Promise<void> {
  try {
    const params = {
      TableName: TDEE_TREND_TABLE,
      Item: {
        userId,
        date,
        tdee,
        timestamp: new Date().toISOString()
      }
    };

    await dynamoDb.send(new PutCommand(params));
    console.log(`Stored TDEE value ${tdee} for user ${userId} on date ${date}`);
  } catch (error) {
    console.error('Error storing TDEE:', error);
    throw error;
  }
}

// Get user profile
async function getUserProfile(userId: string): Promise<any> {
  try {
    const params = {
      TableName: USER_PROFILE_TABLE,
      Key: {
        userId
      }
    };

    const result = await dynamoDb.send(new GetCommand(params));
    return result.Item;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

// Calculate static TDEE based on user profile (fallback method)
async function calculateStaticTDEE(profile: any): Promise<number> {
  try {
    // Extract profile data
    const weightLbs = parseFloat(profile.weight) || 150;
    const heightInches = parseFloat(profile.height) || 67;
    const age = profile.birthday ? calculateAge(profile.birthday) : 30;
    const gender = profile.gender || 'male';

    // Convert to metric
    const weightKg = weightLbs * 0.453592;
    const heightCm = heightInches * 2.54;

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr: number;
    if (gender.toLowerCase() === 'male') {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) + 5;
    } else {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) - 161;
    }

    // Apply activity multiplier (default to moderately active)
    const activityMultiplier = 1.55;
    const tdee = Math.round(bmr * activityMultiplier / 5) * 5;

    console.log('Static TDEE calculation:', {
      weightLbs,
      weightKg,
      heightInches,
      heightCm,
      age,
      gender,
      bmr,
      activityMultiplier,
      tdee
    });

    return tdee;
  } catch (error) {
    console.error('Error calculating static TDEE:', error);
    return 2000; // Default fallback
  }
}

// Calculate age from birthday string
function calculateAge(birthday: string): number {
  try {
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return 30; // Default fallback
  }
}
