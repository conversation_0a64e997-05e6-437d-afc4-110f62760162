#!/bin/bash

# Deploy daily digest Lambda functions
echo "Deploying daily digest Lambda functions..."

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed. Please install it first."
    exit 1
fi

# Ensure AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "Error: AWS credentials not configured or invalid. Please run 'aws configure'."
    exit 1
fi

# Get the current directory
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$DIR")"

# Build the Lambda functions
echo "Building Lambda functions..."
cd "$PROJECT_ROOT"

# Build TypeScript files if needed
echo "Checking TypeScript files..."
if [ -f infrastructure/lambda/tsconfig.json ]; then
    echo "Compiling TypeScript..."
    npx tsc -p infrastructure/lambda/tsconfig.json
else
    echo "No TypeScript config found, assuming files are JavaScript."
fi

# Create a deployment package
echo "Creating deployment package..."
cd "$PROJECT_ROOT/infrastructure/lambda"
rm -rf dist
mkdir -p dist
cp -r *.js dist/

# Make sure utils directory exists before copying
if [ -d utils ]; then
    echo "Copying utils directory..."
    mkdir -p dist/utils
    cp -r utils/*.js dist/utils/
else
    echo "Creating utils directory..."
    mkdir -p dist/utils
    
    # If the files don't exist yet, copy from our newly created files
    echo "Copying new utility files..."
    cp "$PROJECT_ROOT/infrastructure/lambda/utils/groq-api.js" dist/utils/
    cp "$PROJECT_ROOT/infrastructure/lambda/utils/groq-digest-helper.js" dist/utils/
fi

# Check for node_modules
if [ -d node_modules ]; then
    echo "Copying node_modules..."
    cp -r node_modules dist/
else
    echo "No node_modules found, creating minimal dependencies..."
    cd dist
    npm init -y
    npm install https --save
    cd ..
fi

# Print package contents for verification
echo "Deployment package contents:"
find dist -type f | sort

# Zip the package
cd dist
echo "Creating zip file..."
zip -r ../digest-lambda.zip .

# Update Lambda function code
LAMBDA_FUNCTION_NAME="aura-log-digest-api"
echo "Updating Lambda function $LAMBDA_FUNCTION_NAME..."
cd ..

echo "Size of deployment package: $(du -h digest-lambda.zip | cut -f1)"

aws lambda update-function-code \
    --function-name $LAMBDA_FUNCTION_NAME \
    --zip-file fileb://digest-lambda.zip

# Check if the update was successful
if [ $? -eq 0 ]; then
    echo "Lambda function updated successfully!"
    
    # Wait for the function to be updated
    echo "Waiting for function to be updated..."
    aws lambda wait function-updated --function-name $LAMBDA_FUNCTION_NAME
    
    # Test the function
    echo "Testing the Lambda function..."
    aws lambda invoke \
        --function-name $LAMBDA_FUNCTION_NAME \
        --payload '{"httpMethod":"GET","path":"/digest","queryStringParameters":{"startDate":"2023-05-18","endDate":"2023-05-18"}}' \
        --cli-binary-format raw-in-base64-out \
        test-output.json
    
    echo "Test response:"
    cat test-output.json | jq 2>/dev/null || cat test-output.json
else
    echo "Failed to update Lambda function"
fi

# Clean up
echo "Cleaning up..."
rm -rf dist
rm digest-lambda.zip
rm -f test-output.json

echo "Daily digest Lambda function deployment complete!"
exit 0 