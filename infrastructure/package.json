{"name": "lotus-infrastructure", "version": "0.1.0", "bin": {"infrastructure": "bin/infrastructure.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy", "destroy": "cdk destroy"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "20.7.1", "aws-cdk": "2.88.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "~5.1.6"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.549.0", "@aws-sdk/client-dynamodb": "^3.549.0", "@aws-sdk/client-secrets-manager": "^3.549.0", "@aws-sdk/lib-dynamodb": "^3.549.0", "@expo-google-fonts/inter": "^0.2.3", "@google/generative-ai": "^0.8.0", "@react-navigation/native-stack": "^7.3.3", "@types/aws-lambda": "^8.10.92", "aws-cdk-lib": "2.88.0", "aws-jwt-verify": "^4.0.1", "aws-lambda": "^1.0.7", "constructs": "^10.0.0", "expo-splash-screen": "^0.29.22", "expo-status-bar": "^2.0.1", "groq-sdk": "^0.4.0", "jose": "^6.0.11", "node-fetch": "^3.3.2", "source-map-support": "^0.5.21"}}