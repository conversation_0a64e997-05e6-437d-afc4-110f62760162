import * as cdk from 'aws-cdk-lib';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import * as path from 'path';

// This file contains the updates needed for the Lotus CDK stack to fix the daily digest functionality

export function updateLotusStack(stack: cdk.Stack): void {
  // 1. Add the Daily Digest Table
  const dailyDigestTable = new dynamodb.Table(stack, 'DailyDigestTable', {
    tableName: 'lotus-daily-digest',
    partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    sortKey: { name: 'date', type: dynamodb.AttributeType.STRING },
    billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
    removalPolicy: cdk.RemovalPolicy.DESTROY,
    pointInTimeRecovery: true,
  });

  // 2. Update the Digest Lambda with the correct environment variables
  const digestLambda = stack.node.findChild('DigestLambda') as NodejsFunction;

  // Add the DAILY_DIGEST_TABLE_NAME environment variable
  digestLambda.addEnvironment('DAILY_DIGEST_TABLE_NAME', dailyDigestTable.tableName);

  // We can't directly modify the timeout and memory size of an existing Lambda
  // Instead, we'll log a message to remind the user to update these values manually
  console.log('Remember to update the digestLambda timeout to 120 seconds and memory size to 1024 MB in the main stack file');

  // 3. Add IAM permissions for the Digest Lambda to access the Daily Digest table
  digestLambda.addToRolePolicy(
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'dynamodb:PutItem',
        'dynamodb:GetItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:Query',
        'dynamodb:Scan',
        'dynamodb:BatchGetItem',
        'dynamodb:BatchWriteItem'
      ],
      resources: [
        dailyDigestTable.tableArn,
        `${dailyDigestTable.tableArn}/index/*`,
      ],
    })
  );

  // 4. Add API Gateway endpoints for the Digest service
  const api = stack.node.findChild('LotusApi') as apigateway.RestApi;

  // Get the authorizer
  const authorizer = api.node.findChild('LotusAuthorizer') as apigateway.CognitoUserPoolsAuthorizer;

  // Create method options with authorizer
  const methodOptionsWithAuth = {
    authorizer: authorizer,
    authorizationType: apigateway.AuthorizationType.COGNITO,
  };

  // Add Digest endpoints with authorizer
  const digestResource = api.root.addResource('digest');

  // GET /digest - Get digest for date range
  digestResource.addMethod('GET', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

  // POST /digest - Create a new digest
  digestResource.addMethod('POST', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

  // Add date-specific endpoints
  const dateResource = digestResource.addResource('{date}');

  // GET /digest/{date} - Get digest for specific date
  dateResource.addMethod('GET', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

  // PUT /digest/{date} - Update digest for specific date
  dateResource.addMethod('PUT', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

  // Add regenerate endpoint
  const regenerateResource = dateResource.addResource('regenerate');

  // POST /digest/{date}/regenerate - Regenerate digest for specific date
  regenerateResource.addMethod('POST', new apigateway.LambdaIntegration(digestLambda), methodOptionsWithAuth);

  // 5. Update the Chat Service Lambda to include the Digest API URL
  const chatServiceLambda = stack.node.findChild('ChatServiceLambda') as NodejsFunction;

  // Add the DIGEST_API_URL environment variable
  chatServiceLambda.addEnvironment('DIGEST_API_URL', `${api.url}digest`);

  // Add permissions for the Chat Service Lambda to call the Digest API
  chatServiceLambda.addToRolePolicy(
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'execute-api:Invoke'
      ],
      resources: [
        `arn:aws:execute-api:${stack.region}:${stack.account}:${api.restApiId}/*/${digestResource.path}/*`
      ],
    })
  );
}
