# Lotus Infrastructure

This directory contains the AWS infrastructure for the Lotus application, defined using the AWS Cloud Development Kit (CDK).

## Prerequisites

- AWS CLI installed and configured
- Node.js 14.x or higher
- npm or yarn
- AWS CDK v2 installed globally (`npm install -g aws-cdk`)

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Bootstrap your AWS environment (if not already done):
   ```
   cdk bootstrap
   ```

## Deployment

1. Deploy the stack:
   ```
   cdk deploy
   ```

## Configuring Gemini API Integration

The application uses the Google Gemini API for AI-powered health recommendations. To secure the API key:

1. Deploy the stack first (which creates an AWS Secrets Manager secret)
2. After deployment, go to the AWS Secrets Manager console
3. Find the secret with name `lotus/gemini-api-key`
4. Update the secret value with your actual Gemini API key
5. No changes to the client code are needed - the API key is securely stored in AWS and only accessed by the Lambda function

## Architecture

The infrastructure includes:

- **API Gateway**: Provides REST API endpoints for the app
- **Lambda Functions**: Serverless compute for processing API requests
- **DynamoDB Tables**: NoSQL database for storing user profiles and logs
- **Cognito User Pool**: Authentication service
- **Secrets Manager**: Secure storage for API keys
- **IAM Roles and Policies**: Permissions management

## Security

- API keys are stored in AWS Secrets Manager and not exposed to clients
- Lambda functions use IAM roles with least privilege
- Cognito handles user authentication and JWT token validation