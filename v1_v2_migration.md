# Lotus App: v1 to v2 Migration Guide

This document outlines the technical changes required to migrate the Lotus application from v1 (original README.md) to v2 (READMEv2.md) specifications.

## 1. Navigation Structure Changes

### Original (v1) Navigation Structure
- **Dashboard**: AI conversation interface
- **History**: Log history for workouts, meals, and conversations
- **Progress**: Weight tracking and progress visualization
- **Profile**: User profile and settings

### New (v2) Navigation Structure
- **Lotus**: AI conversation interface (renamed from Dashboard)
- **Strength**: Strength tracking and analytics
- **Diet**: Nutrition tracking and body composition
- **Profile**: User profile and settings

## 2. Screen Implementation Changes

### Lotus Screen (formerly Dashboard)
- Rename "Dashboard" to "Lotus" in navigation and UI
- Keep existing conversation functionality
- Enhance with context awareness banner
- Add structured data detection for workouts and meals

### Strength Screen (new)
- Create new screen for strength tracking
- Implement exercise selection
- Add charts for weight/rep progression
- Display PRs for selected exercises
- Show strength profile based on historical performance

### Diet Screen (new)
- Create new screen for nutrition tracking
- Implement macro/calorie goal visualization with circular progress indicators
- Add weight trend chart
- Display key metrics (weight, estimated maintenance calories)
- Show meal suggestions based on remaining macros

### Profile Screen
- Completely revitalized with modern UI and animations
- Added profile picture upload functionality
- Added progress summary cards
- Added achievements section
- Implemented smooth animations for all interactions
- Added smooth fade transition between dark and light mode
- Completed the "Privacy Settings" and "Help & Support" pages

## 3. Component Changes

### Weight Tracking
- Move from Progress screen to Diet screen
- Enhance visualization with better charts
- Integrate with calorie expenditure calculation

### Workout Logging
- Move structured workout data to Strength screen
- Enhance workout visualization
- Add PR detection and celebration

### Meal Logging
- Move structured meal data to Diet screen
- Enhance meal visualization
- Add macro tracking

## 4. Data Structure Changes

No changes to the underlying data structures are required for the MVP. The existing data models for:
- Conversations
- Workouts
- Meals
- Weight entries

will be reused with the new UI components.

## 5. Implementation Phases

### Phase 1: Navigation Structure
- Update MainTabNavigator.tsx to reflect new tab structure
- Create placeholder screens for new tabs
- Update icons and labels

### Phase 2: Screen Implementation
- Implement Lotus screen (rename Dashboard)
- Implement Strength screen
- Implement Diet screen
- Update Profile screen

### Phase 3: Component Migration
- Move weight tracking to Diet screen
- Move workout visualization to Strength screen
- Move meal visualization to Diet screen

### Phase 4: UI Enhancements
- Update all screens to match MacroFactor and other popular gym tracking apps
- Implement circular progress indicators for macro tracking
- Enhance chart visualizations
- Standardize header styling and alignment across all screens
- Fix SVG component usage in Diet screen for macro rings
- Remove custom fade animations between tab navigation for a more responsive experience

## 6. Technical Implementation Details

### Dependencies Added
- react-native-svg: For rendering circular progress indicators in the Diet screen

### Key Files Modified
- app/navigation/MainTabNavigator.tsx: Updated tab structure and icons
- app/screens/LotusScreen.tsx: Created from DashboardScreen.tsx with UI improvements and message handling
- app/screens/StrengthScreen.tsx: New screen for strength tracking
- app/screens/DietScreen.tsx: New screen for nutrition and weight tracking
- app/screens/ProfileScreen.tsx: Completely revitalized with modern UI and animations
- app/screens/PrivacySettingsScreen.tsx: New screen for privacy settings
- app/screens/HelpSupportScreen.tsx: New screen for help and support
- app/theme/AnimatedThemeProvider.tsx: New provider for smooth theme transitions
- app/components/ProfilePictureUploader.tsx: New component for profile picture management
- app/components/AnimatedToggle.tsx: New animated toggle component
- app/components/AnimatedSettingsItem.tsx: New animated settings item component
- app/components/ProgressSummaryCard.tsx: New component for progress summary cards
- app/components/AchievementBadge.tsx: New component for achievement badges

### Bug Fixes
- Fixed message sending functionality in LotusScreen by implementing the handleSubmit and handleSubmitDirect functions
- Fixed message handling in LotusScreen by using handleSubmitDirect instead of the asynchronous state update + handleSubmit pattern
- Fixed SVG component usage in DietScreen for macro rings by properly importing and using Svg components
- Fixed header alignment across all screens for consistent UI
- Fixed meal modal functionality in LotusScreen by implementing the renderMealModal function and adding it to the return statement
- Fixed layout shift in Lotus and Profile screens by changing the SafeAreaView import from 'react-native-safe-area-context' to 'react-native' to match the other screens

### UI Standardization
- Consistent header styling across all screens
- Standardized padding and spacing
- Consistent use of typography and color schemes
- Enhanced visual feedback for user interactions
- Smooth animations for all interactive elements
- Animated transitions between screens
- Smooth fade transition between dark and light mode
- Modern card-based UI design throughout the app

## 7. Future Enhancements (Post-MVP)

- Body fat percentage estimation
- Advanced strength analytics
- Intelligent calorie and expenditure tracking
- Recovery pattern analysis
- Meal recognition model
- Enhanced data visualization with charts
- Workout plan recommendations based on progress
- Social sharing features
