# Animation Guidelines and UX Patterns

This document outlines the animation styles, timing functions, and UX patterns used in the Aura Log application. Following these guidelines will ensure a consistent, smooth, and intuitive user experience across all features.

## Animation Principles

1. **Purposeful Motion** - Every animation should serve a purpose, either to:
   - Guide attention to important changes
   - Create continuity between state transitions
   - Provide feedback for user actions
   - Establish spatial relationships between elements

2. **Consistency** - Use consistent animation patterns for similar actions:
   - Same timing functions for similar transitions
   - Consistent durations for similar element types
   - Predictable behavior across components

3. **Subtlety** - Animations should enhance, not distract:
   - Avoid excessive or gratuitous animation
   - Keep durations short for frequent interactions
   - Use more subtle animations for background elements

4. **Performance** - All animations should be smooth:
   - Use hardware-accelerated properties (transform, opacity)
   - Keep animations on the UI thread when possible
   - Be mindful of device capabilities

## Animation Timings

| Animation Type | Duration | Easing | Usage |
|----------------|----------|--------|-------|
| Micro-feedback | 100-200ms | Ease-out | Button presses, small UI feedbacks |
| Standard transitions | 250-350ms | Ease-in-out | View transitions, expansion/collapse |
| Elaborate animations | 400-600ms | Spring | Entrance/exit of complex UI elements |
| Background effects | 500-800ms | Ease-in-out | Background color shifts, ambient effects |

## Reanimated Animation Patterns

### Button Press Feedback

```typescript
// Button press animation
const buttonScale = useSharedValue(1);

// In press handler
buttonScale.value = withSequence(
  withTiming(0.95, { duration: 100 }),
  withTiming(1, { duration: 150 })
);

// Apply to style
const buttonAnimatedStyle = useAnimatedStyle(() => {
  return {
    transform: [{ scale: buttonScale.value }]
  };
});
```

### Modal Transitions

```typescript
// Modal animation values
const modalScale = useSharedValue(0.95);
const modalOpacity = useSharedValue(0);

// Opening animation
useEffect(() => {
  if (isVisible) {
    modalScale.value = withSpring(1, {
      damping: 15,
      stiffness: 150,
      mass: 0.8
    });
    modalOpacity.value = withTiming(1, { duration: 300 });
  }
}, [isVisible]);

// Closing animation
const closeWithAnimation = () => {
  modalScale.value = withTiming(0.95, { duration: 200 });
  modalOpacity.value = withTiming(0, { duration: 250 });
  
  // Delay actual closing to allow animation to complete
  setTimeout(() => setIsVisible(false), 250);
};

// Apply to style
const modalAnimatedStyle = useAnimatedStyle(() => {
  return {
    opacity: modalOpacity.value,
    transform: [{ scale: modalScale.value }]
  };
});
```

### Expandable Content

```typescript
// Animation values
const contentHeight = useSharedValue(0);
const [isExpanded, setIsExpanded] = useState(false);

// Toggle function
const toggleExpand = () => {
  if (isExpanded) {
    // Collapse animation
    contentHeight.value = withTiming(0, { 
      duration: 250, 
      easing: Easing.inOut(Easing.quad) 
    });
    setTimeout(() => setIsExpanded(false), 260);
  } else {
    // Expand animation - set state first, then animate
    setIsExpanded(true);
    setTimeout(() => {
      contentHeight.value = withTiming(showAll ? 300 : 140, { 
        duration: 300, 
        easing: Easing.out(Easing.quad) 
      });
    }, 10);
  }
};

// Apply to style
const contentAnimatedStyle = useAnimatedStyle(() => {
  return {
    height: contentHeight.value,
    opacity: contentHeight.value > 0 ? 
      withTiming(1, { duration: 200 }) : 
      withTiming(0, { duration: 100 }),
    overflow: 'hidden'
  };
});
```

### Success/Error Feedback

```typescript
// Animation values
const feedbackScale = useSharedValue(0.8);
const feedbackOpacity = useSharedValue(0);

// Show feedback
const showFeedback = () => {
  feedbackOpacity.value = withSequence(
    withSpring(1, {
      damping: 15,
      stiffness: 120,
      mass: 0.8
    }),
    withDelay(1500, withTiming(0, { duration: 300 }))
  );

  feedbackScale.value = withSequence(
    withSpring(1.1, {
      damping: 15,
      stiffness: 120
    }),
    withSpring(1, {
      damping: 12, 
      stiffness: 100
    }),
    withDelay(1500, withTiming(0.8, { duration: 300 }))
  );
};

// Apply to style
const feedbackAnimatedStyle = useAnimatedStyle(() => {
  return {
    opacity: feedbackOpacity.value,
    transform: [{ scale: feedbackScale.value }]
  };
});
```

## Keyboard Handling

Best practices for smooth keyboard interaction:

1. **Predictive Dismissal**:
   - Dismiss keyboard when scrolling begins
   - Add a "Done" button when appropriate
   - Dismiss on background taps

2. **Smooth Transitions**:
   - Animate content repositioning when keyboard appears/disappears
   - Use KeyboardAvoidingView with appropriate settings
   - Consider platform differences (iOS vs Android)

3. **Visual Consistency**:
   - Ensure UI elements don't abruptly jump when keyboard appears
   - Maintain visual context during keyboard transitions
   - Keep important action buttons accessible above keyboard

```typescript
// Smooth keyboard transition
useEffect(() => {
  const keyboardWillShowListener = Keyboard.addListener(
    Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
    (event) => {
      setKeyboardVisible(true);
      // Animate transition when keyboard shows
      contentTranslateY.value = withTiming(-event.endCoordinates.height / 3, {
        duration: 300,
        easing: Easing.out(Easing.cubic)
      });
    }
  );
  
  const keyboardWillHideListener = Keyboard.addListener(
    Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
    () => {
      setKeyboardVisible(false);
      // Animate transition when keyboard hides
      contentTranslateY.value = withTiming(0, {
        duration: 250,
        easing: Easing.out(Easing.cubic)
      });
    }
  );

  return () => {
    keyboardWillShowListener.remove();
    keyboardWillHideListener.remove();
  };
}, []);
```

## Modal Design Patterns

For modal interfaces, follow these principles:

1. **Clear Entry/Exit Points**:
   - Animate entrance and exit
   - Provide clear dismissal mechanisms (Close button, swipe down, tap outside)
   - Consider pull-down indicators for sheet-style modals

2. **Contextual Backgrounds**:
   - Darken/blur the background to focus attention
   - Animate background opacity changes
   - Use spring animations for natural-feeling modal entry

3. **Content Hierarchy**:
   - Primary action buttons should be prominently positioned
   - Most important content at top/center
   - Use spacing and typography to create visual hierarchy

## Platform-Specific Considerations

### iOS

- Use spring animations for natural motion
- Respect inertia and momentum in scrolling
- Support haptic feedback for meaningful interactions
- Sheet-style modals should support pull-to-dismiss

### Android

- Material Design motion standards: deceleration for entering, acceleration for exiting
- Consider ripple effects for touch feedback
- Respect platform-specific navigation patterns
- Faster animation timing than iOS (typically)

## Implementation Guidelines

When implementing animations across the app:

1. Start with standard component animations before adding custom effects
2. Test on both low and high-end devices to ensure performance
3. Add animations iteratively, prioritizing the most important interactions
4. Consider accessibility - allow users to reduce motion if needed
5. Group related animations to create a cohesive experience
6. Test animations at different navigation depths and states

## Next Steps for Implementation

To apply these animation principles throughout the app, focus on these high-impact areas first:

1. Navigation transitions between screens
2. List item animations (entry, exit, selection)
3. Form element feedback and transitions
4. Pull-to-refresh and loading state animations
5. User feedback mechanisms (success, error, progress indicators)
6. Data visualization transitions

Each feature should be audited for animation opportunities using the patterns from this guide.