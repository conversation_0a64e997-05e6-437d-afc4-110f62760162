#!/bin/bash

echo "===== DIGEST FEATURE DEPLOYMENT ====="

# 1. Export the GROQ API key if not already set
if [ -z "$GROQ_API_KEY" ]; then
  echo "GROQ_API_KEY is not set. Please run:"
  echo "export GROQ_API_KEY=your_api_key"
  exit 1
fi

echo "✓ GROQ_API_KEY is set"

# 2. Make the infrastructure deployment script executable
echo "Making deployment script executable..."
chmod +x infrastructure/deploy-digest-fix.sh

# 3. Run a metro clean to clear any bundling caches
echo "Cleaning Metro bundler cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 5
kill $METRO_PID

# 4. Deploy the CDK changes
echo "Deploying CDK changes..."
cd infrastructure
npm run build && cdk deploy --require-approval never
cd ..

echo "Deployment complete!"
echo ""
echo "Next steps:"
echo "1. Run your app with: npm run ios (or npm run android)"
echo "2. Navigate to the Digest screen and test the functionality"
echo "3. Check CloudWatch logs if you encounter any issues" 