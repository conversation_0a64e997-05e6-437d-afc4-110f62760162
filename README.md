**I. Project Purpose & Technology Stack: Lotus**

*   **Project Purpose:** Lotus aims to revolutionize fitness and nutrition tracking by providing an exceptionally intuitive and aesthetically pleasing mobile application. Leveraging the power of Large Language Models (LLMs), it allows users to log workouts, meals, and query their progress using natural, conversational language, drastically reducing the friction associated with traditional tracking methods. The core goal is to deliver a sleek, modern, and seamless user experience that makes consistent health logging effortless and insightful. The application will focus on core tracking (workouts, meals), progress visualization, and user account management within a minimalist black, white, and light blue design philosophy prioritizing fluid animations and clear information hierarchy.
*   **Core Philosophy:** Natural Language First, Minimalist Design, Fluid Interaction.
*   **Target Audience:** Fitness enthusiasts, health-conscious individuals, and tech adopters seeking a faster, more intuitive alternative to complex tracking apps.

*   **Technology Stack:**
    *   **Frontend (Mobile App):**
        *   Framework: React Native (managed via Expo)
        *   Navigation: React Navigation (v6+)
        *   State Management: React Context API (for Auth/Theme), potentially Zustand or Redux Toolkit for complex state.
        *   Animations: `react-native-reanimated` (v2/v3), `Moti`
        *   UI Components: Custom-built or minimally adapted from libraries (e.g., `react-native-paper`) for full theme control.
        *   Local Storage: `Expo SecureStore` (for tokens), AsyncStorage (for preferences).
    *   **Backend (Cloud Infrastructure):**
        *   Provider: Amazon Web Services (AWS)
        *   API Layer: AWS API Gateway (RESTful endpoints)
        *   Compute: AWS Lambda (Node.js or Python runtime) - Serverless functions for processing, LLM interaction, DB operations.
        *   Database: AWS DynamoDB (NoSQL) - Scalable storage for user logs, potentially user profiles.
        *   Authentication: AWS Cognito - User Pool for sign-up/sign-in, identity management.
        *   (Potentially): AWS S3 for storing user-uploaded assets (e.g., profile pictures - future).
    *   **Infrastructure as Code (IaC):**
        *   Tool: AWS Cloud Development Kit (CDK) - TypeScript for defining and provisioning AWS resources.
    *   **External Services:**
        *   LLM Provider: OpenAI API (GPT-3.5/4) or Anthropic API (Claude) - Accessed securely via backend Lambda.
*   **Design Aesthetics:**
    *   **Color Palette:** Primary: `#FFFFFF` (White) / `#121212` (Near Black - Dark Mode); Accent: `#ADD8E6` (Light Blue) and variations (e.g., `#87CEEB`, `#F0F8FF`); Neutrals: Subtle Greys (`#F5F5F5`, `#E0E0E0` / `#282828`, `#3a3a3a`).
    *   **Animations:** Fluid, purposeful, and non-intrusive (using Reanimated/Moti). Focus on smooth transitions, subtle micro-interactions on focus/press, and clear loading/feedback states.

**II. Project Setup: Directory Structure**

*   **Rationale:** A monorepo-like structure is chosen for the MVP to streamline development and dependency management while maintaining logical separation between the application code and the infrastructure code. This facilitates easier cross-referencing during initial development.
*   **Structure:**
    ```
    Lotus/
    ├── app/                 # React Native (Expo) Application Code
    │   ├── assets/
    │   ├── components/
    │   ├── navigation/
    │   ├── screens/
    │   ├── services/        # API interaction logic
    │   ├── state/           # Global state management
    │   ├── utils/
    │   ├── App.js           # Entry point for React Native
    │   ├── app.json         # Expo configuration
    │   ├── babel.config.js
    │   └── package.json     # Frontend dependencies
    │
    ├── infrastructure/      # AWS CDK Infrastructure Code
    │   ├── bin/             # Entry point script for CDK app
    │   ├── lib/             # CDK stack definitions (e.g., ApiStack, DbStack, AuthStack)
    │   ├── test/            # Unit/snapshot tests for infrastructure
    │   ├── cdk.json         # CDK toolkit configuration
    │   ├── package.json     # CDK dependencies (AWS CDK libraries, constructs)
    │   └── tsconfig.json
    │
    └── README.md            # Project overview
    ```
*   **Interaction:** The `infrastructure/` project defines AWS resources (API Gateway endpoints, Lambda ARNs, DynamoDB table names, Cognito Pool ID/Client ID). These identifiers (often exposed as CDK Stack Outputs) are then configured within the `app/` project (e.g., in environment variables or a config file) so the React Native app knows which backend endpoints to call. The CI/CD pipeline will deploy the infrastructure first, then build and deploy the application code (Lambda function code bundles, potentially Expo app updates).

**III. Detailed User Flow & Technical Implementation**

**1. App Launch & Splash Screen**

*   **Purpose:** Provides initial branding, handles asynchronous loading tasks (fonts, assets), and crucially determines the user's authentication state to direct them appropriately.
*   **User Perspective:** The user sees a clean, quick loading screen with the app's logo, providing immediate brand recognition and visual feedback that the app is starting. It's designed to be fast and seamless, leading directly to either the login page or their main dashboard without requiring user action.
*   **UI:** Minimalist. Centered Lotus logo (monochromatic or subtly using light blue). Potentially a very soft, almost imperceptible gradient background (white to slightly off-white/blue-tinted). No interactive elements.
*   **Technical Flow:**
    1.  **React Native Bootstrap:** The native iOS/Android shell launches the JavaScript environment. Expo initializes.
    2.  **Component Mount:** The `SplashScreen` component renders. Any custom fonts are loaded asynchronously using `expo-font`.
    3.  **Logo Animation:** A `Moti` or `Reanimated` animation (e.g., `from={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} transition={{ type: 'timing', duration: 600 }}`) fades in and slightly scales the logo.
    4.  **Auth Check (`useEffect`):**
        *   Attempt to read JWT token (`accessToken` or `idToken`) from `Expo SecureStore`.
        *   **If Token Exists:** Optionally perform a quick validation call (e.g., Cognito `getUser` or a lightweight protected Lambda endpoint) to ensure it's not expired/revoked.
        *   **Navigation Decision:** Based on token presence and validity:
            *   Valid Token -> Navigate to `MainApp` stack (default: `Dashboard` screen).
            *   No/Invalid Token -> Navigate to `Auth` stack (default: `SignIn` screen).
    5.  **Navigation Execution:** `navigation.reset()` method from React Navigation is used to replace the splash screen in the navigation stack, preventing users from navigating back to it.
    6.  **Screen Transition:** A simple cross-fade animation (`cardStyleInterpolator` in React Navigation stack options) manages the visual transition from Splash to the target screen (Auth or MainApp). Duration ~300ms.
*   **Animations:** Logo fade-in/scale, Screen cross-fade transition.

**2. Authentication Screens (Auth Stack)**

*   **Purpose:** To securely manage user identification, allowing new users to register and existing users to log in, thereby gaining access to their personalized data. Also provides a mechanism for password recovery.
*   **User Perspective:** Presents standard, easy-to-understand forms for entering credentials. Clear visual cues (like highlighted input borders) and feedback (loading states, error messages) guide the user through the sign-in or sign-up process. Offers a way to regain access if they forget their password.
*   **UI (General):** Consistent layout across screens. White/black primary background. Black/white text. Input fields (`TextInput`) with padding, rounded corners, and a subtle border (`#E0E0E0` / `#3a3a3a`). On focus, the border animates to the light blue color (`#ADD8E6`) and slightly thicker stroke width. Buttons utilize solid light blue or bordered light blue for primary/secondary actions. Modern sans-serif font (e.g., Inter). Error messages appear below inputs in a distinct color (subtle red, or just primary text color with an icon).
*   **a. Sign In Screen (`SignIn`)**
    *   **Purpose:** Authenticates existing users using their registered credentials.
    *   **User Perspective:** The entry point for returning users. They enter their email and password to access their logs and profile. Links provide options for password reset or creating a new account if needed.
    *   **UI Elements:** Lotus Logo (smaller), "Welcome Back" header, Email `TextInput`, Password `TextInput` (with secureTextEntry toggle icon), "Forgot Password?" `TouchableOpacity` Text (light blue), Primary "Sign In" `TouchableOpacity` Button (light blue background), Secondary "Don't have an account? Sign Up" `TouchableOpacity` Text (light blue).
    *   **Technical Flow:**
        1.  Local state (`useState`) manages email/password input values.
        2.  Client-side validation (using regex for email format, check for non-empty) triggers on blur or submit. Validation errors update state, displaying messages conditionally.
        3.  On "Sign In" press:
            *   Set loading state (`useState`): `isLoading = true`. UI updates button appearance (disabled, text change, embedded `ActivityIndicator` colored light blue).
            *   Call an async function (`signInUser`) within `services/auth.js`.
            *   `signInUser` makes a POST request (using `axios` or `fetch`) to the specific API Gateway endpoint mapped to the Cognito Sign-In Lambda. Body: `{ email, password }`.
            *   **Lambda (Sign-In Function):**
                *   Receives email/password.
                *   Uses AWS SDK (`@aws-sdk/client-cognito-identity-provider`) to call `initiateAuth` on the Cognito User Pool (`AuthFlow: 'USER_PASSWORD_AUTH'`, `ClientId`, `AuthParameters: { USERNAME: email, PASSWORD: password }`).
                *   **On Cognito Success:** Cognito returns `AuthenticationResult` containing `IdToken`, `AccessToken`, `RefreshToken`. Lambda forwards these tokens in the success response (e.g., `statusCode: 200, body: JSON.stringify({ idToken, accessToken, refreshToken })`).
                *   **On Cognito Failure:** Cognito throws errors (e.g., `UserNotFoundException`, `NotAuthorizedException`). Lambda catches these, logs details, and returns a specific error response (e.g., `statusCode: 401/404, body: JSON.stringify({ message: 'Invalid credentials' })`).
            *   **App Response (Frontend):**
                *   Await the API call response.
                *   **On Success (2xx):** Parse tokens. Securely store them using `Expo SecureStore`. Update global auth state (Context API `dispatch({ type: 'SIGN_IN', payload: { tokens, userInfo } })`). Navigate to `MainApp` stack using `navigation.reset()`. Clear loading state.
                *   **On Failure (4xx/5xx):** Parse error message. Display user-friendly message using a Snackbar/Toast component (animates sliding in/out). Clear loading state.
    *   **Animations:** Input focus border color/width transition. Button press scale effect (`Pressable`). Loading indicator spin. Snackbar/Toast slide-in/out animation. Screen slide transition (React Navigation).
*   **b. Sign Up Screen (`SignUp`)**
    *   **Purpose:** Enables new users to create an account within the application.
    *   **User Perspective:** Allows first-time users to join Lotus by providing basic information (name, email, password). Provides clear password requirements and confirmation.
    *   **UI Elements:** Similar layout. Fields for Name, Email, Password, Confirm Password. Password strength indicator (optional visual bar). "Sign Up" button. "Already have an account? Sign In" link.
    *   **Technical Flow:**
        1.  Local state manages input values.
        2.  Client-side validation: includes password match check, minimum length, potentially regex for complexity requirements.
        3.  On "Sign Up" press:
            *   Set loading state.
            *   Call `signUpUser` service function.
            *   POST request to Sign-Up Lambda endpoint. Body: `{ name, email, password }`.
            *   **Lambda (Sign-Up Function):**
                *   Uses AWS SDK to call `signUp` on Cognito User Pool (`ClientId`, `Username: email`, `Password: password`, `UserAttributes: [{ Name: 'name', Value: name }]`).
                *   **On Cognito Success:** Typically requires email verification. Lambda returns success (`statusCode: 200, body: JSON.stringify({ message: 'User created. Check email for verification code.' })`). Could optionally trigger an auto-confirmation flow if desired for MVP simplicity (`adminConfirmSignUp`).
                *   **On Cognito Failure:** Handles errors like `UsernameExistsException`, `InvalidPasswordException`. Returns error response.
            *   **App Response:**
                *   **On Success:** Clear loading state. Display success message (e.g., via Snackbar or modal). Navigate user to `SignIn` screen or potentially a `VerifyEmail` screen (if implementing verification code step).
                *   **On Failure:** Display specific error message. Clear loading state.
    *   **Animations:** Same as Sign In. Potential animation for password strength indicator.
*   **c. Forgot Password Screen (`ForgotPassword`)**
    *   **Purpose:** Provides a self-service mechanism for users to initiate a password reset process if they've forgotten their password.
    *   **User Perspective:** A straightforward way to recover account access by entering their registered email address. They understand they will receive instructions via email.
    *   **UI Elements:** Email `TextInput`, "Send Reset Link" button. Back navigation integrated into the header (React Navigation default).
    *   **Technical Flow:**
        1.  Local state for email. Client-side email format validation.
        2.  On "Send Reset Link" press:
            *   Set loading state.
            *   Call `forgotPassword` service function.
            *   POST to Forgot Password Lambda endpoint. Body: `{ email }`.
            *   **Lambda (Forgot Password Function):**
                *   Uses AWS SDK to call `forgotPassword` on Cognito User Pool (`ClientId`, `Username: email`).
                *   Lambda always returns a generic success response (`statusCode: 200`) regardless of whether the email exists, as per security best practices (prevents user enumeration). Logs actual success/failure internally.
            *   **App Response:**
                *   Clear loading state. Display a consistent confirmation message ("If an account exists for this email, a password reset link has been sent.") via Snackbar. Optionally navigate back to `SignIn`.
    *   **Animations:** Same as Sign In/Up.

**3. Main Application Interface (MainApp Stack - Bottom Tab Navigator)**

*   **Purpose:** Provides the core authenticated experience, allowing users to log data, view their history, track progress, and manage their profile settings. Organized via tabs for clear functional separation.
*   **User Perspective:** The central hub of the app after logging in. Users can easily switch between logging new entries (Dashboard), reviewing past data (History), seeing trends (Progress), and managing their account (Profile) using the intuitive bottom tab bar.
*   **Structure:** `createBottomTabNavigator` (React Navigation). Tabs: `Dashboard`, `History`, `Progress`, `Profile`. Active tab icon and label use the light blue accent (`#87CEEB` or similar). Subtle scale/opacity animation on tab switch.
*   **a. Dashboard Screen (Home Tab)**
    *   **Purpose:** The primary interface for the core natural language logging feature. Offers quick input and immediate feedback, alongside a glance at recent activity.
    *   **User Perspective:** The go-to screen for quickly logging a workout or meal just by typing naturally. It feels like sending a quick message. Users can also see their very latest entries for immediate context.
    *   **UI:**
        *   **Header:** Subtle title ("Dashboard" or "Log") or just rely on the tab bar context.
        *   **Recent Activity:** Small horizontal/vertical `FlatList` near the top, showing 3-5 recent log items (structured data summaries). Each item is tappable (future: leads to `LogDetail`). Uses icons (dumbbell/fork), key info, relative timestamp ("2h ago"). Items animate in (`Moti` list animation).
        *   **Main Input:** A prominent `TextInput` anchored near the bottom, styled distinctively (maybe slightly rounded rectangle, light grey background in light mode, subtle inner shadow). Placeholder guides user: "Log: 'Bench press 3x10 150lbs' or Ask: 'Last run distance?'".
        *   **Send/Log Button:** Icon button (`TouchableOpacity` with an `Ionicons` or similar icon like a paper plane) adjacent to the input. Enabled only when `TextInput` has content. Light blue background/tint when active.
    *   **Technical Flow (Logging & Querying):**
        1.  `TextInput` value managed by local state (`useState`).
        2.  Send button `disabled` state tied to input value length.
        3.  On Send button press:
            *   Set `isProcessing = true`. Display loading state (e.g., pulsating input border with `Reanimated`, button shows `ActivityIndicator`).
            *   Call `processUserInput` service function, passing the raw text.
            *   POST to the main "Processing" Lambda endpoint via API Gateway. Body: `{ textInput: "..." }`. `Authorization` header contains Cognito ID Token.
            *   **Lambda (Processing Function):**
                *   Authenticates user using the ID token. Extracts `userId` (Cognito `sub` claim).
                *   **Intent Recognition:** Uses LLM (or initially, simple keyword spotting - 'what was', 'how many', '?') to differentiate log vs. query.
                *   **If Log:** Constructs prompt for LLM API (e.g., OpenAI `chat.completions`) including instructions to parse fitness/nutrition entities and return structured JSON. Sends user text.
                *   **If Query:** Performs DynamoDB query (`QueryCommand` using `userId` as PK) to retrieve relevant recent history. Constructs prompt for LLM including the user question and the retrieved context data. Sends to LLM API.
                *   LLM API returns JSON (for logs) or text response (for queries).
                *   **Log Path:** Validates LLM JSON structure. Creates DynamoDB item: `{ pk: userId, sk: 'LOG#' + timestamp, type: 'workout/meal', GSI1PK: 'LOG#type', GSI1SK: timestamp, ...structuredDetails }`. Uses `PutCommand` to save to DynamoDB table. Returns success (`statusCode: 201, body: JSON.stringify({ message: 'Logged', loggedItem: structuredItem })`).
                *   **Query Path:** Returns success with the LLM's textual answer (`statusCode: 200, body: JSON.stringify({ message: 'Answer', answer: llmAnswer })`).
                *   **Error Handling:** Catches LLM API errors, parsing errors, DB errors. Returns appropriate error response (`statusCode: 400/500`).
            *   **App Response:**
                *   Clear `isProcessing` state.
                *   **Log Success (201):** Clear `TextInput`. Show brief confirmation animation (e.g., Moti view animating checkmark). Update local state holding recent logs, triggering animated insertion into the "Recent Activity" `FlatList`.
                *   **Query Success (200):** Display the `answer` temporarily above the input field in a chat-bubble-like component (animates in/out). Do not clear input (user might refine query).
                *   **Failure (4xx/5xx):** Display user-friendly error via Snackbar ("Sorry, I couldn't understand that.", "Log failed, try again."). Input text remains.
    *   **Animations:** Input focus highlight. Send button press/loading states. Recent activity items animating in/updating. Typing indicator (optional subtle animation). Query response bubble animation. Confirmation animation.
*   **b. History Screen (History Tab)**
    *   **Purpose:** Provides a complete, searchable, and filterable chronological record of all the user's logged workouts and meals.
    *   **User Perspective:** Allows users to easily review everything they've tracked over time. They can scroll back through days or weeks, see details of past entries, and potentially filter to find specific types of logs.
    *   **UI:** Full-screen `FlatList`.
        *   **Header:** "History" title. Potential Filter/Search icons (light blue). (Filters maybe by date range, type: workout/meal - V2 feature).
        *   **List Items:** Each row represents one log entry. Clear visual distinction (icon, background tint variation) between workout and meal logs. Displays structured details concisely (e.g., Workout: Exercise Name, SetsxReps@Weight; Meal: Description, ~Calories). Timestamp prominent. Uses black/white/grey text, light blue icons/accents.
        *   **Scroll:** Infinite scroll implemented. Loading indicator (`ActivityIndicator`, light blue) at the bottom when fetching more data.
        *   **Empty State:** A centered view with an icon and text ("No logs recorded yet.") when the list is empty.
    *   **Technical Flow:**
        1.  `useFocusEffect` hook triggers data fetch when the screen becomes active.
        2.  Initial Fetch: Call `getLogs` service function. Sends GET request to `/logs` endpoint.
        3.  **Lambda (Get Logs Function):** Authenticates user. Performs DynamoDB `QueryCommand` (`KeyConditionExpression: pk = :userId`, `ScanIndexForward: false` (newest first), `Limit: 20`). Returns array of log items and `LastEvaluatedKey` if more data exists.
        4.  **App Response:** Store logs and `lastEvaluatedKey` in local state (`useState`). `FlatList` renders the items. Set `isLoading = false`.
        5.  **Infinite Scroll (`onEndReached`):** When `FlatList` reaches near the end and `isLoading` is false and `lastEvaluatedKey` exists:
            *   Set `isLoadingMore = true` (to show footer indicator).
            *   Call `getLogs` again, passing the `lastEvaluatedKey` as a query parameter (e.g., `/logs?exclusiveStartKey=...`).
            *   Lambda performs query starting from that key.
            *   Appends new items to the existing logs array, updates `lastEvaluatedKey`. Set `isLoadingMore = false`.
        6.  `FlatList` performance optimizations: `keyExtractor`, `getItemLayout` (if item height is fixed/predictable).
    *   **Animations:** Staggered list item entrance animation (`Moti` or `Reanimated Layout Animations`). Smooth scrolling. Footer loading indicator animation.
*   **c. Progress Screen (Progress Tab - Core Feature for Value)**
    *   **Purpose:** To translate raw logged data into meaningful insights about trends, improvements, and personal records (PRs), helping users understand their fitness journey.
    *   **User Perspective:** This is where users see the results of their consistent logging. They can visualize how their strength is increasing for specific exercises, track calorie intake trends, or monitor workout frequency, motivating them to continue.
    *   **UI:**
        *   **Header:** "Progress".
        *   **Selectors:** Row of buttons or segmented control (`react-native-segmented-control` styled) for high-level categories (e.g., "Workouts", "Nutrition"). Below this, dynamic dropdowns/selectors (`react-native-picker-select` styled) appear based on the category (e.g., Select Exercise for Workouts, Select Metric for Nutrition - Calories/Macros). Time range selector (7d, 1m, 3m, All). All selectors use light blue accents.
        *   **Chart Area:** `react-native-svg-charts` used to display data. Line charts for weight/reps over time, Bar charts for volume/frequency/calories. Uses light blue (`#87CEEB`) for data visualization against the clean background. Includes axes, potentially tooltips on hover/press. Animated chart rendering.
        *   **PR Section:** A card displaying key PRs relevant to the selected view (e.g., "Max Bench Press: 160 lbs", "Fastest 5k: 28 min").
        *   **Loading/Empty State:** Skeleton loaders (`react-content-loader`) for chart area during data fetch. Message like "Log more data to see progress charts." when insufficient data exists.
    *   **Technical Flow:**
        1.  User selections update local state. `useEffect` triggers data fetching when selections change.
        2.  Call `getProgressData` service function with parameters (category, metric, exercise, timeRange).
        3.  GET request to `/progress` endpoint with query parameters.
        4.  **Lambda (Get Progress Data):**
            *   Authenticates user. Parses query parameters.
            *   Performs complex DynamoDB queries, possibly leveraging GSIs (e.g., GSI on `userId`+`exerciseName`+`timestamp` to get data for a specific exercise).
            *   Requires data processing/aggregation *within Lambda*: Iterating through query results, calculating volume (sets*reps*weight), finding max values for PRs, summing calories, filtering by date range.
            *   *Optimization Note:* For very high load, this aggregation logic might be offloaded (e.g., DynamoDB Streams -> Lambda -> Analytics DB or pre-aggregation), but likely overkill for MVP.
            *   Returns structured data formatted specifically for the required chart types (e.g., array of `{ date, value }` for line charts) and PR information.
        5.  **App Response:** Store processed data in state. Pass data to chart components. `react-native-svg-charts` renders visualization. Update PR display section. Handle loading/empty states.
    *   **Animations:** Chart data points/bars animate smoothly on load/update. Selector changes might trigger subtle fade transitions in the chart area. Skeleton loader animation.
*   **d. Profile Screen (Profile Tab)**
    *   **Purpose:** Allows users to manage their account settings, personalize app preferences (units, theme), and access administrative actions like logging out or changing passwords.
    *   **User Perspective:** The place to customize their experience (like setting preferred weight units), manage account security, and find app-related information or support options.
    *   **UI:** Standard settings list view (`ScrollView` or `SectionList`).
        *   **Header:** Displays User Name/Email (fetched from auth state/token). Placeholder for profile pic (future).
        *   **Sections:** Grouped options. Each option is a tappable row with an icon, label, and current value/control.
            *   `Preferences`: `Units` (lbs/kg - opens Modal/Select), `Theme` (Light/Dark/System - uses Switch/Segmented Control).
            *   `Account`: `Change Password` (navigates to new screen), `Logout` (prominent button).
            *   `About`: `Version`, `Privacy Policy`, `Terms of Service` (links).
        *   Use light blue for interactive elements like switches or selected values. Logout button might use light blue fill or border.
    *   **Technical Flow:**
        1.  User info populated from global auth state (decoded ID token).
        2.  **Preference Changes:** Update global/local state. Persist settings using AsyncStorage (e.g., `await AsyncStorage.setItem('@user_preferences', JSON.stringify({ units, theme }))`). Theme change triggers app re-render with updated theme context.
        3.  **Change Password:** Navigates to a dedicated flow (likely separate screens within the Profile stack) that interfaces with Cognito's `changePassword` or forgot/set password flow. Requires current password or verification code.
        4.  **Logout:**
            *   Show confirmation modal ("Are you sure you want to logout?").
            *   On confirmation: Call `signOut` service function.
            *   Optional: Call Cognito `globalSignOut` or `revokeToken` via Lambda to invalidate tokens server-side.
            *   **Crucially:** Clear tokens from `Expo SecureStore`. Clear global auth state (`dispatch({ type: 'SIGN_OUT' })`).
            *   Navigate user back to the `Auth` stack (`navigation.reset()`).
        5.  Links open using `Linking` module from React Native.
    *   **Animations:** Standard screen transitions. Subtle press feedback on list items. Animated Switch component for theme/notifications. Modal animations for confirmations/selections.

