# OSX
.DS_Store

# Node.js
node_modules/
npm-debug.log
yarn-error.log
package-lock.json

# Expo
.expo/
dist/
web-build/

# React Native
*.jsbundle
*.tsbundle
.vscode/
.idea/

# TypeScript
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# AWS CDK
cdk.out/
.cdk.staging/
cdk.context.json

# Build output
build/
dist/
out/

# Log files
*.log

# Dependency directories
jspm_packages/

# Testing
coverage/

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db