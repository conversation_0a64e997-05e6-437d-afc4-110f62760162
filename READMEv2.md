# I. Project Purpose & Technology Stack: Lotus

*   **Project Purpose:** Lotus aims to revolutionize fitness and nutrition tracking by providing an exceptionally intuitive and aesthetically pleasing mobile application. Leveraging the power of Large Language Models (LLMs), it allows users to log workouts, meals, and query their progress using natural, conversational language, drastically reducing the friction associated with traditional tracking methods. The core goal is to deliver a sleek, modern, and seamless user experience that makes consistent health logging effortless and insightful. The application will focus on conversational AI interaction, workout tracking, meal planning, progress visualization, and comprehensive profile data management within a minimalist black, white, and light blue design philosophy prioritizing fluid animations and clear information hierarchy.
*   **Core Philosophy:** AI-Powered Natural Language First, Minimalist Design, Fluid Interaction, Personalized Health Intelligence.
*   **Target Audience:** Fitness enthusiasts, health-conscious individuals, and tech adopters seeking a faster, more intuitive alternative to complex tracking apps.

*   **Technology Stack:**
    *   **Frontend (Mobile App):**
        *   Framework: React Native (managed via Expo)
        *   Navigation: React Navigation (v6+)
        *   State Management: React Context API (for Auth/Theme), potentially Zustand or Redux Toolkit for complex state.
        *   Animations: `react-native-reanimated` (v2/v3), `Moti`
        *   UI Components: Custom-built or minimally adapted from libraries (e.g., `react-native-paper`) for full theme control.
        *   Local Storage: `Expo SecureStore` (for tokens), AsyncStorage (for preferences).
    *   **Backend (Cloud Infrastructure):**
        *   Provider: Amazon Web Services (AWS)
        *   API Layer: AWS API Gateway (RESTful endpoints)
        *   Compute: AWS Lambda (Node.js or Python runtime) - Serverless functions for processing, LLM interaction, DB operations.
        *   Database: AWS DynamoDB (NoSQL) - Scalable storage for user logs, potentially user profiles.
        *   Authentication: AWS Cognito - User Pool for sign-up/sign-in, identity management.
        *   (Potentially): AWS S3 for storing user-uploaded assets (e.g., profile pictures, progress photos - future).
    *   **Infrastructure as Code (IaC):**
        *   Tool: AWS Cloud Development Kit (CDK) - TypeScript for defining and provisioning AWS resources.
    *   **External Services:**
        *   LLM Provider: OpenAI API (GPT-3.5/4) or Anthropic API (Claude) - Accessed securely via backend Lambda.
*   **Design Aesthetics:**
    *   **Color Palette:** Primary: `#FFFFFF` (White) / `#121212` (Near Black - Dark Mode); Accent: `#75ACCA` (Light Blue) and variations (e.g., `#ADD8E6, #87CEEB`, `#F0F8FF`); Neutrals: Subtle Greys (`#F5F5F5`, `#E0E0E0` / `#282828`, `#3a3a3a`).
    *   **Animations:** Fluid, purposeful, and non-intrusive (using Reanimated/Moti). Focus on smooth transitions, subtle micro-interactions on focus/press, and clear loading/feedback states.

# II. Project Setup: Directory Structure

*   **Rationale:** A monorepo-like structure is chosen for the MVP to streamline development and dependency management while maintaining logical separation between the application code and the infrastructure code. This facilitates easier cross-referencing during initial development.
*   **Structure:**
    ```
    Lotus/
    ├── app/                 # React Native (Expo) Application Code
    │   ├── assets/
    │   ├── components/
    │   ├── navigation/
    │   ├── screens/
    │   ├── services/        # API interaction logic
    │   ├── state/           # Global state management
    │   ├── utils/
    │   ├── App.js           # Entry point for React Native
    │   ├── app.json         # Expo configuration
    │   ├── babel.config.js
    │   └── package.json     # Frontend dependencies
    │
    ├── infrastructure/      # AWS CDK Infrastructure Code
    │   ├── bin/             # Entry point script for CDK app
    │   ├── lib/             # CDK stack definitions (e.g., ApiStack, DbStack, AuthStack)
    │   ├── test/            # Unit/snapshot tests for infrastructure
    │   ├── cdk.json         # CDK toolkit configuration
    │   ├── package.json     # CDK dependencies (AWS CDK libraries, constructs)
    │   └── tsconfig.json
    │
    └── README.md            # Project overview
    ```
*   **Interaction:** The `infrastructure/` project defines AWS resources (API Gateway endpoints, Lambda ARNs, DynamoDB table names, Cognito Pool ID/Client ID). These identifiers (often exposed as CDK Stack Outputs) are then configured within the `app/` project (e.g., in environment variables or a config file) so the React Native app knows which backend endpoints to call. The CI/CD pipeline will deploy the infrastructure first, then build and deploy the application code (Lambda function code bundles, potentially Expo app updates).

# III. Detailed User Flow & Technical Implementation

## 1. App Launch & Splash Screen

*   **Purpose:** Provides initial branding, handles asynchronous loading tasks (fonts, assets), and crucially determines the user's authentication state to direct them appropriately.
*   **User Perspective:** The user sees a clean, quick loading screen with the app's logo, providing immediate brand recognition and visual feedback that the app is starting. It's designed to be fast and seamless, leading directly to either the login page or their main dashboard without requiring user action.
*   **UI:** Minimalist. Centered Lotus logo (monochromatic or subtly using light blue). Potentially a very soft, almost imperceptible gradient background (white to slightly off-white/blue-tinted). No interactive elements.
*   **Technical Flow:**
    1.  **React Native Bootstrap:** The native iOS/Android shell launches the JavaScript environment. Expo initializes.
    2.  **Component Mount:** The `SplashScreen` component renders. Any custom fonts are loaded asynchronously using `expo-font`.
    3.  **Logo Animation:** A `Moti` or `Reanimated` animation (e.g., `from={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} transition={{ type: 'timing', duration: 600 }}`) fades in and slightly scales the logo.
    4.  **Auth Check (`useEffect`):**
        *   Attempt to read JWT token (`accessToken` or `idToken`) from `Expo SecureStore`.
        *   **If Token Exists:** Optionally perform a quick validation call (e.g., Cognito `getUser` or a lightweight protected Lambda endpoint) to ensure it's not expired/revoked.
        *   **Navigation Decision:** Based on token presence and validity:
            *   Valid Token -> Navigate to `MainApp` stack (default: `Dashboard` screen).
            *   No/Invalid Token -> Navigate to `Auth` stack (default: `SignIn` screen).
    5.  **Navigation Execution:** `navigation.reset()` method from React Navigation is used to replace the splash screen in the navigation stack, preventing users from navigating back to it.
    6.  **Screen Transition:** A simple cross-fade animation (`cardStyleInterpolator` in React Navigation stack options) manages the visual transition from Splash to the target screen (Auth or MainApp). Duration ~300ms.
*   **Animations:** Logo fade-in/scale, Screen cross-fade transition.

## 2. Authentication Screens (Auth Stack)

*   **Purpose:** To securely manage user identification, allowing new users to register and existing users to log in, thereby gaining access to their personalized data. Also provides a mechanism for password recovery.
*   **User Perspective:** Presents standard, easy-to-understand forms for entering credentials. Clear visual cues (like highlighted input borders) and feedback (loading states, error messages) guide the user through the sign-in or sign-up process. Offers a way to regain access if they forget their password.
*   **UI (General):** Consistent layout across screens. White/black primary background. Black/white text. Input fields (`TextInput`) with padding, rounded corners, and a subtle border (`#E0E0E0` / `#3a3a3a`). On focus, the border animates to the light blue color (`#ADD8E6`) and slightly thicker stroke width. Buttons utilize solid light blue or bordered light blue for primary/secondary actions. Modern sans-serif font (e.g., Inter). Error messages appear below inputs in a distinct color (subtle red, or just primary text color with an icon).
*   **a. Sign In Screen (`SignIn`)**
    *   **Purpose:** Authenticates existing users using their registered credentials.
    *   **User Perspective:** The entry point for returning users. They enter their email and password to access their logs and profile. Links provide options for password reset or creating a new account if needed.
    *   **UI Elements:** Lotus Logo (smaller), "Welcome Back" header, Email `TextInput`, Password `TextInput` (with secureTextEntry toggle icon), "Forgot Password?" `TouchableOpacity` Text (light blue), Primary "Sign In" `TouchableOpacity` Button (light blue background), Secondary "Don't have an account? Sign Up" `TouchableOpacity` Text (light blue).
    *   **Technical Flow:**
        1.  Local state (`useState`) manages email/password input values.
        2.  Client-side validation (using regex for email format, check for non-empty) triggers on blur or submit. Validation errors update state, displaying messages conditionally.
        3.  On "Sign In" press:
            *   Set loading state (`useState`): `isLoading = true`. UI updates button appearance (disabled, text change, embedded `ActivityIndicator` colored light blue).
            *   Call an async function (`signInUser`) within `services/auth.js`.
            *   `signInUser` makes a POST request (using `axios` or `fetch`) to the specific API Gateway endpoint mapped to the Cognito Sign-In Lambda. Body: `{ email, password }`.
            *   **Lambda (Sign-In Function):**
                *   Receives email/password.
                *   Uses AWS SDK (`@aws-sdk/client-cognito-identity-provider`) to call `initiateAuth` on the Cognito User Pool (`AuthFlow: 'USER_PASSWORD_AUTH'`, `ClientId`, `AuthParameters: { USERNAME: email, PASSWORD: password }`).
                *   **On Cognito Success:** Cognito returns `AuthenticationResult` containing `IdToken`, `AccessToken`, `RefreshToken`. Lambda forwards these tokens in the success response (e.g., `statusCode: 200, body: JSON.stringify({ idToken, accessToken, refreshToken })`).
                *   **On Cognito Failure:** Cognito throws errors (e.g., `UserNotFoundException`, `NotAuthorizedException`). Lambda catches these, logs details, and returns a specific error response (e.g., `statusCode: 401/404, body: JSON.stringify({ message: 'Invalid credentials' })`).
            *   **App Response (Frontend):**
                *   Await the API call response.
                *   **On Success (2xx):** Parse tokens. Securely store them using `Expo SecureStore`. Update global auth state (Context API `dispatch({ type: 'SIGN_IN', payload: { tokens, userInfo } })`). Navigate to `MainApp` stack using `navigation.reset()`. Clear loading state.
                *   **On Failure (4xx/5xx):** Parse error message. Display user-friendly message using a Snackbar/Toast component (animates sliding in/out). Clear loading state.
    *   **Animations:** Input focus border color/width transition. Button press scale effect (`Pressable`). Loading indicator spin. Snackbar/Toast slide-in/out animation. Screen slide transition (React Navigation).
*   **b. Sign Up Screen (`SignUp`)**
    *   **Purpose:** Enables new users to create an account within the application.
    *   **User Perspective:** Allows first-time users to join Lotus by providing basic information (name, email, password). Provides clear password requirements and confirmation.
    *   **UI Elements:** Similar layout. Fields for Name, Email, Password, Confirm Password. Password strength indicator (optional visual bar). "Sign Up" button. "Already have an account? Sign In" link.
    *   **Technical Flow:**
        1.  Local state manages input values.
        2.  Client-side validation: includes password match check, minimum length, potentially regex for complexity requirements.
        3.  On "Sign Up" press:
            *   Set loading state.
            *   Call `signUpUser` service function.
            *   POST request to Sign-Up Lambda endpoint. Body: `{ name, email, password }`.
            *   **Lambda (Sign-Up Function):**
                *   Uses AWS SDK to call `signUp` on Cognito User Pool (`ClientId`, `Username: email`, `Password: password`, `UserAttributes: [{ Name: 'name', Value: name }]`).
                *   **On Cognito Success:** Typically requires email verification. Lambda returns success (`statusCode: 200, body: JSON.stringify({ message: 'User created. Check email for verification code.' })`). Could optionally trigger an auto-confirmation flow if desired for MVP simplicity (`adminConfirmSignUp`).
                *   **On Cognito Failure:** Handles errors like `UsernameExistsException`, `InvalidPasswordException`. Returns error response.
            *   **App Response:**
                *   **On Success:** Clear loading state. Display success message (e.g., via Snackbar or modal). Navigate user to `SignIn` screen or potentially a `VerifyEmail` screen (if implementing verification code step).
                *   **On Failure:** Display specific error message. Clear loading state.
    *   **Animations:** Same as Sign In. Potential animation for password strength indicator.
*   **c. Forgot Password Screen (`ForgotPassword`)**
    *   **Purpose:** Provides a self-service mechanism for users to initiate a password reset process if they've forgotten their password.
    *   **User Perspective:** A straightforward way to recover account access by entering their registered email address. They understand they will receive instructions via email.
    *   **UI Elements:** Email `TextInput`, "Send Reset Link" button. Back navigation integrated into the header (React Navigation default).
    *   **Technical Flow:**
        1.  Local state for email. Client-side email format validation.
        2.  On "Send Reset Link" press:
            *   Set loading state.
            *   Call `forgotPassword` service function.
            *   POST to Forgot Password Lambda endpoint. Body: `{ email }`.
            *   **Lambda (Forgot Password Function):**
                *   Uses AWS SDK to call `forgotPassword` on Cognito User Pool (`ClientId`, `Username: email`).
                *   Lambda always returns a generic success response (`statusCode: 200`) regardless of whether the email exists, as per security best practices (prevents user enumeration). Logs actual success/failure internally.
            *   **App Response:**
                *   Clear loading state. Display a consistent confirmation message ("If an account exists for this email, a password reset link has been sent.") via Snackbar. Optionally navigate back to `SignIn`.
    *   **Animations:** Same as Sign In/Up.

## 3. Main Application Interface (MainApp Stack - Bottom Tab Navigator)

*   **Purpose:** Provides the core authenticated experience, allowing users to interact with Lotus AI for recommendations and logging, track and visualize strength progress, monitor diet and body composition, and manage their profile settings. Organized via tabs for clear functional separation based on the core pillars: AI Interaction, Strength, Diet, and Profile.
*   **User Perspective:** The central hub of the app after logging in. Users can easily switch between AI conversation and logging (Lotus), strength tracking and insights (Strength), diet monitoring and planning (Diet), and profile management (Profile) using the intuitive bottom tab bar.
*   **Structure:** `createBottomTabNavigator` (React Navigation). Tabs: `Lotus`, `Strength`, `Diet`, `Profile`. Active tab icon and label use the light blue accent (`#87CEEB` or similar). Subtle scale/opacity animation on tab switch.
*   **a. Lotus Screen (Main AI Interaction Tab)**
    *   **Purpose:** The primary interface for interacting with Lotus AI. Provides personalized workout and meal recommendations based on remembered history, answers fitness questions, and enables automatic logging via conversation. Acts as the starting point for initiating workouts.
    *   **User Perspective:** A conversational interface where users can naturally interact with an AI assistant that remembers their workout history (progression, frequency), dietary preferences/habits, and progress over time. The AI provides personalized suggestions (e.g., suggesting push day after rest) and adapts based on user responses (e.g., switching to pull). Users can log workouts/meals or start a planned workout directly from the chat.
    *   **UI:**
        *   **Header:** App name or subtle title ("Lotus AI" or just "Lotus").
        *   **Chat History:** `FlatList` displaying the ongoing conversation (user vs. AI). AI messages styled distinctly (light blue bubbles). AI responses include suggestions, personalized workout advice (e.g., suggesting rep targets based on history), and confirmations.
        *   **Context Awareness Banner:** Optional banner showing key remembered details (e.g., "Last workout: Push - Yesterday", "Calories left: 850").
        *   **Input Area:** Text input field ("Talk to Lotus AI...") and send button. Quick-action buttons like "Start Workout" may appear contextually or persistently.
    *   **Technical Flow:**
        1.  Initialize chat with a context-aware greeting/suggestion from Lotus AI (e.g., "Good Evening! Today you should hit push...").
        2.  **Message Processing:**
            *   User message sent to Lambda.
            *   **Lambda (AI Conversation):** Retrieves user context (workout/meal history, strength profile, goals) from DynamoDB. Constructs detailed LLM prompt including history and user message. Calls LLM API. Processes LLM response (parsing structured data for logging/recommendations, generating conversational text). Updates knowledge base based on interaction (e.g., logging time taken to increase reps).
            *   Returns formatted response.
        3.  **App Response:** Displays AI message. If loggable data detected, prompts user for confirmation ("Log this workout/meal?").
        4.  **Automatic Logging:** On confirmation, sends structured data to logging Lambda -> DynamoDB. Updates chat ("Logged: ...").
        5.  **"Start Workout" Flow:**
            *   User taps "Start Workout" button (prompted by AI or user initiative).
            *   Navigates to a dedicated active workout tracking interface (potentially a new screen pushed onto the stack or presented modally), pre-populated with the suggested/selected workout plan. This interface resembles the description in the *original* 'Workout Screen (Active Workout Tab)' section (list of exercises, set tracking, completion).
    *   **Animations:** Message arrival, AI typing indicator, smooth scrolling, transitions to workout tracking interface.
*   **b. Strength Screen**
    *   **Purpose:** To provide insights on strength progression over time, visualize performance trends for specific exercises, and display the user's overall strength profile developed by the AI. Helps users understand their strength gains and plan future workouts.
    *   **User Perspective:** Users come here to see how their strength is improving. They can select specific exercises and view charts of weight/reps over time, see their calculated personal records (PRs), and understand their 'strength profile' based on historical performance and progression speed. The AI uses this data to suggest target weights for upcoming sessions.
    *   **UI:**
        *   **Header:** "Strength".
        *   **Selectors:** Dropdown/selector to choose specific exercises or muscle groups. Time range selector (1m, 3m, 6m, All).
        *   **Chart Area:** `react-native-svg-charts` displaying line charts for weight/rep progression for the selected exercise over the chosen time range. Uses light blue for data visualization. Axes and tooltips included.
        *   **PR Section:** Card displaying key PRs for selected/major exercises (e.g., "Max Bench Press: 160 lbs - achieved 2 weeks ago").
        *   **Strength Profile Summary:** Textual or graphical summary generated by the AI, highlighting progression rates, potential plateaus, and maybe comparing different muscle groups (e.g., "Upper body strength increasing 10% faster than lower body over last 3 months"). Suggestion area for next workout targets (e.g., "Aim for 155 lbs on Bench Press next session").
        *   **Loading/Empty State:** Skeleton loaders or message ("Log workouts to see strength progress.").
    *   **Technical Flow:**
        1.  User selections (exercise, time range) update state, triggering data fetch.
        2.  Call `getStrengthProgress` service function. GET request to `/strength-progress` endpoint (or `/progress?category=strength`).
        3.  **Lambda (Get Strength Data):** Authenticates user. Queries DynamoDB for workout logs, filtering by exercise and time range (potentially using GSIs). Processes data: calculates progression trends, identifies PRs, runs the strength profile algorithm (analyzing time to increase weight/reps).
        4.  Returns structured data for charts, PRs, and the strength profile summary/suggestions.
        5.  **App Response:** Store data in state. Pass data to chart components and display PRs/profile insights. Handle loading/empty states.
    *   **Animations:** Chart data points/lines animate on load/update. Smooth transitions if changing exercise selection.
*   **c. Diet Screen**
    *   **Purpose:** To track nutritional intake against goals, monitor body composition trends (weight, estimated BF%), view calculated energy expenditure, and receive meal suggestions based on remaining macros/calories.
    *   **User Perspective:** This is the hub for nutrition and body tracking. Users can log food (potentially via the Lotus tab, reflected here), see their daily progress towards macro/calorie goals (like Apple Watch rings), track their weight and estimated body fat percentage over time, understand their calculated maintenance calories (expenditure), and get suggestions for meals fitting their remaining daily budget.
    *   **UI:**
        *   **Header:** "Diet". Date selector (view today or past days).
        *   **Macro/Calorie Goals:** Prominent circular progress indicators (similar to Apple Watch rings) for Calories, Protein, Fat, and Carbs, showing percentage completion for the selected day.
        *   **Trend Charts:**
            *   Line chart showing weight trend over time. Optional overlay for estimated Body Fat % trend (calculated using weight, height, potentially progress pictures if implemented).
            *   Bar/Line chart showing daily calorie intake vs. calculated expenditure trend over time.
        *   **Key Metrics Display:** Cards showing current logged weight, estimated BF%, calculated maintenance calories (Expenditure Trend).
        *   **Meal Suggestion Area:** Section suggesting meals based on time of day and remaining macros/calories (e.g., "Snack idea: Greek yogurt with berries - ~15g Protein, 150 Cals").
        *   **Logging Buttons:** Quick buttons like "Log Weight", "Log Food" (could navigate to Lotus tab or a dedicated logging interface).
    *   **Technical Flow:**
        1.  Fetch data for the selected date/range on screen focus or date change. Call `getDietProgress` service function. GET request to `/diet-progress` (or `/progress?category=diet`).
        2.  **Lambda (Get Diet Data):** Authenticates user. Queries DynamoDB for meal logs, weight logs for the relevant period. Retrieves user goals (macros/calories) and profile info (height). Calculates daily totals. Runs expenditure algorithm (comparing weight changes to intake over time). Runs BF% estimation algorithm (using weight, height, potentially picture data - future). Generates meal suggestions based on remaining macros and time.
        3.  Returns structured data for rings, charts, metrics, and suggestions.
        4.  **App Response:** Update state. Render progress rings, charts, display metrics, show meal suggestions. Handle loading/empty states. Body Fat % estimation requires user opt-in or falls back to simple weight tracking.
    *   **Animations:** Progress ring fill animations. Chart data animations. Smooth transitions between days.
*   **d. Profile Screen (Profile Tab)**
    *   **Purpose:** Allows users to manage their account settings, set/update fitness and diet goals, input body measurements, customize app preferences (units), and access administrative functions like logout.
    *   **User Perspective:** The place to customize their Lotus experience, set calorie/macro targets, input weight/height for calculations, change units (lbs/kg), manage their account, and find app info.
    *   **UI:** Standard settings list view (`ScrollView` or `SectionList`).
        *   **Header:** User Name/Email. Placeholder for profile pic.
        *   **Goal Setting Section:** Inputs/sliders for Calorie, Protein, Fat, Carb targets. Weight goal input.
        *   **Measurement Section:** Input fields for Height, Current Weight. Display area for estimated BF% (if enabled). Last updated indicators. Option to opt-in/out of BF% estimation.
        *   **Preferences Section:** `Units` toggle (lbs/kg). Theme selector (if implemented). Notification preferences.
        *   **Account Section:** `Change Password`, `Logout` button. Links to Privacy Policy, Terms.
    *   **Technical Flow:**
        1.  Load user profile/settings data from global state/API on screen focus.
        2.  **Goal/Measurement/Preference Updates:** Changes update local/global state. On save/blur, call service function to send updates to Lambda -> DynamoDB (for goals, measurements) or save locally via AsyncStorage (for preferences like units). Ensure consistency across the app.
        3.  **Account Actions:** Standard flows for password change (via Cognito) and logout (clear tokens, clear state, navigate to Auth).
    *   **Animations:** Standard screen transitions. Subtle feedback on input changes. Animated switch/toggle components. Modal animations for confirmations.

## 4. Special Features & Intelligence Layer

*   **Lotus AI Memory & Personalization**
    *   **Purpose:** Creates a truly personalized fitness assistant by maintaining comprehensive knowledge about the user's fitness journey, preferences, and patterns.
    *   **Technical Implementation:**
        *   **User Timeline Database:** DynamoDB table tracking chronological workout and nutrition events.
        *   **Strength Profile Module:** Algorithm analyzing exercise progression rates to determine strengths/weaknesses.
        *   **Dietary Pattern Recognition:** System tracking meal frequency, composition, and timing to identify patterns.
        *   **LLM Prompt Engineering:** Carefully crafted system prompts that include relevant historical context from the database within token limits.
        *   **Response Parsing:** Backend logic to extract structured data from natural language responses for automatic logging.
    *   **Key Capabilities:**
        *   Remembers specific exercise weights, reps, and progression rates.
        *   Recalls dietary preferences and reaction to previous nutrition recommendations.
        *   Understands workout split schedule and recovery patterns.
        *   Identifies when user is stuck on specific exercises and suggests techniques to break plateaus.
        *   Adapts recommendations based on previous feedback and results.

*   **Intelligent Calorie & Expenditure Tracking**
    *   **Purpose:** Provides accurate nutritional guidance based on actual metabolic response rather than generic formulas.
    *   **Technical Implementation:**
        *   **Reverse TDEE Algorithm:** Backend calculation comparing actual weight changes to caloric intake over time.
        *   **Adaptive Recommendation System:** Adjusts calorie and macro targets based on progress toward goals.
        *   **Meal Recognition Model:** LLM-powered system to estimate macros and calories from natural language meal descriptions.
    *   **Key Capabilities:**
        *   Calculates true maintenance calories based on weight response to diet.
        *   Suggests meal options based on remaining macro budget at different times of day.
        *   Provides increasingly accurate estimates as more data is collected.
        *   Allows natural language food logging with automatic macro calculation.

*   **Strength Analytics & Workout Intelligence**
    *   **Purpose:** Delivers data-driven insights about strength progression and optimizes workout recommendations.
    *   **Technical Implementation:**
        *   **Exercise Progression Tracking:** System monitoring weight/rep increases across time periods.
        *   **Recovery Pattern Analysis:** Algorithm estimating optimal training frequency based on performance data.
        *   **PR Detection & Celebration:** Automatic identification of personal records with motivational feedback.
    *   **Key Capabilities:**
        *   Suggests appropriate weight increases based on previous performance.
        *   Recommends optimal rest periods between training specific muscle groups.
        *   Identifies plateaus and suggests programming changes.
        *   Celebrates milestones and PRs with motivational feedback.