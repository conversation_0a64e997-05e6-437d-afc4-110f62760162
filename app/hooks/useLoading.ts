import { useState, useCallback } from 'react';

interface UseLoadingOptions {
  initialState?: boolean;
  minLoadingTime?: number;
}

export function useLoading({ initialState = false, minLoadingTime = 500 }: UseLoadingOptions = {}) {
  const [isLoading, setIsLoading] = useState(initialState);
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(undefined);
  
  const startLoading = useCallback((message?: string) => {
    setIsLoading(true);
    if (message) {
      setLoadingMessage(message);
    }
  }, []);
  
  const stopLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingMessage(undefined);
  }, []);
  
  const withLoading = useCallback(async <T>(
    fn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    startLoading(message);
    const startTime = Date.now();
    
    try {
      const result = await fn();
      
      // Ensure minimum loading time for better UX
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
      }
      
      return result;
    } finally {
      stopLoading();
    }
  }, [startLoading, stopLoading, minLoadingTime]);
  
  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    withLoading
  };
}

export default useLoading;
