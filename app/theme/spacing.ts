/**
 * Standardized spacing system for consistent layouts
 * Based on 8px grid system for better design consistency
 */

export const spacing = {
  // Base unit (8px)
  unit: 8,
  
  // Micro spacing (for fine adjustments)
  xs: 4,   // 0.5 units
  sm: 8,   // 1 unit
  md: 16,  // 2 units
  lg: 24,  // 3 units
  xl: 32,  // 4 units
  xxl: 48, // 6 units
  xxxl: 64, // 8 units
  
  // Semantic spacing
  padding: {
    screen: 20,        // Standard screen padding
    container: 16,     // Container padding
    card: 16,          // Card internal padding
    section: 24,       // Section spacing
    component: 12,     // Component internal spacing
    element: 8,        // Element spacing
    tight: 4,          // Tight spacing
  },
  
  margin: {
    screen: 20,        // Screen margins
    section: 24,       // Between sections
    component: 16,     // Between components
    element: 8,        // Between elements
    tight: 4,          // Tight margins
  },
  
  // Layout spacing
  layout: {
    headerHeight: 60,
    tabBarHeight: 50,
    buttonHeight: 48,
    inputHeight: 48,
    cardRadius: 12,
    buttonRadius: 24,
    inputRadius: 8,
  },
  
  // Responsive breakpoints
  breakpoints: {
    small: 320,
    medium: 768,
    large: 1024,
  },
} as const;

/**
 * Helper functions for responsive spacing
 */
export const getResponsiveSpacing = (
  small: keyof typeof spacing,
  medium?: keyof typeof spacing,
  large?: keyof typeof spacing
) => {
  return {
    small: spacing[small],
    medium: medium ? spacing[medium] : spacing[small],
    large: large ? spacing[large] : medium ? spacing[medium] : spacing[small],
  };
};

/**
 * Common spacing patterns
 */
export const spacingPatterns = {
  // Screen layouts
  screenPadding: {
    paddingHorizontal: spacing.padding.screen,
    paddingVertical: spacing.padding.screen,
  },
  
  screenPaddingHorizontal: {
    paddingHorizontal: spacing.padding.screen,
  },
  
  screenPaddingVertical: {
    paddingVertical: spacing.padding.screen,
  },
  
  // Container layouts
  containerPadding: {
    padding: spacing.padding.container,
  },
  
  containerMargin: {
    margin: spacing.margin.component,
  },
  
  // Card layouts
  cardPadding: {
    padding: spacing.padding.card,
  },
  
  cardMargin: {
    marginBottom: spacing.margin.component,
  },
  
  // Section layouts
  sectionSpacing: {
    marginBottom: spacing.margin.section,
  },
  
  sectionPadding: {
    paddingVertical: spacing.padding.section,
  },
  
  // Component layouts
  componentSpacing: {
    marginBottom: spacing.margin.component,
  },
  
  componentPadding: {
    padding: spacing.padding.component,
  },
  
  // Element layouts
  elementSpacing: {
    marginBottom: spacing.margin.element,
  },
  
  elementPadding: {
    padding: spacing.padding.element,
  },
  
  // Button layouts
  buttonSpacing: {
    marginVertical: spacing.margin.element,
  },
  
  // Input layouts
  inputSpacing: {
    marginBottom: spacing.margin.component,
  },
} as const;

export default spacing;
