import React, { createContext, useContext, useState, useEffect } from 'react';
import { Animated, Easing } from 'react-native';
import { ThemeProvider, useTheme } from './ThemeProvider';
import { getColors } from './colors';

interface AnimatedThemeContextType {
  animatedColors: {
    background: Animated.AnimatedInterpolation<string>;
    text: Animated.AnimatedInterpolation<string>;
    textSecondary: Animated.AnimatedInterpolation<string>;
    textTertiary: Animated.AnimatedInterpolation<string>;
    primary: Animated.AnimatedInterpolation<string>;
    primaryLight: Animated.AnimatedInterpolation<string>;
    secondary: Animated.AnimatedInterpolation<string>;
    card: Animated.AnimatedInterpolation<string>;
    border: Animated.AnimatedInterpolation<string>;
    shadow: Animated.AnimatedInterpolation<string>;
    error: Animated.AnimatedInterpolation<string>;
    success: Animated.AnimatedInterpolation<string>;
    warning: Animated.AnimatedInterpolation<string>;
  };
  transitionProgress: Animated.Value;
  colors: ReturnType<typeof getColors>;
}

const AnimatedThemeContext = createContext<AnimatedThemeContextType | null>(null);

export const useAnimatedTheme = () => {
  const context = useContext(AnimatedThemeContext);
  if (!context) {
    throw new Error('useAnimatedTheme must be used within an AnimatedThemeProvider');
  }
  return context;
};

export const AnimatedThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isDark, colors } = useTheme();
  const [transitionProgress] = useState(new Animated.Value(isDark ? 1 : 0));

  // Update the animation value when the theme changes
  useEffect(() => {
    Animated.timing(transitionProgress, {
      toValue: isDark ? 1 : 0,
      duration: 600, // Longer, smoother transition duration
      easing: Easing.inOut(Easing.cubic), // Smooth cubic easing
      useNativeDriver: false,
    }).start();
  }, [isDark, transitionProgress]);

  // Get light and dark colors
  const lightColors = getColors(false);
  const darkColors = getColors(true);

  // Create animated interpolations for each color
  const animatedColors = {
    background: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.background, darkColors.background],
    }),
    text: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.text, darkColors.text],
    }),
    textSecondary: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.textSecondary, darkColors.textSecondary],
    }),
    textTertiary: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.textTertiary, darkColors.textTertiary],
    }),
    primary: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.primary, darkColors.primary],
    }),
    primaryLight: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.primaryLight, darkColors.primaryLight],
    }),
    secondary: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.accent, darkColors.accent],
    }),
    card: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.card, darkColors.card],
    }),
    border: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.border, darkColors.border],
    }),
    shadow: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.shadow, darkColors.shadow],
    }),
    error: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.error, darkColors.error],
    }),
    success: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.success, darkColors.success],
    }),
    warning: transitionProgress.interpolate({
      inputRange: [0, 1],
      outputRange: [lightColors.warning, darkColors.warning],
    }),
  };

  return (
    <AnimatedThemeContext.Provider value={{ animatedColors, transitionProgress, colors }}>
      {children}
    </AnimatedThemeContext.Provider>
  );
};

// Wrapper component that combines ThemeProvider and AnimatedThemeProvider
export const AnimatedThemeProviderWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider>
      <AnimatedThemeProvider>{children}</AnimatedThemeProvider>
    </ThemeProvider>
  );
};
