import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getColors } from './colors';

type Theme = 'dark' | 'light' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  isDark: boolean;
  colors: ReturnType<typeof getColors>;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'dark',
  setTheme: () => {},
  isDark: true,
  colors: getColors(true),
});

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const systemColorScheme = useColorScheme();
  const [theme, setTheme] = useState<Theme>('dark');

  useEffect(() => {
    // Load saved theme preference
    AsyncStorage.getItem('@theme_preference').then((savedTheme: string | null) => {
      if (savedTheme) {
        setTheme(savedTheme as Theme);
      }
    });
  }, []);

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme);
    // Use promise-based approach instead of async/await
    AsyncStorage.setItem('@theme_preference', newTheme)
      .catch(error => console.error('Error saving theme preference:', error));
  };

  const isDark = theme === 'system' ? systemColorScheme === 'dark' : theme === 'dark';
  const colors = getColors(isDark);

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleThemeChange, isDark, colors }}>
      {children}
    </ThemeContext.Provider>
  );
}