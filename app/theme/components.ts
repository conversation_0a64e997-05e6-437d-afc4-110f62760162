/**
 * Standardized component styles for consistent UI
 */

import { StyleSheet, Platform } from 'react-native';
import { spacing } from './spacing';
import { typography } from './typography';

/**
 * Common component style patterns
 */
export const componentStyles = {
  // Container styles
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Will be overridden by theme
  },
  
  screenContainer: {
    flex: 1,
    ...spacing.spacingPatterns.screenPadding,
  },
  
  contentContainer: {
    flex: 1,
    ...spacing.spacingPatterns.containerPadding,
  },
  
  // Card styles
  card: {
    backgroundColor: 'transparent', // Will be overridden by theme
    borderRadius: spacing.layout.cardRadius,
    ...spacing.spacingPatterns.cardPadding,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  
  cardElevated: {
    backgroundColor: 'transparent', // Will be overridden by theme
    borderRadius: spacing.layout.cardRadius,
    ...spacing.spacingPatterns.cardPadding,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  
  // Button styles
  button: {
    height: spacing.layout.buttonHeight,
    borderRadius: spacing.layout.buttonRadius,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.padding.component,
    ...spacing.spacingPatterns.buttonSpacing,
  },
  
  buttonPrimary: {
    height: spacing.layout.buttonHeight,
    borderRadius: spacing.layout.buttonRadius,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.padding.component,
    ...spacing.spacingPatterns.buttonSpacing,
  },
  
  buttonSecondary: {
    height: spacing.layout.buttonHeight,
    borderRadius: spacing.layout.buttonRadius,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.padding.component,
    borderWidth: 1,
    backgroundColor: 'transparent',
    ...spacing.spacingPatterns.buttonSpacing,
  },
  
  buttonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
  },
  
  // Input styles
  input: {
    height: spacing.layout.inputHeight,
    borderRadius: spacing.layout.inputRadius,
    paddingHorizontal: spacing.padding.component,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    borderWidth: 1,
    ...spacing.spacingPatterns.inputSpacing,
  },
  
  inputMultiline: {
    minHeight: spacing.layout.inputHeight * 2,
    borderRadius: spacing.layout.inputRadius,
    paddingHorizontal: spacing.padding.component,
    paddingVertical: spacing.padding.element,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    borderWidth: 1,
    textAlignVertical: 'top',
    ...spacing.spacingPatterns.inputSpacing,
  },
  
  // Text styles
  heading1: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamily.bold,
    lineHeight: typography.lineHeights.tight,
    ...spacing.spacingPatterns.elementSpacing,
  },
  
  heading2: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    lineHeight: typography.lineHeights.tight,
    ...spacing.spacingPatterns.elementSpacing,
  },
  
  heading3: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    lineHeight: typography.lineHeights.normal,
    ...spacing.spacingPatterns.elementSpacing,
  },
  
  bodyText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    lineHeight: typography.lineHeights.relaxed,
    ...spacing.spacingPatterns.elementSpacing,
  },
  
  caption: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: typography.lineHeights.normal,
  },
  
  // Modal styles
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  
  modalContent: {
    backgroundColor: 'transparent', // Will be overridden by theme
    borderRadius: spacing.layout.cardRadius,
    padding: spacing.padding.section,
    margin: spacing.margin.screen,
    maxHeight: '80%',
    width: '90%',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
      },
      android: {
        elevation: 10,
      },
    }),
  },
  
  // List styles
  listContainer: {
    flex: 1,
  },
  
  listItem: {
    paddingVertical: spacing.padding.component,
    paddingHorizontal: spacing.padding.container,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.padding.section,
  },
  
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: spacing.margin.element,
    textAlign: 'center',
  },
  
  // Error styles
  errorContainer: {
    backgroundColor: 'transparent', // Will be overridden by theme
    borderRadius: spacing.layout.inputRadius,
    padding: spacing.padding.component,
    margin: spacing.margin.element,
    borderWidth: 1,
  },
  
  errorText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
  
  // Success styles
  successContainer: {
    backgroundColor: 'transparent', // Will be overridden by theme
    borderRadius: spacing.layout.inputRadius,
    padding: spacing.padding.component,
    margin: spacing.margin.element,
    borderWidth: 1,
  },
  
  successText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
} as const;

/**
 * Helper function to create themed component styles
 */
export const createThemedStyles = (colors: any) => ({
  ...componentStyles,
  
  // Override background colors with theme colors
  container: {
    ...componentStyles.container,
    backgroundColor: colors.background,
  },
  
  card: {
    ...componentStyles.card,
    backgroundColor: colors.card,
  },
  
  cardElevated: {
    ...componentStyles.cardElevated,
    backgroundColor: colors.card,
  },
  
  buttonPrimary: {
    ...componentStyles.buttonPrimary,
    backgroundColor: colors.primary,
  },
  
  buttonSecondary: {
    ...componentStyles.buttonSecondary,
    borderColor: colors.border,
  },
  
  input: {
    ...componentStyles.input,
    backgroundColor: colors.card,
    borderColor: colors.border,
    color: colors.text,
  },
  
  inputMultiline: {
    ...componentStyles.inputMultiline,
    backgroundColor: colors.card,
    borderColor: colors.border,
    color: colors.text,
  },
  
  modalContent: {
    ...componentStyles.modalContent,
    backgroundColor: colors.background,
  },
  
  listItem: {
    ...componentStyles.listItem,
    borderBottomColor: colors.border,
  },
  
  errorContainer: {
    ...componentStyles.errorContainer,
    backgroundColor: colors.error + '20',
    borderColor: colors.error,
  },
  
  successContainer: {
    ...componentStyles.successContainer,
    backgroundColor: colors.success + '20',
    borderColor: colors.success,
  },
});

export default componentStyles;
