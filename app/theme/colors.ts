const lightColors = {
  // Primary colors
  primary: '#ADD8E6', // Light blue
  primaryDark: '#87CEEB',
  primaryLight: '#F0F8FF',

  // Background colors
  background: '#FFFFFF',
  surface: '#F5F5F5',
  surfaceLight: '#FAFAFA',
  surfaceDark: '#E0E0E0',

  // Text colors
  text: '#121212',
  textSecondary: '#666666',
  textTertiary: '#999999',

  // Accent colors
  accent: '#ADD8E6',
  accentDark: '#87CEEB',
  accentLight: '#F0F8FF',

  // Status colors
  success: '#34C759',
  error: '#FF3B30',
  warning: '#FF9500',
  info: '#007AFF',

  // Border colors
  border: '#E0E0E0',
  borderLight: '#3A3A3A',
  borderDark: '#1A1A1A',

  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',

  card: '#FFFFFF',
  shadow: '#000000',
} as const;

const darkColors = {
  // Primary colors
  primary: '#87CEEB', // Slightly darker blue for dark mode
  primaryDark: '#5F9EA0',
  primaryLight: '#1A1A1A',

  // Background colors
  background: '#121212',
  surface: '#1E1E1E',
  surfaceLight: '#2A2A2A',
  surfaceDark: '#000000',

  // Text colors
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  textTertiary: '#808080',

  // Accent colors
  accent: '#87CEEB',
  accentDark: '#5F9EA0',
  accentLight: '#1A1A1A',

  // Status colors
  success: '#34C759',
  error: '#FF453A',
  warning: '#FFD60A',
  info: '#0A84FF',

  // Border colors
  border: '#2A2A2A',
  borderLight: '#3A3A3A',
  borderDark: '#1A1A1A',

  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.7)',
  overlayLight: 'rgba(0, 0, 0, 0.5)',
  overlayDark: 'rgba(0, 0, 0, 0.9)',

  card: '#1E1E1E',
  shadow: '#000000',
} as const;

export const getColors = (isDark: boolean) => isDark ? darkColors : lightColors;