{"expo": {"name": "Lotus", "slug": "lotus", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#121212"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.mattneto.lotus", "buildNumber": "2", "infoPlist": {"NSLocationWhenInUseUsageDescription": "Lotus uses your location to provide personalized weather information and meal suggestions based on local conditions.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Lotus uses your location to provide personalized weather information and meal suggestions based on local conditions.", "UIBackgroundModes": ["location", "location"], "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#121212"}, "package": "com.mattneto.lotus", "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "FOREGROUND_SERVICE", "ACCESS_BACKGROUND_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "config": {"googleMaps": {"apiKey": "YOUR_GOOGLE_MAPS_API_KEY_HERE"}}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-secure-store", "expo-asset", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow <PERSON> to use your location for personalized weather and meal suggestions.", "locationWhenInUsePermission": "Allow <PERSON> to use your location for personalized weather and meal suggestions."}]], "newArchEnabled": true, "experiments": {"tsconfigPaths": true}, "owner": "mat<PERSON><PERSON>o", "extra": {"eas": {"projectId": "4ac036b1-221a-4bf4-b2b7-0281c410c0a8"}}}}