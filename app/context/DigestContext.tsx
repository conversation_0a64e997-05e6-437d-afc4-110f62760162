import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { View } from 'react-native';
import { DayDigest, getDayDigest } from '../services/digestService';
import { addDays, format, startOfWeek } from 'date-fns';
import { useAuth } from './AuthContext'; // Assuming AuthContext provides user info/auth status

// Define the shape of the context state
interface DigestContextState {
  digests: Record<string, DayDigest>; // Keyed by date 'YYYY-MM-DD'
  selectedDate: string; // 'YYYY-MM-DD'
  isLoading: boolean;
  error: string | null;
  fetchDigestForDate: (date: Date) => Promise<void>;
  refreshWeek: () => Promise<void>;
  setSelectedDate: (date: string) => void;
  updateActivity: (date: string, activityId: string, updates: Partial<any>) => void; // TODO: Define Activity type
  resetDigestLoadingFlag: () => Promise<boolean>; // Function to reset the digest loading flag
}

// Create the context with a default undefined value
const DigestContext = createContext<DigestContextState | undefined>(undefined);

// Define the props for the provider
interface DigestProviderProps {
  children: ReactNode;
  initialDigests?: DayDigest[];
}

// Create the provider component
export const DigestProvider: React.FC<DigestProviderProps> = ({ children, initialDigests }) => {
  const [digests, setDigests] = useState<Record<string, DayDigest>>({});
  const [selectedDate, setSelectedDateState] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { isAuthenticated } = useAuth(); // Use auth context

  // Initialize digests with pre-fetched data
  useEffect(() => {
    if (initialDigests && initialDigests.length > 0) {
      const initialDigestsMap = initialDigests.reduce((acc, digest) => {
        acc[format(new Date(digest.date), 'yyyy-MM-dd')] = digest;
        return acc;
      }, {} as Record<string, DayDigest>);
      setDigests(initialDigestsMap);
    }
  }, [initialDigests]);

  // Fetch digest for the selected date if not already loaded
  useEffect(() => {
    if (isAuthenticated && !digests[selectedDate]) {
      fetchDigestForDate(new Date(selectedDate));
    }
  }, [selectedDate, digests, isAuthenticated]); // Depend on selectedDate and digests

  // Refresh the week's digests when the component mounts and user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Only log once during initialization
      console.log('[DigestContext] User is authenticated, refreshing week digests');
      refreshWeek();
    }
  }, [isAuthenticated]); // Only depend on isAuthenticated to avoid infinite loops

  const fetchDigestForDate = async (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    if (digests[dateString]) {
      // Already loaded
      return;
    }

    setIsLoading(true);
    setError(null);

    // Import the createDigestOnAPI function
    const { createDigestOnAPI } = require('../services/digestService');
    const today = new Date();

    try {
      // First try to get the existing digest - this avoids creating duplicates
      const digest = await getDayDigest(date);

      // If we have a valid digest with activities, use it
      if (digest && digest.activities && digest.activities.length > 0) {
        setDigests(prevDigests => ({
          ...prevDigests,
          [dateString]: digest,
        }));
        setIsLoading(false);
        return;
      }

      // For future dates (including today), create a new digest if needed
      if (date >= today) {
        // Only create a new digest if we don't have one or it's empty
        const newDigest = await createDigestOnAPI(dateString);

        if (newDigest && newDigest.activities && newDigest.activities.length > 0) {
          setDigests(prevDigests => ({
            ...prevDigests,
            [dateString]: newDigest,
          }));
          setIsLoading(false);
          return;
        }
      }

      // If we get here for a future date, we failed to get a valid digest
      if (date >= today) {
        setDigests(prevDigests => ({
          ...prevDigests,
          [dateString]: {
            date: dateString,
            activities: [],
            noDataReason: 'api-error',
            error: 'Failed to generate digest. Please try again.'
          }
        }));
        setError(`Failed to create digest for ${format(date, 'MMMM d, yyyy')}`);
      } else {
        // For past dates with no activities, set an empty digest with a reason
        setDigests(prevDigests => ({
          ...prevDigests,
          [dateString]: {
            date: dateString,
            activities: [],
            noDataReason: 'no-data-collected'
          }
        }));
        setError(`No data available for ${format(date, 'MMMM d, yyyy')}`);
      }
    } catch (err: any) {
      console.error(`[DigestContext] Error handling digest for ${dateString}:`, err);

      // Set an appropriate error digest
      setDigests(prevDigests => ({
        ...prevDigests,
        [dateString]: {
          date: dateString,
          activities: [],
          noDataReason: date >= today ? 'api-error' : 'no-data-collected',
          error: date >= today ? 'Failed to generate digest. Please try again.' : undefined
        }
      }));

      if (date >= today) {
        setError(`Failed to create digest for ${format(date, 'MMMM d, yyyy')}`);
      } else {
        setError(`No data available for ${format(date, 'MMMM d, yyyy')}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const refreshWeek = async () => {
    if (!isAuthenticated) return;

    setIsLoading(true);
    setError(null);
    try {
      const today = new Date();

      // Get the current week plus the next week (14 days total)
      const start = startOfWeek(today, { weekStartsOn: 0 }); // Assuming week starts on Sunday
      const end = addDays(today, 6); // Only include 1 week ahead to reduce API calls

      console.log(`[DigestContext] Refreshing digests from ${format(start, 'yyyy-MM-dd')} to ${format(end, 'yyyy-MM-dd')}`);

      // Create an array of dates from start to end
      const dates: Date[] = [];
      let currentDate = start;
      while (currentDate <= end) {
        dates.push(new Date(currentDate));
        currentDate = addDays(currentDate, 1);
      }

      // Import the necessary functions
      const { createDigestOnAPI } = require('../services/digestService');

      // Process dates in parallel with a limit on concurrent requests
      const batchSize = 3; // Process 3 dates at a time to avoid overwhelming the API
      const digestsMap: Record<string, DayDigest> = {};

      // First, try to get existing digests for all dates in parallel batches
      for (let i = 0; i < dates.length; i += batchSize) {
        const batch = dates.slice(i, i + batchSize);
        const batchPromises = batch.map(async (date) => {
          const dateString = format(date, 'yyyy-MM-dd');
          try {
            // First try to get existing digest
            const digest = await getDayDigest(date);
            if (digest && digest.activities && digest.activities.length > 0) {
              return { dateString, digest };
            }

            // For future dates, create if needed
            if (date >= today) {
              const newDigest = await createDigestOnAPI(dateString);
              if (newDigest && newDigest.activities && newDigest.activities.length > 0) {
                return { dateString, digest: newDigest };
              }
            }

            // Return empty digest with appropriate reason
            return {
              dateString,
              digest: {
                date: dateString,
                activities: [],
                noDataReason: date >= today ? 'api-error' : 'no-data-collected'
              }
            };
          } catch (err) {
            console.error(`[DigestContext] Error handling digest for ${dateString}:`, err);
            // Return empty digest with appropriate reason
            return {
              dateString,
              digest: {
                date: dateString,
                activities: [],
                noDataReason: date >= today ? 'api-error' : 'no-data-collected'
              }
            };
          }
        });

        // Wait for the current batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Add results to the digests map
        batchResults.forEach(({ dateString, digest }) => {
          digestsMap[dateString] = digest;
        });
      }

      // Update the state with all the digests
      setDigests(prevDigests => ({
        ...prevDigests,
        ...digestsMap,
      }));
      console.log(`[DigestContext] Refreshed ${Object.keys(digestsMap).length} daily digests.`);

    } catch (err: any) {
      console.error('[DigestContext] Error refreshing weekly digest:', err);
      setError('Failed to refresh weekly digest.');
    } finally {
      setIsLoading(false);
    }
  };

  const setSelectedDate = (dateString: string) => {
    setSelectedDateState(dateString);
  };

  const updateActivity = (dateString: string, activityId: string, updates: Partial<any>) => {
    setDigests(prevDigests => {
      const dayDigest = prevDigests[dateString];
      if (!dayDigest) return prevDigests;

      const updatedActivities = dayDigest.activities.map(activity =>
        activity.id === activityId ? { ...activity, ...updates } : activity
      );

      return {
        ...prevDigests,
        [dateString]: {
          ...dayDigest,
          activities: updatedActivities,
        },
      };
    });
    // TODO: Implement API call to persist the update
  };

  // Function to reset the digest loading flag
  const resetDigestLoadingFlag = async () => {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.removeItem('SKIP_DIGEST_LOADING');
      await AsyncStorage.removeItem('DIGEST_401_ERROR_COUNT');
      await AsyncStorage.removeItem('LAST_DIGEST_401_ERROR_TIME');
      console.log('[DigestContext] Reset digest loading flags successfully');

      // Refresh the digests after resetting the flag
      await refreshWeek();

      return true;
    } catch (error) {
      console.error('[DigestContext] Error resetting digest loading flag:', error);
      return false;
    }
  };

  const contextValue: DigestContextState = {
    digests,
    selectedDate,
    isLoading,
    error,
    fetchDigestForDate,
    refreshWeek,
    setSelectedDate,
    updateActivity,
    resetDigestLoadingFlag,
  };

  return (
    <DigestContext.Provider value={contextValue}>
      {/* Wrap children in a View to ensure proper rendering in React Native */}
      <View style={{ flex: 1 }}>
        {children}
      </View>
    </DigestContext.Provider>
  );
};

// Custom hook to consume the context
export const useDigest = () => {
  const context = useContext(DigestContext);
  if (context === undefined) {
    throw new Error('useDigest must be used within a DigestProvider');
  }
  return context;
};