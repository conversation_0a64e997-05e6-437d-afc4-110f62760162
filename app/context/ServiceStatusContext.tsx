import React, { createContext, useState, useContext, useEffect } from 'react';
import { testChatAPIConnectivity } from '../services/chatService';

interface ServiceStatusContextType {
  isAIServiceAvailable: boolean;
  lastChecked: Date | null;
  checkServiceStatus: () => Promise<void>;
  serviceStatusMessage: string;
  dismissStatusMessage: () => void;
  showStatusMessage: boolean;
}

const ServiceStatusContext = createContext<ServiceStatusContextType>({
  isAIServiceAvailable: true,
  lastChecked: null,
  checkServiceStatus: async () => {},
  serviceStatusMessage: '',
  dismissStatusMessage: () => {},
  showStatusMessage: false,
});

export const useServiceStatus = () => useContext(ServiceStatusContext);

export const ServiceStatusProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAIServiceAvailable, setIsAIServiceAvailable] = useState<boolean>(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [serviceStatusMessage, setServiceStatusMessage] = useState<string>(
    'AI service is currently unavailable. Some features may be limited.'
  );
  const [showStatusMessage, setShowStatusMessage] = useState<boolean>(false);

  const checkServiceStatus = async () => {
    try {
      const result = await testChatAPIConnectivity();
      setIsAIServiceAvailable(result.success);
      setLastChecked(new Date());
      
      if (!result.success) {
        // Set appropriate message based on error
        if (result.details?.responseStatus === 504) {
          setServiceStatusMessage('AI service is experiencing timeouts. Some features may be limited.');
        } else if (result.details?.responseStatus === 502) {
          setServiceStatusMessage('AI service is experiencing server issues. Retrying automatically...');
        } else if (result.details?.responseStatus === 503 ||
                  (result.details?.responseData &&
                   result.details.responseData.toString().includes('Service Unavailable'))) {
          setServiceStatusMessage('AI service is temporarily unavailable. Some features may be limited.');
        } else {
          setServiceStatusMessage('AI service is currently experiencing issues. Some features may be limited.');
        }
        setShowStatusMessage(true);
      } else {
        setShowStatusMessage(false);
      }
    } catch (error) {
      console.error('Error checking service status:', error);
      setIsAIServiceAvailable(false);
      setLastChecked(new Date());
      setServiceStatusMessage('Unable to verify AI service status. Some features may be limited.');
      setShowStatusMessage(true);
    }
  };

  const dismissStatusMessage = () => {
    setShowStatusMessage(false);
  };

  // Check service status on initial load
  useEffect(() => {
    checkServiceStatus();
    
    // Set up periodic checks (every 5 minutes)
    const intervalId = setInterval(() => {
      checkServiceStatus();
    }, 5 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);

  // Also check service status when the app comes back to the foreground
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        checkServiceStatus();
      }
    };

    // Add app state change listener
    // This is a placeholder - in a real app, you'd use AppState from react-native
    // AppState.addEventListener('change', handleAppStateChange);
    
    // return () => {
    //   AppState.removeEventListener('change', handleAppStateChange);
    // };
  }, []);

  return (
    <ServiceStatusContext.Provider
      value={{
        isAIServiceAvailable,
        lastChecked,
        checkServiceStatus,
        serviceStatusMessage,
        dismissStatusMessage,
        showStatusMessage,
      }}
    >
      {children}
    </ServiceStatusContext.Provider>
  );
};
