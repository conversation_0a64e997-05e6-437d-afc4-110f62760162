import React, { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef} from 'react';
import { getProfile, saveProfile, UserProfile, getProfileFromStorage, saveProfileToStorage } from '../services/profile';
import { useAuth } from './AuthContext';

// Default profile to use when no data is available
// This ensures consistent layout during transitions
const DEFAULT_PROFILE: UserProfile = {
  name: 'User',
  birthday: '',
  weight: '',
  height: '',
  fitnessGoal: ''
};

interface ProfileContextType {
  profile: UserProfile; // Always return at least default profile
  isLoading: boolean;
  setProfile: (profile: UserProfile) => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const { tokens, isAuthenticated } = useAuth();
  // Start with default profile to prevent layout shifts
  const [profile, setProfileState] = useState<UserProfile>(DEFAULT_PROFILE);
  const [isLoading, setIsLoading] = useState(false);
  const [lastTokenUsed, setLastTokenUsed] = useState<string | null>(null);

  // Pre-load profile from storage immediately on mount
  useEffect(() => {
    const loadLocalProfile = async () => {
      try {
        const localProfile = await getProfileFromStorage();
        if (localProfile) {
          setProfileState(localProfile);
        }
      } catch (error) {
        console.error("[ProfileProvider] Error loading local profile:", error);
      }
    };

    loadLocalProfile();
  }, []);

  // Track local profile load to avoid excessive logging
  const profileLoadAttempted = useRef(false);

  // Memoized loadProfile function with optimized background loading
  const loadProfile = useCallback(async (forceRefresh = false) => {
    // Only log on first load or when forcing refresh
    if (forceRefresh || !profileLoadAttempted.current) {
      console.log('[ProfileProvider] loadProfile started, forceRefresh =', forceRefresh);
    }

    try {
      // Only show loading indicator when forcing refresh
      if (forceRefresh) {
        setIsLoading(true);
      }

      // First check local storage for immediate data
      const localProfile = await getProfileFromStorage();

      // Update UI immediately with local data if available
      if (localProfile) {
        // Only log on first load
        if (!profileLoadAttempted.current) {
          console.log('[ProfileProvider] Found local profile data');
          profileLoadAttempted.current = true;
        }
        setProfileState(localProfile);
      }

      // Skip server call if not forcing refresh and we have local data
      if (!forceRefresh && localProfile && localProfile.userId) {
        // Only log on first successful load
        if (!profileLoadAttempted.current) {
          console.log('[ProfileProvider] Using cached profile data');
          profileLoadAttempted.current = true;
        }
        if (forceRefresh) setIsLoading(false);
        return;
      }

      // In background, try to get fresh data from server
      if (tokens?.accessToken) {
        try {
          console.log('[ProfileProvider] Fetching profile from server in background');
          setLastTokenUsed(tokens.accessToken);

          const userProfile = await getProfile(tokens.accessToken, tokens.idToken);

          if (userProfile) {
            console.log('[ProfileProvider] Server profile loaded');
            setProfileState(userProfile);
          } else if (localProfile) {
            // Sync local profile to server if needed
            console.log('[ProfileProvider] Syncing local profile to server');
            try {
              const savedProfile = await saveProfile(localProfile, tokens.accessToken, tokens.idToken);
              console.log('[ProfileProvider] Sync successful');
              setProfileState(savedProfile);
            } catch (syncError) {
              console.error('[ProfileProvider] Sync failed:', syncError);
              // Keep using local profile
            }
          } else {
            // If no profile found (404 from server), reset to DEFAULT_PROFILE to trigger onboarding
            console.log('[ProfileProvider] No profile found on server, resetting to default');
            setProfileState(DEFAULT_PROFILE);
          }
        } catch (serverError) {
          console.error('[ProfileProvider] Server error:', serverError);
          // We already have default data, so UI remains stable
        }
      } else {
        console.warn('[ProfileProvider] No access token available');
      }
    } catch (error) {
      console.error('[ProfileProvider] Error loading profile:', error);
      // Ensure we always have at least the default profile
      if (!profile || !profile.name) {
        setProfileState(DEFAULT_PROFILE);
      }
    } finally {
      if (forceRefresh) {
        setIsLoading(false);
      }
    }
  }, [tokens, profile]);

  // Manual refresh function
  const refreshProfile = useCallback(async () => {
    return loadProfile(true);
  }, [loadProfile]);

  // Track auth state changes for debugging
  const prevAuthState = useRef({ isAuthenticated, accessToken: tokens?.accessToken });

  // Load profile when auth state changes
  useEffect(() => {
    // Only log and process when actual changes occur
    const tokenChanged = tokens?.accessToken !== lastTokenUsed;
    const authChanged = prevAuthState.current.isAuthenticated !== isAuthenticated ||
                        prevAuthState.current.accessToken !== tokens?.accessToken;

    // Update reference for next comparison
    prevAuthState.current = { isAuthenticated, accessToken: tokens?.accessToken };

    if (isAuthenticated && tokens) {
      // Only load profile and log if there was an actual change
      if (authChanged) {
        // Only log on initial load or when state actually changes
        console.log('[ProfileProvider] Auth state changed. Token changed:', tokenChanged);
        loadProfile(tokenChanged);
      }
    } else if (!isAuthenticated) {
      // Reset to default when logged out, but never to null
      // Only log when actually transitioning from authenticated to not
      if (authChanged) {
        console.log('[ProfileProvider] User not authenticated, using default profile');
      }
      setProfileState(DEFAULT_PROFILE);
      setLastTokenUsed(null);
      setIsLoading(false);
    }
  }, [isAuthenticated, tokens, loadProfile, lastTokenUsed]);

  // Save profile changes
  const setProfile = async (newProfile: UserProfile) => {
    setIsLoading(true);
    try {
      console.log('[ProfileContext] Saving profile changes');

      // Always update UI state immediately
      setProfileState(newProfile);

      // Save to local storage first for offline access
      await saveProfileToStorage(newProfile);

      // Then try to sync with server if possible
      if (tokens?.accessToken) {
        try {
          console.log('[ProfileContext] Syncing with server');
          const updatedProfile = await saveProfile(newProfile, tokens.accessToken, tokens.idToken);

          if (updatedProfile && JSON.stringify(updatedProfile) !== JSON.stringify(newProfile)) {
            console.log('[ProfileContext] Server returned updated profile');
            setProfileState(updatedProfile);
          }
        } catch (saveError: any) {
          console.error('[ProfileContext] Server sync error:', saveError);

          // Only throw for auth errors
          if (saveError.response?.status === 401 ||
              (saveError.message && saveError.message.includes('401'))) {
            throw new Error('Authentication failed during profile update');
          }
          // For other errors, we keep the local changes
        }
      } else {
        console.warn('[ProfileContext] No access token - changes saved locally only');
      }
    } catch (error) {
      console.error('[ProfileContext] Profile update error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Create a stable profile object for better memoization
  const profileContext = useMemo(() => ({
    // Always provide at least the default profile, never null
    profile: profile || DEFAULT_PROFILE,
    isLoading,
    setProfile,
    refreshProfile,
  }), [profile, isLoading, setProfile, refreshProfile]);

  return (
    <ProfileContext.Provider value={profileContext}>
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}

// Export a standalone function to get the profile for use in services
export async function getProfileForContext(): Promise<UserProfile | null> {
  try {
    // First try to get from storage
    const profile = await getProfileFromStorage();
    if (profile) {
      return profile;
    }
    return null;
  } catch (error) {
    console.error('Error getting profile for context:', error);
    return null;
  }
}