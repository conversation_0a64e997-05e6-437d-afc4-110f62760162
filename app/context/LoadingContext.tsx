import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect, useRef } from 'react';
import { View, Text, ActivityIndicator, BackHandler, Platform, Animated } from 'react-native';
import LoadingScreen from '../components/LoadingScreen';

interface LoadingContextType {
  isLoading: boolean;
  loadingMessage?: string;
  showLoading: (message?: string) => void;
  hideLoading: () => void;
  withLoading: <T>(fn: () => Promise<T>, message?: string) => Promise<T>;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useGlobalLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useGlobalLoading must be used within a LoadingProvider');
  }
  return context;
};

interface LoadingProviderProps {
  children: ReactNode;
  minLoadingTime?: number;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({
  children,
  minLoadingTime = 500 // Reduced from 800 to prevent potential memory issues
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string | undefined>(undefined);
  const [initialLoading, setInitialLoading] = useState(false); // Changed to false to avoid double loading screen
  const [shouldRenderContent, setShouldRenderContent] = useState(true); // Changed to true to show content immediately

  // Add fade animations
  const fadeOpacity = useRef(new Animated.Value(0)).current; // Start with 0 opacity
  const contentOpacity = useRef(new Animated.Value(1)).current; // Start with full opacity

  // Disable back button during loading on Android
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (isLoading) {
        return true; // Prevent default behavior while loading
      }
      return false;
    });

    return () => {
      backHandler.remove();
    };
  }, [isLoading]);

  const showLoading = useCallback((message?: string) => {
    setIsLoading(true);
    setLoadingMessage(message);
  }, []);

  const hideLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingMessage(undefined);
  }, []);

  const withLoading = useCallback(async <T>(
    fn: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    showLoading(message);
    const startTime = Date.now();
    const MAX_LOADING_TIME = 10000; // 10 seconds max to prevent hanging

    // Set a safety timeout to ensure loading screen always closes
    const safetyTimeout = setTimeout(() => {
      console.warn('Loading operation exceeded maximum time and was force-completed');
      hideLoading();
    }, MAX_LOADING_TIME);

    try {
      // Use Promise.race to ensure the function doesn't hang indefinitely
      const result = await Promise.race([
        fn(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Operation timed out')), MAX_LOADING_TIME)
        )
      ]) as T;

      // Ensure minimum loading time for better UX
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
      }

      return result;
    } catch (error) {
      console.error('Error in withLoading:', error);
      throw error;
    } finally {
      clearTimeout(safetyTimeout);
      hideLoading();
    }
  }, [showLoading, hideLoading, minLoadingTime]);

  const value = {
    isLoading,
    loadingMessage,
    showLoading,
    hideLoading,
    withLoading
  };

  // Render the app content with loading overlay when needed
  return (
    <LoadingContext.Provider value={value}>
      {children}

      {/* Loading overlay with fade-in animation */}
      {isLoading && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999
          }}
        >
          <LoadingScreen
            fullscreen
            showLogo={false}
            size="medium"
            message={loadingMessage || "Loading..."}
            fadeInDuration={400} // Faster fade for in-app loading for responsiveness
          />
        </View>
      )}
    </LoadingContext.Provider>
  );
};

export default LoadingProvider;
