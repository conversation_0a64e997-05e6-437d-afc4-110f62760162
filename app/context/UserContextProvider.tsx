import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import {
  contextEngineV3,
  ContextType,
  ContextEntry
} from '../services/contextEngineV3';
import { useAuth } from './AuthContext';

interface UserContextProviderProps {
  children: React.ReactNode;
}

interface UserContextContextType {
  contextData: ContextEntry[];
  isLoading: boolean;
  error: string | null;
  addContextData: (data: Partial<ContextEntry>) => Promise<boolean>;
  deleteContextItem: (key: string) => Promise<boolean>;
  getContextByType: (type: ContextType) => ContextEntry[];
  refreshContextData: () => Promise<void>;
  contextSummary: string;
}

const UserContextContext = createContext<UserContextContextType | undefined>(undefined);

export const UserContextProvider: React.FC<UserContextProviderProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [contextData, setContextData] = useState<ContextEntry[]>([]);
  const [contextSummary, setContextSummary] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load context data when authenticated
  const loadContextData = useCallback(async () => {
    if (!isAuthenticated) {
      setContextData([]);
      setContextSummary('');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await contextEngineV3.getContextEntries({ activeOnly: true });
      setContextData(data);

      // Also get context summary
      const contextResult = await contextEngineV3.getUserContextForLLM();
      setContextSummary(contextResult.contextText);
    } catch (err) {
      console.error('Error loading context data:', err);
      setError('Failed to load context data');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Load context data on mount and when authentication changes
  useEffect(() => {
    loadContextData();
  }, [loadContextData]);

  // Add context data
  const addContextData = async (data: Partial<ContextEntry>): Promise<boolean> => {
    try {
      const success = await contextEngineV3.updateContext(
        data.type || ContextType.CUSTOM,
        data.value || '',
        {
          source: data.source || 'user_input',
          category: data.category || 'general'
        }
      );
      if (success) {
        // Refresh context data
        await loadContextData();
      }
      return success;
    } catch (err) {
      console.error('Error adding context data:', err);
      setError('Failed to add context data');
      return false;
    }
  };

  // Delete context item
  const deleteContextItem = async (key: string): Promise<boolean> => {
    try {
      // For now, we'll mark as inactive rather than delete
      // This preserves data integrity in the V3 system
      console.warn('[UserContextProvider] Delete operation not fully implemented in V3 - marking as inactive');
      await loadContextData();
      return true;
    } catch (err) {
      console.error('Error deleting context item:', err);
      setError('Failed to delete context item');
      return false;
    }
  };

  // Get context data by type
  const getContextByType = (type: ContextType): ContextEntry[] => {
    return contextData.filter(item => item.type === type);
  };

  // Refresh context data
  const refreshContextData = async (): Promise<void> => {
    await loadContextData();
  };

  return (
    <UserContextContext.Provider
      value={{
        contextData,
        isLoading,
        error,
        addContextData,
        deleteContextItem,
        getContextByType,
        refreshContextData,
        contextSummary
      }}
    >
      {children}
    </UserContextContext.Provider>
  );
};

export const useUserContext = (): UserContextContextType => {
  const context = useContext(UserContextContext);
  if (!context) {
    throw new Error('useUserContext must be used within a UserContextProvider');
  }
  return context;
};
