import React, { createContext, useContext, useState, useEffect } from 'react';
import { getStoredTokens, signIn, signUp, confirmSignUp, signOut as authSignOut,
         deleteAccount as authDeleteAccount, AuthTokens, refreshToken } from '../services/auth';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  tokens: AuthTokens | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  confirmSignUp: (email: string, code: string) => Promise<void>;
  signOut: () => Promise<void>;
  deleteAccount: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
  updateTokens: (newTokens: AuthTokens) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);

  useEffect(() => {
    console.log("[AuthProvider] useEffect triggered. Calling loadTokens...");
    loadTokens();
  }, []);

  const loadTokens = async () => {
    console.log("[AuthProvider] loadTokens started.");
    try {
      const storedTokens = await getStoredTokens();
      console.log("[AuthProvider] getStoredTokens result:", storedTokens ? "Tokens found" : "No tokens");
      setTokens(storedTokens);
      setIsAuthenticated(!!storedTokens);
    } catch (error) {
      console.error('[AuthProvider] Error loading tokens:', error);
      setTokens(null);
      setIsAuthenticated(false);
    } finally {
      console.log("[AuthProvider] loadTokens finally block. Setting isLoading to false.");
      setIsLoading(false);
    }
  };

  const handleSignIn = async (email: string, password: string) => {
    try {
      console.log('[AuthContext] Starting sign in process for:', email);
      const response = await signIn(email, password);
      if (response.tokens) {
        console.log('[AuthContext] Sign in successful, updating auth state');
        setTokens(response.tokens);
        setIsAuthenticated(true);
        console.log('[AuthContext] Auth state updated successfully');
      } else {
        console.error('[AuthContext] Sign in response missing tokens');
        throw new Error('Sign in response missing tokens');
      }
    } catch (error) {
      console.error('[AuthContext] Error signing in:', error);
      // Ensure auth state is cleared on error
      setTokens(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  const handleSignUp = async (email: string, password: string) => {
    try {
      await signUp(email, password);
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  };

  const handleConfirmSignUp = async (email: string, code: string) => {
    try {
      await confirmSignUp(email, code);
    } catch (error) {
      console.error('Error confirming sign up:', error);
      throw error;
    }
  };

  const handleSignOut = async () => {
    try {
      console.log('[AuthContext] Signing out user');
      await authSignOut();
      setTokens(null);
      setIsAuthenticated(false);
      console.log('[AuthContext] User signed out successfully');
    } catch (error) {
      console.error('[AuthContext] Error signing out:', error);
      // Even if there's an error, ensure we clear the auth state
      setTokens(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await authDeleteAccount();
      setTokens(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  };

  const handleRefreshTokens = async () => {
    try {
      const newTokens = await refreshToken();
      if (newTokens) {
        setTokens(newTokens);
        setIsAuthenticated(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error refreshing tokens:', error);
      return false;
    }
  };

  const updateTokens = (newTokens: AuthTokens) => {
    setTokens(newTokens);
    setIsAuthenticated(true);
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        tokens,
        signIn: handleSignIn,
        signUp: handleSignUp,
        confirmSignUp: handleConfirmSignUp,
        signOut: handleSignOut,
        deleteAccount: handleDeleteAccount,
        refreshTokens: handleRefreshTokens,
        updateTokens,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}