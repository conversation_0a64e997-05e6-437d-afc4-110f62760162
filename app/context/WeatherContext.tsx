import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { WeatherData } from '../services/nutritionService';
import { getCurrentWeather } from '../services/nutritionService';

interface WeatherContextState {
  weatherData: WeatherData | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refreshWeather: (forceRefresh?: boolean) => Promise<void>;
}

const WeatherContext = createContext<WeatherContextState | undefined>(undefined);

interface WeatherProviderProps {
  children: React.ReactNode;
}

export const WeatherProvider: React.FC<WeatherProviderProps> = ({ children }) => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Check if weather data is stale (older than 30 minutes)
  const isWeatherStale = useCallback(() => {
    if (!lastUpdated || !weatherData) return true;
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    return lastUpdated < thirtyMinutesAgo;
  }, [lastUpdated, weatherData]);

  // Refresh weather data
  const refreshWeather = useCallback(async (forceRefresh: boolean = false) => {
    // Don't fetch if we have recent data and not forcing refresh
    if (!forceRefresh && !isWeatherStale() && weatherData) {
      console.log('[WeatherContext] Using cached weather data');
      return;
    }

    // Don't start a new request if one is already in progress
    if (isLoading) {
      console.log('[WeatherContext] Weather request already in progress');
      return;
    }

    console.log('[WeatherContext] Fetching weather data...', { forceRefresh, isStale: isWeatherStale() });
    setIsLoading(true);
    setError(null);

    try {
      const weather = await getCurrentWeather(forceRefresh);
      
      if (weather) {
        setWeatherData(weather);
        setLastUpdated(new Date());
        console.log('[WeatherContext] Weather data updated successfully');
      } else {
        throw new Error('No weather data received');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch weather data';
      console.error('[WeatherContext] Error fetching weather:', errorMessage);
      setError(errorMessage);
      
      // Set unavailable weather data if we don't have any cached data
      if (!weatherData) {
        setWeatherData({ unavailable: true } as WeatherData);
      }
    } finally {
      setIsLoading(false);
    }
  }, [isWeatherStale, weatherData, isLoading]);

  // Load weather data on mount
  useEffect(() => {
    refreshWeather(false);
  }, []);

  // Auto-refresh weather data every 30 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (isWeatherStale()) {
        console.log('[WeatherContext] Auto-refreshing stale weather data');
        refreshWeather(false);
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    return () => clearInterval(interval);
  }, [isWeatherStale, refreshWeather]);

  const contextValue: WeatherContextState = {
    weatherData,
    isLoading,
    error,
    lastUpdated,
    refreshWeather,
  };

  return (
    <WeatherContext.Provider value={contextValue}>
      {children}
    </WeatherContext.Provider>
  );
};

// Custom hook to use weather context
export const useWeather = () => {
  const context = useContext(WeatherContext);
  if (context === undefined) {
    throw new Error('useWeather must be used within a WeatherProvider');
  }
  return context;
};

export default WeatherContext;
