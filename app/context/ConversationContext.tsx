import React, { createContext, useState, useEffect, useContext, ReactNode, useCallback } from 'react';
import {
  Log,
  Message,
  getConversationLogs,
  createConversation as createConversationService,
  addMessageToConversation as addMessageService,
  deleteConversationLog as deleteLogService,
  deleteAllConversations as deleteAllConversationsService,
  syncConversationInBackground // Import sync function if needed for manual refresh
} from '../services/conversationService';

interface ConversationContextType {
  conversations: Log[];
  isLoading: boolean;
  error: string | null;
  fetchConversations: () => Promise<void>;
  createConversation: (description?: string) => Promise<Log | null>;
  addMessage: (conversationId: string, message: Message) => Promise<boolean>;
  deleteConversation: (conversationId: string) => Promise<boolean>;
  deleteAllConversations: () => Promise<boolean>;
  getConversationById: (conversationId: string) => Log | undefined;
  refreshConversationMessages: (conversationId: string) => void; // Function to trigger background sync
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

interface ConversationProviderProps {
  children: ReactNode;
}

export const ConversationProvider: React.FC<ConversationProviderProps> = ({ children }) => {
  const [conversations, setConversations] = useState<Log[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConversations = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedConversations = await getConversationLogs();
      setConversations(fetchedConversations);
    } catch (err: any) {
      console.error("Error fetching conversations:", err);
      setError('Failed to fetch conversations.');
      setConversations([]); // Clear conversations on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchConversations();
    // Potential: Set up interval polling or listeners if needed in future
    // const intervalId = setInterval(fetchConversations, 60000); // Example: Refresh every minute
    // return () => clearInterval(intervalId);
  }, [fetchConversations]);

  const createConversation = async (description: string = 'New Conversation'): Promise<Log | null> => {
    try {
      const newConversation = await createConversationService(description);
      if (newConversation) {
        setConversations(prev => [newConversation, ...prev]); // Add to the top
        return newConversation;
      }
      return null;
    } catch (err) {
      console.error("Error creating conversation in context:", err);
      setError('Failed to create conversation.');
      return null;
    }
  };

  const addMessage = async (conversationId: string, message: Message): Promise<boolean> => {
    console.log(`[ConvContext] Adding message to conversation: ${conversationId}`, {
      messageRole: message.role,
      messageContent: message.content?.substring(0, 30) + '...',
      hasTimestamp: !!message.timestamp
    });
    
    try {
      // Validate inputs
      if (!conversationId) {
        console.error('[ConvContext] Missing conversationId in addMessage');
        return false;
      }
      
      if (!message || !message.content) {
        console.error('[ConvContext] Invalid message in addMessage:', message);
        return false;
      }
      
      // Save to service
      const success = await addMessageService(conversationId, message);
      console.log(`[ConvContext] Service result for addMessage: ${success ? 'SUCCESS' : 'FAILED'}`);
      
      if (success) {
        // Update the specific conversation in the state
        setConversations(prev => {
          // Find the conversation
          const targetConv = prev.find(conv => conv.id === conversationId);
          if (!targetConv) {
            console.warn(`[ConvContext] Conversation not found in state: ${conversationId}`);
            return prev;
          }
          
          // Ensure message has timestamp
          const messageWithTimestamp = {
            ...message,
            timestamp: message.timestamp || new Date().toISOString()
          };
          
          // Update just that conversation
          return prev.map(conv => {
            if (conv.id === conversationId) {
              const updatedMessages = [...(conv.messages || []), messageWithTimestamp];
              // Sort messages after adding
              updatedMessages.sort((a, b) => new Date(a.timestamp!).getTime() - new Date(b.timestamp!).getTime());
              const updated = { ...conv, messages: updatedMessages };
              console.log(`[ConvContext] Updated conversation state:`, {
                conversationId, 
                oldMessageCount: conv.messages?.length || 0,
                newMessageCount: updatedMessages.length
              });
              return updated;
            }
            return conv;
          });
        });
        return true;
      }
      
      console.warn(`[ConvContext] Service addMessage failed but didn't throw error`);
      return false;
    } catch (err) {
      console.error(`[ConvContext] ERROR adding message to ${conversationId}:`, err);
      setError('Failed to add message.');
      return false;
    }
  };

  const deleteConversation = async (conversationId: string): Promise<boolean> => {
    try {
      const success = await deleteLogService(conversationId); // Use the generic deleteLog
      if (success) {
        setConversations(prev => prev.filter(conv => conv.id !== conversationId));
        return true;
      }
      return false;
    } catch (err) {
      console.error(`Error deleting conversation ${conversationId} in context:`, err);
      setError('Failed to delete conversation.');
      return false;
    }
  };

  const deleteAllConversations = async (): Promise<boolean> => {
    try {
      console.log('[ConversationContext] Starting to delete all conversations...');
      const success = await deleteAllConversationsService();
      if (success) {
        console.log('[ConversationContext] All conversations deleted successfully, clearing state');
        setConversations([]); // Clear all conversations from state
        return true;
      }
      console.warn('[ConversationContext] Delete all conversations service returned false');
      return false;
    } catch (err) {
      console.error('Error deleting all conversations in context:', err);
      setError('Failed to delete all conversations.');
      return false;
    }
  };

  const getConversationById = (conversationId: string): Log | undefined => {
    return conversations.find(conv => conv.id === conversationId);
  };

  // Function to manually trigger a background sync for a specific conversation's messages
  const refreshConversationMessages = (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation) {
        console.log(`[Context] Triggering background sync for conversation: ${conversationId}`);
        syncConversationInBackground(conversationId);
    } else {
        console.warn(`[Context] Cannot refresh messages for non-existent conversation: ${conversationId}`);
    }
  };


  return (
    <ConversationContext.Provider value={{
      conversations,
      isLoading,
      error,
      fetchConversations,
      createConversation,
      addMessage,
      deleteConversation,
      deleteAllConversations,
      getConversationById,
      refreshConversationMessages
    }}>
      {children}
    </ConversationContext.Provider>
  );
};

export const useConversations = (): ConversationContextType => {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
}; 