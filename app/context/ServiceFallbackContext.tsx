import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { serviceHealthChecker, HealthCheckResult } from '../services/serviceHealthCheck';

interface ServiceFallbackContextType {
  healthStatus: HealthCheckResult | null;
  isServiceHealthy: (serviceName: string) => boolean;
  getServiceStatus: (serviceName: string) => 'healthy' | 'degraded' | 'down' | 'unknown';
  refreshHealthCheck: () => Promise<void>;
  isOverallHealthy: boolean;
  lastChecked: string | null;
  fallbackMode: boolean;
}

const ServiceFallbackContext = createContext<ServiceFallbackContextType>({
  healthStatus: null,
  isServiceHealthy: () => true,
  getServiceStatus: () => 'unknown',
  refreshHealthCheck: async () => {},
  isOverallHealthy: true,
  lastChecked: null,
  fallbackMode: false,
});

export const useServiceFallback = () => useContext(ServiceFallbackContext);

interface ServiceFallbackProviderProps {
  children: ReactNode;
}

export const ServiceFallbackProvider: React.FC<ServiceFallbackProviderProps> = ({ children }) => {
  const [healthStatus, setHealthStatus] = useState<HealthCheckResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkHealth = async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      const health = await serviceHealthChecker.checkAllServices(forceRefresh);
      setHealthStatus(health);
    } catch (error) {
      console.error('[ServiceFallback] Error checking service health:', error);
      // Set a fallback health status
      setHealthStatus({
        overall: 'down',
        services: [],
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshHealthCheck = async () => {
    await checkHealth(true);
  };

  const isServiceHealthy = (serviceName: string): boolean => {
    if (!healthStatus) return true; // Assume healthy if no data
    const service = healthStatus.services.find(s => s.name === serviceName);
    return service?.status === 'healthy';
  };

  const getServiceStatus = (serviceName: string): 'healthy' | 'degraded' | 'down' | 'unknown' => {
    if (!healthStatus) return 'unknown';
    const service = healthStatus.services.find(s => s.name === serviceName);
    return service?.status || 'unknown';
  };

  // Check health on mount and when app becomes active
  useEffect(() => {
    checkHealth();

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Check health when app becomes active, but don't force refresh
        checkHealth(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Set up periodic health checks (every 5 minutes)
    const intervalId = setInterval(() => {
      checkHealth(false);
    }, 5 * 60 * 1000);

    return () => {
      subscription.remove();
      clearInterval(intervalId);
    };
  }, []);

  const contextValue: ServiceFallbackContextType = {
    healthStatus,
    isServiceHealthy,
    getServiceStatus,
    refreshHealthCheck,
    isOverallHealthy: healthStatus?.overall === 'healthy',
    lastChecked: healthStatus?.timestamp || null,
    fallbackMode: healthStatus?.overall === 'down' || healthStatus?.overall === 'degraded',
  };

  return (
    <ServiceFallbackContext.Provider value={contextValue}>
      {children}
    </ServiceFallbackContext.Provider>
  );
};

/**
 * Hook for components that need to handle service failures gracefully
 */
export const useServiceAwareness = (serviceName: string) => {
  const { isServiceHealthy, getServiceStatus, fallbackMode } = useServiceFallback();
  
  const serviceHealthy = isServiceHealthy(serviceName);
  const serviceStatus = getServiceStatus(serviceName);
  
  return {
    isHealthy: serviceHealthy,
    status: serviceStatus,
    shouldUseFallback: !serviceHealthy || fallbackMode,
    canRetry: serviceStatus === 'degraded', // Degraded services might recover
  };
};

/**
 * Higher-order component that provides service fallback capabilities
 */
export const withServiceFallback = <P extends object>(
  Component: React.ComponentType<P>,
  serviceName: string,
  fallbackComponent?: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => {
    const { shouldUseFallback } = useServiceAwareness(serviceName);
    
    if (shouldUseFallback && fallbackComponent) {
      const FallbackComponent = fallbackComponent;
      return <FallbackComponent {...props} />;
    }
    
    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withServiceFallback(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default ServiceFallbackProvider;
