import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { generateStats, generateInsights, StatCard, InsightCard } from '../services/analytics';
import WeightChart from '../components/WeightChart';
import WeightStats from '../components/WeightStats';
import WeightEntry from '../components/WeightEntry';
import WeightHistory from '../components/WeightHistory';
import { WeightEntry as WeightEntryType } from '../services/weightTracking';

export default function ProgressScreen() {
  const { colors, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<StatCard[]>([]);
  const [insights, setInsights] = useState<InsightCard[]>([]);
  const [selectedWeight, setSelectedWeight] = useState<WeightEntryType | null>(null);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [lastLoadTime, setLastLoadTime] = useState(0);
  const loadingTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadData();
    
    // Clean up any pending timeouts on unmount
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  const loadData = async () => {
    // Prevent refreshing too frequently (minimum 2 seconds between loads)
    const now = Date.now();
    if (now - lastLoadTime < 2000) {
      console.log('Skipping data load - too soon since last load');
      return;
    }
    
    // Set loading state only if not already loading
    if (!isLoading) {
      setIsLoading(true);
    }
    
    setLastLoadTime(now);
    
    try {
      const stats = await generateStats();
      const insights = await generateInsights();
      setStats(stats);
      setInsights(insights);
    } catch (error) {
      console.error('Error loading progress data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleWeightAdded = useCallback(() => {
    console.log('Weight added, refreshing data...');
    
    // Clear any pending refresh
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    
    // Use a short timeout to avoid multiple rapid refreshes
    loadingTimeoutRef.current = setTimeout(() => {
      // Refresh all data when a new weight is added
      loadData();
      // Increment refresh counter to trigger component updates
      setRefreshCounter(prev => prev + 1);
      // Force a re-render of the chart by updating the selected weight
      setSelectedWeight(null);
      
      loadingTimeoutRef.current = null;
    }, 500);
  }, []);

  const handleWeightSelected = useCallback((entry: WeightEntryType) => {
    setSelectedWeight(entry);
  }, []);

  const renderStatCard = ({ title, value, icon, trend }: StatCard) => (
    <View style={[styles.statCard, { backgroundColor: colors.card }]}>
      <View style={[styles.iconContainer, { backgroundColor: colors.primaryLight }]}>
        <Ionicons name={icon as any} size={24} color={colors.primary} />
      </View>
      <View style={styles.statContent}>
        <Text style={[styles.statTitle, { color: colors.textSecondary }]}>{title}</Text>
        <Text style={[styles.statValue, { color: colors.text }]}>{value}</Text>
        {trend && (
          <View style={styles.trendContainer}>
            <Ionicons
              name={trend.isPositive ? 'trending-up' : 'trending-down'}
              size={16}
              color={trend.isPositive ? colors.success : colors.error}
            />
            <Text
              style={[
                styles.trendText,
                {
                  color: trend.isPositive ? colors.success : colors.error,
                },
              ]}
            >
              {Math.abs(trend.value)}%
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderInsightCard = (insight: InsightCard) => (
    <View 
      key={insight.title} 
      style={[
        styles.insightCard, 
        { 
          backgroundColor: colors.card,
          shadowColor: isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.2)'
        }
      ]}
    >
      <View style={[styles.insightIcon, { backgroundColor: colors.primaryLight }]}>
        <Ionicons name={insight.icon as any} size={24} color={colors.primary} />
      </View>
      <View style={styles.insightContent}>
        <Text style={[styles.insightTitle, { color: colors.text }]}>{insight.title}</Text>
        <Text style={[styles.insightDescription, { color: colors.textSecondary }]}>
          {insight.description}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.background,
          // Ensure this fills the entire screen area with solid background
          flex: 1,
          position: 'relative',
          zIndex: 1
        }
      ]}
    >
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <Text style={[styles.title, { color: colors.text }]}>Progress</Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
            progressViewOffset={10}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        bounces={true}
      >
        {/* Integrated Weight Tracking Card */}
        <View style={[
          styles.mainCard, 
          {
            backgroundColor: colors.card,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 4,
          }
        ]}>
          {/* Quick Add Weight Button at the top of card */}
          <View style={styles.quickAddContainer}>
            <WeightEntry 
              onWeightAdded={handleWeightAdded}
              floatingStyle={true} 
            />
            <WeightStats 
              refreshTrigger={refreshCounter} 
              compactView={true}
            />
          </View>
          
          {/* Chart with integrated time selectors */}
          <WeightChart
            onPointSelect={handleWeightSelected}
            refreshTrigger={refreshCounter}
            enhancedUX={true}
          />
        </View>

        {/* Weight History Section - Now a separate card with upgraded UI */}
        <View style={[
          styles.historyCard,
          {
            backgroundColor: colors.card,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 4,
          }
        ]}>
          <WeightHistory 
            onRefresh={handleWeightAdded} 
            refreshTrigger={refreshCounter}
            isInsideScrollView={true}
            enhancedUX={true}
          />
        </View>

        {/* Stats Section - Only show if there are stats */}
        {stats.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ionicons name="stats-chart" size={20} color={colors.primary} style={styles.sectionIcon} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Activity Stats</Text>
            </View>
            <View style={styles.statsGrid}>
              {stats.map((stat, index) => (
                <View key={index} style={styles.statCardWrapper}>
                  {renderStatCard(stat)}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Insights Section - Only show if there are insights */}
        {insights.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ionicons name="bulb-outline" size={20} color={colors.primary} style={styles.sectionIcon} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Insights</Text>
            </View>
            {insights.map(renderInsightCard)}
          </View>
        )}

        {/* Bottom padding */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 25,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 15,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  mainCard: {
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 15,
    borderRadius: 20,
    overflow: 'hidden',
  },
  historyCard: {
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 20,
    overflow: 'hidden',
  },
  quickAddContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.1)',
  },
  addWeightButtonContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 5,
  },
  section: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  statCardWrapper: {
    width: '50%',
    padding: 5,
  },
  statCard: {
    padding: 15,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  statContent: {
    flex: 1,
  },
  statTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 4,
  },
  statValue: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 300,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.bold,
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    marginBottom: 30,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    backgroundColor: '#fff', // Will be overridden by theme
  },
  insightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: typography.sizes.md + 4,
  },
  bottomPadding: {
    height: 40,
  },
});