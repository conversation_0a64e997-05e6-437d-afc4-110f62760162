import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Linking,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import AnimatedSettingsItem from '../components/AnimatedSettingsItem';

interface FAQItem {
  question: string;
  answer: string;
}

const HelpSupportScreen: React.FC = () => {
  const { colors } = useTheme();
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);
  const [feedbackText, setFeedbackText] = useState('');
  const [isSending, setIsSending] = useState(false);
  
  // Animation values for FAQ items
  const faqAnimations = useRef<Animated.Value[]>([]).current;
  
  // FAQ data
  const faqs: FAQItem[] = [
    {
      question: 'How do I track my workouts?',
      answer: 'You can track your workouts by going to the Strength tab and tapping on "Add Workout". You can then select from a list of exercises or create your own custom workout routine.',
    },
    {
      question: 'How do I log my meals?',
      answer: 'To log your meals, go to the Diet tab and tap on "Add Meal". You can search for foods in our database or create custom meals. You can also use the Lotus AI assistant to help you log meals by describing what you ate.',
    },
    {
      question: 'Can I export my data?',
      answer: 'Yes! You can export your data by going to Profile > Settings > Data & Privacy > Export Data. Your data will be exported in a CSV format that you can open in spreadsheet applications.',
    },
    {
      question: 'How do I change my profile information?',
      answer: 'You can update your profile information by going to the Profile tab and tapping on "Edit Profile". From there, you can change your name, email, profile picture, and other personal details.',
    },
    {
      question: 'Is my data secure?',
      answer: 'Yes, we take data security very seriously. All your personal information is encrypted and stored securely. We never share your data with third parties without your explicit consent.',
    },
  ];
  
  // Initialize animation values for FAQ items
  if (faqAnimations.length === 0) {
    faqs.forEach(() => {
      faqAnimations.push(new Animated.Value(0));
    });
  }
  
  // Toggle FAQ expansion
  const toggleFAQ = (index: number) => {
    if (expandedFAQ === index) {
      // Collapse
      Animated.timing(faqAnimations[index], {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start(() => {
        setExpandedFAQ(null);
      });
    } else {
      // Expand
      if (expandedFAQ !== null) {
        // Collapse currently expanded FAQ
        Animated.timing(faqAnimations[expandedFAQ], {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }).start();
      }
      
      setExpandedFAQ(index);
      Animated.timing(faqAnimations[index], {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  };
  
  // Handle contact support
  const contactSupport = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Support%20Request')
      .catch(() => {
        Alert.alert(
          'Error',
          'Could not open email client. <NAME_EMAIL> directly.'
        );
      });
  };
  
  // Handle feedback submission
  const submitFeedback = () => {
    if (!feedbackText.trim()) {
      Alert.alert('Error', 'Please enter your feedback before submitting.');
      return;
    }
    
    setIsSending(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSending(false);
      setFeedbackText('');
      Alert.alert(
        'Thank You!',
        'Your feedback has been submitted successfully. We appreciate your input!',
        [{ text: 'OK' }]
      );
    }, 1500);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Help & Support</Text>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Contact Us
          </Text>
          
          <AnimatedSettingsItem
            title="Email Support"
            description="Get help from our support team"
            icon="mail"
            type="navigation"
            onPress={contactSupport}
            iconColor={colors.primary}
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Visit Help Center"
            description="Browse our knowledge base"
            icon="help-circle"
            type="navigation"
            onPress={() => Linking.openURL('https://lotusapp.com/help')}
            iconColor="#5856D6"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Report a Bug"
            description="Let us know if something isn't working"
            icon="bug"
            type="navigation"
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
            iconColor="#FF9500"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Frequently Asked Questions
          </Text>
          
          {faqs.map((faq, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.faqItem, { backgroundColor: colors.card }]}
              onPress={() => toggleFAQ(index)}
              activeOpacity={0.8}
            >
              <View style={styles.faqHeader}>
                <Text style={[styles.faqQuestion, { color: colors.text }]}>
                  {faq.question}
                </Text>
                <Animated.View
                  style={{
                    transform: [
                      {
                        rotate: faqAnimations[index].interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '180deg'],
                        }),
                      },
                    ],
                  }}
                >
                  <Ionicons
                    name="chevron-down"
                    size={20}
                    color={colors.textSecondary}
                  />
                </Animated.View>
              </View>
              
              <Animated.View
                style={[
                  styles.faqAnswerContainer,
                  {
                    maxHeight: faqAnimations[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 500],
                    }),
                    opacity: faqAnimations[index],
                  },
                ]}
              >
                <Text style={[styles.faqAnswer, { color: colors.textSecondary }]}>
                  {faq.answer}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          ))}
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Send Feedback
          </Text>
          
          <View style={[styles.feedbackContainer, { backgroundColor: colors.card }]}>
            <TextInput
              style={[
                styles.feedbackInput,
                {
                  color: colors.text,
                  backgroundColor: colors.background + '80',
                  borderColor: colors.border,
                },
              ]}
              placeholder="Share your thoughts or suggestions..."
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              value={feedbackText}
              onChangeText={setFeedbackText}
            />
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                {
                  backgroundColor: colors.primary,
                  opacity: feedbackText.trim() ? 1 : 0.6,
                },
              ]}
              onPress={submitFeedback}
              disabled={!feedbackText.trim() || isSending}
            >
              {isSending ? (
                <Text style={styles.submitButtonText}>Sending...</Text>
              ) : (
                <Text style={styles.submitButtonText}>Submit Feedback</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.appInfo}>
          <Text style={[styles.appVersion, { color: colors.textSecondary }]}>
            Lotus v2.0.0
          </Text>
          <Text style={[styles.copyright, { color: colors.textTertiary }]}>
            © 2023 Lotus Health & Fitness
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 16,
    marginLeft: 4,
  },
  faqItem: {
    borderRadius: 16,
    marginBottom: 12,
    overflow: 'hidden',
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestion: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    flex: 1,
    marginRight: 8,
  },
  faqAnswerContainer: {
    overflow: 'hidden',
  },
  faqAnswer: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  feedbackContainer: {
    borderRadius: 16,
    padding: 16,
  },
  feedbackInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  appInfo: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  appVersion: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  copyright: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
  },
});

export default HelpSupportScreen;
