import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { getLogs, Log, WorkoutExercise, WorkoutData, deleteLog, MealData, saveMealFromConversation, saveWorkoutFromConversation } from '../services/conversationService';
import { useNavigation } from '@react-navigation/native';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { MainTabParamList } from '../navigation/MainTabNavigator';

type HistoryScreenNavigationProp = BottomTabNavigationProp<MainTabParamList, 'History'>;

export default function HistoryScreen() {
  const { colors } = useTheme();
  const navigation = useNavigation<HistoryScreenNavigationProp>();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [logs, setLogs] = useState<Log[]>([]);
  const [filter, setFilter] = useState<'all' | 'workout' | 'meal' | 'conversation'>('all');
  const [selectedWorkout, setSelectedWorkout] = useState<Log | null>(null);
  const [showWorkoutModal, setShowWorkoutModal] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState<Log | null>(null);
  const [showMealModal, setShowMealModal] = useState(false);
  const [isItemPressed, setIsItemPressed] = useState<string | null>(null);

  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    setIsLoading(true);
    try {
      const fetchedLogs = await getLogs();
      const sortedLogs = fetchedLogs.sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
      setLogs(sortedLogs);
    } catch (error) {
      console.error('Error loading logs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLogs();
    setRefreshing(false);
  };

  const filteredLogs = logs.filter(log =>
    filter === 'all' ? true : log.type === filter
  );

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLogIcon = (type: string) => {
    switch (type) {
      case 'workout':
        return 'barbell';
      case 'meal':
        return 'restaurant';
      case 'conversation':
        return 'chatbubbles';
      default:
        return 'document-text';
    }
  };

  const getMessageCount = (log: Log) => {
    return log.messages?.length || 0;
  };

  const getSnippet = (log: Log) => {
    if (log.type !== 'conversation' || !log.messages || log.messages.length === 0) {
      return log.description;
    }

    // Get the last message content
    const lastMessage = log.messages[log.messages.length - 1];

    // Create a snippet (first 50 chars)
    const snippet = lastMessage.content.substring(0, 50);
    return snippet + (lastMessage.content.length > 50 ? '...' : '');
  };

  const viewWorkout = (log: Log) => {
    if (log.type === 'workout' && log.workoutData) {
      setSelectedWorkout(log);
      setShowWorkoutModal(true);
    }
  };

  const viewMeal = (log: Log) => {
    if (log.type === 'meal' && log.metrics?.meal) {
      setSelectedMeal(log);
      setShowMealModal(true);
    }
  };

  const viewConversation = (log: Log) => {
    // Navigate to Dashboard with the conversation ID
    navigation.navigate('Dashboard', { conversationId: log.id });
  };

  const handleDeleteLog = async (logId: string) => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to delete this item? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteLog(logId);
              if (success) {
                // Update the local state to remove the deleted log
                setLogs(prevLogs => prevLogs.filter(log => log.id !== logId));
              }
            } catch (error) {
              console.error('Error deleting log:', error);
              Alert.alert('Error', 'Failed to delete the item. Please try again.');
            }
          }
        }
      ]
    );
  };

  const renderLogItem = useCallback(({ item, index }: { item: Log, index: number }) => {
    return (
      <TouchableOpacity
        key={`history-log-${item.id}-${index}`}
        style={[
        styles.logItem,
        {
          backgroundColor: isItemPressed === item.id
            ? colors.border + '40'
            : colors.card
        }
      ]}
      onPress={() => {
        if (item.type === 'conversation') {
          viewConversation(item);
        } else if (item.type === 'workout') {
          viewWorkout(item);
        } else if (item.type === 'meal') {
          viewMeal(item);
        }
      }}
      onLongPress={() => {
        setIsItemPressed(null);
        handleDeleteLog(item.id);
      }}
      onPressIn={() => setIsItemPressed(item.id)}
      onPressOut={() => setIsItemPressed(null)}
      delayLongPress={500}
      activeOpacity={0.7}
    >
      <View style={[styles.logIcon, {
        backgroundColor: colors.primaryLight,
        borderRadius: item.type === 'conversation' ? 8 : 20, // Square-ish for conversations
      }]}>
        <Ionicons
          name={getLogIcon(item.type)}
          size={24}
          color={colors.primary}
        />
      </View>
      <View style={styles.logContent}>
        <Text style={[styles.logDescription, { color: colors.text }]}>{getSnippet(item)}</Text>
        <View style={styles.dateTimeContainer}>
          <Text style={[styles.logTimestamp, { color: colors.textSecondary }]}>
            {formatDate(item.timestamp)}
          </Text>
          <Text style={[styles.logTimestamp, { color: colors.textSecondary }]}>
            {formatTime(item.timestamp)}
          </Text>
        </View>
        {item.type === 'conversation' && item.messages && (
          <View style={styles.logDetailsRow}>
            <Ionicons name="chatbubbles-outline" size={14} color={colors.textSecondary} />
            <Text style={[styles.logDetailsText, { color: colors.textSecondary }]}>
              {getMessageCount(item)} messages
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
  }, [colors, isItemPressed, handleDeleteLog, viewConversation, viewWorkout, formatDate, formatTime, getMessageCount, getLogIcon, getSnippet]);

  // Render a single exercise item in the workout detail view
  const renderExerciseItem = (exercise: WorkoutExercise, index: number) => (
    <View key={index} style={[styles.exerciseItem, { backgroundColor: colors.background + '20' }]}>
      <Text style={[styles.exerciseName, { color: colors.text }]}>
        {index + 1}. {exercise.name}
      </Text>

      {exercise.sets.map((set, setIdx) => (
        <View key={setIdx} style={styles.setItem}>
          <Text style={[styles.setText, { color: colors.textSecondary }]}>
            Set {setIdx + 1}: {set.reps} reps
            {set.weight ? ` at ${set.weight} lbs` : ''}
            {set.notes ? ` (${set.notes})` : ''}
          </Text>
        </View>
      ))}

      {exercise.notes && (
        <Text style={[styles.exerciseNotes, { color: colors.textSecondary }]}>
          Notes: {exercise.notes}
        </Text>
      )}
    </View>
  );

  // State for saving meal
  const [savingMeal, setSavingMeal] = useState(false);

  // Meal details modal
  const renderMealModal = () => {
    if (!selectedMeal || !selectedMeal.metrics?.meal) return null;

    const meal = selectedMeal.metrics.meal;

    return (
      <Modal
        visible={showMealModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMealModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
          <View style={[styles.modalContent, {
            backgroundColor: colors.card,
            height: '92%',
            width: '92%',
            borderRadius: 20,
            overflow: 'hidden',
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 10 },
            shadowOpacity: 0.25,
            shadowRadius: 15,
            elevation: 10,
          }]}>
            <View style={[styles.modalHeader, {
              backgroundColor: colors.primary,
              paddingVertical: 16,
              paddingHorizontal: 20,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }]}>
              <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>
                Recipe Details
              </Text>
              <TouchableOpacity
                onPress={() => setShowMealModal(false)}
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Ionicons name="close" size={20} color="#fff" />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalBody}
              contentContainerStyle={{ padding: 20 }}
            >
              {meal.description && (
                <View style={styles.mealSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Description</Text>
                  <Text style={[styles.mealDescription, { color: colors.textSecondary }]}>
                    {meal.description}
                  </Text>
                </View>
              )}

              {/* Nutrition Facts */}
              {(meal.calories || meal.protein || meal.carbs || meal.fat) && (
                <View style={styles.mealSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Facts</Text>
                  <View style={styles.nutritionContainer}>
                    {meal.calories && (
                      <View style={styles.nutritionItem}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{meal.calories}</Text>
                      </View>
                    )}
                    {meal.protein && (
                      <View style={styles.nutritionItem}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{meal.protein}g</Text>
                      </View>
                    )}
                    {meal.carbs && (
                      <View style={styles.nutritionItem}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{meal.carbs}g</Text>
                      </View>
                    )}
                    {meal.fat && (
                      <View style={styles.nutritionItem}>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>{meal.fat}g</Text>
                      </View>
                    )}
                  </View>
                </View>
              )}

              {/* Ingredients */}
              {meal.ingredients && meal.ingredients.length > 0 && (
                <View style={styles.mealSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
                  {meal.ingredients.map((ingredient, index) => (
                    <View key={index} style={styles.ingredientItem}>
                      <Ionicons name="checkmark-circle" size={16} color={colors.primary} />
                      <Text style={[styles.ingredientText, { color: colors.text, marginLeft: 8 }]}>
                        {ingredient}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Steps */}
              {meal.steps && meal.steps.length > 0 && (
                <View style={styles.mealSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Instructions</Text>
                  {meal.steps.map((step, index) => (
                    <View key={index} style={styles.stepItem}>
                      <Text style={[styles.stepNumber, {
                        color: colors.primary,
                        fontWeight: 'bold',
                        marginRight: 8
                      }]}>
                        {index + 1}.
                      </Text>
                      <Text style={[styles.stepText, { color: colors.text, flex: 1 }]}>
                        {step}
                      </Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Tags */}
              {meal.tags && meal.tags.length > 0 && (
                <View style={styles.mealSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Tags</Text>
                  <View style={styles.tagsContainer}>
                    {meal.tags.map((tag, idx) => (
                      <View key={idx} style={[styles.tagChip, {
                        backgroundColor: colors.primaryLight + '40',
                        paddingVertical: 4,
                        paddingHorizontal: 8,
                        borderRadius: 6,
                        marginRight: 8,
                        marginBottom: 4,
                      }]}>
                        <Text style={[styles.tagText, {
                          color: colors.primary,
                        }]}>
                          {tag}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </ScrollView>

            <View style={{
              borderTopWidth: 1,
              borderTopColor: colors.border + '40',
              padding: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              backgroundColor: colors.card,
            }}>
              <TouchableOpacity
                style={{
                  backgroundColor: colors.primaryLight + '30',
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  borderRadius: 24,
                  minWidth: 120,
                  alignItems: 'center',
                  borderWidth: 1,
                  borderColor: colors.primary,
                }}
                onPress={async () => {
                  if (selectedMeal?.metrics?.meal) {
                    try {
                      setSavingMeal(true);
                      const mealId = await saveMealFromConversation(selectedMeal.metrics.meal);
                      if (mealId) {
                        Alert.alert('Success', 'Recipe saved to your collection!');
                        setShowMealModal(false);
                      } else {
                        Alert.alert('Error', 'Failed to save recipe. Please try again.');
                      }
                    } catch (error) {
                      console.error('Error saving meal:', error);
                      Alert.alert('Error', 'An unexpected error occurred.');
                    } finally {
                      setSavingMeal(false);
                    }
                  }
                }}
              >
                {savingMeal ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <Text style={{
                    color: colors.primary,
                    fontWeight: 'bold',
                    fontSize: 16
                  }}>
                    Save Recipe
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary,
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  borderRadius: 24,
                  minWidth: 120,
                  alignItems: 'center',
                  shadowColor: colors.primary,
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 0.2,
                  shadowRadius: 5,
                  elevation: 3,
                }}
                onPress={() => setShowMealModal(false)}
              >
                <Text style={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: 16
                }}>
                  Close
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  // State for saving workout
  const [savingWorkout, setSavingWorkout] = useState(false);

  // Improved workout details modal with better styling
  const renderWorkoutModal = () => {
    if (!selectedWorkout || !selectedWorkout.workoutData) return null;

    const workout = selectedWorkout.workoutData;

    return (
      <Modal
        visible={showWorkoutModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowWorkoutModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
          <View style={[styles.modalContent, {
            backgroundColor: colors.card,
            height: '92%',
            width: '92%',
            borderRadius: 20,
            overflow: 'hidden',
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 10 },
            shadowOpacity: 0.25,
            shadowRadius: 15,
            elevation: 10,
          }]}>
            <View style={[styles.modalHeader, {
              backgroundColor: colors.primary,
              paddingVertical: 16,
              paddingHorizontal: 20,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }]}>
              <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>
                Workout Details
              </Text>
              <TouchableOpacity
                onPress={() => setShowWorkoutModal(false)}
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Ionicons name="close" size={20} color="#fff" />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalBody}
              contentContainerStyle={{ padding: 20 }}
            >
              <Text style={[styles.modalHeading, {
                color: colors.text,
                fontSize: 22,
                fontWeight: 'bold',
                marginBottom: 16
              }]}>
                {workout.title}
              </Text>
              <View style={[styles.workoutInfo, {
                flex: 1, // Use flex instead of flexGrow
                marginTop: 8,
                marginBottom: 16,
                paddingBottom: 20,
              }]}>
              <View style={[styles.workoutInfo, {
                backgroundColor: colors.background + '30',
                padding: 16,
                borderRadius: 12,
                marginBottom: 16,
              }]}>
                <Text style={[styles.workoutDate, {
                  color: colors.textSecondary,
                  fontSize: 14,
                  marginBottom: 4,
                }]}>
                  {formatDate(selectedWorkout.timestamp)}
                </Text>

                {workout.duration && (
                  <Text style={[styles.workoutDuration, {
                    color: colors.textSecondary,
                    fontSize: 14,
                    marginBottom: 8,
                  }]}>
                    Duration: {workout.duration} minutes
                  </Text>
                )}

                {workout.tags && workout.tags.length > 0 && (
                  <View style={[styles.tagsContainer, {
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    marginTop: 8
                  }]}>
                    {workout.tags.map((tag, idx) => (
                      <View key={idx} style={[styles.tagChip, {
                        backgroundColor: colors.primaryLight + '40',
                        paddingVertical: 4,
                        paddingHorizontal: 8,
                        borderRadius: 6,
                        marginRight: 8,
                        marginBottom: 4,
                      }]}>
                        <Text style={[styles.tagText, {
                          color: colors.primary,
                          fontSize: 12,
                        }]}>
                          {tag}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              <Text style={[styles.exercisesHeader, {
                color: colors.text,
                fontSize: 18,
                fontWeight: '600',
                marginBottom: 12,
                marginTop: 8,
              }]}>
                Exercises
              </Text>

              {workout.exercises.map((exercise, idx) => (
                <View key={idx} style={[styles.exerciseItem, {
                  backgroundColor: colors.card,
                  borderRadius: 12,
                  marginVertical: 8,
                  padding: 16,
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.1,
                  shadowRadius: 2,
                  elevation: 2,
                }]}>
                  <Text style={[styles.exerciseName, {
                    color: colors.text,
                    fontSize: 16,
                    fontWeight: '600',
                    marginBottom: 8,
                  }]}>
                    {idx + 1}. {exercise.name}
                  </Text>

                  {exercise.sets && exercise.sets.length > 0 && (
                    <View style={styles.setsContainer}>
                      <View style={[styles.setHeader, {
                        borderBottomColor: colors.border + '40',
                        borderBottomWidth: 1,
                        paddingBottom: 8,
                        marginBottom: 8,
                        flexDirection: 'row',
                      }]}>
                        <Text style={[styles.setHeaderText, { color: colors.textSecondary, width: 40 }]}>Set</Text>
                        <Text style={[styles.setHeaderText, { color: colors.textSecondary, flex: 1 }]}>Reps</Text>
                        <Text style={[styles.setHeaderText, { color: colors.textSecondary, flex: 1 }]}>Weight</Text>
                        <Text style={[styles.setHeaderText, { color: colors.textSecondary, flex: 2 }]}>Notes</Text>
                      </View>

                      {exercise.sets.map((set, setIdx) => (
                        <View key={setIdx} style={[styles.setItem, {
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: 8,
                        }]}>
                          <Text style={[styles.setNumber, {
                            color: colors.primary,
                            width: 40,
                            textAlign: 'center',
                            fontWeight: '600',
                          }]}>
                            {setIdx + 1}
                          </Text>
                          <Text style={[styles.setText, {
                            color: colors.text,
                            flex: 1,
                            textAlign: 'left',
                          }]}>
                            {set.reps || '-'}
                          </Text>
                          <Text style={[styles.setText, {
                            color: colors.text,
                            flex: 1,
                            textAlign: 'left',
                          }]}>
                            {set.weight || '-'}
                          </Text>
                          <Text style={[styles.setText, {
                            color: colors.text,
                            flex: 2,
                            textAlign: 'left',
                          }]}>
                            {set.notes || '-'}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {exercise.notes && (
                    <Text style={[styles.exerciseNotes, {
                      color: colors.textSecondary,
                      backgroundColor: colors.primaryLight + '20',
                      padding: 12,
                      borderRadius: 8,
                      marginTop: 8,
                      fontStyle: 'italic',
                    }]}>
                      {exercise.notes}
                    </Text>
                  )}
                </View>
              ))}

              {workout.notes && (
                <View style={[styles.notesSection, {
                  marginTop: 16,
                  backgroundColor: colors.background + '30',
                  padding: 16,
                  borderRadius: 12,
                }]}>
                  <Text style={[styles.notesHeader, {
                    color: colors.text,
                    fontSize: 16,
                    fontWeight: '600',
                    marginBottom: 8,
                  }]}>
                    Notes
                  </Text>
                  <Text style={[styles.notesText, {
                    color: colors.text,
                    lineHeight: 20,
                  }]}>
                    {workout.notes}
                  </Text>
                </View>
              )}
              </View>
            </ScrollView>

            <View style={{
              borderTopWidth: 1,
              borderTopColor: colors.border + '40',
              padding: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              backgroundColor: colors.card,
            }}>
              <TouchableOpacity
                style={{
                  backgroundColor: colors.primaryLight + '30',
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  borderRadius: 24,
                  minWidth: 120,
                  alignItems: 'center',
                  borderWidth: 1,
                  borderColor: colors.primary,
                }}
                onPress={async () => {
                  if (selectedWorkout?.workoutData) {
                    try {
                      setSavingWorkout(true);
                      const workoutId = await saveWorkoutFromConversation(selectedWorkout.workoutData);
                      if (workoutId) {
                        Alert.alert('Success', 'Workout saved to your collection!');
                        setShowWorkoutModal(false);
                      } else {
                        Alert.alert('Error', 'Failed to save workout. Please try again.');
                      }
                    } catch (error) {
                      console.error('Error saving workout:', error);
                      Alert.alert('Error', 'An unexpected error occurred.');
                    } finally {
                      setSavingWorkout(false);
                    }
                  }
                }}
              >
                {savingWorkout ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <Text style={{
                    color: colors.primary,
                    fontWeight: 'bold',
                    fontSize: 16
                  }}>
                    Save Workout
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary,
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  borderRadius: 24,
                  minWidth: 120,
                  alignItems: 'center',
                  shadowColor: colors.primary,
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 0.2,
                  shadowRadius: 5,
                  elevation: 3,
                }}
                onPress={() => setShowWorkoutModal(false)}
              >
                <Text style={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: 16
                }}>
                  Close
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.background,
          // Ensure this fills the entire screen area with solid background
          flex: 1,
          position: 'relative',
          zIndex: 1
        }
      ]}
    >
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <Text style={[styles.title, { color: colors.text }]}>History</Text>
      </View>

      <View style={styles.filterContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterScrollContent}
          bounces={true}
          alwaysBounceHorizontal={true}
          decelerationRate="normal"
        >
          <TouchableOpacity
            style={[
              styles.filterChip,
              filter === 'all' && [styles.activeFilterChip, { backgroundColor: colors.primary }],
              { borderColor: colors.border }
            ]}
            onPress={() => setFilter('all')}
          >
            <Text
              style={[
                styles.filterText,
                { color: filter === 'all' ? colors.background : colors.textSecondary },
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterChip,
              filter === 'conversation' && [styles.activeFilterChip, { backgroundColor: colors.primary }],
              { borderColor: colors.border }
            ]}
            onPress={() => setFilter('conversation')}
          >
            <Ionicons
              name="chatbubbles-outline"
              size={16}
              color={filter === 'conversation' ? colors.background : colors.textSecondary}
              style={styles.filterIcon}
            />
            <Text
              style={[
                styles.filterText,
                { color: filter === 'conversation' ? colors.background : colors.textSecondary },
              ]}
            >
              Conversations
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterChip,
              filter === 'workout' && [styles.activeFilterChip, { backgroundColor: colors.primary }],
              { borderColor: colors.border }
            ]}
            onPress={() => setFilter('workout')}
          >
            <Ionicons
              name="barbell-outline"
              size={16}
              color={filter === 'workout' ? colors.background : colors.textSecondary}
              style={styles.filterIcon}
            />
            <Text
              style={[
                styles.filterText,
                { color: filter === 'workout' ? colors.background : colors.textSecondary },
              ]}
            >
              Workouts
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterChip,
              filter === 'meal' && [styles.activeFilterChip, { backgroundColor: colors.primary }],
              { borderColor: colors.border }
            ]}
            onPress={() => setFilter('meal')}
          >
            <Ionicons
              name="restaurant-outline"
              size={16}
              color={filter === 'meal' ? colors.background : colors.textSecondary}
              style={styles.filterIcon}
            />
            <Text
              style={[
                styles.filterText,
                { color: filter === 'meal' ? colors.background : colors.textSecondary },
              ]}
            >
              Meals
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredLogs}
          renderItem={(props) => {
            // Create a wrapper with stable key for each item
            const { item, index } = props;
            return React.createElement(
              React.Fragment,
              { key: `list-item-${item.id}-${index}` },
              renderLogItem(props)
            );
          }}
          keyExtractor={(item, index) => `history-item-${item.id}-${index}`}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={true}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          onEndReachedThreshold={0.5}
          removeClippedSubviews={true}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons name="calendar-outline" size={60} color={colors.textSecondary} />
              <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
                No Logs Found
              </Text>
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                {filter === 'all'
                  ? 'Start asking health questions and logging your workouts!'
                  : filter === 'conversation'
                  ? 'No conversations found. Try asking a health question.'
                  : `No ${filter} logs found. Try logging a ${filter} or check a different filter.`}
              </Text>
            </View>
          }
        />
      )}

      {/* Add the workout and meal modals */}
      {renderWorkoutModal()}
      {renderMealModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  // Modal shared styles
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    maxWidth: 600,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalBody: {
    flex: 1,
  },
  modalHeading: {
    marginBottom: 10,
  },

  // Meal modal styles
  mealSection: {
    marginBottom: 20,
  },
  mealDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  nutritionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  nutritionItem: {
    width: '50%',
    marginBottom: 10,
  },
  nutritionLabel: {
    fontSize: 14,
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ingredientText: {
    fontSize: 16,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  stepNumber: {
    fontSize: 16,
  },
  stepText: {
    fontSize: 16,
    lineHeight: 22,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 10,
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    width: '100%',
  },
  filterScrollContent: {
    paddingHorizontal: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 6,
    borderRadius: 30,
    borderWidth: 1,
  },
  activeFilterChip: {
    borderWidth: 0,
  },
  filterIcon: {
    marginRight: 6,
  },
  filterText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
  logItem: {
    flexDirection: 'row',
    padding: 14,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  logIcon: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  logContent: {
    flex: 1,
  },
  logDescription: {
    fontSize: 15,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 6,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  logTimestamp: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
  },
  logDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  logDetailsText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    marginLeft: 4,
  },
  emptyState: {
    marginTop: 40,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyStateTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    display: 'flex',
    flexDirection: 'column',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },
  closeButton: {
    padding: 8,
    marginLeft: 8,
  },
  workoutDetails: {
    flex: 1,
  },
  workoutDetailsContent: {
    paddingVertical: 16,
    paddingBottom: 24,
  },
  workoutInfo: {
    marginBottom: 16,
  },
  workoutDate: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: typography.fontFamily.regular,
  },
  workoutDuration: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: typography.fontFamily.regular,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tagChip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.medium,
  },
  exercisesHeader: {
    fontSize: 16,
    marginBottom: 12,
    fontFamily: typography.fontFamily.medium,
  },
  exerciseItem: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    fontFamily: typography.fontFamily.semibold,
  },
  setItem: {
    marginLeft: 16,
    marginBottom: 4,
  },
  setText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  exerciseNotes: {
    marginTop: 8,
    marginLeft: 16,
    fontStyle: 'italic',
    fontSize: 13,
    fontFamily: typography.fontFamily.regular,
  },
  notesSection: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  notesHeader: {
    fontSize: 16,
    marginBottom: 8,
    fontFamily: typography.fontFamily.medium,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: typography.fontFamily.regular,
  },
  setsContainer: {
    marginBottom: 16,
  },
  setHeader: {
    borderBottomColor: '#e0e0e0',
    borderBottomWidth: 1,
    paddingBottom: 8,
    marginBottom: 8,
    flexDirection: 'row',
  },
  setHeaderText: {
    color: '#666',
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
  },
  setNumber: {
    color: '#333',
    width: 40,
    textAlign: 'center',
    fontWeight: '600',
  },
  closeFullButton: {
    backgroundColor: '#FFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 8,
  },
  closeFullButtonText: {
    color: '#333',
    fontWeight: '600',
    fontSize: 16,
  },
});