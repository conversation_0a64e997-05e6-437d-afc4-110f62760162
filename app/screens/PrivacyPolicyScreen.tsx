import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';

// Define the profile navigator param list type
type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  PrivacySettings: undefined;
  PrivacyPolicy: undefined;
  TermsOfService: undefined;
  HelpSupport: undefined;
};

const PrivacyPolicyScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<NativeStackNavigationProp<ProfileStackParamList>>();

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      <SafeAreaView style={[styles.safeArea, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            accessibilityLabel="Go back"
          >
            <Ionicons name="chevron-back" size={28} color={colors.primary} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Privacy Policy</Text>
          <View style={styles.placeholder} />
        </View>
        
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Introduction
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              This Privacy Policy explains how Lotus ("we", "us", or "our") collects, uses, and shares your information when you use our mobile application and related services (collectively, the "Service").
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              By using our Service, you agree to the collection and use of information in accordance with this policy. We will not use or share your information with anyone except as described in this Privacy Policy.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Information Collection and Use
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              While using our Service, we may ask you to provide us with certain personally identifiable information that can be used to contact or identify you. This may include, but is not limited to:
            </Text>
            <View style={styles.bulletList}>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Email address</Text>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Name</Text>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Phone number</Text>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Physical characteristics (height, weight)</Text>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Fitness and nutrition data</Text>
              <Text style={[styles.bulletPoint, { color: colors.textSecondary }]}>• Device information</Text>
            </View>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              We collect this information for the purpose of providing the Service, identifying and communicating with you, responding to your requests, and improving our services.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Log Data
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              When you access the Service, we may collect information that your browser or device sends whenever you visit our Service ("Log Data"). This Log Data may include information such as your device's Internet Protocol ("IP") address, browser type, browser version, the pages of our Service that you visit, the time and date of your visit, the time spent on those pages, and other statistics.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Cookies and Similar Technologies
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              Cookies are small data files that are placed on your device or computer and often include an anonymous unique identifier. We use cookies and similar tracking technologies to track activity on our Service and hold certain information.
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept cookies, you may not be able to use some portions of our Service.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Data Processing and Storage
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              All of your data is processed and stored on AWS (Amazon Web Services) infrastructure. We use industry-standard security measures to protect your data, including encryption, access controls, and regular security assessments.
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              Your data is stored in DynamoDB tables with appropriate partitioning and isolation, and accessed through secure Lambda functions that follow the principle of least privilege.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Security
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              The security of your Personal Information is important to us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your Personal Information, we cannot guarantee its absolute security.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Changes to This Privacy Policy
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "effective date" at the top of this page.
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Contact Us
            </Text>
            <Text style={[styles.paragraph, { color: colors.textSecondary }]}>
              If you have any questions about this Privacy Policy, please contact us:
            </Text>
            <Text style={[styles.contactInfo, { color: colors.primary }]}>
              <EMAIL>
            </Text>
          </View>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.textTertiary }]}>
              Effective Date: July 1, 2023
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    marginTop: Platform.OS === 'ios' ? 10 : 0,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 44,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 15,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 22,
    marginBottom: 12,
  },
  bulletList: {
    marginVertical: 8,
    marginLeft: 8,
  },
  bulletPoint: {
    fontSize: 15,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 24,
  },
  contactInfo: {
    fontSize: 15,
    fontFamily: typography.fontFamily.medium,
    marginTop: 8,
    marginBottom: 16,
  },
  footer: {
    marginTop: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 13,
    fontFamily: typography.fontFamily.regular,
  },
});

export default PrivacyPolicyScreen; 