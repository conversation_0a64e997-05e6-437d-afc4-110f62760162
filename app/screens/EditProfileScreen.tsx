import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { useProfile } from '../context/ProfileContext';

export default function EditProfileScreen() {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const { colors } = useTheme();
  const { profile, setProfile, isLoading } = useProfile();
  
  const [formData, setFormData] = useState({
    name: profile?.name || '',
    birthday: profile?.birthday || '',
    weight: profile?.weight || '',
    height: profile?.height || '',
    fitnessGoal: profile?.fitnessGoal || '',
  });
  
  const [isSaving, setIsSaving] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  
  const handleChange = (field: string, value: string) => {
    // For height and weight fields, only allow numeric input
    if ((field === 'height' || field === 'weight') && value !== '') {
      // Only allow numbers and decimal point
      if (!/^[0-9]*\.?[0-9]*$/.test(value)) {
        return;
      }
    }
    
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };
  
  const handleFocus = useCallback((field: string) => {
    setFocusedField(field);
    if (field === 'birthday') {
      setDatePickerVisible(true);
    }
  }, []);

  const handleBlur = useCallback(() => {
    setFocusedField(null);
  }, []);
  
  const showDatePicker = () => {
    setDatePickerVisible(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisible(false);
  };

  const handleDateConfirm = (date: Date) => {
    // Format date as MM/DD/YYYY
    const formattedDate = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
    setFormData(prev => ({
      ...prev,
      birthday: formattedDate
    }));
    hideDatePicker();
  };
  
  const handleSave = async () => {
    // Form validation
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Name is required');
      return;
    }
    
    // Validate height
    if (formData.height && (!parseFloat(formData.height) || parseFloat(formData.height) <= 0)) {
      Alert.alert('Error', 'Please enter a valid height');
      return;
    }
    
    // Validate weight
    if (formData.weight && (!parseFloat(formData.weight) || parseFloat(formData.weight) <= 0)) {
      Alert.alert('Error', 'Please enter a valid weight');
      return;
    }
    
    setIsSaving(true);
    
    try {
      await setProfile({
        ...profile,
        ...formData,
      });
      
      navigation.goBack();
    } catch (error) {
      console.error('Failed to save profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const renderInputField = (
    label: string,
    field: keyof typeof formData,
    placeholder: string,
    icon: keyof typeof Ionicons.glyphMap,
    keyboardType: 'default' | 'numeric' | 'email-address' = 'default',
    isRequired: boolean = false
  ) => {
    const isFocused = focusedField === field;
    
    // Common label rendering
    const renderLabel = () => (
      <View style={styles.labelContainer}>
        <Ionicons 
          name={icon} 
          size={20} 
          color={isFocused ? colors.primary : colors.textSecondary} 
        />
        <Text 
          style={[
            styles.label, 
            { color: isFocused ? colors.primary : colors.text }
          ]}
        >
          {label}
          {isRequired && <Text style={[styles.requiredStar, { color: colors.error }]}> *</Text>}
        </Text>
      </View>
    );
    
    // Special case for birthday field to use date picker
    if (field === 'birthday') {
      return (
        <View style={styles.inputContainer}>
          {renderLabel()}
          <TouchableOpacity
            style={[
              styles.input,
              styles.datePickerInput,
              { 
                backgroundColor: colors.surfaceLight,
                borderColor: isFocused ? colors.primary : colors.border,
                borderWidth: isFocused ? 2 : 1,
              }
            ]}
            onPress={showDatePicker}
          >
            <Text 
              style={[
                styles.dateText,
                { color: formData.birthday ? colors.text : colors.textTertiary }
              ]}
            >
              {formData.birthday || placeholder}
            </Text>
            <Ionicons 
              name="calendar" 
              size={20} 
              color={colors.textSecondary}
              style={styles.calendarIcon} 
            />
          </TouchableOpacity>
          <Text style={[styles.helperText, { color: colors.textTertiary }]}>
            Tap to select your date of birth
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.inputContainer}>
        {renderLabel()}
        <TextInput
          style={[
            styles.input,
            { 
              color: colors.text,
              backgroundColor: colors.surfaceLight,
              borderColor: isFocused ? colors.primary : colors.border,
              borderWidth: isFocused ? 2 : 1,
            }
          ]}
          value={formData[field]}
          onChangeText={(value) => handleChange(field, value)}
          placeholder={placeholder}
          placeholderTextColor={colors.textTertiary}
          keyboardType={keyboardType}
          onFocus={() => handleFocus(field)}
          onBlur={handleBlur}
        />
        {field === 'height' && (
          <Text style={[styles.helperText, { color: colors.textTertiary }]}>
            Enter height in inches
          </Text>
        )}
        {field === 'weight' && (
          <Text style={[styles.helperText, { color: colors.textTertiary }]}>
            Enter weight in pounds
          </Text>
        )}
      </View>
    );
  };
  
  // Helper function to parse MM/DD/YYYY string to Date object
  const parseDateString = (dateString: string): Date => {
    if (!dateString) return new Date();
    
    try {
      // Try to parse MM/DD/YYYY format
      const parts = dateString.split('/');
      if (parts.length === 3) {
        const month = parseInt(parts[0], 10) - 1; // JS months are 0-indexed
        const day = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);
        
        // Check if it's a valid date
        if (!isNaN(month) && !isNaN(day) && !isNaN(year)) {
          const date = new Date(year, month, day);
          if (date.getFullYear() === year && date.getMonth() === month && date.getDate() === day) {
            return date;
          }
        }
      }
    } catch (error) {
      console.warn('Error parsing date string:', error);
    }
    
    // If parsing fails, return current date
    return new Date();
  };
  
  if (isLoading) {
    return (
      <View 
        style={[
          styles.loadingContainer, 
          { 
            backgroundColor: colors.background,
            // Ensure this fills the entire screen area with solid background
            flex: 1,
            position: 'relative',
            zIndex: 1
          }
        ]}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading profile...</Text>
      </View>
    );
  }
  
  return (
    <SafeAreaView 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.background,
          // Ensure this fills the entire screen area with solid background
          flex: 1,
          position: 'relative',
          zIndex: 1
        }
      ]}
    >
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Edit Profile</Text>
        <View style={styles.rightPlaceholder} />
      </View>
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.form}>
            {renderInputField('Name', 'name', 'Enter your name', 'person-outline', 'default', true)}
            {renderInputField('Birthday', 'birthday', 'MM/DD/YYYY', 'calendar-outline')}
            {renderInputField('Weight (lbs)', 'weight', 'Enter weight in lbs', 'barbell-outline', 'numeric')}
            {renderInputField('Height (in)', 'height', 'Enter height in inches', 'resize-outline', 'numeric')}
            {renderInputField('Fitness Goal', 'fitnessGoal', 'Enter your fitness goal', 'fitness-outline')}
          </View>
          
          <Text style={[styles.requiredFieldsNote, { color: colors.textTertiary }]}>
            * Required fields
          </Text>
          
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton, { borderColor: colors.border }]}
              onPress={() => navigation.goBack()}
              disabled={isSaving}
            >
              <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.button,
                styles.saveButton,
                { backgroundColor: colors.primary },
                isSaving && { opacity: 0.7 }
              ]}
              onPress={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Text style={[styles.buttonText, styles.saveButtonText, { color: colors.background }]}>
                  Save Changes
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        mode="date"
        onConfirm={handleDateConfirm}
        onCancel={hideDatePicker}
        maximumDate={new Date()} // Prevents selecting future dates
        date={parseDateString(formData.birthday)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.bold,
  },
  backButton: {
    padding: 4,
  },
  rightPlaceholder: {
    width: 32,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 20,
    gap: 24,
  },
  inputContainer: {
    gap: 8,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  label: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  input: {
    height: 54,
    borderRadius: 10,
    borderWidth: 1,
    paddingHorizontal: 15,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  dateText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    paddingVertical: 15,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    gap: 12,
    marginBottom: 20,
  },
  button: {
    height: 54,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  cancelButton: {
    borderWidth: 1,
  },
  saveButton: {
    backgroundColor: '#ADD8E6',
  },
  buttonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  saveButtonText: {
    color: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  requiredStar: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  helperText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
    marginLeft: 4,
  },
  requiredFieldsNote: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  datePickerInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  calendarIcon: {
    marginRight: 15,
  },
}); 