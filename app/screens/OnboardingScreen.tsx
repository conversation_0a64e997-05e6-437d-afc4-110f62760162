import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Keyboard
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { useTheme } from '../theme/ThemeProvider';
import { useProfile } from '../context/ProfileContext';
import { typography } from '../theme/typography';
import { format } from 'date-fns';

const { width } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  inputType?: 'text' | 'date' | 'numeric' | 'select';
  inputPlaceholder?: string;
  inputLabel?: string;
  options?: string[];
}

interface UserProfile {
  name: string;
  birthday: string;
  weight: string;
  height: string;
  fitnessGoal: string;
}

export default function OnboardingScreen() {
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const { colors } = useTheme();
  const { setProfile: saveProfileToContext } = useProfile();
  const scrollX = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const buttonOpacity = useRef(new Animated.Value(1)).current;

  const [profile, setProfileState] = useState<UserProfile>({
    name: '',
    birthday: '',
    weight: '',
    height: '',
    fitnessGoal: 'General Fitness',
  });

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Lotus',
      description: "Let's set up your profile to personalize your experience.",
    },
    {
      id: 'name',
      title: "What's your name?",
      description: "We'll use this to personalize your experience.",
      inputType: 'text',
      inputPlaceholder: 'Enter your name',
      inputLabel: 'Name',
    },
    {
      id: 'birthday',
      title: "When's your birthday?",
      description: "This helps us provide age-appropriate insights.",
      inputType: 'date',
      inputPlaceholder: 'Select your birthday',
      inputLabel: 'Birthday',
    },
    {
      id: 'weight',
      title: "What's your current weight?",
      description: "Used for tracking progress and calculations.",
      inputType: 'numeric',
      inputPlaceholder: 'Enter weight (lbs)',
      inputLabel: 'Weight (lbs)',
    },
    {
      id: 'height',
      title: "What's your height?",
      description: "Helps in calculating metrics like BMI.",
      inputType: 'numeric',
      inputPlaceholder: 'Enter height (inches)',
      inputLabel: 'Height (in)',
    },
    {
      id: 'fitnessGoal',
      title: "What's your main fitness goal?",
      description: "Helps tailor recommendations and insights.",
      inputType: 'select',
      inputLabel: 'Fitness Goal',
      options: ['Weight Loss', 'Muscle Gain', 'Endurance', 'General Fitness'],
    },
    {
      id: 'complete',
      title: "You're all set!",
      description: "Your profile is ready. Let's start your health journey!",
    },
  ];

  const saveProfile = async () => {
    Keyboard.dismiss();
    setIsLoading(true);
    try {
      if (!profile.name.trim() || !profile.birthday || !profile.weight.trim() || !profile.height.trim()) {
        Alert.alert('Incomplete Profile', 'Please fill in all required fields before finishing.');
        setIsLoading(false);
        const firstInvalidIndex = steps.findIndex(step => step.inputLabel && !profile[step.id as keyof UserProfile]);
        if (firstInvalidIndex !== -1 && firstInvalidIndex < currentIndex) {
           scrollViewRef.current?.scrollTo({ x: width * firstInvalidIndex, animated: true });
        }
        return;
      }
      await saveProfileToContext(profile);
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    Keyboard.dismiss();
    if (isNextDisabled()) return;

    if (currentIndex < steps.length - 1) {
      scrollViewRef.current?.scrollTo({ x: width * (currentIndex + 1), animated: true });
    } else {
      saveProfile();
    }
  };

  const handleBack = () => {
    Keyboard.dismiss();
    if (currentIndex > 0) {
      scrollViewRef.current?.scrollTo({ x: width * (currentIndex - 1), animated: true });
    }
  };

  const showDatePicker = () => {
    Keyboard.dismiss();
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirmDate = (date: Date) => {
    const formattedDate = format(date, 'yyyy-MM-dd');
    handleInputChange('birthday', formattedDate);
    hideDatePicker();
  };

  const handleInputChange = (key: keyof UserProfile, value: string) => {
    if ((key === 'weight' || key === 'height') && value && !/^[0-9]*\.?[0-9]*$/.test(value)) {
      return;
    }
    setProfileState(prev => ({ ...prev, [key]: value }));
  };

  const renderInput = (step: OnboardingStep) => {
    const key = step.id as keyof UserProfile;

    switch (step.inputType) {
      case 'text':
      case 'numeric':
        return (
          <View style={styles.inputContainer}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>{step.inputLabel}</Text>
            <TextInput
              style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
              placeholder={step.inputPlaceholder}
              placeholderTextColor={colors.textTertiary}
              value={profile[key] as string}
              onChangeText={(value) => handleInputChange(key, value)}
              keyboardType={step.inputType === 'numeric' ? 'numeric' : 'default'}
              autoCapitalize={step.id === 'name' ? 'words' : 'none'}
              returnKeyType="done"
            />
          </View>
        );
      case 'date':
        return (
          <View style={styles.inputContainer}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>{step.inputLabel}</Text>
            <TouchableOpacity
              style={[styles.input, styles.dateInputTouchable, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={showDatePicker}
            >
              <Text style={[styles.dateInputText, { color: profile.birthday ? colors.text : colors.textTertiary }]}>
                {profile.birthday ? format(new Date(profile.birthday), 'MMMM d, yyyy') : step.inputPlaceholder}
              </Text>
              <Ionicons name="calendar-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        );
      case 'select':
        return (
          <View style={styles.inputContainer}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>{step.inputLabel}</Text>
            <View style={styles.optionsContainer}>
              {step.options?.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: profile.fitnessGoal === option ? colors.primary : colors.surface,
                      borderColor: profile.fitnessGoal === option ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => handleInputChange('fitnessGoal', option)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      { color: profile.fitnessGoal === option ? colors.background : colors.text }
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  const isNextDisabled = () => {
    const step = steps[currentIndex];
    if (!step.inputLabel) return false;
    const key = step.id as keyof UserProfile;
    const value = profile[key];
    return isLoading || !value || (typeof value === 'string' && !value.trim());
  };

  useEffect(() => {
    const disabled = isNextDisabled();
    Animated.timing(buttonOpacity, {
      toValue: disabled ? 0.5 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [currentIndex, profile, isLoading]);

  const handleMomentumScrollEnd = (event: any) => {
    const newIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top', 'bottom']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 20 : 0}
      >
        {currentIndex > 0 && steps[currentIndex].id !== 'complete' && (
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={colors.primary} />
          </TouchableOpacity>
        )}

        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          scrollEventThrottle={16}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false }
          )}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          keyboardShouldPersistTaps="always"
          style={{ flex: 1 }}
        >
          {steps.map((step, index) => (
            <View key={index} style={styles.slideContainer}>
              <View style={styles.slideContent}>
                 <Text style={[styles.title, { color: colors.text }]}>{step.title}</Text>
                 <Text style={[styles.description, { color: colors.textSecondary }]}>{step.description}</Text>
                 {renderInput(step)}
              </View>
            </View>
          ))}
        </ScrollView>

        <View style={styles.bottomContainer}>
          <View style={styles.pagination}>
            {steps.map((_, index) => {
              const inputRange = [(index - 1) * width, index * width, (index + 1) * width];
              const dotOpacity = scrollX.interpolate({
                inputRange,
                outputRange: [0.3, 1, 0.3],
                extrapolate: 'clamp',
              });
              const dotWidth = scrollX.interpolate({
                inputRange,
                outputRange: [8, 16, 8],
                extrapolate: 'clamp',
              });
              return <Animated.View key={index} style={[styles.dot, { width: dotWidth, opacity: dotOpacity, backgroundColor: colors.primary }]} />;
            })}
          </View>

          <Animated.View style={{ width: '100%', opacity: buttonOpacity }}>
            <TouchableOpacity
              style={[
                styles.nextButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={handleNext}
              disabled={isNextDisabled()}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.background} />
              ) : (
                <Text style={styles.nextButtonText}>
                  {currentIndex === steps.length - 1 ? 'Finish & Start' : 'Next'}
                </Text>
              )}
            </TouchableOpacity>
          </Animated.View>
        </View>

        <DateTimePickerModal
          isVisible={isDatePickerVisible}
          mode="date"
          onConfirm={handleConfirmDate}
          onCancel={hideDatePicker}
          maximumDate={new Date()}
          date={profile.birthday ? new Date(profile.birthday) : new Date(new Date().setFullYear(new Date().getFullYear() - 18))}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 15 : 25,
    left: 15,
    zIndex: 10,
    padding: 10,
  },
  slideContainer: {
    width: width,
    flex: 1,
    justifyContent: 'center',
    paddingBottom: 150,
  },
  slideContent: {
     paddingHorizontal: 30,
     alignItems: 'center',
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamily.bold,
    textAlign: 'center',
    marginBottom: 15,
  },
  description: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
    paddingHorizontal: 10,
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 15,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  dateInputTouchable: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 15,
  },
  dateInputText: {
     fontSize: typography.sizes.md,
     fontFamily: typography.fontFamily.regular,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
  },
  optionButton: {
    width: '48%',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  optionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: Platform.OS === 'ios' ? 30 : 20,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  nextButton: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.bold,
  },
});