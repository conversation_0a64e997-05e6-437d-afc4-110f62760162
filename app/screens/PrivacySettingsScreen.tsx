import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import AnimatedSettingsItem from '../components/AnimatedSettingsItem';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PrivacySettings {
  dataCollection: boolean;
  locationTracking: boolean;
  analyticsSharing: boolean;
  personalizedContent: boolean;
  thirdPartySharing: boolean;
  marketingEmails: boolean;
}

const PrivacySettingsScreen: React.FC = () => {
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState<PrivacySettings>({
    dataCollection: true,
    locationTracking: false,
    analyticsSharing: true,
    personalizedContent: true,
    thirdPartySharing: false,
    marketingEmails: true,
  });

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await AsyncStorage.getItem('privacySettings');
        if (savedSettings) {
          setSettings(JSON.parse(savedSettings));
        }
      } catch (error) {
        console.error('Error loading privacy settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings when changed
  const saveSettings = async (newSettings: PrivacySettings) => {
    setIsSaving(true);
    try {
      await AsyncStorage.setItem('privacySettings', JSON.stringify(newSettings));
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle toggle changes
  const handleToggle = (key: keyof PrivacySettings) => (value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  // Handle data deletion request
  const handleDataDeletion = () => {
    Alert.alert(
      'Delete All Data',
      'Are you sure you want to request deletion of all your data? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Simulate API call
            setIsSaving(true);
            setTimeout(() => {
              setIsSaving(false);
              Alert.alert(
                'Request Submitted',
                'Your data deletion request has been submitted. You will receive a confirmation email shortly.',
                [{ text: 'OK' }]
              );
            }, 1500);
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Privacy Settings</Text>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {isSaving && (
          <View style={[styles.savingOverlay, { backgroundColor: colors.background + 'CC' }]}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.savingText, { color: colors.text }]}>Saving...</Text>
          </View>
        )}
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Data Collection
          </Text>
          
          <AnimatedSettingsItem
            title="Usage Data Collection"
            description="Allow us to collect data about how you use the app to improve our services"
            icon="analytics"
            type="toggle"
            value={settings.dataCollection}
            onToggle={handleToggle('dataCollection')}
            iconColor={colors.primary}
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Location Tracking"
            description="Allow us to track your location for workout mapping features"
            icon="location"
            type="toggle"
            value={settings.locationTracking}
            onToggle={handleToggle('locationTracking')}
            iconColor="#FF9500"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Analytics Sharing"
            description="Share anonymous usage data to help us improve the app"
            icon="bar-chart"
            type="toggle"
            value={settings.analyticsSharing}
            onToggle={handleToggle('analyticsSharing')}
            iconColor="#5856D6"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Personalization
          </Text>
          
          <AnimatedSettingsItem
            title="Personalized Content"
            description="Receive content tailored to your preferences and activity"
            icon="person"
            type="toggle"
            value={settings.personalizedContent}
            onToggle={handleToggle('personalizedContent')}
            iconColor="#34C759"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Third-Party Data Sharing"
            description="Allow sharing of your data with trusted partners"
            icon="share"
            type="toggle"
            value={settings.thirdPartySharing}
            onToggle={handleToggle('thirdPartySharing')}
            iconColor="#FF3B30"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Marketing Emails"
            description="Receive emails about new features and promotions"
            icon="mail"
            type="toggle"
            value={settings.marketingEmails}
            onToggle={handleToggle('marketingEmails')}
            iconColor="#007AFF"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
        </View>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Your Data
          </Text>
          
          <AnimatedSettingsItem
            title="Export Your Data"
            description="Download a copy of all your personal data"
            icon="download"
            type="button"
            onPress={() => {
              Alert.alert(
                'Export Data',
                'Your data export request has been submitted. You will receive an email with download instructions shortly.',
                [{ text: 'OK' }]
              );
            }}
            iconColor="#30B0C7"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
          
          <AnimatedSettingsItem
            title="Delete All Data"
            description="Request permanent deletion of all your data"
            icon="trash"
            type="button"
            onPress={handleDataDeletion}
            iconColor="#FF3B30"
            backgroundColor={colors.card}
            textColor={colors.text}
            descriptionColor={colors.textSecondary}
          />
        </View>
        
        <View style={styles.infoSection}>
          <Text style={[styles.infoTitle, { color: colors.text }]}>
            About Your Privacy
          </Text>
          <Text style={[styles.infoText, { color: colors.textSecondary }]}>
            At Lotus, we take your privacy seriously. We only collect data that helps us improve your experience and provide better services. You can change your privacy settings at any time.
          </Text>
          <Text style={[styles.infoText, { color: colors.textSecondary, marginTop: 12 }]}>
            For more information, please read our{' '}
            <Text style={[styles.linkText, { color: colors.primary }]}>
              Privacy Policy
            </Text>{' '}
            and{' '}
            <Text style={[styles.linkText, { color: colors.primary }]}>
              Terms of Service
            </Text>
            .
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 16,
    marginLeft: 4,
  },
  infoSection: {
    marginTop: 8,
    marginBottom: 24,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    borderStyle: 'dashed',
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
  },
  linkText: {
    fontFamily: typography.fontFamily.medium,
    textDecorationLine: 'underline',
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  savingText: {
    marginTop: 12,
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
  },
});

export default PrivacySettingsScreen;
