import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  Platform,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import { useProfile } from '../context/ProfileContext';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { MainTabParamList } from '../navigation/MainTabNavigator';
import Header from '../components/common/Header';
import { getCurrentWeather, WeatherData } from '../services/nutritionService';
import { getTodaysHighlights, Highlight } from '../services/dashboardService';
import ProgressSummaryCard from '../components/ProgressSummaryCard';
import DailyDashboardCard from '../components/dashboard/DailyDashboardCard';
import CollapsibleContainer from '../components/dashboard/CollapsibleContainer';
import DashboardSection from '../components/dashboard/DashboardSection';
import { useAuth } from '../context/AuthContext';

export default function DashboardScreen() {
  const { colors } = useTheme();
  const { profile } = useProfile();
  const navigation = useNavigation<BottomTabNavigationProp<MainTabParamList>>();
  const { tokens } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loadingWeather, setLoadingWeather] = useState(true);
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [loadingHighlights, setLoadingHighlights] = useState(true);

  const loadData = async (forceRefresh = false) => {
    if (forceRefresh) {
      setRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      await Promise.all([
        loadWeatherData(forceRefresh),
        loadHighlights(forceRefresh),
      ]);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      if (forceRefresh) {
        setRefreshing(false);
      } else {
        setIsLoading(false);
      }
    }
  };

  const loadWeatherData = async (forceRefresh = false) => {
    setLoadingWeather(true);
    try {
      const weather = await getCurrentWeather(forceRefresh);
      setWeatherData(weather);
    } catch (error) {
      console.error('Error loading weather data:', error);
      setWeatherData({ unavailable: true } as WeatherData);
    } finally {
      setLoadingWeather(false);
    }
  };

  const loadHighlights = async (forceRefresh = false) => {
    setLoadingHighlights(true);
    try {
      const todayHighlights = await getTodaysHighlights(forceRefresh);
      setHighlights(todayHighlights);
    } catch (error) {
      console.error('Error loading highlights:', error);
    } finally {
      setLoadingHighlights(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const onRefresh = () => {
    loadData(true);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return `Good morning, ${profile?.name || 'User'}`;
    if (hour < 18) return `Good afternoon, ${profile?.name || 'User'}`;
    return `Good evening, ${profile?.name || 'User'}`;
  };

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Header
        title="Dashboard"
        weatherData={weatherData}
        loadingWeather={loadingWeather}
        onRefreshWeather={() => loadWeatherData(true)}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={colors.primary} />
        }
      >
        <Animated.View entering={FadeIn.duration(500)}>
          <Text style={[styles.greetingText, { color: colors.text }]}>{getGreeting()}</Text>
        </Animated.View>

        <DashboardSection title="Today's Highlights" icon="sparkles-outline">
          {loadingHighlights ? (
            <ActivityIndicator color={colors.primary} style={{ marginTop: 20 }} />
          ) : (
            <DailyDashboardCard highlights={highlights} />
          )}
        </DashboardSection>

        <DashboardSection title="Your Progress" icon="trending-up-outline">
          <ProgressSummaryCard title="Weight" value="185 lbs" icon="barbell-outline" />
        </DashboardSection>

        <DashboardSection title="Quick Actions" icon="flash-outline">
          <View style={styles.quickActionsContainer}>
            <TouchableOpacity style={[styles.quickActionButton, { backgroundColor: colors.card }]} onPress={() => navigation.navigate('Diet')}>
              <Ionicons name="restaurant-outline" size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>Log Meal</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.quickActionButton, { backgroundColor: colors.card }]} onPress={() => navigation.navigate('Strength')}>
              <Ionicons name="barbell-outline" size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>Log Workout</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.quickActionButton, { backgroundColor: colors.card }]} onPress={() => navigation.navigate('Digest')}>
              <Ionicons name="list-outline" size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>View Digest</Text>
            </TouchableOpacity>
          </View>
        </DashboardSection>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    padding: 16,
  },
  greetingText: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 24,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  quickActionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginTop: 8,
  },
});
