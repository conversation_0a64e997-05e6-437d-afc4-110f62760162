# Lotus App - Build & Distribution Guide

This guide explains how to build and distribute beta versions of the Lotus app using Expo EAS Build.

## 🚀 Quick Start

### Prerequisites

1. **Expo Account**: Create a free account at [expo.dev](https://expo.dev)
2. **EAS CLI**: Already installed via the setup script
3. **Apple Developer Account** (optional, for iOS TestFlight distribution)

### Initial Setup

1. **Login to EAS**:
   ```bash
   eas login
   ```

2. **Configure your project**:
   ```bash
   eas build:configure
   ```
   This will update your `app.json` with a project ID.

3. **Update app.json**:
   - Replace `"your-expo-username"` with your actual Expo username
   - The project ID will be automatically generated

## 📱 Build Types

### 1. Development Build
**Best for**: Active development and testing with hot reload
- Creates a custom development client
- Includes all your native dependencies
- Works like Expo Go but with your specific setup

```bash
# Build for Android (APK)
eas build --profile development --platform android

# Build for iOS
eas build --profile development --platform ios
```

### 2. Preview Build
**Best for**: Beta testing with stakeholders
- Standalone app that doesn't require Expo Go
- Internal distribution only
- Perfect for sharing with testers

```bash
# Build for Android (APK)
eas build --profile preview --platform android

# Build for iOS (Simulator)
eas build --profile preview --platform ios
```

### 3. Production Build
**Best for**: App Store submission (when ready)
- Optimized for production
- Creates AAB for Android, IPA for iOS

```bash
# Build for production
eas build --profile production --platform all
```

## 🔄 Easy Build Script

Use the interactive script for a guided experience:

```bash
./scripts/build-and-distribute.sh
```

This script provides a menu-driven interface for all build operations.

## 📤 Distribution Methods

### Option 1: Direct APK Distribution (Android)
1. Build with `preview` profile for Android
2. Download the APK from your Expo dashboard
3. Share the APK file directly with testers
4. Testers enable "Install from unknown sources" and install

### Option 2: Expo Dashboard Links
1. Go to [expo.dev/accounts/[username]/projects/lotus/builds](https://expo.dev)
2. Find your build and click "Install"
3. Share the generated link with testers
4. Testers can install directly from the link

### Option 3: TestFlight (iOS - Requires Apple Developer Account)
1. Build with `preview` or `production` profile
2. Download the IPA file
3. Upload to App Store Connect
4. Add testers to TestFlight
5. Distribute via TestFlight

### Option 4: Internal Distribution Services
- **Firebase App Distribution**: Free, supports both iOS and Android
- **Microsoft App Center**: Free tier available
- **Diawi**: Simple file sharing for iOS/Android

## 🛠 Advanced Configuration

### Custom Build Profiles

You can create additional build profiles in `eas.json`:

```json
{
  "build": {
    "staging": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_API_URL": "https://staging-api.yourapp.com"
      }
    }
  }
}
```

### Environment Variables

Set different API endpoints for different builds:

```json
{
  "build": {
    "preview": {
      "env": {
        "EXPO_PUBLIC_API_URL": "https://staging-api.yourapp.com"
      }
    },
    "production": {
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api.yourapp.com"
      }
    }
  }
}
```

## 📋 Testing Workflow

### For Development
1. Build development client once
2. Install on your device
3. Use `expo start --dev-client` for hot reload during development

### For Beta Testing
1. Build preview version
2. Share link or APK with testers
3. Collect feedback
4. Iterate and rebuild as needed

### For Stakeholder Reviews
1. Build preview version
2. Create a simple landing page with download links
3. Include release notes and known issues

## 🔧 Troubleshooting

### Common Issues

1. **Build fails with native dependencies**:
   - Ensure all native dependencies are properly configured
   - Check that plugins are correctly listed in `app.json`

2. **iOS build fails**:
   - Verify bundle identifier is unique
   - Check that iOS configuration is correct

3. **Android build fails**:
   - Verify package name is unique
   - Check Android permissions and configuration

### Getting Help

- **Expo Documentation**: [docs.expo.dev](https://docs.expo.dev)
- **EAS Build Docs**: [docs.expo.dev/build/introduction](https://docs.expo.dev/build/introduction)
- **Community Forum**: [forums.expo.dev](https://forums.expo.dev)

## 💡 Best Practices

1. **Version Management**: Update version in `app.json` for each build
2. **Release Notes**: Keep track of changes for each build
3. **Testing**: Test on multiple devices before distributing
4. **Feedback**: Set up a simple feedback collection system
5. **Rollback Plan**: Keep previous working builds available

## 🎯 Recommended Beta Distribution Flow

1. **Week 1**: Development builds for core team
2. **Week 2**: Preview builds for internal stakeholders
3. **Week 3**: Preview builds for external beta testers
4. **Week 4**: Production build preparation

This approach ensures quality while getting feedback early and often. 