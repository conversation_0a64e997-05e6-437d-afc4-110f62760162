// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add the following to resolve Node.js built-in modules used by libraries like axios
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  crypto: require.resolve('crypto-browserify'),
  stream: require.resolve('stream-browserify'),
  assert: require.resolve('assert/'),
  url: require.resolve('url/'),
  http: require.resolve('stream-http'),
  https: require.resolve('stream-http'),
  zlib: require.resolve('zlib-browserify'),
  process: require.resolve('process/browser'),
  buffer: require.resolve('buffer/'),
  util: require.resolve('util/'),
  events: require.resolve('events/'),
};

// Add 'cjs' to sourceExts if not already present
config.resolver.sourceExts.push('cjs');

module.exports = config;
