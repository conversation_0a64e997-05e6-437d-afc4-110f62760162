{"name": "lotus", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "dev-client": "expo start --dev-client", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "build:ios": "eas build --profile preview --platform ios", "distribute": "./scripts/build-and-distribute.sh", "setup:eas": "eas build:configure"}, "dependencies": {"@babel/preset-env": "^7.26.9", "@babel/runtime": "^7.27.0", "@expo-google-fonts/inter": "^0.2.3", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@types/date-fns": "^2.5.3", "assert": "^2.1.0", "axios": "^1.8.4", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "53.0.11", "expo-asset": "~11.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "~18.1.5", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "jwt-decode": "^4.0.0", "moti": "^0.27.4", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.12.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-charts": "^5.4.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5", "zlib-browserify": "^0.0.3", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.0", "typescript": "~5.8.3"}, "private": true}