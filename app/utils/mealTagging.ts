/**
 * Utility functions for generating and managing meal tags
 */

import { MealData } from '../services/conversationService';

// Common meal types
const MEAL_TYPES = {
  breakfast: ['breakfast', 'morning', 'brunch', 'oatmeal', 'cereal', 'pancake', 'waffle', 'egg', 'toast', 'bagel', 'muffin', 'smoothie'],
  lunch: ['lunch', 'sandwich', 'wrap', 'salad', 'soup', 'bowl'],
  dinner: ['dinner', 'supper', 'evening meal', 'roast', 'steak', 'casserole'],
  snack: ['snack', 'appetizer', 'small plate', 'finger food', 'dip'],
  dessert: ['dessert', 'sweet', 'cake', 'cookie', 'ice cream', 'pudding', 'pie', 'chocolate', 'candy']
};

// Common protein sources
const PROTEIN_SOURCES = {
  chicken: ['chicken', 'poultry'],
  beef: ['beef', 'steak', 'ground beef', 'burger'],
  pork: ['pork', 'ham', 'bacon', 'sausage'],
  fish: ['fish', 'salmon', 'tuna', 'cod', 'tilapia', 'halibut', 'seafood', 'shrimp', 'prawn'],
  vegetarian: ['vegetarian', 'plant-based', 'meatless'],
  vegan: ['vegan', 'plant-based'],
  tofu: ['tofu', 'tempeh', 'seitan'],
  beans: ['beans', 'lentils', 'chickpeas', 'legumes'],
  eggs: ['egg', 'eggs', 'omelette', 'frittata']
};

// Common cuisines
const CUISINES = {
  italian: ['italian', 'pasta', 'pizza', 'risotto', 'lasagna'],
  mexican: ['mexican', 'taco', 'burrito', 'quesadilla', 'enchilada', 'tortilla'],
  asian: ['asian', 'chinese', 'japanese', 'thai', 'vietnamese', 'korean', 'stir-fry', 'sushi', 'curry'],
  indian: ['indian', 'curry', 'masala', 'tikka', 'tandoori'],
  mediterranean: ['mediterranean', 'greek', 'hummus', 'falafel', 'tahini', 'olive oil'],
  american: ['american', 'burger', 'fries', 'hot dog', 'bbq', 'barbecue'],
  french: ['french', 'croissant', 'baguette', 'ratatouille'],
  middle_eastern: ['middle eastern', 'falafel', 'hummus', 'shawarma', 'kebab']
};

// Diet types
const DIET_TYPES = {
  keto: ['keto', 'ketogenic', 'low-carb', 'high-fat'],
  paleo: ['paleo', 'paleolithic', 'caveman'],
  low_carb: ['low-carb', 'low carb', 'carb-free'],
  high_protein: ['high-protein', 'high protein', 'protein-rich'],
  gluten_free: ['gluten-free', 'gluten free', 'no gluten'],
  dairy_free: ['dairy-free', 'dairy free', 'no dairy', 'lactose-free'],
  nut_free: ['nut-free', 'nut free', 'no nuts'],
  vegetarian: ['vegetarian', 'no meat'],
  vegan: ['vegan', 'plant-based', 'no animal products']
};

// Cooking methods
const COOKING_METHODS = {
  baked: ['bake', 'baked', 'roast', 'roasted', 'oven'],
  grilled: ['grill', 'grilled', 'barbecue', 'bbq'],
  fried: ['fry', 'fried', 'pan-fried', 'deep-fried'],
  steamed: ['steam', 'steamed'],
  slow_cooked: ['slow cook', 'slow cooker', 'crockpot', 'crock pot'],
  instant_pot: ['instant pot', 'pressure cook', 'pressure cooker'],
  raw: ['raw', 'no-cook', 'uncooked'],
  air_fryer: ['air fry', 'air fryer']
};

// Meal characteristics
const CHARACTERISTICS = {
  quick: ['quick', 'fast', 'easy', 'simple', '30 minute', '15 minute', '20 minute'],
  healthy: ['healthy', 'nutritious', 'nutrient-dense', 'superfood'],
  comfort: ['comfort', 'hearty', 'filling', 'cozy', 'warm'],
  spicy: ['spicy', 'hot', 'chili', 'jalapeno', 'cayenne'],
  sweet: ['sweet', 'sugary', 'dessert'],
  savory: ['savory', 'umami', 'rich'],
  light: ['light', 'refreshing', 'fresh'],
  seasonal: ['seasonal', 'summer', 'winter', 'fall', 'spring', 'holiday']
};

/**
 * Extracts tags from meal data based on title, description, and ingredients
 * @param meal The meal data to extract tags from
 * @returns An array of tags
 */
export function extractTagsFromMeal(meal: MealData): string[] {
  if (!meal) return [];

  const tags: Set<string> = new Set();
  const searchText = `${meal.title} ${meal.description} ${meal.ingredients?.join(' ') || ''}`.toLowerCase();

  // Add time of day tag based on meal title and description
  for (const [type, keywords] of Object.entries(MEAL_TYPES)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(type);
    }
  }

  // Add protein source tags
  for (const [protein, keywords] of Object.entries(PROTEIN_SOURCES)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(protein);
    }
  }

  // Add cuisine tags
  for (const [cuisine, keywords] of Object.entries(CUISINES)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(cuisine);
    }
  }

  // Add diet type tags
  for (const [diet, keywords] of Object.entries(DIET_TYPES)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(diet);
    }
  }

  // Add cooking method tags
  for (const [method, keywords] of Object.entries(COOKING_METHODS)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(method);
    }
  }

  // Add characteristic tags
  for (const [characteristic, keywords] of Object.entries(CHARACTERISTICS)) {
    if (keywords.some(keyword => searchText.includes(keyword))) {
      tags.add(characteristic);
    }
  }

  // Add calorie-based tags
  if (meal.calories) {
    if (meal.calories < 300) tags.add('low-calorie');
    else if (meal.calories > 600) tags.add('high-calorie');
  }

  // Add macronutrient-based tags
  if (meal.protein && meal.calories) {
    // If protein makes up more than 30% of calories
    if ((meal.protein * 4) / meal.calories > 0.3) tags.add('high-protein');
  }

  if (meal.carbs && meal.calories) {
    // If carbs make up less than 15% of calories
    if ((meal.carbs * 4) / meal.calories < 0.15) tags.add('low-carb');
    // If carbs make up more than 60% of calories
    else if ((meal.carbs * 4) / meal.calories > 0.6) tags.add('high-carb');
  }

  if (meal.fat && meal.calories) {
    // If fat makes up more than 40% of calories
    if ((meal.fat * 9) / meal.calories > 0.4) tags.add('high-fat');
  }

  // If we have existing tags, include them
  if (meal.tags && Array.isArray(meal.tags)) {
    meal.tags.forEach(tag => tags.add(tag.toLowerCase()));
  }

  // Ensure we always have at least one meal type tag
  if (!tags.has('breakfast') && !tags.has('lunch') && !tags.has('dinner') && !tags.has('snack') && !tags.has('dessert')) {
    // Default to the most likely meal type based on calories
    if (meal.calories) {
      if (meal.calories < 200) tags.add('snack');
      else if (meal.calories < 400) tags.add('breakfast');
      else if (meal.calories < 600) tags.add('lunch');
      else tags.add('dinner');
    } else {
      // If no calories, make an educated guess based on title
      const title = meal.title.toLowerCase();
      if (title.includes('breakfast') || title.includes('morning')) tags.add('breakfast');
      else if (title.includes('lunch')) tags.add('lunch');
      else if (title.includes('dinner') || title.includes('supper')) tags.add('dinner');
      else if (title.includes('snack') || title.includes('appetizer')) tags.add('snack');
      else if (title.includes('dessert') || title.includes('sweet') || title.includes('cake') || title.includes('cookie')) tags.add('dessert');
      else tags.add('meal'); // Generic fallback
    }
  }

  return Array.from(tags);
}

/**
 * Determines the primary category for a meal based on its tags
 * @param meal The meal data or tags to categorize
 * @returns The primary category (Breakfast, Lunch, Dinner, Snack, Dessert, or Other)
 */
export function getPrimaryCategory(meal: MealData | string[]): string {
  const tags = Array.isArray(meal) ? meal : (meal.tags || []);
  const lowerTags = tags.map(tag => tag.toLowerCase());

  // Check for meal type tags in priority order
  if (lowerTags.some(tag => tag === 'breakfast' || tag.includes('breakfast'))) return 'Breakfast';
  if (lowerTags.some(tag => tag === 'lunch' || tag.includes('lunch'))) return 'Lunch';
  if (lowerTags.some(tag => tag === 'dinner' || tag.includes('dinner'))) return 'Dinner';
  if (lowerTags.some(tag => tag === 'snack' || tag.includes('snack'))) return 'Snack';
  if (lowerTags.some(tag => tag === 'dessert' || tag.includes('dessert'))) return 'Dessert';

  // If no meal type tags, check the title and description
  if (!Array.isArray(meal)) {
    const searchText = `${meal.title} ${meal.description}`.toLowerCase();
    
    for (const [type, keywords] of Object.entries(MEAL_TYPES)) {
      if (keywords.some(keyword => searchText.includes(keyword))) {
        return type.charAt(0).toUpperCase() + type.slice(1);
      }
    }
  }

  return 'Other';
}
