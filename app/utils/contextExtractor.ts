import {
  addDietaryRestriction,
  addInjury,
  addLifeUpdate,
  addPreference,
  addGoal,
  addChatSummary,
  addWeightHistory,
  ContextType
} from '../services/contextService';

// Extract dietary restrictions from chat messages
export function extractDietaryRestrictions(message: string): string[] {
  // Look for common dietary restriction patterns
  const dietaryPatterns = [
    // Dietary lifestyles
    /(?:I (?:am|'m)(?: a)? (vegan|vegetarian|pescatarian|flexitarian|paleo|keto))/i,

    // Allergies and intolerances with "to" construction
    /(?:I (?:don't|do not|cannot) eat|I'm allergic to|I have an allergy to|I can't eat) ([\w\s]+)/i,

    // Allergies and intolerances with direct naming
    /(?:I have (?:a|an)) ([\w\s]+) (?:allergy|intolerance|sensitivity)/i,
    /(?:I have) ([\w\s]+) (?:allergy|intolerance|sensitivity)/i,

    // Specific food allergies
    /(?:I'm allergic to|I have an? allergy to|I can't eat|I cannot eat) ((?:dairy|gluten|nuts|peanuts|shellfish|soy|eggs|fish|wheat|milk|tree nuts|sesame|mustard|celery|lupin|sulphites|sulphur dioxide|molluscs|crustaceans))/i,

    // Diet plans
    /(?:I'm|I am) (?:on a|following a|doing|trying) ([\w\s]+) (?:diet|eating plan|nutrition plan|protocol)/i,

    // Avoidance patterns
    /(?:I avoid|I don't consume|I don't eat|I stay away from) ([\w\s]+)/i,
    /(?:I can't have|I cannot have|I must avoid) ([\w\s]+)/i,

    // Direct statements about food restrictions
    /(?:I need to avoid|I need to stay away from|I'm not allowed to eat|I'm not supposed to eat) ([\w\s]+)/i,

    // Medical conditions requiring dietary restrictions
    /(?:I have) (celiac disease|diabetes|IBS|irritable bowel syndrome|Crohn's disease|ulcerative colitis|GERD|acid reflux)/i
  ];

  const restrictions: string[] = [];

  for (const pattern of dietaryPatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const restriction = match[1].trim();
      console.log(`Extracted dietary restriction: ${restriction}`);

      // Only add to the array, don't save to context here
      restrictions.push(restriction);
    }
  }

  return restrictions;
}

// Extract injuries from chat messages
export function extractInjuries(message: string): string[] {
  // Look for common injury patterns
  const injuryPatterns = [
    // Direct injury statements
    /(?:I (?:injured|hurt|broke)) (?:my) ([\w\s]+)/i,
    /(?:I have a|I've got a) ([\w\s]+) (?:injury|strain|sprain|pain|tear|fracture|break)/i,

    // Body part conditions
    /(?:My) ([\w\s]+) (?:is|are) (?:injured|hurting|painful|sore|strained|sprained|broken|fractured|torn)/i,

    // Recovery statements
    /(?:I'm dealing with|I'm recovering from) (?:a|an) ([\w\s]+) (?:injury|strain|sprain|tear|fracture|break)/i,

    // Pain descriptions
    /(?:I have pain in|I'm experiencing pain in|It hurts when I move) (?:my) ([\w\s]+)/i,

    // Specific injuries
    /(?:I have) (?:a|an) ((?:ACL|MCL|PCL|rotator cuff|meniscus|labrum|achilles|hamstring|quad|calf|bicep|tricep) (?:tear|strain|injury|rupture))/i,

    // Chronic conditions
    /(?:I have|I suffer from) ((?:arthritis|tendonitis|bursitis|sciatica|plantar fasciitis|shin splints|tennis elbow|golfer's elbow|carpal tunnel|frozen shoulder|disc herniation|slipped disc|bulging disc|spondylosis|spondylolisthesis))/i,

    // Joint problems
    /(?:I have) (?:a|an) ((?:bad|problematic|injured|painful|arthritic|unstable) (?:knee|shoulder|elbow|wrist|ankle|hip|back|neck))/i,

    // Surgery mentions
    /(?:I had|I've had|I recently had) ([\w\s]+) (?:surgery|operation|procedure)/i,

    // Limitations
    /(?:I can't|I cannot|I'm unable to) ((?:bend|lift|twist|raise|extend|flex|rotate|move) (?:my) (?:knee|shoulder|elbow|wrist|ankle|hip|back|neck|arm|leg))/i,

    // Direct statements about broken bones
    /(?:I broke|I've broken|I have broken) (?:my) ([\w\s]+)/i
  ];

  const injuries: string[] = [];

  for (const pattern of injuryPatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const injury = match[1].trim();
      console.log(`Extracted injury: ${injury}`);

      // Only add to the array, don't save to context here
      injuries.push(injury);
    }
  }

  return injuries;
}

// Extract life updates from chat messages
export function extractLifeUpdates(message: string): string[] {
  // Look for common life update patterns
  const lifeUpdatePatterns = [
    /(?:I (?:just|recently)) ([\w\s]+)/i,
    /(?:I've been) ([\w\s]+)/i,
    /(?:I started) ([\w\s]+)/i,
    /(?:I'm going to|I am going to|I plan to) ([\w\s]+)/i,
    /(?:I've decided to|I have decided to) ([\w\s]+)/i
  ];

  const updates: string[] = [];

  for (const pattern of lifeUpdatePatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const update = match[0].trim(); // Use the full match for better context
      console.log(`Extracted life update: ${update}`);

      // Only add to the array, don't save to context here
      updates.push(update);
    }
  }

  return updates;
}

// Extract preferences from chat messages
export function extractPreferences(message: string): string[] {
  // Look for common preference patterns
  const preferencePatterns = [
    /(?:I (?:prefer|like|enjoy)) ([\w\s]+)/i,
    /(?:I (?:don't|do not) (?:like|enjoy|prefer)) ([\w\s]+)/i,
    /(?:My favorite) ([\w\s]+) (?:is|are) ([\w\s]+)/i,
    /(?:I'm a fan of|I love) ([\w\s]+)/i
  ];

  const preferences: string[] = [];

  for (const pattern of preferencePatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const preference = match[0].trim(); // Use the full match for better context
      console.log(`Extracted preference: ${preference}`);

      // Only add to the array, don't save to context here
      preferences.push(preference);
    }
  }

  return preferences;
}

// Extract goals from chat messages
export function extractGoals(message: string): string[] {
  // Look for common goal patterns
  const goalPatterns = [
    /(?:I want to|I'd like to|I would like to) ([\w\s]+)/i,
    /(?:My goal is to) ([\w\s]+)/i,
    /(?:I'm trying to|I am trying to) ([\w\s]+)/i,
    /(?:I aim to|I'm aiming to) ([\w\s]+)/i,
    /(?:I hope to) ([\w\s]+)/i
  ];

  const goals: string[] = [];

  for (const pattern of goalPatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const goal = match[0].trim(); // Use the full match for better context
      console.log(`Extracted goal: ${goal}`);

      // Only add to the array, don't save to context here
      goals.push(goal);
    }
  }

  return goals;
}

// Extract weight from chat messages
export function extractWeight(message: string): number | null {
  // Look for weight patterns
  const weightPatterns = [
    /(?:I (?:weigh|am)) ([\d\.]+) (?:pounds|lbs|lb)/i,
    /(?:My weight is) ([\d\.]+) (?:pounds|lbs|lb)/i,
    /(?:I'm|I am) ([\d\.]+) (?:pounds|lbs|lb)/i
  ];

  for (const pattern of weightPatterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      const weight = parseFloat(match[1]);
      if (!isNaN(weight) && weight > 0) {
        console.log(`Extracted weight: ${weight} lbs`);

        // Only return the weight, don't save to context here
        return weight;
      }
    }
  }

  return null;
}

// Generate a summary of a conversation
export function generateConversationSummary(messages: Array<{role: string, content: string}>): string | null {
  if (messages.length < 3) {
    return null; // Not enough messages to summarize
  }

  // Extract user messages
  const userMessages = messages
    .filter(msg => msg.role === 'user')
    .map(msg => msg.content);

  if (userMessages.length === 0) {
    return null;
  }

  // Create a simple summary
  const summary = `Conversation about: ${userMessages[userMessages.length - 1].substring(0, 100)}`;

  return summary;
}

// Process a chat message to extract context
export async function processMessageForContext(message: string, conversationId?: string, messageId?: string): Promise<void> {
  console.log('[Context Extractor] Processing message for context extraction');

  try {
    // Use the new Context Engine V3
    const {
      contextEngineV3,
      ContextType: NewContextType,
      updateUserPreferences,
      updateDietaryRestrictions,
      updateInjuries
    } = require('../services/contextEngineV3');

    // Generate a unique message ID if not provided
    const uniqueId = messageId || `msg_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

    // 1. First, store the raw message content as chat history
    await contextEngineV3.updateContext(NewContextType.CHAT_HISTORY, {
      message,
      messageId: uniqueId,
      conversationId: conversationId || 'unknown',
      timestamp: new Date().toISOString()
    }, {
      source: 'user_input',
      category: 'chat_message'
    });

    // 2. Extract all context types in a single pass
    const dietaryRestrictions = extractDietaryRestrictions(message);
    const injuries = extractInjuries(message);
    const lifeUpdates = extractLifeUpdates(message);
    const preferences = extractPreferences(message);
    const goals = extractGoals(message);
    const weight = extractWeight(message);

    // 3. Store each type of extracted context using the new context engine

    // Dietary restrictions (critical data - always overwrite for safety)
    if (dietaryRestrictions.length > 0) {
      console.log(`[Context Extractor] Storing ${dietaryRestrictions.length} dietary restrictions`);
      await updateDietaryRestrictions(dietaryRestrictions, {
        overwrite: false, // Add to existing restrictions
        source: 'chat_extraction',
        category: 'extracted_from_chat'
      });
    }

    // Injuries (critical data - always overwrite for safety)
    if (injuries.length > 0) {
      console.log(`[Context Extractor] Storing ${injuries.length} injuries`);
      await updateInjuries(injuries, {
        overwrite: false, // Add to existing injuries
        source: 'chat_extraction',
        category: 'extracted_from_chat'
      });
    }

    // Life updates (add to user profile)
    if (lifeUpdates.length > 0) {
      console.log(`[Context Extractor] Storing ${lifeUpdates.length} life updates`);
      for (const item of lifeUpdates) {
        await contextEngineV3.updateContext(NewContextType.USER_PROFILE, {
          update: item,
          extractedAt: new Date().toISOString()
        }, {
          source: 'chat_extraction',
          category: 'life_update',
          merge: true
        });
      }
    }

    // Preferences (add to existing preferences)
    if (preferences.length > 0) {
      console.log(`[Context Extractor] Storing ${preferences.length} preferences`);
      const preferencesText = preferences.join('; ');
      await updateUserPreferences(preferencesText, {
        overwrite: false, // Add to existing preferences
        source: 'chat_extraction',
        category: 'extracted_from_chat'
      });
    }

    // Goals (add to existing goals)
    if (goals.length > 0) {
      console.log(`[Context Extractor] Storing ${goals.length} goals`);
      for (const item of goals) {
        await contextEngineV3.updateContext(NewContextType.GOALS, {
          goal: item,
          extractedAt: new Date().toISOString()
        }, {
          source: 'chat_extraction',
          category: 'extracted_goal'
        });
      }
    }

    // Weight (add to weight history)
    if (weight !== null) {
      console.log(`[Context Extractor] Storing weight: ${weight}`);
      await contextEngineV3.updateContext(NewContextType.WEIGHT_HISTORY, {
        weight: weight,
        date: new Date().toISOString(),
        source: 'chat_extraction'
      }, {
        source: 'chat_extraction',
        category: 'weight_update'
      });
    }

    console.log('[Context Extractor] Successfully processed and stored all context');
  } catch (error) {
    console.error('[Context Extractor] Error processing message context:', error);
  }
}

// Process a conversation to extract context and generate summary
export async function processConversationForContext(
  messages: Array<{role: string, content: string}>,
  conversationId?: string
): Promise<void> {
  console.log('[Context Extractor] Processing conversation for context extraction');

  if (!messages || messages.length === 0) {
    console.log('[Context Extractor] No messages to process');
    return;
  }

  try {
    // Get the most recent message (which should be the one just added)
    const mostRecentMessage = messages[messages.length - 1];

    // Process only the most recent message for context extraction
    if (mostRecentMessage) {
      const messageId = `conv_${conversationId || 'unknown'}_${Date.now()}`;
      console.log(`[Context Extractor] Processing message with ID: ${messageId}`);

      // Process the message for context extraction
      await processMessageForContext(
        mostRecentMessage.content,
        conversationId,
        messageId
      );
    }

    // Generate a summary of the conversation
    const summary = generateConversationSummary(messages);
    if (summary) {
      const { addContextEntry, ContextType } = require('../services/contextService');
      await addContextEntry({
        type: ContextType.CHAT_SUMMARY,
        value: summary,
        source: 'system',
        messageId: `summary_${Date.now()}`,
        conversationId: conversationId || 'unknown'
      });
      console.log('[Context Extractor] Stored conversation summary');
    }

    console.log('[Context Extractor] Successfully processed conversation');
  } catch (error) {
    console.error('[Context Extractor] Error processing conversation:', error);
  }
}
