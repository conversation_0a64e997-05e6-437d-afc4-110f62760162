import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStoredTokens, AuthTokens } from '../services/auth';

export const debugAuthState = async () => {
  console.log('=== AUTH DEBUG START ===');
  
  try {
    // Check SecureStore split storage
    console.log('1. Checking SecureStore split storage...');
    const [accessToken, idToken, refreshToken, format] = await Promise.all([
      SecureStore.getItemAsync('auth_token_access'),
      SecureStore.getItemAsync('auth_token_id'),
      SecureStore.getItemAsync('auth_token_refresh'),
      SecureStore.getItemAsync('auth_tokens_format')
    ]);
    
    console.log('Split storage results:', {
      format,
      hasAccessToken: !!accessToken,
      hasIdToken: !!idToken,
      hasRefreshToken: !!refreshToken,
      accessTokenLength: accessToken?.length || 0,
      idTokenLength: idToken?.length || 0,
      refreshTokenLength: refreshToken?.length || 0
    });

    // Check legacy SecureStore storage
    console.log('2. Checking legacy SecureStore storage...');
    const legacyTokens = await SecureStore.getItemAsync('auth_tokens');
    console.log('Legacy storage:', {
      hasLegacyTokens: !!legacyTokens,
      legacyTokensLength: legacyTokens?.length || 0
    });

    // Check AsyncStorage fallback
    console.log('3. Checking AsyncStorage fallback...');
    const fallbackTokens = await AsyncStorage.getItem('auth_tokens_fallback');
    console.log('Fallback storage:', {
      hasFallbackTokens: !!fallbackTokens,
      fallbackTokensLength: fallbackTokens?.length || 0
    });

    // Test the getStoredTokens function
    console.log('4. Testing getStoredTokens function...');
    const retrievedTokens = await getStoredTokens();
    console.log('Retrieved tokens:', {
      hasTokens: !!retrievedTokens,
      hasAccessToken: !!(retrievedTokens?.accessToken),
      hasIdToken: !!(retrievedTokens?.idToken),
      hasRefreshToken: !!(retrievedTokens?.refreshToken),
      accessTokenLength: retrievedTokens?.accessToken?.length || 0,
      idTokenLength: retrievedTokens?.idToken?.length || 0,
      refreshTokenLength: retrievedTokens?.refreshToken?.length || 0
    });

    // If we have tokens, validate their structure
    if (retrievedTokens) {
      console.log('5. Validating token structure...');
      const isValid = validateTokenStructure(retrievedTokens);
      console.log('Token validation result:', isValid);
      
      if (retrievedTokens.refreshToken) {
        console.log('6. Refresh token details:', {
          length: retrievedTokens.refreshToken.length,
          startsCorrectly: retrievedTokens.refreshToken.startsWith('ey') || retrievedTokens.refreshToken.length > 50,
          preview: retrievedTokens.refreshToken.substring(0, 20) + '...'
        });
      }
    }

  } catch (error) {
    console.error('Auth debug error:', error);
  }
  
  console.log('=== AUTH DEBUG END ===');
};

const validateTokenStructure = (tokens: AuthTokens): boolean => {
  try {
    // Check if all required fields exist and are non-empty strings
    if (!tokens.accessToken || typeof tokens.accessToken !== 'string' || tokens.accessToken.length === 0) {
      console.log('Invalid access token');
      return false;
    }
    
    if (!tokens.idToken || typeof tokens.idToken !== 'string' || tokens.idToken.length === 0) {
      console.log('Invalid ID token');
      return false;
    }
    
    if (!tokens.refreshToken || typeof tokens.refreshToken !== 'string' || tokens.refreshToken.length === 0) {
      console.log('Invalid refresh token');
      return false;
    }

    // Check if tokens look like JWTs (for access and ID tokens)
    const jwtPattern = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    
    if (!jwtPattern.test(tokens.accessToken)) {
      console.log('Access token does not match JWT pattern');
      return false;
    }
    
    if (!jwtPattern.test(tokens.idToken)) {
      console.log('ID token does not match JWT pattern');
      return false;
    }

    // Refresh token might not be a JWT, so we just check it's a reasonable length
    if (tokens.refreshToken.length < 20) {
      console.log('Refresh token seems too short');
      return false;
    }

    console.log('All tokens passed validation');
    return true;
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
};

export const clearAllAuthData = async () => {
  console.log('=== CLEARING ALL AUTH DATA ===');
  
  try {
    await Promise.allSettled([
      // Clear SecureStore
      SecureStore.deleteItemAsync('auth_tokens_format'),
      SecureStore.deleteItemAsync('auth_token_access'),
      SecureStore.deleteItemAsync('auth_token_id'),
      SecureStore.deleteItemAsync('auth_token_refresh'),
      SecureStore.deleteItemAsync('auth_tokens'),
      
      // Clear AsyncStorage
      AsyncStorage.removeItem('auth_tokens_fallback'),
      AsyncStorage.removeItem('@user_profile')
    ]);
    
    console.log('All auth data cleared successfully');
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
}; 