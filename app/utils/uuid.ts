/**
 * Simple UUID generator for React Native
 * This is a fallback for environments where crypto.getRandomValues() is not available
 */

// Generate a random hexadecimal string of specified length
const randomHex = (length: number): string => {
  let result = '';
  const characters = '0123456789abcdef';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// Generate a UUID v4 format string
export const generateUUID = (): string => {
  // Format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  // Where y is one of: 8, 9, a, or b
  const uuid = [
    randomHex(8),
    randomHex(4),
    // Version 4 UUID always has the third group start with '4'
    '4' + randomHex(3),
    // The first character of the fourth group is limited to 8, 9, a, or b
    '89ab'[Math.floor(Math.random() * 4)] + randomHex(3),
    randomHex(12)
  ].join('-');
  
  return uuid;
};
