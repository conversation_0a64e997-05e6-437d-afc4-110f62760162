import { getStoredTokens } from '../services/auth';
import { api } from '../services/apiClient';

export const debugTokens = async () => {
  try {
    const tokens = await getStoredTokens();
    
    if (!tokens) {
      console.log('[Token Debug] No tokens found');
      return;
    }

    console.log('[Token Debug] Token lengths:', {
      accessToken: tokens.accessToken?.length || 0,
      idToken: tokens.idToken?.length || 0,
      refreshToken: tokens.refreshToken?.length || 0
    });

    // Decode ID token payload (without verification)
    if (tokens.idToken) {
      try {
        const idTokenPayload = JSON.parse(atob(tokens.idToken.split('.')[1]));
        console.log('[Token Debug] ID Token payload:', {
          token_use: idTokenPayload.token_use,
          aud: idTokenPayload.aud,
          iss: idTokenPayload.iss,
          exp: new Date(idTokenPayload.exp * 1000).toISOString(),
          sub: idTokenPayload.sub,
          email: idTokenPayload.email
        });
      } catch (e) {
        console.error('[Token Debug] Failed to decode ID token:', e);
      }
    }

    // Decode access token payload (without verification)
    if (tokens.accessToken) {
      try {
        const accessTokenPayload = JSON.parse(atob(tokens.accessToken.split('.')[1]));
        console.log('[Token Debug] Access Token payload:', {
          token_use: accessTokenPayload.token_use,
          aud: accessTokenPayload.aud,
          iss: accessTokenPayload.iss,
          exp: new Date(accessTokenPayload.exp * 1000).toISOString(),
          sub: accessTokenPayload.sub,
          client_id: accessTokenPayload.client_id,
          scope: accessTokenPayload.scope
        });
      } catch (e) {
        console.error('[Token Debug] Failed to decode access token:', e);
      }
    }
  } catch (error) {
    console.error('[Token Debug] Error:', error);
  }
};

export const testApiEndpoints = async () => {
  try {
    console.log('[API Test] Testing different endpoints with different token types...');
    
    const tokens = await getStoredTokens();
    if (!tokens) {
      console.log('[API Test] No tokens available');
      return;
    }

    const apiUrl = process.env.EXPO_PUBLIC_API_URL?.replace(/\/$/, '') || 'https://2fl5484vo7.execute-api.us-east-1.amazonaws.com/prod';
    const dateString = new Date().toISOString().split('T')[0];

    // Test 1: Digest endpoint with ID token
    console.log('[API Test] Test 1: Digest endpoint with ID token');
    try {
      const response1 = await fetch(`${apiUrl}/digest/${dateString}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.idToken}`,
          'Content-Type': 'application/json'
        }
      });
      console.log(`[API Test] ID token result: ${response1.status} ${response1.statusText}`);
      if (!response1.ok) {
        const errorText = await response1.text();
        console.log(`[API Test] ID token error details:`, errorText);
      }
    } catch (e) {
      console.error('[API Test] ID token test failed:', e);
    }

    // Test 2: Digest endpoint with access token
    console.log('[API Test] Test 2: Digest endpoint with access token');
    try {
      const response2 = await fetch(`${apiUrl}/digest/${dateString}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      console.log(`[API Test] Access token result: ${response2.status} ${response2.statusText}`);
      if (!response2.ok) {
        const errorText = await response2.text();
        console.log(`[API Test] Access token error details:`, errorText);
      }
    } catch (e) {
      console.error('[API Test] Access token test failed:', e);
    }

    // Test 3: Chat endpoint with ID token (for comparison)
    console.log('[API Test] Test 3: Chat endpoint with ID token');
    try {
      const response3 = await fetch(`${apiUrl}/chat/conversations`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.idToken}`,
          'Content-Type': 'application/json'
        }
      });
      console.log(`[API Test] Chat with ID token result: ${response3.status} ${response3.statusText}`);
    } catch (e) {
      console.error('[API Test] Chat test failed:', e);
    }

    // Test 4: Chat endpoint with access token (for comparison)
    console.log('[API Test] Test 4: Chat endpoint with access token');
    try {
      const response4 = await fetch(`${apiUrl}/chat/conversations`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      console.log(`[API Test] Chat with access token result: ${response4.status} ${response4.statusText}`);
    } catch (e) {
      console.error('[API Test] Chat access token test failed:', e);
    }

  } catch (error) {
    console.error('[API Test] Error:', error);
  }
};

export const testDigestRegenerate = async () => {
  try {
    console.log('[Digest Test] Testing digest regenerate endpoint with both token types...');
    
    const tokens = await getStoredTokens();
    if (!tokens) {
      console.log('[Digest Test] No tokens available');
      return;
    }

    const apiUrl = process.env.EXPO_PUBLIC_API_URL?.replace(/\/$/, '') || 'https://2fl5484vo7.execute-api.us-east-1.amazonaws.com/prod';
    const dateString = new Date().toISOString().split('T')[0];

    const requestBody = {
      forceRegenerate: true,
      bypassCache: true,
      includeContext: true,
      refreshContext: true
    };

    // Test with ID token
    console.log('[Digest Test] Testing regenerate with ID token...');
    try {
      const response1 = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokens.idToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      console.log(`[Digest Test] ID token regenerate result: ${response1.status} ${response1.statusText}`);
      if (response1.ok) {
        const data = await response1.json();
        console.log(`[Digest Test] ID token success - got ${data.activities?.length || 0} activities`);
      } else {
        const errorText = await response1.text();
        console.log(`[Digest Test] ID token error:`, errorText);
      }
    } catch (e) {
      console.error('[Digest Test] ID token regenerate failed:', e);
    }

    // Test with access token
    console.log('[Digest Test] Testing regenerate with access token...');
    try {
      const response2 = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      console.log(`[Digest Test] Access token regenerate result: ${response2.status} ${response2.statusText}`);
      if (response2.ok) {
        const data = await response2.json();
        console.log(`[Digest Test] Access token success - got ${data.activities?.length || 0} activities`);
      } else {
        const errorText = await response2.text();
        console.log(`[Digest Test] Access token error:`, errorText);
      }
    } catch (e) {
      console.error('[Digest Test] Access token regenerate failed:', e);
    }

  } catch (error) {
    console.error('[Digest Test] Error:', error);
  }
};

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).debugTokens = debugTokens;
  (window as any).testApiEndpoints = testApiEndpoints;
  (window as any).testDigestRegenerate = testDigestRegenerate;
} 