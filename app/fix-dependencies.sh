#!/bin/bash

# Create the directory structure if it doesn't exist
mkdir -p node_modules/@react-navigation/core/node_modules/use-latest-callback

# Create a minimal package.json file
echo '{
  "name": "use-latest-callback",
  "version": "0.1.0",
  "main": "index.js"
}' > node_modules/@react-navigation/core/node_modules/use-latest-callback/package.json

# Create a minimal index.js file
echo 'module.exports = function useLatestCallback(callback) {
  return callback;
};' > node_modules/@react-navigation/core/node_modules/use-latest-callback/index.js

echo "Dependency fix applied successfully!"
