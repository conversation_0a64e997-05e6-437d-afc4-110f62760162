import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useFonts } from '@expo-google-fonts/inter';
import { View, ActivityIndicator, AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeProvider, useTheme } from './theme/ThemeProvider';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ProfileProvider, useProfile } from './context/ProfileContext';
import { ConversationProvider } from './context/ConversationContext';
import { LoadingProvider } from './context/LoadingContext';
import { UserContextProvider } from './context/UserContextProvider';
import { WeatherProvider } from './context/WeatherContext';
import LoadingScreen from './components/LoadingScreen';
import AppLoader from './components/AppLoader';
import { InitResult } from './services/AppInitializer';
import SignIn from './screens/SignIn';
import SignUp from './screens/SignUp';
import VerifyEmail from './screens/VerifyEmail';
import OnboardingScreen from './screens/OnboardingScreen';
import MainApp from './screens/MainApp';
import { fadeTransition, slideTransition } from './navigation/transitions';
import { checkAndResetDailyNutrition } from './services/nutritionResetService';
import AchievementNotificationProvider from './components/AchievementNotificationProvider';
import ErrorBoundary from './components/ErrorBoundary';
import { ServiceFallbackProvider } from './context/ServiceFallbackContext';

const Stack = createNativeStackNavigator();

// Define the navigation theme creator as a memoized function
function createNavTheme(colors: any, isDark: any) {
  return {
    colors: {
      background: colors.background,
      card: colors.card,
      text: colors.text,
      border: colors.border,
      primary: colors.primary,
      notification: colors.notification || colors.primary,
    },
    dark: isDark
  };
}

// Pass initialization props to RootNavigator
interface RootNavigatorProps {
  onInitialized?: (result: InitResult) => void;
  onInitError?: (error: Error) => void;
  timeout?: number;
}

// Use a simple function component instead of React.memo with custom comparison
function RootNavigator({ onInitialized, onInitError, timeout }: RootNavigatorProps) {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { profile, isLoading: profileLoading } = useProfile();
  const { colors } = useTheme();

  // Show loading indicator while authentication is loading
  if (authLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Determine which stack to show based on auth state
  if (!isAuthenticated) {
    // Auth stack
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="SignIn" component={SignIn} />
        <Stack.Screen name="SignUp" component={SignUp} />
        <Stack.Screen name="VerifyEmail" component={VerifyEmail} />
      </Stack.Navigator>
    );
  } else {
    // Authenticated flow: Show AppLoader while initializing, then Onboarding or MainApp
    return (
      <AppLoader
        onInitialized={onInitialized}
        onInitError={onInitError}
        timeout={timeout}
      >
        {/* Check profile loading state *after* AppLoader initialization */}
        {profileLoading ? (
           <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
             <ActivityIndicator size="large" color={colors.primary} />
           </View>
        ) : !profile.userId || !profile.name || profile.name === 'User' ? (
          // Onboarding stack - show when profile is missing, incomplete, or has default values
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="Onboarding" component={OnboardingScreen} />
          </Stack.Navigator>
        ) : (
          // Main app
          <Stack.Navigator screenOptions={{ headerShown: false }}>
            <Stack.Screen name="MainApp" component={MainApp} />
          </Stack.Navigator>
        )}
      </AppLoader>
    );
  }
}

// Pass initialization props to AppContent
interface AppContentProps {
  onInitialized?: (result: InitResult) => void;
  onInitError?: (error: Error) => void;
  timeout?: number;
}

function AppContent({ onInitialized, onInitError, timeout }: AppContentProps) {
  const { colors, isDark } = useTheme();

  // Create theme object only once when colors change
  const navTheme = React.useMemo(() => createNavTheme(colors, isDark), [colors, isDark]);

  // Check for nutrition reset and achievements when app becomes active
  useEffect(() => {
    // Function to check nutrition reset - only do this on app activate
    const checkNutrition = async () => {
      try {
        // Check nutrition reset regardless of auth state
        await checkAndResetDailyNutrition();
      } catch (error) {
        console.error('[AppContent] Error in checkNutrition:', error);
      }
    };

    // Check on initial load with brief delay to prioritize UI rendering
    setTimeout(checkNutrition, 2000);

    // Set up AppState listener to check for nutrition reset when app becomes active
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        checkNutrition();
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, []);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('[App] Critical error caught by boundary:', error);
        console.error('[App] Error info:', errorInfo);
      }}
    >
      <ServiceFallbackProvider>
        <LoadingProvider>
          <AuthProvider>
            <ProfileProvider>
              <UserContextProvider>
                <WeatherProvider>
                  <ConversationProvider>
                    <NavigationContainer theme={navTheme}>
                      <StatusBar style="auto" />
                      <AchievementNotificationProvider>
                        <RootNavigator
                          onInitialized={onInitialized}
                          onInitError={onInitError}
                          timeout={timeout}
                        />
                      </AchievementNotificationProvider>
                    </NavigationContainer>
                  </ConversationProvider>
                </WeatherProvider>
              </UserContextProvider>
            </ProfileProvider>
          </AuthProvider>
        </LoadingProvider>
      </ServiceFallbackProvider>
    </ErrorBoundary>
  );
}

// Handle app initialization
export default function App() {
  // Record app start time for synchronization timing
  useEffect(() => {
    AsyncStorage.setItem('app_start_time', Date.now().toString())
      .catch(error => console.error('Failed to record app start time:', error));
  }, []);

  // Load fonts
  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': require('@expo-google-fonts/inter/Inter_400Regular.ttf'),
    'Inter-Medium': require('@expo-google-fonts/inter/Inter_500Medium.ttf'),
    'Inter-SemiBold': require('@expo-google-fonts/inter/Inter_600SemiBold.ttf'),
    'Inter-Bold': require('@expo-google-fonts/inter/Inter_700Bold.ttf'),
  });

  const handleInitialized = (result: InitResult) => {
    // Log initialization result for debugging
    if (!result.success) {
      console.error('App initialization completed with errors:', result.errors);
    }
  };

  const handleInitError = (error: Error) => {
    console.error('App initialization failed with error:', error);
  };

  if (!fontsLoaded && !fontError) {
    return (
      <LoadingScreen />
    );
  }

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AppContent
          onInitialized={handleInitialized}
          onInitError={handleInitError}
          timeout={15000} // Reduced from 30000
        />
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
