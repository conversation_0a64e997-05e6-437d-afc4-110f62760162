import React, { useState } from 'react';
import { RefreshControl, ScrollView, ScrollViewProps } from 'react-native';
import { useTheme } from '../theme/ThemeProvider';

interface PullToRefreshProps extends ScrollViewProps {
  onRefresh: () => Promise<void> | void;
  refreshing?: boolean;
  children: React.ReactNode;
}

/**
 * Reusable pull-to-refresh component with consistent styling
 */
export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  refreshing: externalRefreshing,
  children,
  ...scrollViewProps
}) => {
  const { colors } = useTheme();
  const [internalRefreshing, setInternalRefreshing] = useState(false);
  
  const isRefreshing = externalRefreshing !== undefined ? externalRefreshing : internalRefreshing;

  const handleRefresh = async () => {
    if (externalRefreshing === undefined) {
      setInternalRefreshing(true);
    }
    
    try {
      await onRefresh();
    } finally {
      if (externalRefreshing === undefined) {
        setInternalRefreshing(false);
      }
    }
  };

  return (
    <ScrollView
      {...scrollViewProps}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={colors.primary}
          colors={[colors.primary]}
          progressBackgroundColor={colors.card}
          title="Pull to refresh"
          titleColor={colors.textSecondary}
        />
      }
    >
      {children}
    </ScrollView>
  );
};

export default PullToRefresh;
