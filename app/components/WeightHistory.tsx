import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Animated, { SlideInRight } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { WeightEntry, getWeightEntries } from '../services/weightTracking';

interface WeightHistoryProps {
  onRefresh: () => void;
  refreshTrigger?: number; // A value that changes to trigger refresh
  isInsideScrollView?: boolean; // Flag to prevent nested virtualized lists
  enhancedUX?: boolean; // Flag to use enhanced UX styling
}

const WeightHistory: React.FC<WeightHistoryProps> = ({ 
  onRefresh, 
  refreshTrigger, 
  isInsideScrollView = false,
  enhancedUX = false
}) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const [entries, setEntries] = useState<WeightEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);
  const [newEntryId, setNewEntryId] = useState<string | null>(null);
  const previousEntriesRef = useRef<WeightEntry[]>([]);

  useEffect(() => {
    loadEntries();
  }, []);

  // Add a separate effect to reload data when the component receives focus
  useEffect(() => {
    const unsubscribe = navigation?.addListener('focus', () => {
      loadEntries();
    });
    return unsubscribe;
  }, [navigation]);

  // Add an effect to reload data when the refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      console.log('WeightHistory: Refresh triggered');
      loadEntries();
    }
  }, [refreshTrigger]);

  const loadEntries = async () => {
    setIsLoading(true);
    try {
      const data = await getWeightEntries();
      // Sort by date, newest first
      const sortedData = [...data].sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      // Check for new entries
      if (previousEntriesRef.current.length > 0 && sortedData.length > previousEntriesRef.current.length) {
        // Find the new entry (should be the first one since they're sorted newest first)
        const newEntry = sortedData[0];
        if (newEntry) {
          // Use the date as a unique identifier
          setNewEntryId(newEntry.date);
          // Clear the new entry highlight after 3 seconds
          setTimeout(() => setNewEntryId(null), 3000);
        }
      }

      // Update the previous entries reference
      previousEntriesRef.current = sortedData;

      setEntries(sortedData);
    } catch (error) {
      console.error('Error loading weight entries:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const renderItem = ({ item }: { item: WeightEntry }) => {
    // Check if this is the new entry
    const isNewEntry = item.date === newEntryId;

    // Use Animated.View for the entry item
    const EntryComponent = isNewEntry ? Animated.View : View;

    // Define entering animation for new entries
    const enteringAnimation = isNewEntry ?
      SlideInRight.duration(400).springify() :
      undefined;

    return (
      <EntryComponent
        style={[
          styles.entryContainer,
          { backgroundColor: colors.card },
          isNewEntry && { borderLeftWidth: 4, borderLeftColor: colors.success }
        ]}
        entering={enteringAnimation}
      >
      <View style={styles.entryHeader}>
        <View style={styles.dateContainer}>
          <Text style={[styles.date, { color: colors.text }]}>
            {formatDate(item.date)}
          </Text>
          <Text style={[styles.time, { color: colors.textSecondary }]}>
            {formatTime(item.date)}
          </Text>
        </View>
        <View style={styles.weightContainer}>
          <Text style={[styles.weight, { color: colors.primary }]}>
            {item.value.toFixed(1)} lbs
          </Text>
        </View>
      </View>

      {item.note && (
        <View style={styles.noteContainer}>
          <Text style={[styles.noteLabel, { color: colors.textSecondary }]}>Note:</Text>
          <Text style={[styles.note, { color: colors.text }]}>{item.note}</Text>
        </View>
      )}

      {isNewEntry && (
        <View style={[styles.newBadge, { backgroundColor: colors.success }]}>
          <Text style={styles.newBadgeText}>New</Text>
        </View>
      )}
    </EntryComponent>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  }

  if (entries.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No weight entries yet
        </Text>
      </View>
    );
  }

  // We'll use regular style instead of animated values to avoid the hooks error
  const containerStyle = {
    opacity: isExpanded ? 1 : 0,
    maxHeight: isExpanded ? 500 : 0,
    overflow: 'hidden' as const
  };
  
  // Enhanced toggle with haptic feedback (if the platform supported it)
  const enhancedToggle = () => {
    // If the platform supported haptic feedback, we would add it here
    toggleExpand();
  };

  return (
    <View style={[
      styles.container,
      enhancedUX && { marginTop: 0 }
    ]}>
      <View 
        style={[
          styles.headerContainer, 
          { 
            backgroundColor: colors.card,
            borderRadius: enhancedUX ? 0 : 16,
            borderBottomLeftRadius: isExpanded ? 0 : (enhancedUX ? 0 : 16),
            borderBottomRightRadius: isExpanded ? 0 : (enhancedUX ? 0 : 16),
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: enhancedUX ? 0 : 0.1,
            shadowRadius: 2,
            elevation: enhancedUX ? 0 : 1,
            borderBottomWidth: enhancedUX ? 1 : 0,
            borderBottomColor: isExpanded ? 'rgba(150, 150, 150, 0.1)' : 'transparent',
          }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.header,
            enhancedUX && { paddingVertical: 16 }
          ]}
          onPress={enhancedToggle}
          activeOpacity={0.7}
        >
          <View style={styles.headerTitleSection}>
            <Ionicons 
              name="time-outline" 
              size={enhancedUX ? 22 : 20} 
              color={colors.primary} 
              style={styles.headerIcon} 
            />
            <Text style={[
              styles.title, 
              { color: colors.text },
              enhancedUX && { fontSize: typography.sizes.xl }
            ]}>
              Weight History
            </Text>
            <View style={styles.entriesIndicator}>
              <Text style={[styles.entriesCount, { color: colors.textSecondary }]}>
                {entries.length} entries
              </Text>
            </View>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity 
              onPress={onRefresh} 
              style={[
                styles.refreshButton,
                enhancedUX && {
                  backgroundColor: colors.primaryLight + '40',
                  borderRadius: 20,
                  width: 36,
                  height: 36,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }
              ]}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Ionicons 
                name="refresh" 
                size={enhancedUX ? 18 : 20}
                color={colors.primary} 
              />
            </TouchableOpacity>
            <View
              style={{
                backgroundColor: enhancedUX ? 
                  (isExpanded ? colors.primaryLight + '40' : 'transparent') : 
                  'transparent',
                borderRadius: 20,
                width: enhancedUX ? 36 : 20,
                height: enhancedUX ? 36 : 20,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Ionicons
                name="chevron-down"
                size={enhancedUX ? 18 : 20}
                color={colors.textSecondary}
              />
            </View>
          </View>
        </TouchableOpacity>
      </View>

      <View 
        style={[
          styles.listContainer, 
          { 
            backgroundColor: colors.card,
            borderBottomLeftRadius: enhancedUX ? 0 : 16,
            borderBottomRightRadius: enhancedUX ? 0 : 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: enhancedUX ? 0 : 0.1,
            shadowRadius: 2,
            elevation: enhancedUX ? 0 : 1,
          },
          containerStyle
        ]}
      >
        {isInsideScrollView ? (
          // When inside a ScrollView, use a simple non-virtualized list
          <View style={[
            styles.listContent,
            enhancedUX && { padding: 10 }
          ]}>
            {entries.slice(0, 20).map((item, index) => (
              <View key={item.date}>
                {renderItem({ item })}
              </View>
            ))}
            {entries.length > 20 && (
              <TouchableOpacity 
                style={[
                  styles.viewMoreButton,
                  enhancedUX && { 
                    backgroundColor: colors.primaryLight + '20',
                    paddingVertical: 14,
                    marginTop: 16,
                    borderRadius: 12
                  }
                ]}
                onPress={() => navigation.navigate('History')}
              >
                <Text style={[
                  { color: colors.primary },
                  enhancedUX && { fontFamily: typography.fontFamily.semibold }
                ]}>
                  View all {entries.length} entries
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          // When not inside a ScrollView, use FlatList for virtualization
          <FlatList
            data={entries}
            renderItem={renderItem}
            keyExtractor={(item) => item.date}
            contentContainerStyle={[
              styles.listContent,
              enhancedUX && { padding: 10 }
            ]}
            showsVerticalScrollIndicator={false}
            initialNumToRender={10}
            windowSize={5}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginVertical: 12,
    overflow: 'hidden',
  },
  headerContainer: {
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 14,
    backgroundColor: 'transparent',
  },
  headerTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIcon: {
    marginRight: 8,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshButton: {
    marginRight: 15,
    padding: 2,
  },
  entriesIndicator: {
    marginLeft: 8,
  },
  entriesCount: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
  },
  listContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 150, 150, 0.1)',
    paddingTop: 5,
    marginTop: -1, // Overlap the border
    overflow: 'hidden',
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  listContent: {
    paddingBottom: 10,
  },
  entryContainer: {
    marginHorizontal: 15,
    marginVertical: 6,
    padding: 15,
    borderRadius: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateContainer: {
    flex: 1,
  },
  date: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  time: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 2,
  },
  weightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  weight: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginRight: 5,
  },
  noteContainer: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 150, 150, 0.1)',
  },
  noteLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  note: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    lineHeight: typography.sizes.md + 5, // Improve readability with better line height
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  newBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    paddingVertical: 3,
    paddingHorizontal: 8,
    borderRadius: 10,
  },
  newBadgeText: {
    color: '#fff',
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  viewMoreButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10, 
    borderRadius: 8,
  },
});

export default WeightHistory;
