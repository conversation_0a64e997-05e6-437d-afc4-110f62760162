import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';
import AchievementBadge from './AchievementBadge';
import {
  Achievement,
  AchievementCategory
} from '../services/sharedTypes';
import {
  getUserAchievements,
  getDefaultAchievements,
  syncAchievementsWithBackend
} from '../services/achievementService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AchievementsModalProps {
  visible: boolean;
  onClose: () => void;
}

const AchievementsModal: React.FC<AchievementsModalProps> = ({
  visible,
  onClose,
}) => {
  const { colors, isDark } = useTheme();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<AchievementCategory | 'all'>('all');
  const [unlockedCount, setUnlockedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsynced, setHasUnsynced] = useState(false);

  useEffect(() => {
    if (visible) {
      loadAchievements();
    }
  }, [visible]);

  useEffect(() => {
    const checkUnsynced = async () => {
      const pendingJson = await AsyncStorage.getItem('@achievements_pending_sync');
      setHasUnsynced(!!pendingJson && JSON.parse(pendingJson).length > 0);
    };
    if (visible) checkUnsynced();
  }, [visible, achievements]);

  const loadAchievements = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Always load from local storage first for instant UI
      const localAchievements = await getUserAchievements(false);
      setAchievements(localAchievements);
      setUnlockedCount(localAchievements.filter(a => a.status === 'unlocked').length);
      setTotalCount(localAchievements.length);
      setIsLoading(false);
      // Start a background sync
      syncAchievementsWithBackend();
    } catch (error) {
      setError('Unable to load achievements. Please check your connection or try again later.');
      setAchievements([]);
      setUnlockedCount(0);
      setTotalCount(0);
      setIsLoading(false);
    }
  };

  // Sort achievements: unlocked first, then in-progress, then locked
  const sortedAchievements = [...achievements].sort((a, b) => {
    // First sort by status
    if (a.status === 'unlocked' && b.status !== 'unlocked') return -1;
    if (a.status !== 'unlocked' && b.status === 'unlocked') return 1;
    if (a.status === 'in_progress' && b.status === 'locked') return -1;
    if (a.status === 'locked' && b.status === 'in_progress') return 1;

    // Then sort by progress
    return b.progress - a.progress;
  });

  // Log the sorted achievements
  console.log(`[AchievementsModal] Sorted ${sortedAchievements.length} achievements`);

  const filteredAchievements = selectedCategory === 'all'
    ? sortedAchievements
    : sortedAchievements.filter(a => a.category === selectedCategory);

  // Log the filtered achievements
  console.log(`[AchievementsModal] Filtered to ${filteredAchievements.length} achievements for category ${selectedCategory}`);

  const renderCategoryButton = (category: AchievementCategory | 'all', label: string, icon: string) => {
    const isSelected = selectedCategory === category;

    // Calculate counts for this category
    const categoryAchievements = category === 'all'
      ? achievements
      : achievements.filter(a => a.category === category);

    const unlockedCount = categoryAchievements.filter(a => a.status === 'unlocked').length;
    const totalCount = categoryAchievements.length;
    const countText = `${unlockedCount}/${totalCount}`;

    return (
      <TouchableOpacity
        style={[
          styles.categoryButton,
          {
            backgroundColor: isSelected ? colors.primary : colors.card,
            borderColor: colors.border,
          },
        ]}
        onPress={() => setSelectedCategory(category)}
      >
        <Ionicons
          name={icon as any}
          size={16}
          color={isSelected ? colors.background : colors.text}
        />
        <Text
          style={[
            styles.categoryButtonText,
            {
              color: isSelected ? colors.background : colors.text,
            },
          ]}
        >
          {label}
        </Text>
        <View style={[
          styles.categoryCount,
          {
            backgroundColor: isSelected ? colors.background : colors.primary,
            opacity: isSelected ? 0.9 : 0.7
          }
        ]}>
          <Text style={[
            styles.categoryCountText,
            {
              color: isSelected ? colors.primary : colors.background
            }
          ]}>
            {countText}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (error) {
    return (
      <Modal visible={visible} animationType="slide" onRequestClose={onClose} transparent={true}>
        <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>Achievements</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color={colors.error || 'red'} style={{ marginBottom: 16 }} />
            <Text style={[styles.errorText, { color: colors.error || 'red' }]}>{error}</Text>
          </View>
        </SafeAreaView>
      </Modal>
    );
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>Achievements</Text>
          {hasUnsynced && (
            <View style={{ width: 10, height: 10, borderRadius: 5, backgroundColor: colors.warning || 'orange', marginLeft: 8 }} />
          )}
          <View style={styles.placeholder} />
        </View>

        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: colors.card }]}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: colors.primary,
                  width: `${totalCount > 0 ? (unlockedCount / totalCount) * 100 : 0}%`,
                },
              ]}
            />
          </View>
          <Text style={[styles.progressText, { color: colors.textSecondary }]}>
            {unlockedCount} of {totalCount} achievements unlocked
          </Text>
        </View>

        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScrollContent}
          >
            {renderCategoryButton('all', 'All', 'trophy-outline')}
            {renderCategoryButton(AchievementCategory.WORKOUT, 'Workout', 'fitness-outline')}
            {renderCategoryButton(AchievementCategory.NUTRITION, 'Nutrition', 'restaurant-outline')}
            {renderCategoryButton(AchievementCategory.WEIGHT, 'Weight', 'trending-down-outline')}
            {renderCategoryButton(AchievementCategory.STREAK, 'Streaks', 'flame-outline')}
            {renderCategoryButton(AchievementCategory.GENERAL, 'General', 'star-outline')}
          </ScrollView>
        </View>

        {isLoading && achievements.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading achievements...</Text>
          </View>
        ) : filteredAchievements.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="trophy-outline" size={60} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>No achievements found</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              {selectedCategory === 'all'
                ? 'Start using the app to earn achievements!'
                : `No ${selectedCategory} achievements available yet.`}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredAchievements}
            renderItem={({ item, index }) => {
              // Log each achievement as it's rendered
              console.log(`[AchievementsModal] Rendering achievement: ${item.title}, id: ${item.id}, status: ${item.status}, progress: ${item.progress}`);

              return (
                <AchievementBadge
                  key={`${item.id}-${index}`} // Add index to ensure uniqueness
                  title={item.title}
                  description={item.description}
                  icon={item.icon as any}
                  unlocked={item.status === 'unlocked'}
                  progress={item.progress}
                  color={colors.primary}
                  backgroundColor={colors.card}
                  textColor={colors.text}
                  delay={index * 100}
                />
              );
            }}
            keyExtractor={(item, index) => `${item.id}-${index}`} // Add index to ensure uniqueness
            contentContainerStyle={styles.achievementsList}
          />
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  closeButton: {
    padding: 5,
  },
  placeholder: {
    width: 34, // Same width as close button for centering
  },
  title: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesScrollContent: {
    paddingHorizontal: 15,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
  },
  categoryButtonText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
  },
  categoryCount: {
    marginLeft: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 30,
    alignItems: 'center',
  },
  categoryCountText: {
    fontSize: 10,
    fontFamily: typography.fontFamily.semibold,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 15,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  achievementsList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default AchievementsModal;
