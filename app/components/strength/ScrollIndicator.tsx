import React, { useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

interface ScrollIndicatorProps {
  visible: boolean;
}

const ScrollIndicator: React.FC<ScrollIndicatorProps> = ({ visible }) => {
  const { colors } = useTheme();
  const opacity = new Animated.Value(visible ? 1 : 0);
  
  useEffect(() => {
    Animated.timing(opacity, {
      toValue: visible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [visible, opacity]);
  
  return (
    <Animated.View 
      style={[
        styles.container, 
        { opacity },
        { backgroundColor: colors.primary }
      ]}
    >
      <View style={styles.indicator} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 10,
    alignSelf: 'center',
    width: 36,
    height: 5,
    borderRadius: 3,
    opacity: 0.6,
  },
  indicator: {
    width: '100%',
    height: '100%',
  },
});

export default ScrollIndicator;
