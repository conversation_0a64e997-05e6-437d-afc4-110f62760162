import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Alert,
  TextInput,
  Dimensions,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, {
  FadeIn,
  SlideInDown,
  FadeOut,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolate
} from 'react-native-reanimated';
import { generateUUID } from '../../utils/uuid';
import { saveLog, WorkoutData, WorkoutExercise } from '../../services/conversationService';
import { parseWorkoutEntry, createFallbackExercise } from '../../services/workoutParserService';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

interface ActiveWorkoutModalProps {
  visible: boolean;
  onClose: () => void;
  workout: WorkoutData;
  onWorkoutCompleted: () => void;
}

const ActiveWorkoutModal: React.FC<ActiveWorkoutModalProps> = ({
  visible,
  onClose,
  workout,
  onWorkoutCompleted,
}) => {
  const { colors } = useTheme();
  const [activeWorkout, setActiveWorkout] = useState<WorkoutData>({ ...workout });
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [currentSetIndex, setCurrentSetIndex] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [notes, setNotes] = useState('');
  const [isPaused, setIsPaused] = useState(false);
  const [customExerciseInput, setCustomExerciseInput] = useState('');
  const [pauseTime, setPauseTime] = useState<Date | null>(null);
  const [isProcessingExercise, setIsProcessingExercise] = useState(false);
  const [isEditingExercise, setIsEditingExercise] = useState(false);
  const [editingExerciseInput, setEditingExerciseInput] = useState('');

  // Screen dimensions
  const screenWidth = Dimensions.get('window').width;

  // Fixed dimensions for exercise card
  const CARD_WIDTH = screenWidth * 0.85;
  const CARD_HEIGHT = 300;

  // Initialize workout tracking when modal opens
  useEffect(() => {
    if (visible) {
      setActiveWorkout({ ...workout });
      setCurrentExerciseIndex(0);
      setCurrentSetIndex(0);
      setStartTime(new Date());
      setNotes('');

      // Initialize workout
    }
  }, [visible, workout]);

  // Add navigation functions for the exercise carousel
  const goToPreviousExercise = () => {
    if (activeWorkout.exercises && activeWorkout.exercises.length > 0) {
      const newIndex = currentExerciseIndex > 0
        ? currentExerciseIndex - 1
        : activeWorkout.exercises.length - 1; // Loop back to the end

      setCurrentExerciseIndex(newIndex);
      setCurrentSetIndex(0);
    }
  };

  const goToNextExercise = () => {
    if (activeWorkout.exercises && activeWorkout.exercises.length > 0) {
      const newIndex = currentExerciseIndex < activeWorkout.exercises.length - 1
        ? currentExerciseIndex + 1
        : 0; // Loop back to the beginning

      setCurrentExerciseIndex(newIndex);
      setCurrentSetIndex(0);
    }
  };

  // Track elapsed time
  useEffect(() => {
    if (!visible || !startTime || isPaused) return;

    const timer = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, startTime, isPaused]);

  // Handle pause and resume
  const handlePauseResume = () => {
    if (isPaused) {
      // Resume the timer
      if (pauseTime && startTime) {
        // Adjust the start time to account for the pause duration
        const pauseDuration = new Date().getTime() - pauseTime.getTime();
        setStartTime(new Date(startTime.getTime() + pauseDuration));
      }
      setPauseTime(null);
    } else {
      // Pause the timer
      setPauseTime(new Date());
    }
    setIsPaused(!isPaused);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getCurrentExercise = (): WorkoutExercise | null => {
    if (!activeWorkout.exercises || activeWorkout.exercises.length === 0) return null;
    return activeWorkout.exercises[currentExerciseIndex];
  };

  const getCurrentSet = () => {
    const exercise = getCurrentExercise();

    // Handle case where exercise doesn't exist or sets is not an array
    if (!exercise) return null;

    // If sets is not an array, convert it to an array with a single set
    const sets = Array.isArray(exercise.sets)
      ? exercise.sets
      : typeof exercise.sets === 'number'
        ? Array(exercise.sets).fill({ reps: exercise.reps, weight: exercise.weight })
        : [];

    // Check if the sets array is empty or the current index is out of bounds
    if (sets.length === 0 || currentSetIndex >= sets.length) return null;

    return sets[currentSetIndex];
  };

  const handleSetComplete = (weight?: number, reps?: number) => {
    // Update the current set with the actual values
    const updatedWorkout = { ...activeWorkout };
    const exercise = updatedWorkout.exercises[currentExerciseIndex];

    // Ensure exercise.sets is an array
    if (!Array.isArray(exercise.sets)) {
      // Convert numeric sets to an array of set objects
      if (typeof exercise.sets === 'number') {
        exercise.sets = Array(exercise.sets).fill({
          reps: exercise.reps,
          weight: exercise.weight
        });
      } else {
        // If sets is not a number or array, create a default array with one set
        exercise.sets = [{ reps: exercise.reps, weight: exercise.weight }];
      }
    }

    // Now we can safely update the current set
    if (Array.isArray(exercise.sets) && currentSetIndex < exercise.sets.length) {
      exercise.sets[currentSetIndex] = {
        ...exercise.sets[currentSetIndex],
        weight: weight !== undefined ? weight : (exercise.sets[currentSetIndex]?.weight || exercise.weight),
        reps: reps !== undefined ? reps : (exercise.sets[currentSetIndex]?.reps || exercise.reps),
        completed: true
      };
    }

    setActiveWorkout(updatedWorkout);

    // Get the sets array length safely
    const setsLength = Array.isArray(exercise.sets) ? exercise.sets.length : 0;

    // Move to the next set or exercise
    if (currentSetIndex < setsLength - 1) {
      // Move to next set
      setCurrentSetIndex(currentSetIndex + 1);
    } else {
      // Move to next exercise
      if (currentExerciseIndex < activeWorkout.exercises.length - 1) {
        setCurrentExerciseIndex(currentExerciseIndex + 1);
        setCurrentSetIndex(0);
      } else {
        // Workout complete
        Alert.alert(
          'Workout Complete',
          'You have completed all exercises. Would you like to save this workout?',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Save',
              onPress: handleSaveWorkout,
            },
          ]
        );
      }
    }
  };

  // Handle adding a custom exercise
  const handleAddCustomExercise = async () => {
    if (!customExerciseInput.trim()) return;

    // Show loading indicator
    setIsProcessingExercise(true);

    try {
      // Use the LLM to parse the exercise
      const parsedExercise = await parseWorkoutEntry(customExerciseInput);

      let newExercise: WorkoutExercise;

      if (parsedExercise) {
        // Use the LLM-parsed exercise
        newExercise = parsedExercise;
      } else {
        // Fallback to regex parsing if LLM fails
        console.log('LLM parsing failed, using fallback parser');
        newExercise = createFallbackExercise(customExerciseInput);
      }

      // Add the exercise to the workout
      const updatedWorkout = { ...activeWorkout };
      if (!updatedWorkout.exercises) {
        updatedWorkout.exercises = [];
      }
      updatedWorkout.exercises.push(newExercise);
      setActiveWorkout(updatedWorkout);

      // Clear the input
      setCustomExerciseInput('');

      // If this is the first exercise, set it as current
      if (updatedWorkout.exercises.length === 1) {
        setCurrentExerciseIndex(0);
        setCurrentSetIndex(0);
      }

      // Show success message
      Alert.alert(
        "Exercise Added",
        `Added ${newExercise.name} to your workout`,
        [{ text: "OK" }],
        { cancelable: true }
      );
    } catch (error) {
      console.error('Error adding custom exercise:', error);
      Alert.alert(
        "Error",
        "Failed to add exercise. Please try again.",
        [{ text: "OK" }],
        { cancelable: true }
      );
    } finally {
      setIsProcessingExercise(false);
    }
  };

  // Handle editing an exercise
  const handleEditExercise = () => {
    if (!currentExercise) return;

    // Set the editing state and initialize the input with the current exercise description
    setIsEditingExercise(true);
    setEditingExerciseInput(`${currentExercise.name} ${Array.isArray(currentExercise.sets) ? currentExercise.sets.length : 1} sets`);

    // Add reps and weight/duration info if available
    if (Array.isArray(currentExercise.sets) && currentExercise.sets.length > 0) {
      const firstSet = currentExercise.sets[0];
      if (firstSet.duration && firstSet.duration > 0) {
        setEditingExerciseInput(prev => `${prev} ${firstSet.duration} seconds`);
      } else if (firstSet.reps) {
        setEditingExerciseInput(prev => `${prev} ${firstSet.reps} reps`);
        if (firstSet.weight && firstSet.weight > 0) {
          setEditingExerciseInput(prev => `${prev} ${firstSet.weight} lbs`);
        }
      }
    }
  };

  // Handle saving the edited exercise
  const handleSaveEditedExercise = async () => {
    if (!editingExerciseInput.trim() || !currentExercise) return;

    setIsProcessingExercise(true);

    try {
      // Use the LLM to parse the exercise
      const parsedExercise = await parseWorkoutEntry(editingExerciseInput);

      let updatedExercise: WorkoutExercise;

      if (parsedExercise) {
        // Use the LLM-parsed exercise
        updatedExercise = parsedExercise;
      } else {
        // Fallback to regex parsing if LLM fails
        console.log('LLM parsing failed, using fallback parser');
        updatedExercise = createFallbackExercise(editingExerciseInput);
      }

      // Update the exercise in the workout
      const updatedWorkout = { ...activeWorkout };
      if (updatedWorkout.exercises && updatedWorkout.exercises.length > currentExerciseIndex) {
        updatedWorkout.exercises[currentExerciseIndex] = updatedExercise;
        setActiveWorkout(updatedWorkout);
      }

      // Exit editing mode
      setIsEditingExercise(false);
      setEditingExerciseInput('');

      // Show success message
      Alert.alert(
        "Exercise Updated",
        `Updated to ${updatedExercise.name}`,
        [{ text: "OK" }],
        { cancelable: true }
      );
    } catch (error) {
      console.error('Error updating exercise:', error);
      Alert.alert(
        "Error",
        "Failed to update exercise. Please try again.",
        [{ text: "OK" }],
        { cancelable: true }
      );
    } finally {
      setIsProcessingExercise(false);
    }
  };

  // Handle deleting the current exercise
  const handleDeleteExercise = () => {
    if (!currentExercise) return;

    Alert.alert(
      "Delete Exercise",
      `Are you sure you want to delete ${currentExercise.name}?`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            // Remove the exercise from the workout
            const updatedWorkout = { ...activeWorkout };
            if (updatedWorkout.exercises && updatedWorkout.exercises.length > currentExerciseIndex) {
              updatedWorkout.exercises.splice(currentExerciseIndex, 1);
              setActiveWorkout(updatedWorkout);

              // Update the current exercise index
              if (currentExerciseIndex >= updatedWorkout.exercises.length) {
                setCurrentExerciseIndex(Math.max(0, updatedWorkout.exercises.length - 1));
              }
              setCurrentSetIndex(0);
            }
          }
        }
      ]
    );
  };





  const handleSaveWorkout = async () => {
    setIsSaving(true);
    try {
      // Calculate duration in minutes
      const duration = Math.floor(elapsedTime / 60);

      // Create a completed workout log
      const completedWorkout: WorkoutData = {
        ...activeWorkout,
        duration,
        notes: notes || activeWorkout.notes,
        completed: true,
        completedAt: new Date().toISOString()
      };

      // Generate a unique ID for the log
      const logId = generateUUID();

      // Save to logs with the format the backend expects
      await saveLog({
        id: logId,
        type: 'workout',
        timestamp: new Date().toISOString(),
        description: `Completed: ${completedWorkout.title}`,
        // Include both formats to ensure compatibility
        workoutData: completedWorkout,
        metrics: {
          workout: completedWorkout
        }
      });

      console.log('Completed workout saved successfully with ID:', logId);

      // Notify parent and close
      onWorkoutCompleted();
      onClose();

      // Show success message
      Alert.alert('Success', 'Workout saved successfully!');
    } catch (error) {
      console.error('Error saving completed workout:', error);
      Alert.alert('Error', 'Failed to save workout');
    } finally {
      setIsSaving(false);
    }
  };

  // Render the current exercise card
  const renderExerciseCard = () => {
    // Get the current exercise
    const currentExercise = activeWorkout.exercises?.[currentExerciseIndex];

    if (!currentExercise) {
      return null;
    }

    return (
      <View style={styles.exerciseCardContainer}>
        <View
          style={[styles.exerciseCard, {
            backgroundColor: colors.card,
            width: CARD_WIDTH,
            height: CARD_HEIGHT,
            // Add subtle border for better visual separation
            borderWidth: 1,
            borderColor: colors.border,
          }]}
        >
          <View style={styles.exerciseHeader}>
            <Text style={[styles.exerciseName, { color: colors.text }]}>
              {currentExercise.name}
            </Text>

            <View style={styles.exerciseActions}>
              <TouchableOpacity
                style={[styles.exerciseActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={handleEditExercise}
              >
                <Ionicons name="pencil" size={18} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.exerciseActionButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={handleDeleteExercise}
              >
                <Ionicons name="trash" size={18} color={colors.error} />
              </TouchableOpacity>
            </View>
          </View>

          {currentSet && (
            <View style={styles.setContainer}>
              <Text style={[styles.setTitle, { color: colors.textSecondary }]}>
                Set {currentSetIndex + 1} of {Array.isArray(currentExercise.sets) ? currentExercise.sets.length : 0}
              </Text>

              <View style={styles.setDetails}>
                <View style={styles.setValueContainer}>
                  <Text style={[styles.setLabel, { color: colors.textSecondary }]}>Weight</Text>
                  <TextInput
                    style={[styles.setInput, {
                      color: colors.text,
                      backgroundColor: colors.background,
                      borderColor: colors.border
                    }]}
                    keyboardType="numeric"
                    defaultValue={currentSet.weight?.toString() || ''}
                    placeholder="lbs"
                    placeholderTextColor={colors.textSecondary}
                  />
                </View>

                <View style={styles.setValueContainer}>
                  <Text style={[styles.setLabel, { color: colors.textSecondary }]}>
                    {currentSet.duration && currentSet.duration > 0 ? 'Duration' : 'Reps'}
                  </Text>
                  <TextInput
                    style={[styles.setInput, {
                      color: colors.text,
                      backgroundColor: colors.background,
                      borderColor: colors.border
                    }]}
                    keyboardType="numeric"
                    defaultValue={
                      currentSet.duration && currentSet.duration > 0
                        ? currentSet.duration.toString()
                        : currentSet.reps?.toString() || ''
                    }
                    placeholder={currentSet.duration && currentSet.duration > 0 ? "sec" : "#"}
                    placeholderTextColor={colors.textSecondary}
                  />
                </View>
              </View>

              <TouchableOpacity
                style={[styles.completeButton, { backgroundColor: colors.primary }]}
                onPress={() => handleSetComplete()}
              >
                <Text style={styles.completeButtonText}>Complete Set</Text>
              </TouchableOpacity>
            </View>
          )}

          {currentExercise.notes && (
            <View style={styles.notesContainer}>
              <Text style={[styles.notesLabel, { color: colors.textSecondary }]}>Notes:</Text>
              <Text style={[styles.notesText, { color: colors.text }]}>
                {currentExercise.notes}
              </Text>
            </View>
          )}

          {currentExercise.muscleGroup && (
            <View style={styles.muscleGroupContainer}>
              <Text style={[styles.muscleGroupLabel, { color: colors.textSecondary }]}>
                Muscle Group:
              </Text>
              <Text style={[styles.muscleGroupText, { color: colors.primary }]}>
                {currentExercise.muscleGroup}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Render the exercise navigation
  const renderExerciseNavigation = () => {
    if (!activeWorkout.exercises || activeWorkout.exercises.length <= 1) {
      return null;
    }

    return (
      <View style={styles.exerciseNavigationContainer}>
        <TouchableOpacity
          style={[styles.navigationButton, { backgroundColor: colors.card }]}
          onPress={goToPreviousExercise}
        >
          <Ionicons name="chevron-back" size={24} color={colors.primary} />
        </TouchableOpacity>

        <View style={styles.exerciseProgressContainer}>
          <Text style={[styles.exerciseProgressText, { color: colors.text }]}>
            {currentExerciseIndex + 1} / {activeWorkout.exercises.length}
          </Text>

          <View style={styles.progressDotsContainer}>
            {activeWorkout.exercises.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setCurrentExerciseIndex(index)}
                style={styles.paginationDotContainer}
                activeOpacity={0.7}
              >
                <View
                  style={[
                    styles.progressDot,
                    {
                      backgroundColor: index === currentExerciseIndex
                        ? colors.primary
                        : index < currentExerciseIndex
                          ? colors.primaryLight
                          : colors.border,
                      width: index === currentExerciseIndex ? 20 : 8,
                      height: 8,
                      borderRadius: 4,
                    }
                  ]}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <TouchableOpacity
          style={[styles.navigationButton, { backgroundColor: colors.card }]}
          onPress={goToNextExercise}
        >
          <Ionicons name="chevron-forward" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  const currentExercise = getCurrentExercise();
  const currentSet = getCurrentSet();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Animated.View
        style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
      >
        <Animated.View
          style={[styles.modalContainer, { backgroundColor: colors.background }]}
          entering={SlideInDown.duration(400).springify()}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {activeWorkout.title}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                Alert.alert(
                  'Exit Workout',
                  'Are you sure you want to exit? Your progress will be lost.',
                  [
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                    {
                      text: 'Exit',
                      onPress: onClose,
                      style: 'destructive',
                    },
                  ]
                );
              }}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={[styles.timerContainer, { backgroundColor: colors.card }]}>
            <Ionicons name="time-outline" size={20} color={colors.primary} />
            <Text style={[styles.timerText, { color: colors.text }]}>
              {formatTime(elapsedTime)}
            </Text>

            <View style={styles.workoutControls}>
              <TouchableOpacity
                style={[styles.controlButton, { backgroundColor: isPaused ? colors.primary : colors.card, borderColor: colors.border }]}
                onPress={handlePauseResume}
              >
                <Ionicons
                  name={isPaused ? "play" : "pause"}
                  size={18}
                  color={isPaused ? "#fff" : colors.text}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.controlButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={() => {
                  Alert.alert(
                    'End Workout',
                    'Are you sure you want to end this workout?',
                    [
                      {
                        text: 'Cancel',
                        style: 'cancel',
                      },
                      {
                        text: 'End & Save',
                        onPress: handleSaveWorkout,
                      },
                    ]
                  );
                }}
              >
                <Ionicons name="stop" size={18} color={colors.error} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Custom workout input for adding exercises */}
          {activeWorkout.isCustom && (
            <View style={[styles.customExerciseContainer, { backgroundColor: colors.card }]}>
              <Text style={[styles.customExerciseLabel, { color: colors.text }]}>
                Add Exercise
              </Text>
              <View style={styles.customExerciseInputRow}>
                <TextInput
                  style={[
                    styles.customExerciseInput,
                    {
                      backgroundColor: colors.background,
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="E.g., Bench Press 3 sets 10 reps 135 lbs or Plank 3 sets 60 seconds"
                  placeholderTextColor={colors.textSecondary}
                  value={customExerciseInput}
                  onChangeText={setCustomExerciseInput}
                />
                <TouchableOpacity
                  style={[
                    styles.addExerciseButton,
                    { backgroundColor: colors.primary },
                    (!customExerciseInput.trim() || isProcessingExercise) && { opacity: 0.5 }
                  ]}
                  onPress={handleAddCustomExercise}
                  disabled={!customExerciseInput.trim() || isProcessingExercise}
                >
                  {isProcessingExercise ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Ionicons name="add" size={24} color="#fff" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}

          {renderExerciseNavigation()}

          <ScrollView style={styles.contentContainer}>
            {isEditingExercise && currentExercise ? (
              <View style={[styles.editExerciseContainer, { backgroundColor: colors.card }]}>
                <Text style={[styles.editExerciseTitle, { color: colors.text }]}>
                  Edit Exercise
                </Text>
                <TextInput
                  style={[
                    styles.editExerciseInput,
                    {
                      backgroundColor: colors.background,
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="E.g., Bench Press 3 sets 10 reps 135 lbs"
                  placeholderTextColor={colors.textSecondary}
                  value={editingExerciseInput}
                  onChangeText={setEditingExerciseInput}
                />
                <View style={styles.editButtonsContainer}>
                  <TouchableOpacity
                    style={[
                      styles.editButton,
                      { backgroundColor: colors.card, borderColor: colors.border }
                    ]}
                    onPress={() => setIsEditingExercise(false)}
                  >
                    <Text style={[styles.editButtonText, { color: colors.text }]}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.editButton,
                      { backgroundColor: colors.primary },
                      !editingExerciseInput.trim() && { opacity: 0.5 }
                    ]}
                    onPress={handleSaveEditedExercise}
                    disabled={!editingExerciseInput.trim() || isProcessingExercise}
                  >
                    {isProcessingExercise ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Text style={styles.editButtonText}>Save</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            ) : activeWorkout.exercises && activeWorkout.exercises.length > 0 ? (
              renderExerciseCard()
            ) : activeWorkout.isCustom && (!activeWorkout.exercises || activeWorkout.exercises.length === 0) ? (
              <View style={[styles.emptyExerciseCard, { backgroundColor: colors.card }]}>
                <Ionicons name="barbell-outline" size={40} color={colors.textSecondary} />
                <Text style={[styles.emptyExerciseTitle, { color: colors.text }]}>
                  No exercises yet
                </Text>
                <Text style={[styles.emptyExerciseText, { color: colors.textSecondary }]}>
                  Use the input above to add exercises to your workout
                </Text>
              </View>
            ) : null}

            <View style={[styles.workoutNotesContainer, { backgroundColor: colors.card }]}>
              <Text style={[styles.workoutNotesLabel, { color: colors.textSecondary }]}>
                Workout Notes:
              </Text>
              <TextInput
                style={[styles.workoutNotesInput, {
                  color: colors.text,
                  backgroundColor: colors.background,
                  borderColor: colors.border
                }]}
                multiline
                placeholder="Add notes about this workout session..."
                placeholderTextColor={colors.textSecondary}
                value={notes}
                onChangeText={setNotes}
              />
            </View>

            <TouchableOpacity
              style={[styles.finishButton, { backgroundColor: colors.primary }]}
              onPress={handleSaveWorkout}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.finishButtonText}>Finish & Save Workout</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '90%',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 15,
    top: 15,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 15,
  },
  timerText: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  workoutControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
    borderWidth: 1,
  },
  customExerciseContainer: {
    marginHorizontal: 20,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  customExerciseLabel: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 10,
  },
  customExerciseInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customExerciseInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
  },
  addExerciseButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  progressItem: {
    height: 4,
    flex: 1,
    marginHorizontal: 2,
    borderRadius: 2,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  exerciseCardContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  exerciseCard: {
    borderRadius: 15,
    padding: 12, // Reduced padding to fit more content
    marginBottom: 0,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden', // Prevent content from overflowing
    alignSelf: 'center', // Center the card
  },
  exerciseName: {
    fontSize: 16, // Reduced font size
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 10, // Reduced margin
    flexShrink: 1, // Allow text to shrink if needed
    flexWrap: 'wrap', // Allow text to wrap
    maxHeight: 40, // Reduced max height
    overflow: 'hidden', // Hide overflow text
  },
  setContainer: {
    marginBottom: 15,
  },
  setTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 10,
  },
  setDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  setValueContainer: {
    width: '48%',
  },
  setLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 5,
  },
  setInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
  },
  completeButton: {
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  notesContainer: {
    marginTop: 5,
  },
  notesLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 5,
  },
  notesText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    fontStyle: 'italic',
  },
  workoutNotesContainer: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
  },
  workoutNotesLabel: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 10,
  },
  workoutNotesInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 80,
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    textAlignVertical: 'top',
  },
  finishButton: {
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  finishButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyExerciseCard: {
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyExerciseTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 10,
    marginBottom: 5,
  },
  emptyExerciseText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    marginBottom: 10,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start', // Align to top to handle multi-line text
    marginBottom: 10,
    flexWrap: 'wrap', // Allow wrapping for very long names
  },
  exerciseActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exerciseActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    borderWidth: 1,
  },
  exerciseNavigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  navigationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  exerciseProgressContainer: {
    alignItems: 'center',
    flex: 1,
  },
  exerciseProgressText: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 8,
  },
  progressDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressDot: {
    marginHorizontal: 3,
  },
  carousel: {
    width: '100%',
    marginBottom: 15, // Add bottom margin
    height: 330, // Fixed height to prevent layout shifts (300 for card + 30 for padding)
  },
  carouselContainer: {
    paddingVertical: 15,
    alignItems: 'center', // Center the cards horizontally
  },
  paginationDotContainer: {
    padding: 8, // Larger touch target
  },
  swipeHintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
    paddingVertical: 5,
    paddingHorizontal: 10,
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 15,
  },
  swipeHintText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.medium,
    marginHorizontal: 5,
  },
  muscleGroupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  muscleGroupLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginRight: 5,
  },
  muscleGroupText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.semibold,
  },
  editExerciseContainer: {
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
  },
  editExerciseTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 15,
  },
  editExerciseInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
  },
  editButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    borderWidth: 1,
  },
  editButtonText: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    color: '#fff',
  },
});

export default ActiveWorkoutModal;
