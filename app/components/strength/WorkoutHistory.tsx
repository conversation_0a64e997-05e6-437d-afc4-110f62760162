import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { WorkoutData } from '../../services/conversationService';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface WorkoutHistoryProps {
  workouts: {
    id: string;
    title: string;
    date: string;
    exercises: {
      name: string;
      sets: number | Array<any>;
      reps?: number;
      weight?: number;
    }[];
  }[];
  onSelectWorkout?: (workout: any) => void;
}

const WorkoutHistory: React.FC<WorkoutHistoryProps> = ({ workouts, onSelectWorkout }) => {
  const { colors, isDark } = useTheme();
  const [expandedWorkout, setExpandedWorkout] = useState<string | null>(null);
  const initialExpansionDone = useRef<boolean>(false);

  // Toggle workout expansion
  const toggleWorkout = (id: string) => {
    setExpandedWorkout(expandedWorkout === id ? null : id);
    // Mark that user has manually toggled a workout
    initialExpansionDone.current = true;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Render a workout item
  const renderWorkoutItem = ({ item }: { item: WorkoutHistoryProps['workouts'][0] }) => {
    const isExpanded = expandedWorkout === item.id;

    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(200)}
        style={[
          styles.workoutItem,
          {
            backgroundColor: colors.card,
            borderColor: isDark ? colors.border : 'transparent',
            borderWidth: isDark ? 1 : 0,
          }
        ]}
      >
        <TouchableOpacity
          style={styles.workoutHeader}
          onPress={() => toggleWorkout(item.id)}
          activeOpacity={0.7}
        >
          <View style={styles.workoutTitleContainer}>
            <Text style={[styles.workoutTitle, { color: colors.text }]}>
              {item.title}
            </Text>
            <Text style={[styles.workoutDate, { color: colors.textSecondary }]}>
              {formatDate(item.date)}
            </Text>
          </View>

          <View style={styles.workoutActions}>
            <Text style={[styles.exerciseCount, { color: colors.textSecondary }]}>
              {item.exercises.length} {item.exercises.length === 1 ? 'exercise' : 'exercises'}
            </Text>
            <Ionicons
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={colors.textSecondary}
              style={styles.expandIcon}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <Animated.View
            entering={FadeIn.duration(200)}
            style={styles.workoutDetails}
          >
            {item.exercises.map((exercise, index) => (
              <View
                key={`${item.id}-${index}`}
                style={[
                  styles.exerciseItem,
                  index < item.exercises.length - 1 && {
                    borderBottomWidth: 1,
                    borderBottomColor: colors.border
                  }
                ]}
              >
                <Text style={[styles.exerciseName, { color: colors.text }]}>
                  {exercise.name}
                </Text>
                <Text style={[styles.exerciseDetails, { color: colors.textSecondary }]}>
                  {typeof exercise.sets === 'number' ? (
                    `${exercise.sets} sets × ${exercise.reps || '-'} reps` +
                    `${exercise.weight ? ` at ${exercise.weight} lbs` : ''}`
                  ) : (
                    `${Array.isArray(exercise.sets) ? exercise.sets.length : 0} sets`
                  )}
                </Text>
              </View>
            ))}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  if (!workouts || workouts.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.card }]}>
        <Ionicons name="calendar-outline" size={40} color={colors.textSecondary} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          No workout history
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
          Complete workouts to see your history here
        </Text>
      </View>
    );
  }

  // If we have workouts but none are expanded, expand the first one by default
  // But only do this once and only if the user hasn't manually toggled any workout
  if (workouts.length > 0 && expandedWorkout === null && !initialExpansionDone.current) {
    // Use setTimeout to avoid state updates during render
    setTimeout(() => {
      setExpandedWorkout(workouts[0].id);
      initialExpansionDone.current = true;
    }, 0);
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="calendar-outline" size={20} color={colors.primary} />
        <Text style={[styles.title, { color: colors.text }]}>Recent Workouts</Text>
      </View>

      <FlatList
        data={workouts}
        renderItem={renderWorkoutItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 5,
  },
  workoutItem: {
    borderRadius: 12,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  workoutTitleContainer: {
    flex: 1,
  },
  workoutTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  workoutDate: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  workoutActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exerciseCount: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginRight: 5,
  },
  expandIcon: {
    marginLeft: 5,
  },
  workoutDetails: {
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  exerciseItem: {
    paddingVertical: 10,
  },
  exerciseName: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  exerciseDetails: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  emptyContainer: {
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 15,
    marginBottom: 5,
  },
  emptySubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
});

export default WorkoutHistory;
