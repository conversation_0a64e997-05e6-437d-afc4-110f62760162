import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { WorkoutData } from '../../services/conversationService';
import { generateUUID } from '../../utils/uuid';
import { saveLog } from '../../services/logService';

interface CreateWorkoutFallbackProps {
  onClose: () => void;
  onWorkoutCreated: (workout: WorkoutData) => void;
  initialDescription?: string;
}

const CreateWorkoutFallback: React.FC<CreateWorkoutFallbackProps> = ({
  onClose,
  onWorkoutCreated,
  initialDescription,
}) => {
  const { colors, isDark } = useTheme();
  const [title, setTitle] = useState('');
  const [exercises, setExercises] = useState([
    { name: '', sets: 3, reps: 10, weight: 0 }
  ]);
  const [isSaving, setIsSaving] = useState(false);

  const addExercise = () => {
    setExercises([...exercises, { name: '', sets: 3, reps: 10, weight: 0 }]);
  };

  const removeExercise = (index: number) => {
    const newExercises = [...exercises];
    newExercises.splice(index, 1);
    setExercises(newExercises);
  };

  const updateExercise = (index: number, field: string, value: any) => {
    const newExercises = [...exercises];
    // @ts-ignore
    newExercises[index][field] = field === 'name' ? value : Number(value);
    setExercises(newExercises);
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a workout title');
      return;
    }

    if (exercises.some(ex => !ex.name.trim())) {
      Alert.alert('Error', 'Please enter a name for all exercises');
      return;
    }

    setIsSaving(true);
    try {
      // Create workout data
      const workout: WorkoutData = {
        title: title.trim(),
        exercises: exercises.map(ex => ({
          name: ex.name.trim(),
          sets: ex.sets,
          reps: ex.reps,
          weight: ex.weight,
        })),
        duration: 45, // Default duration
        notes: 'Created manually'
      };

      // Save to logs
      const logId = generateUUID();
      await saveLog({
        id: logId,
        type: 'workout',
        timestamp: new Date().toISOString(),
        description: workout.title,
        workoutData: workout,
        metrics: {
          workout: workout
        }
      });

      console.log('Workout saved successfully with ID:', logId);
      
      // Notify parent
      onWorkoutCreated(workout);
      onClose();
    } catch (error) {
      console.error('Error saving workout:', error);
      Alert.alert('Error', 'Failed to save workout template');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
    >
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Create Workout Manually</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <Text style={[styles.label, { color: colors.text }]}>Workout Title</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: colors.text,
                backgroundColor: isDark ? colors.background : colors.card,
                borderColor: colors.border,
              },
            ]}
            placeholder="Enter workout title"
            placeholderTextColor={colors.textSecondary}
            value={title}
            onChangeText={setTitle}
          />

          <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 20 }]}>Exercises</Text>
          
          {exercises.map((exercise, index) => (
            <View
              key={index}
              style={[
                styles.exerciseContainer,
                {
                  backgroundColor: isDark ? colors.background : colors.card,
                  borderColor: colors.border,
                },
              ]}
            >
              <View style={styles.exerciseHeader}>
                <Text style={[styles.exerciseNumber, { color: colors.primary }]}>
                  Exercise {index + 1}
                </Text>
                {exercises.length > 1 && (
                  <TouchableOpacity onPress={() => removeExercise(index)}>
                    <Ionicons name="trash-outline" size={20} color={colors.error} />
                  </TouchableOpacity>
                )}
              </View>

              <Text style={[styles.label, { color: colors.text }]}>Name</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    color: colors.text,
                    backgroundColor: isDark ? colors.card : colors.background,
                    borderColor: colors.border,
                  },
                ]}
                placeholder="Exercise name"
                placeholderTextColor={colors.textSecondary}
                value={exercise.name}
                onChangeText={(value) => updateExercise(index, 'name', value)}
              />

              <View style={styles.row}>
                <View style={styles.column}>
                  <Text style={[styles.label, { color: colors.text }]}>Sets</Text>
                  <TextInput
                    style={[
                      styles.input,
                      styles.smallInput,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? colors.card : colors.background,
                        borderColor: colors.border,
                      },
                    ]}
                    placeholder="3"
                    placeholderTextColor={colors.textSecondary}
                    value={exercise.sets.toString()}
                    onChangeText={(value) => updateExercise(index, 'sets', value)}
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.column}>
                  <Text style={[styles.label, { color: colors.text }]}>Reps</Text>
                  <TextInput
                    style={[
                      styles.input,
                      styles.smallInput,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? colors.card : colors.background,
                        borderColor: colors.border,
                      },
                    ]}
                    placeholder="10"
                    placeholderTextColor={colors.textSecondary}
                    value={exercise.reps.toString()}
                    onChangeText={(value) => updateExercise(index, 'reps', value)}
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.column}>
                  <Text style={[styles.label, { color: colors.text }]}>Weight (lbs)</Text>
                  <TextInput
                    style={[
                      styles.input,
                      styles.smallInput,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? colors.card : colors.background,
                        borderColor: colors.border,
                      },
                    ]}
                    placeholder="0"
                    placeholderTextColor={colors.textSecondary}
                    value={exercise.weight.toString()}
                    onChangeText={(value) => updateExercise(index, 'weight', value)}
                    keyboardType="number-pad"
                  />
                </View>
              </View>
            </View>
          ))}

          <TouchableOpacity
            style={[styles.addButton, { borderColor: colors.primary }]}
            onPress={addExercise}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
            <Text style={[styles.addButtonText, { color: colors.primary }]}>Add Exercise</Text>
          </TouchableOpacity>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: colors.border }]}
            onPress={onClose}
            disabled={isSaving}
          >
            <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSave}
            disabled={isSaving}
          >
            <Text style={styles.saveButtonText}>
              {isSaving ? 'Saving...' : 'Save Workout'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  closeButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 5,
  },
  input: {
    height: 45,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 15,
  },
  exerciseContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  exerciseNumber: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  column: {
    flex: 1,
    marginRight: 10,
  },
  smallInput: {
    flex: 1,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginVertical: 10,
  },
  addButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  saveButton: {
    flex: 2,
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default CreateWorkoutFallback;
