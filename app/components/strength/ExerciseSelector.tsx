import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  SectionList,
  ScrollView,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import SearchBar from '../common/SearchBar';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import ScrollIndicator from './ScrollIndicator';

// Import LinearGradient with error handling
let LinearGradient: any = null;
try {
  // Dynamic import to handle the case where the package might not be installed
  LinearGradient = require('expo-linear-gradient').LinearGradient;
} catch (error) {
  // If the package is not available, we'll use a fallback
  console.log('expo-linear-gradient not available, using fallback');
}

// Define the Exercise interface
interface Exercise {
  id: string;
  name: string;
  muscleGroup?: string;
  lastWeight?: number;
  lastReps?: number;
  lastUsed?: string;
  favorite?: boolean;
  pr?: {
    weight: number;
    reps: number;
    date: string;
  };
}

interface ExerciseSelectorProps {
  exercises: Exercise[];
  selectedExercise: Exercise | null;
  onSelectExercise: (exercise: Exercise) => void;
}

// Define muscle groups for categorization
const muscleGroups = [
  'Recent',
  'Favorites',
  'Chest',
  'Back',
  'Shoulders',
  'Arms',
  'Legs',
  'Core',
  'Full Body',
  'Other',
];

// Helper function to categorize exercises
const categorizeExercises = (exercises: Exercise[]) => {
  // Create a map to store exercises by muscle group
  const exercisesByGroup: Record<string, Exercise[]> = {};

  // Initialize all muscle groups with empty arrays
  muscleGroups.forEach(group => {
    exercisesByGroup[group] = [];
  });

  // Sort exercises by last used date (most recent first)
  const sortedExercises = [...exercises].sort((a, b) => {
    if (!a.lastUsed) return 1;
    if (!b.lastUsed) return -1;
    return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
  });

  // Add recent exercises (last 5)
  exercisesByGroup['Recent'] = sortedExercises.slice(0, 5);

  // Add favorite exercises
  exercisesByGroup['Favorites'] = exercises.filter(e => e.favorite);

  // Categorize remaining exercises by muscle group
  exercises.forEach(exercise => {
    const group = exercise.muscleGroup || 'Other';
    if (muscleGroups.includes(group) && group !== 'Recent' && group !== 'Favorites') {
      exercisesByGroup[group].push(exercise);
    } else if (group !== 'Recent' && group !== 'Favorites') {
      exercisesByGroup['Other'].push(exercise);
    }
  });

  // Remove empty groups
  Object.keys(exercisesByGroup).forEach(group => {
    if (exercisesByGroup[group].length === 0) {
      delete exercisesByGroup[group];
    }
  });

  return exercisesByGroup;
};

const ExerciseSelector: React.FC<ExerciseSelectorProps> = ({
  exercises,
  selectedExercise,
  onSelectExercise,
}) => {
  const { colors, isDark } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  // Always use grid view for search results
  const [expandedGroups, setExpandedGroups] = useState<string[]>(['Recent', 'Favorites']);

  // Filter exercises based on search query
  const filteredExercises = useMemo(() => {
    if (!searchQuery.trim()) {
      return exercises;
    }

    const query = searchQuery.toLowerCase().trim();
    return exercises.filter(exercise =>
      exercise.name.toLowerCase().includes(query) ||
      (exercise.muscleGroup && exercise.muscleGroup.toLowerCase().includes(query))
    );
  }, [exercises, searchQuery]);

  // Categorize exercises by muscle group
  const exercisesByGroup = useMemo(() => {
    return categorizeExercises(filteredExercises);
  }, [filteredExercises]);

  // Convert to section list format
  const sections = useMemo(() => {
    return Object.keys(exercisesByGroup).map(group => ({
      title: group,
      data: exercisesByGroup[group],
    }));
  }, [exercisesByGroup]);

  // Toggle expanded state for a group
  const toggleGroup = (group: string) => {
    setExpandedGroups(prev =>
      prev.includes(group)
        ? prev.filter(g => g !== group)
        : [...prev, group]
    );
  };

  // Clear search query
  const clearSearch = () => {
    setSearchQuery('');
  };

  // Render an exercise item
  const renderExerciseItem = (exercise: Exercise) => {
    const isSelected = selectedExercise?.id === exercise.id;
    const isSearching = searchQuery.trim().length > 0;

    return (
      <TouchableOpacity
        style={[
          styles.exerciseItem,
          isSearching ? styles.gridExerciseItem : null,
          {
            backgroundColor: isSelected
              ? colors.primaryLight + (isDark ? '40' : '30')
              : colors.card,
            borderColor: isSelected
              ? colors.primary
              : isDark ? colors.border : 'transparent',
            shadowColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)',
          }
        ]}
        onPress={() => onSelectExercise(exercise)}
      >
        <Text
          style={[styles.exerciseName, { color: colors.text }]}
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {exercise.name}
        </Text>
        {exercise.lastWeight && (
          <Text
            style={[styles.exerciseStats, { color: colors.textSecondary }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {exercise.lastWeight} lbs × {exercise.lastReps} reps
          </Text>
        )}
        {isSearching && exercise.muscleGroup && (
          <View style={styles.tagContainer}>
            <Text style={[styles.muscleGroupTag, { backgroundColor: colors.primaryLight + '40', color: colors.primary }]}>
              {exercise.muscleGroup}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Render a section header
  const renderSectionHeader = ({ section }: { section: { title: string, data: Exercise[] } }) => {
    const isExpanded = expandedGroups.includes(section.title);

    return (
      <TouchableOpacity
        style={[
          styles.sectionHeader,
          { borderBottomColor: colors.border }
        ]}
        onPress={() => toggleGroup(section.title)}
      >
        <View style={styles.sectionTitleContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {section.title}
          </Text>
          <Text style={[styles.sectionCount, { color: colors.textSecondary }]}>
            {section.data.length}
          </Text>
        </View>
        <Ionicons
          name={isExpanded ? 'chevron-up' : 'chevron-down'}
          size={20}
          color={colors.textSecondary}
        />
      </TouchableOpacity>
    );
  };

  // Define all hooks at the top level of the component
  const flatListRef = useRef<FlatList>(null);
  const [canScroll, setCanScroll] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Check if content is scrollable
  const handleContentSizeChange = (width: number, height: number) => {
    // Get the container height (approximate)
    const containerHeight = 330; // This should be slightly less than the container height in StrengthScreen
    setCanScroll(height > containerHeight);
  };

  // Track scroll position
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setIsScrolled(offsetY > 10);
  };

  // If searching, show a flat list of results
  if (searchQuery.trim()) {

    return (
      <View style={styles.container}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
          placeholder="Search exercises..."
        />

        <View style={styles.resultsHeader}>
          <Text style={[styles.resultsText, { color: colors.textSecondary }]}>
            {filteredExercises.length} {filteredExercises.length === 1 ? 'result' : 'results'}
          </Text>
        </View>

        <View style={styles.listContainer}>
          <FlatList
            ref={flatListRef}
            data={filteredExercises}
            renderItem={({ item }) => (
              <View style={styles.gridItemContainer}>
                {renderExerciseItem(item)}
              </View>
            )}
            keyExtractor={(item) => item.id}
            numColumns={2}
            contentContainerStyle={[
              styles.exerciseList,
              styles.gridContainer,
              // Add extra padding at the bottom to show there's more content
              canScroll ? { paddingBottom: 40 } : null
            ]}
            columnWrapperStyle={styles.gridRow}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={handleContentSizeChange}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            ListEmptyComponent={
              <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
                <Ionicons name="search-outline" size={40} color={colors.textSecondary} />
                <Text style={[styles.emptyStateText, { color: colors.text }]}>
                  No exercises found
                </Text>
                <Text style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}>
                  Try a different search term
                </Text>
              </View>
            }
            ListFooterComponent={
              canScroll ? (
                <View style={{ height: 20 }} />
              ) : null
            }
          />

          {/* Subtle gradient fade at the bottom to indicate scrollability */}
          {canScroll && LinearGradient ? (
            <LinearGradient
              colors={[
                'rgba(0,0,0,0)',
                isDark ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.05)'
              ]}
              style={styles.gradientFade}
              pointerEvents="none"
            />
          ) : canScroll ? (
            // Fallback when LinearGradient is not available
            <View
              style={[
                styles.gradientFade,
                { backgroundColor: isDark ? 'rgba(0,0,0,0.05)' : 'rgba(0,0,0,0.02)' }
              ]}
              pointerEvents="none"
            />
          ) : null}

          {/* Scroll indicator */}
          <ScrollIndicator visible={canScroll && !isScrolled} />
        </View>
      </View>
    );
  }

  // Otherwise show grouped sections
  return (
    <View style={styles.container}>
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        onClear={clearSearch}
        placeholder="Search exercises..."
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        {sections.map(section => (
          <Animated.View
            key={section.title}
            entering={FadeIn.duration(200)}
            exiting={FadeOut.duration(200)}
          >
            {renderSectionHeader({ section })}

            {expandedGroups.includes(section.title) && (
              <View style={styles.sectionContent}>
                <FlatList
                  data={section.data}
                  renderItem={({ item }) => (
                    <View style={styles.horizontalItemContainer}>
                      {renderExerciseItem(item)}
                    </View>
                  )}
                  keyExtractor={(item) => item.id}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.horizontalList}
                />
              </View>
            )}
          </Animated.View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  exerciseList: {
    paddingBottom: 20,
    paddingHorizontal: 5,
  },
  gridContainer: {
    paddingHorizontal: 5,
    paddingTop: 5,
  },
  gridRow: {
    justifyContent: 'space-between',
    marginBottom: 0, // Remove extra margin between rows
  },
  gridItemContainer: {
    width: '48%', // Just under 50% to account for margin
    marginBottom: 12,
  },
  exerciseItem: {
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: '100%',
  },
  gridExerciseItem: {
    width: '100%',
    height: 130, // Slightly taller for better content display
  },
  exerciseName: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  exerciseStats: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    position: 'absolute',
    bottom: 10,
    left: 15,
    right: 15,
  },
  muscleGroupTag: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    overflow: 'hidden',
    alignSelf: 'flex-start',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  sectionCount: {
    fontSize: typography.sizes.sm,
    marginLeft: 8,
  },
  sectionContent: {
    marginVertical: 10,
  },
  horizontalList: {
    paddingHorizontal: 5,
  },
  horizontalItemContainer: {
    width: 180,
    height: 100,
    marginRight: 10,
  },
  resultsHeader: {
    marginBottom: 10,
    paddingHorizontal: 5,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  listContainer: {
    flex: 1,
    position: 'relative',
  },
  gradientFade: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 40,
    zIndex: 1,
    opacity: 0.8,
  },
  emptyState: {
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  emptyStateText: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 15,
    marginBottom: 5,
  },
  emptyStateSubtext: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
});

export default ExerciseSelector;
