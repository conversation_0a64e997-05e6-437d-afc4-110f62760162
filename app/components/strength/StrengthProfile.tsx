import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';

interface StrengthProfileProps {
  exercise: {
    name: string;
    muscleGroup?: string;
    lastWeight?: number;
    lastReps?: number;
    pr?: {
      weight: number;
      reps: number;
      date: string;
    };
  };
  progressRate?: number; // Percentage increase over time
  suggestedWeight?: number; // Suggested weight for next workout
}

const StrengthProfile: React.FC<StrengthProfileProps> = ({
  exercise,
  progressRate = 5, // Default progress rate
  suggestedWeight
}) => {
  const { colors, isDark } = useTheme();

  // Calculate suggested weight if not provided
  const calculatedSuggestedWeight = suggestedWeight ||
    (exercise.lastWeight ? Math.round(exercise.lastWeight * 1.05) : 0);

  // Generate a strength insight based on the data
  const generateInsight = () => {
    if (!exercise.lastWeight) {
      return "Start tracking this exercise to get personalized insights.";
    }

    if (progressRate > 10) {
      return `You're making excellent progress on ${exercise.name}. Keep up the great work!`;
    } else if (progressRate > 5) {
      return `You're making steady progress on ${exercise.name}. Consider increasing intensity for faster gains.`;
    } else if (progressRate > 0) {
      return `You're making slow progress on ${exercise.name}. Try increasing volume or frequency.`;
    } else if (progressRate < 0) {
      return `Your performance on ${exercise.name} has decreased. This could be due to fatigue or technique changes.`;
    } else {
      return `You may have reached a plateau on ${exercise.name}. Consider varying your rep ranges or adding accessory exercises.`;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <Ionicons name="analytics-outline" size={20} color={colors.primary} />
        <Text style={[styles.title, { color: colors.text }]}>Strength Profile</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.insightContainer}>
          <Text style={[styles.insightText, { color: colors.text }]}>
            {generateInsight()}
          </Text>
        </View>

        <View style={[styles.divider, { backgroundColor: colors.border }]} />

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Progress Rate
            </Text>
            <Text style={[
              styles.statValue,
              {
                color: progressRate > 0 ? colors.primary :
                       progressRate < 0 ? colors.error :
                       colors.textSecondary
              }
            ]}>
              {progressRate > 0 ? '+' : ''}{progressRate}%
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Last Weight
            </Text>
            <Text style={[styles.statValue, { color: colors.text }]}>
              {exercise.lastWeight || 0} lbs
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Suggested Next
            </Text>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {calculatedSuggestedWeight} lbs
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  content: {
    paddingHorizontal: 5,
  },
  insightContainer: {
    marginBottom: 15,
  },
  insightText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 22,
  },
  divider: {
    height: 1,
    width: '100%',
    marginBottom: 15,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 5,
  },
  statValue: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default StrengthProfile;
