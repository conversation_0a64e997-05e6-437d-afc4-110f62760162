import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { WorkoutData, WorkoutExercise } from '../../services/conversationService';
import Animated, { FadeIn, FadeOut, SlideInDown } from 'react-native-reanimated';

interface WorkoutSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  workoutTemplates: WorkoutData[];
  onSelectWorkout: (workout: WorkoutData) => void;
  onCreateCustomWorkout: (prompt: string) => void;
}

const WorkoutSelectionModal: React.FC<WorkoutSelectionModalProps> = ({
  visible,
  onClose,
  workoutTemplates,
  onSelectWorkout,
  onCreateCustomWorkout,
}) => {
  const { colors, isDark } = useTheme();
  const [activeTab, setActiveTab] = useState<'templates' | 'custom' | 'aggregate'>('templates');
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedExercises, setSelectedExercises] = useState<{[key: string]: boolean}>({});
  const [aggregateWorkoutName, setAggregateWorkoutName] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<WorkoutData | null>(null);

  // Extract all unique exercises from workout templates
  const [allExercises, setAllExercises] = useState<WorkoutExercise[]>([]);
  const [exercisesByMuscleGroup, setExercisesByMuscleGroup] = useState<{[key: string]: WorkoutExercise[]}>({});
  const [expandedGroups, setExpandedGroups] = useState<string[]>(['All']);

  // Extract all exercises when templates change
  useEffect(() => {
    const uniqueExercises = new Map<string, WorkoutExercise>();
    const groupedExercises: {[key: string]: WorkoutExercise[]} = { 'All': [] };

    workoutTemplates.forEach(template => {
      template.exercises.forEach(exercise => {
        if (!uniqueExercises.has(exercise.name)) {
          const exerciseWithMuscleGroup = {
            ...exercise,
            muscleGroup: exercise.muscleGroup || 'Other'
          };
          uniqueExercises.set(exercise.name, exerciseWithMuscleGroup);

          // Add to All group
          groupedExercises['All'].push(exerciseWithMuscleGroup);

          // Add to specific muscle group
          const group = exerciseWithMuscleGroup.muscleGroup;
          if (!groupedExercises[group]) {
            groupedExercises[group] = [];
          }
          groupedExercises[group].push(exerciseWithMuscleGroup);
        }
      });
    });

    setAllExercises(Array.from(uniqueExercises.values()));
    setExercisesByMuscleGroup(groupedExercises);
  }, [workoutTemplates]);

  // Toggle muscle group expansion
  const toggleMuscleGroup = (group: string) => {
    setExpandedGroups(prev => {
      if (prev.includes(group)) {
        return prev.filter(g => g !== group);
      } else {
        return [...prev, group];
      }
    });
  };

  // Reset state when modal closes
  const handleClose = () => {
    setActiveTab('templates');
    setCustomPrompt('');
    setSelectedExercises({});
    setAggregateWorkoutName('');
    setSelectedTemplate(null);
    onClose();
  };

  // Handle custom workout creation
  const handleCreateCustomWorkout = () => {
    if (customPrompt.trim()) {
      onCreateCustomWorkout(customPrompt);
      handleClose();
    }
  };

  // Handle aggregate workout creation
  const handleCreateAggregateWorkout = () => {
    // Get all selected exercises
    const exercises = allExercises.filter(exercise =>
      selectedExercises[exercise.name]
    );

    // Create a new workout with the selected exercises
    if (exercises.length > 0) {
      const aggregateWorkout: WorkoutData = {
        title: aggregateWorkoutName || 'Custom Workout',
        exercises,
      };
      onSelectWorkout(aggregateWorkout);
      handleClose();
    }
  };

  // Toggle exercise selection for aggregate workout
  const toggleExerciseSelection = (exerciseName: string) => {
    setSelectedExercises(prev => ({
      ...prev,
      [exerciseName]: !prev[exerciseName]
    }));
  };

  // Render a workout template item
  const renderWorkoutTemplate = ({ item }: { item: WorkoutData }) => {
    const isSelected = selectedTemplate && selectedTemplate.title === item.title;

    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(200)}
      >
        <TouchableOpacity
          style={[
            styles.templateItem,
            {
              backgroundColor: colors.card,
              borderColor: isSelected ? colors.primary : (isDark ? colors.border : 'transparent'),
              borderWidth: isSelected ? 2 : (isDark ? 1 : 0),
            }
          ]}
          onPress={() => setSelectedTemplate(item)}
        >
          <View style={styles.templateHeader}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "barbell-outline"}
              size={20}
              color={colors.primary}
            />
            <Text style={[styles.templateTitle, { color: colors.text }]}>
              {item.title}
            </Text>
          </View>
          <Text style={[styles.templateDetails, { color: colors.textSecondary }]}>
            {item.exercises.length} {item.exercises.length === 1 ? 'exercise' : 'exercises'}
          </Text>
          <Ionicons name="chevron-forward" size={18} color={colors.textSecondary} style={styles.templateIcon} />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Render an exercise item for aggregate selection
  const renderAggregateItem = ({ item }: { item: WorkoutExercise }) => (
    <View key={item.name} style={styles.exerciseItemWrapper}>
      <TouchableOpacity
        style={[
          styles.aggregateItem,
          {
            backgroundColor: colors.card,
            borderColor: selectedExercises[item.name] ? colors.primary : (isDark ? colors.border : 'transparent'),
            borderWidth: selectedExercises[item.name] ? 2 : (isDark ? 1 : 0),
          }
        ]}
        onPress={() => toggleExerciseSelection(item.name)}
      >
        <View style={styles.aggregateHeader}>
          <View style={[
            styles.aggregateCheckbox,
            { borderColor: selectedExercises[item.name] ? colors.primary : colors.border }
          ]}>
            {selectedExercises[item.name] && (
              <Ionicons name="checkmark" size={16} color={colors.primary} />
            )}
          </View>
          <Text
            style={[styles.aggregateTitle, { color: colors.text }]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {item.name}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
        <Animated.View
          entering={SlideInDown.duration(300)}
          style={[
            styles.modalContainer,
            { backgroundColor: colors.background }
          ]}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Start Workout
            </Text>
            <TouchableOpacity onPress={handleClose}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'templates' && { borderBottomColor: colors.primary, borderBottomWidth: 2 }
              ]}
              onPress={() => setActiveTab('templates')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'templates' ? colors.primary : colors.textSecondary }
                ]}
              >
                Templates
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'custom' && { borderBottomColor: colors.primary, borderBottomWidth: 2 }
              ]}
              onPress={() => setActiveTab('custom')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'custom' ? colors.primary : colors.textSecondary }
                ]}
              >
                Custom
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'aggregate' && { borderBottomColor: colors.primary, borderBottomWidth: 2 }
              ]}
              onPress={() => setActiveTab('aggregate')}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === 'aggregate' ? colors.primary : colors.textSecondary }
                ]}
              >
                Aggregate
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {activeTab === 'templates' && (
              <View style={styles.templatesContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Select a Workout Template
                </Text>
                {workoutTemplates.length > 0 ? (
                  <>
                    <FlatList
                      data={workoutTemplates}
                      renderItem={renderWorkoutTemplate}
                      keyExtractor={(item) => item.title}
                      scrollEnabled={false}
                    />

                    <TouchableOpacity
                      style={[
                        styles.startWorkoutButton,
                        { backgroundColor: colors.primary },
                        !selectedTemplate && { opacity: 0.5 }
                      ]}
                      disabled={!selectedTemplate}
                      onPress={() => {
                        if (selectedTemplate) {
                          onSelectWorkout(selectedTemplate);
                          handleClose();
                        }
                      }}
                    >
                      <Ionicons name="play" size={20} color="#fff" style={{ marginRight: 8 }} />
                      <Text style={styles.startWorkoutButtonText}>Start Workout</Text>
                    </TouchableOpacity>
                  </>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                      No workout templates available
                    </Text>
                    <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                      Create templates in the Strength tab
                    </Text>
                  </View>
                )}
              </View>
            )}

            {activeTab === 'custom' && (
              <View style={styles.customContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Create a Custom Workout
                </Text>
                <View style={[styles.customInfoBox, { backgroundColor: colors.card, borderColor: colors.border }]}>
                  <Ionicons name="information-circle-outline" size={24} color={colors.primary} style={styles.infoIcon} />
                  <View style={styles.infoTextContainer}>
                    <Text style={[styles.infoTitle, { color: colors.text }]}>
                      How to use Custom Workout
                    </Text>
                    <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                      Start your workout and describe exercises as you go. Simply tell Lotus what exercise you're doing,
                      including sets, reps, and weights, and it will be logged automatically.
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={[
                    styles.customButton,
                    { backgroundColor: colors.primary }
                  ]}
                  onPress={() => {
                    // Create an empty custom workout
                    const customWorkout: WorkoutData = {
                      title: "Custom Workout",
                      exercises: [],
                      isCustom: true
                    };
                    onSelectWorkout(customWorkout);
                    handleClose();
                  }}
                >
                  <Ionicons name="add-circle-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
                  <Text style={styles.buttonText}>Start Custom Workout</Text>
                </TouchableOpacity>
              </View>
            )}

            {activeTab === 'aggregate' && (
              <View style={styles.aggregateContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Create Custom Workout
                </Text>
                <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
                  Select individual exercises to combine
                </Text>

                <TextInput
                  style={[
                    styles.aggregateNameInput,
                    {
                      backgroundColor: colors.card,
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="Name your custom workout"
                  placeholderTextColor={colors.textSecondary}
                  value={aggregateWorkoutName}
                  onChangeText={setAggregateWorkoutName}
                />

                {Object.keys(exercisesByMuscleGroup).length > 0 ? (
                  <View style={styles.muscleGroupsContainer}>
                    {Object.keys(exercisesByMuscleGroup).sort().map(group => (
                      <View key={group} style={styles.muscleGroupSection}>
                        <TouchableOpacity
                          style={[
                            styles.muscleGroupHeader,
                            { borderBottomColor: colors.border }
                          ]}
                          onPress={() => toggleMuscleGroup(group)}
                        >
                          <Text style={[styles.muscleGroupTitle, { color: colors.text }]}>
                            {group} ({exercisesByMuscleGroup[group].length})
                          </Text>
                          <Ionicons
                            name={expandedGroups.includes(group) ? 'chevron-up' : 'chevron-down'}
                            size={20}
                            color={colors.textSecondary}
                          />
                        </TouchableOpacity>

                        {expandedGroups.includes(group) && (
                          <View style={styles.exercisesContainer}>
                            {exercisesByMuscleGroup[group].map(exercise => renderAggregateItem({ item: exercise }))}
                          </View>
                        )}
                      </View>
                    ))}
                  </View>
                ) : (
                  <View style={styles.emptyContainer}>
                    <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                      No exercises available
                    </Text>
                    <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                      Create workouts in the Strength tab first
                    </Text>
                  </View>
                )}

                <TouchableOpacity
                  style={[
                    styles.aggregateButton,
                    { backgroundColor: colors.primary },
                    Object.values(selectedExercises).filter(Boolean).length === 0 && { opacity: 0.5 }
                  ]}
                  onPress={handleCreateAggregateWorkout}
                  disabled={Object.values(selectedExercises).filter(Boolean).length === 0}
                >
                  <Text style={styles.buttonText}>Create Custom Workout</Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  modalContent: {
    padding: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
  },
  tabText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  templatesContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 10,
  },
  sectionSubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
  },
  templateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 10,
  },
  templateDetails: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginLeft: 30,
  },
  templateIcon: {
    marginLeft: 'auto',
  },
  customContainer: {
    marginBottom: 20,
  },
  customInfoBox: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  infoTextContainer: {
    flex: 1,
  },
  infoTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 5,
  },
  infoText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
  },
  customButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonText: {
    color: '#fff',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    marginBottom: 5,
  },
  emptySubtext: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  aggregateContainer: {
    marginBottom: 20,
  },
  aggregateNameInput: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 20,
  },
  aggregateList: {
    marginBottom: 20,
  },
  exerciseItemWrapper: {
    width: '48%',
    marginBottom: 10,
  },
  aggregateItem: {
    padding: 12,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: 70,
  },
  aggregateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aggregateCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  aggregateTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    flexShrink: 1,
  },
  aggregateDetails: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginLeft: 30,
    marginTop: 5,
  },
  aggregateButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
  },
  exerciseRow: {
    justifyContent: 'space-between',
  },
  muscleGroupsContainer: {
    marginBottom: 20,
  },
  muscleGroupSection: {
    marginBottom: 10,
  },
  muscleGroupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  muscleGroupTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  exercisesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingTop: 10,
  },
  startWorkoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 10,
    marginTop: 20,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  startWorkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default WorkoutSelectionModal;
