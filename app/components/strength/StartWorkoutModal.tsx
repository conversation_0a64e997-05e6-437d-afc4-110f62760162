import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { WorkoutData } from '../../services/conversationService';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutUp } from 'react-native-reanimated';

interface StartWorkoutModalProps {
  visible: boolean;
  workout: WorkoutData | null;
  onClose: () => void;
  onStart: () => void;
}

const StartWorkoutModal: React.FC<StartWorkoutModalProps> = ({
  visible,
  workout,
  onClose,
  onStart,
}) => {
  const { colors, isDark } = useTheme();

  if (!workout) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <Pressable 
        style={styles.overlay} 
        onPress={onClose}
      >
        <Animated.View
          entering={SlideInDown.springify().damping(15)}
          exiting={SlideOutUp.springify().damping(15)}
          style={[
            styles.modalContainer,
            { 
              backgroundColor: colors.card,
              borderColor: isDark ? colors.border : 'transparent',
              borderWidth: isDark ? 1 : 0,
              shadowColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)',
            }
          ]}
        >
          <Pressable style={styles.modalContent} onPress={(e) => e.stopPropagation()}>
            <View style={styles.headerRow}>
              <Ionicons name="fitness-outline" size={24} color={colors.primary} />
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Start Workout
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <View style={[styles.workoutInfo, { borderColor: colors.border }]}>
              <Text style={[styles.workoutTitle, { color: colors.text }]}>
                {workout.title}
              </Text>
              <Text style={[styles.workoutDetails, { color: colors.textSecondary }]}>
                {workout.exercises.length} {workout.exercises.length === 1 ? 'exercise' : 'exercises'}
              </Text>
            </View>
            
            <View style={styles.exerciseList}>
              {workout.exercises.slice(0, 3).map((exercise, index) => (
                <Animated.View 
                  key={`${exercise.name}-${index}`}
                  entering={FadeIn.delay(100 * index)}
                  style={[
                    styles.exerciseItem,
                    { 
                      backgroundColor: isDark ? colors.background : colors.card,
                      borderColor: colors.border,
                    }
                  ]}
                >
                  <Ionicons name="barbell-outline" size={18} color={colors.primary} style={styles.exerciseIcon} />
                  <Text style={[styles.exerciseName, { color: colors.text }]} numberOfLines={1}>
                    {exercise.name}
                  </Text>
                </Animated.View>
              ))}
              
              {workout.exercises.length > 3 && (
                <Text style={[styles.moreExercises, { color: colors.textSecondary }]}>
                  +{workout.exercises.length - 3} more
                </Text>
              )}
            </View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton, { borderColor: colors.border }]}
                onPress={onClose}
              >
                <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.startButton, { backgroundColor: colors.primary }]}
                onPress={onStart}
              >
                <Text style={styles.startButtonText}>Start Workout</Text>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Animated.View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 20,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 5,
  },
  modalContent: {
    padding: 20,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 10,
    flex: 1,
  },
  closeButton: {
    padding: 5,
  },
  workoutInfo: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  workoutTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 5,
  },
  workoutDetails: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  exerciseList: {
    marginBottom: 20,
  },
  exerciseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 10,
    marginBottom: 8,
    borderWidth: 1,
  },
  exerciseIcon: {
    marginRight: 10,
  },
  exerciseName: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  moreExercises: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    marginTop: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  button: {
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  startButton: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  startButtonText: {
    color: '#fff',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default StartWorkoutModal;
