import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, SlideInDown, FadeOut } from 'react-native-reanimated';
import { generateUUID } from '../../utils/uuid';
import { saveLog, WorkoutData } from '../../services/conversationService';
import { processWorkoutPrompt } from '../../services/chatService';

interface CreateWorkoutModalProps {
  visible: boolean;
  onClose: () => void;
  onWorkoutCreated: (workout: WorkoutData) => void;
}

const CreateWorkoutModal: React.FC<CreateWorkoutModalProps> = ({
  visible,
  onClose,
  onWorkoutCreated,
}) => {
  const { colors } = useTheme();
  const [workoutDescription, setWorkoutDescription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [createdWorkout, setCreatedWorkout] = useState<WorkoutData | null>(null);
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    // Reset state when modal opens
    if (visible) {
      setWorkoutDescription('');
      setCreatedWorkout(null);
      setIsProcessing(false);

      // Focus the input after a short delay
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [visible]);

  const handleCreateWorkout = async () => {
    if (!workoutDescription.trim()) {
      Alert.alert('Error', 'Please describe your workout');
      return;
    }

    setIsProcessing(true);
    try {
      // Process the workout description using the LLM
      const result = await processWorkoutPrompt(workoutDescription);

      if (result.workout) {
        setCreatedWorkout(result.workout);
      } else {
        throw new Error('Failed to create workout');
      }
    } catch (error: any) {
      console.error('Error creating workout:', error);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to create workout. Please try again.';
      let errorTitle = 'Error';

      // Check for specific error messages in the response data
      const responseData = error.response?.data;
      const errorResponseMessage = responseData?.error || responseData?.message || '';

      // Log detailed error information for debugging
      console.log('Detailed error info:', {
        message: error.message,
        status: error.response?.status,
        responseData,
        errorResponseMessage
      });

      // Check if this is a Groq service issue or a Lambda/API Gateway issue
      if (errorResponseMessage.includes('Service Unavailable') ||
          error.message.includes('503')) {
        // This is definitely a Groq service issue
        errorTitle = 'AI Service Temporarily Unavailable';
        errorMessage = 'Our AI service provider (Groq) is currently experiencing technical difficulties. This is affecting all AI features in the app. Please try again later when the service is restored.';
      } else if (error.message.includes('504') || errorResponseMessage.includes('timeout')) {
        // This could be an API Gateway timeout
        errorTitle = 'Request Timeout';
        errorMessage = 'The request timed out before completion. This could be due to high demand on our servers or the complexity of your workout description. Please try a simpler description or try again later.';
      } else if (error.message.includes('502')) {
        // This is likely an issue between API Gateway and Lambda
        errorTitle = 'Server Communication Error';
        errorMessage = 'There was an issue with our server communication. Our team has been notified. Please try again later.';
      } else {
        // Generic server error
        errorTitle = 'Server Error';
        errorMessage = 'We encountered an unexpected error while processing your request. Please try again later.';
      }

      // Handle network errors separately
      if (error.message.includes('Network Error')) {
        errorTitle = 'Connection Issue';
        errorMessage = 'Network connection issue. Please check your internet connection and try again.';
      }

      Alert.alert(errorTitle, errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSaveWorkout = async () => {
    if (!createdWorkout) return;

    try {
      // Format the workout data to match what the backend expects
      // The log Lambda expects workoutData instead of metrics.workout
      const logId = generateUUID();
      await saveLog({
        id: logId,
        type: 'workout',
        timestamp: new Date().toISOString(),
        description: createdWorkout.title,
        // Include both formats to ensure compatibility
        workoutData: createdWorkout,
        metrics: {
          workout: createdWorkout
        }
      });

      console.log('Workout saved successfully with ID:', logId);

      // Notify parent component
      onWorkoutCreated(createdWorkout);

      // Close modal
      onClose();

      // Show success message
      Alert.alert('Success', 'Workout template saved successfully!');
    } catch (error) {
      console.error('Error saving workout:', error);
      Alert.alert('Error', 'Failed to save workout template');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Animated.View
        style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <Animated.View
            style={[styles.modalContainer, { backgroundColor: colors.background }]}
            entering={SlideInDown.duration(400).springify()}
          >
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>

            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {createdWorkout ? 'Review Workout' : 'Create New Workout'}
            </Text>

            {!createdWorkout ? (
              // Step 1: Enter workout description
              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>
                  Describe your workout in natural language
                </Text>
                <TextInput
                  ref={inputRef}
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      backgroundColor: colors.card,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="E.g., Upper body workout with 3 sets of bench press, 3 sets of shoulder press, and 3 sets of tricep extensions"
                  placeholderTextColor={colors.textSecondary}
                  value={workoutDescription}
                  onChangeText={setWorkoutDescription}
                  multiline
                  numberOfLines={5}
                  textAlignVertical="top"
                />

                <TouchableOpacity
                  style={[
                    styles.createButton,
                    { backgroundColor: colors.primary },
                    !workoutDescription.trim() && { opacity: 0.7 }
                  ]}
                  onPress={handleCreateWorkout}
                  disabled={!workoutDescription.trim() || isProcessing}
                >
                  {isProcessing ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.buttonText}>Create Workout</Text>
                  )}
                </TouchableOpacity>
              </View>
            ) : (
              // Step 2: Review created workout
              <View style={styles.reviewContainer}>
                <View style={[styles.workoutHeader, { borderBottomColor: colors.border }]}>
                  <Text style={[styles.workoutTitle, { color: colors.text }]}>
                    {createdWorkout.title}
                  </Text>
                </View>

                <ScrollView style={styles.workoutReviewContainer}>
                  {createdWorkout.exercises.map((exercise, index) => (
                    <View
                      key={index}
                      style={[
                        styles.exerciseItem,
                        index < createdWorkout.exercises.length - 1 && {
                          borderBottomWidth: 1,
                          borderBottomColor: colors.border
                        }
                      ]}
                    >
                      <View style={styles.exerciseHeader}>
                        <View style={[styles.exerciseNumberBadge, { backgroundColor: colors.primaryLight }]}>
                          <Text style={[styles.exerciseNumber, { color: colors.primary }]}>
                            {index + 1}
                          </Text>
                        </View>
                        <Text style={[styles.exerciseName, { color: colors.text }]}>
                          {exercise.name}
                        </Text>
                      </View>

                      <View style={styles.exerciseDetails}>
                        {Array.isArray(exercise.sets) && exercise.sets.length > 0 ? (
                          // Handle array of sets (original format)
                          exercise.sets.map((set, setIndex) => (
                            <View key={setIndex} style={styles.setItem}>
                              <Text style={[styles.setText, { color: colors.textSecondary }]}>
                                Set {setIndex + 1}:
                              </Text>
                              <Text style={[styles.setDetails, { color: colors.text }]}>
                                {typeof set === 'object' ? (
                                  `${set.reps || '-'} reps ${set.weight ? `at ${set.weight}${typeof set.weight === 'number' ? ' lbs' : ''}` : ''}`
                                ) : (
                                  typeof set === 'string' ? set : JSON.stringify(set)
                                )}
                              </Text>
                            </View>
                          ))
                        ) : (
                          // Handle non-array sets (new format from Groq)
                          <View style={styles.exerciseSummary}>
                            {typeof exercise.sets === 'number' ? (
                              <View style={styles.exerciseSummaryTextContainer}>
                                <Text style={[styles.exerciseSummaryText, { color: colors.text, fontWeight: 'bold' }]}>
                                  {exercise.sets} sets
                                </Text>
                                {exercise.reps ? (
                                  <Text style={[styles.exerciseSummaryText, { color: colors.text }]}>
                                    {' × '}{exercise.reps} reps
                                  </Text>
                                ) : null}
                                {exercise.weight && exercise.weight > 0 ? (
                                  <Text style={[styles.exerciseSummaryText, { color: colors.text }]}>
                                    {' at '}{exercise.weight} lbs
                                  </Text>
                                ) : null}
                              </View>
                            ) : (
                              <Text style={[styles.exerciseSummaryText, { color: colors.text }]}>
                                Details not available
                              </Text>
                            )}
                          </View>
                        )}

                        {exercise.notes && (
                          <Text style={[styles.exerciseNotes, { color: colors.textSecondary }]}>
                            Note: {exercise.notes}
                          </Text>
                        )}
                      </View>
                    </View>
                  ))}
                </ScrollView>

                <View style={styles.saveButtonContainer}>
                  <TouchableOpacity
                    style={[styles.saveButton, { backgroundColor: colors.primary }]}
                    onPress={handleSaveWorkout}
                  >
                    <Text style={styles.buttonText}>Save Workout Template</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </Animated.View>
        </KeyboardAvoidingView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    height: '80%', // Use fixed height instead of maxHeight
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    marginBottom: 50, // Add margin at the bottom to position the modal higher
    display: 'flex', // Ensure flex display
    flexDirection: 'column', // Stack children vertically
  },
  closeButton: {
    position: 'absolute',
    top: 15,
    right: 15,
    zIndex: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 20,
    marginTop: 10,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
  },
  reviewContainer: {
    flex: 1,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  inputLabel: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 10,
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    minHeight: 120,
  },
  createButton: {
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  workoutReviewContainer: {
    flex: 1,
    paddingBottom: 10,
    minHeight: 250, // Ensure a minimum height
    marginBottom: 10, // Add margin at the bottom
  },
  workoutHeader: {
    borderBottomWidth: 1,
    paddingBottom: 15,
    marginBottom: 15,
  },
  workoutTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  exerciseItem: {
    paddingVertical: 15,
  },
  exerciseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  exerciseNumberBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  exerciseNumber: {
    fontSize: 14,
    fontFamily: typography.fontFamily.semibold,
  },
  exerciseName: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
  },
  exerciseDetails: {
    marginLeft: 38,
  },
  setItem: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  setText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    width: 60,
  },
  setDetails: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  exerciseSummary: {
    marginBottom: 8,
    paddingVertical: 5,
  },
  exerciseSummaryTextContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  exerciseSummaryText: {
    fontSize: 15,
    fontFamily: typography.fontFamily.medium,
    marginVertical: 5,
  },
  exerciseNotes: {
    fontSize: 13,
    fontFamily: typography.fontFamily.italic,
    marginTop: 5,
    fontStyle: 'italic',
  },
  saveButtonContainer: {
    paddingBottom: 10,
    marginTop: 'auto', // Push to the bottom of the container
  },
  saveButton: {
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 30, // Increased bottom margin for better visibility
  },
});

export default CreateWorkoutModal;
