import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON>hart, <PERSON>Axi<PERSON>, XAxis } from 'react-native-svg-charts';
import * as shape from 'd3-shape';
import { Circle, Line, G, Text as SvgText } from 'react-native-svg';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { Grid as OriginalGrid } from 'react-native-svg-charts';

// Create a complete wrapper for Grid component to avoid defaultProps warning
// This completely replaces the original Grid component with one that uses default parameters
interface GridProps {
  svg?: any;
  belowChart?: boolean;
  ticks?: number[];
  x?: (d: any) => any;
  y?: (d: any) => any;
  [key: string]: any;
}

const Grid: React.FC<GridProps> = ({
  svg = {},
  belowChart = false,
  ticks = [10],
  x = (d) => d,
  y = (d) => d,
  ...restProps
}) => {
  // Pass all props to the original Grid component
  return <OriginalGrid
    svg={svg}
    belowChart={belowChart}
    ticks={ticks}
    x={x}
    y={y}
    {...restProps}
  />;
};

interface StrengthChartProps {
  data: {
    date: string;
    weight: number;
    reps: number;
  }[];
  timeRange: 'week' | 'month' | 'year' | 'all';
}

const StrengthChart: React.FC<StrengthChartProps> = ({ data, timeRange }) => {
  const { colors, isDark } = useTheme();

  // If no data, show empty state
  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No data available for this time range
        </Text>
        <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
          Complete workouts with this exercise to see progress
        </Text>
      </View>
    );
  }

  // Need at least 2 data points for a meaningful chart
  if (data.length < 2) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          Not enough data for chart
        </Text>
        <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
          Need at least 2 workouts to show progress
        </Text>
      </View>
    );
  }

  // Format dates based on time range
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);

    switch (timeRange) {
      case 'week':
        // For week view, show single letter day (M, T, W, etc.)
        const days = ['S', 'M', 'T', 'W', 'Th', 'F', 'Sa'];
        return days[date.getDay()];
      case 'month':
        // For month view, show day of month
        return date.getDate().toString();
      case 'year':
        // For year view, show month abbreviation
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months[date.getMonth()];
      case 'all':
        // For all time view, show month/year
        return `${date.getMonth() + 1}/${date.getFullYear().toString().substr(2, 2)}`;
      default:
        return date.toLocaleDateString();
    }
  };

  // Sort data by date
  const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Extract weight data for the chart
  const weightData = sortedData.map(item => ({
    value: item.weight,
    date: item.date,
    reps: item.reps,
  }));

  // Calculate min and max for Y axis (weight)
  const minWeight = Math.max(0, Math.min(...weightData.map(item => item.value)) - 10);
  const maxWeight = Math.max(...weightData.map(item => item.value)) + 10;

  // Format dates for X axis
  const xAxisData = sortedData.map(item => formatDate(item.date));

  // Custom decorator for data points
  const Decorator = ({ x, y, data }: any) => {
    return data.map((value: any, index: number) => (
      <Circle
        key={index}
        cx={x(index)}
        cy={y(value.value)}
        r={4}
        stroke={colors.primary}
        fill={isDark ? colors.card : 'white'}
        strokeWidth={2}
      />
    ));
  };

  // Custom decorator for tooltips
  const Tooltip = ({ x, y, data }: any) => {
    // Only show tooltip for the last point
    const index = data.length - 1;
    const value = data[index];

    return (
      <G x={x(index)} y={y(value.value) - 15}>
        <G y={-35}>
          <SvgText
            x={0}
            y={0}
            alignmentBaseline={'middle'}
            textAnchor={'middle'}
            fontSize={12}
            fontWeight={'bold'}
            fill={colors.primary}
          >
            {`${value.value} lbs`}
          </SvgText>
          <SvgText
            x={0}
            y={15}
            alignmentBaseline={'middle'}
            textAnchor={'middle'}
            fontSize={10}
            fill={colors.textSecondary}
          >
            {`${value.reps} reps`}
          </SvgText>
        </G>
        <Line
          x1={0}
          y1={-15}
          x2={0}
          y2={0}
          stroke={colors.primary}
          strokeWidth={1}
          strokeDasharray={[2, 2]}
        />
      </G>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.chartContainer}>
        <YAxis
          data={weightData}
          yAccessor={({ item }) => item.value}
          contentInset={{ top: 20, bottom: 20 }}
          svg={{ fill: colors.textSecondary, fontSize: 10 }}
          numberOfTicks={5}
          min={minWeight}
          max={maxWeight}
          formatLabel={(value) => `${value}`}
        />
        <View style={styles.chartWrapper}>
          <LineChart
            style={styles.chart}
            data={weightData}
            yAccessor={({ item }) => item.value}
            xAccessor={({ index }) => index}
            svg={{ stroke: colors.primary, strokeWidth: 2 }}
            contentInset={{ top: 20, bottom: 20, left: 10, right: 10 }}
            curve={shape.curveMonotoneX}
            animate={true}
            animationDuration={300}
          >
            <Grid svg={{ stroke: colors.border, strokeOpacity: 0.5 }} belowChart={true} />
            <Decorator />
            <Tooltip />
          </LineChart>
          <XAxis
            // Use array of numbers for data to avoid the type error
            data={weightData.map((_, i) => i)}
            formatLabel={(_, index) => xAxisData[index] || ''}
            contentInset={{ left: 10, right: 10 }}
            svg={{ fill: colors.textSecondary, fontSize: 10 }}
          />
        </View>
      </View>
      <View style={[styles.legendContainer, { borderTopColor: colors.border }]}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: colors.primary }]} />
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Weight (lbs)</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 250,
    width: '100%',
  },
  chartContainer: {
    flexDirection: 'row',
    height: 220,
    paddingRight: 10,
  },
  chartWrapper: {
    flex: 1,
    marginLeft: 10,
  },
  chart: {
    flex: 1,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 10,
    borderTopWidth: 1,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  legendText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  emptyContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  emptyText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
    marginBottom: 5,
  },
  emptySubtext: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    opacity: 0.8,
  },
});

export default StrengthChart;
