import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { WorkoutData } from '../../services/conversationService';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface WorkoutTemplateSelectorProps {
  workouts: WorkoutData[];
  isLoading: boolean;
  onSelectWorkout: (workout: WorkoutData) => void;
  onCreateNewWorkout: () => void;
}

const WorkoutTemplateSelector: React.FC<WorkoutTemplateSelectorProps> = ({
  workouts,
  isLoading,
  onSelectWorkout,
  onCreateNewWorkout,
}) => {
  const { colors, isDark } = useTheme();

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  }

  const renderWorkoutItem = ({ item }: { item: WorkoutData }) => {
    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(200)}
      >
        <TouchableOpacity
          style={[
            styles.workoutItem,
            {
              backgroundColor: colors.card,
              shadowColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)',
              borderColor: isDark ? colors.border : 'transparent',
              borderWidth: isDark ? 1 : 0,
            }
          ]}
          onPress={() => onSelectWorkout(item)}
        >
          <Ionicons
            name="barbell-outline"
            size={20}
            color={colors.primary}
            style={styles.workoutIcon}
          />
          <View style={styles.workoutDetails}>
            <Text style={[styles.workoutTitle, { color: colors.text }]} numberOfLines={1}>
              {item.title}
            </Text>
            <Text style={[styles.workoutExerciseCount, { color: colors.textSecondary }]}>
              {item.exercises.length} {item.exercises.length === 1 ? 'exercise' : 'exercises'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // No need to combine workouts with 'create_new' anymore since we have a fixed button

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Workout Templates</Text>

        {/* Always visible Create New button */}
        <TouchableOpacity
          style={[
            styles.createNewFixedButton,
            {
              backgroundColor: colors.primary,
              shadowColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)',
            }
          ]}
          onPress={onCreateNewWorkout}
        >
          <Ionicons name="add" size={18} color="#fff" style={{ marginRight: 5 }} />
          <Text style={styles.createNewFixedText}>Create New</Text>
        </TouchableOpacity>
      </View>

      {/* Only show the FlatList if there are workout templates */}
      {workouts.length > 0 && (
        <FlatList
          data={workouts} // Remove 'create_new' from data since we have a fixed button now
          renderItem={renderWorkoutItem}
          keyExtractor={(item) => item.title}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      )}

      {/* Show a message if no templates exist */}
      {workouts.length === 0 && !isLoading && (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No workout templates yet. Create your first one!
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  title: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  listContent: {
    paddingHorizontal: 5,
    paddingBottom: 5,
  },
  loadingContainer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  workoutItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    padding: 15,
    marginRight: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    minWidth: 180,
    maxWidth: 220,
  },
  workoutIcon: {
    marginRight: 10,
  },
  workoutDetails: {
    flex: 1,
  },
  workoutTitle: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 4,
  },
  workoutExerciseCount: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
  },
  createNewButton: {
    borderRadius: 12,
    padding: 15,
    marginRight: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    minWidth: 140,
    height: 80,
    justifyContent: 'center',
  },
  createNewContent: {
    alignItems: 'center',
  },
  createNewText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 5,
  },
  createNewFixedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  createNewFixedText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
});

export default WorkoutTemplateSelector;
