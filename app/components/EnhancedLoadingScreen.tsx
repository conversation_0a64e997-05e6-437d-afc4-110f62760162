import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Dimensions,
  Animated,
  Easing,
  Platform
} from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import BufferingIndicator from './ui/BufferingIndicator';

interface EnhancedLoadingScreenProps {
  progress?: number; // 0 to 1
  message?: string;
  statusMessages?: string[];
  showLogo?: boolean;
  showProgressBar?: boolean;
  fadeInDuration?: number;
  isError?: boolean;
  errorMessage?: string;
}

const { width, height } = Dimensions.get('window');

const EnhancedLoadingScreen: React.FC<EnhancedLoadingScreenProps> = ({
  progress = 0,
  message = 'Loading...',
  statusMessages = [],
  showLogo = true,
  showProgressBar = true,
  fadeInDuration = 400,
  isError = false,
  errorMessage
}) => {
  const { colors, isDark } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // Update progress animation when progress changes
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
      easing: Easing.linear
    }).start();
  }, [progress, progressAnim]);

  // Start fade-in animation when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: fadeInDuration,
      useNativeDriver: true,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1)
    }).start();
  }, [fadeAnim, fadeInDuration]);

  // Get current status message - use the most recent one
  const currentStatusMessage = statusMessages.length > 0
    ? statusMessages[statusMessages.length - 1]
    : '';

  // Background color with opacity
  const bgColor = isDark ? 'rgba(18, 18, 18, 0.98)' : 'rgba(255, 255, 255, 0.98)';

  // Progress bar color
  const progressBarColor = isError ? colors.error : colors.primary;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: bgColor,
          opacity: fadeAnim
        }
      ]}
    >
      <View style={styles.content}>
        {showLogo && (
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
        )}

        <Text
          style={[
            styles.message,
            {
              color: isError ? colors.error : colors.text
            }
          ]}
        >
          {isError ? (errorMessage || 'Error loading app') : message}
        </Text>

        {showProgressBar && (
          <View style={styles.progressBarContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  backgroundColor: progressBarColor,
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%']
                  })
                }
              ]}
            />
          </View>
        )}

        {!isError && statusMessages.length > 0 && (
          <Text
            style={[
              styles.statusMessage,
              {
                color: colors.textSecondary
              }
            ]}
          >
            {currentStatusMessage}
          </Text>
        )}

        {isError && (
          <View style={styles.errorContainer}>
            <BufferingIndicator
              size="small"
              color={colors.error}
              dotCount={3}
            />
            <Text style={[styles.errorHint, { color: colors.textSecondary }]}>
              Tap to retry
            </Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  content: {
    width: width * 0.8,
    maxWidth: 400,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    marginBottom: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 100,
    height: 100,
    ...Platform.select({
      web: {
        width: 160,
        height: 160,
      },
    }),
  },
  message: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 20,
    textAlign: 'center',
  },
  progressBarContainer: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(150, 150, 150, 0.2)',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 20,
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  statusMessage: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    marginBottom: 40,
    maxWidth: '80%',
  },
  errorContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  errorHint: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginTop: 10,
  },
});

export default EnhancedLoadingScreen;
