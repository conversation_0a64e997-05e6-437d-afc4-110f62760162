import React, { useState, useEffect } from 'react';
import { Text, StyleSheet, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { format } from 'date-fns';

interface TimeDisplayProps {
  showIcon?: boolean;
  showDate?: boolean;
  showSeconds?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

const TimeDisplay: React.FC<TimeDisplayProps> = ({
  showIcon = true,
  showDate = false,
  showSeconds = false,
  size = 'medium',
  style,
}) => {
  const { colors, isDark } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every second or minute based on showSeconds
  useEffect(() => {
    const intervalTime = showSeconds ? 1000 : 60000; // 1 second or 1 minute
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, intervalTime);

    return () => clearInterval(interval);
  }, [showSeconds]);

  // Format time based on props
  const getFormattedTime = () => {
    const timeFormat = showSeconds ? 'h:mm:ss a' : 'h:mm a';
    return format(currentTime, timeFormat);
  };

  // Format date if needed
  const getFormattedDate = () => {
    return format(currentTime, 'EEEE, MMMM d');
  };

  // Get font size based on size prop
  const getFontSize = () => {
    switch (size) {
      case 'small':
        return typography.sizes.sm;
      case 'large':
        return typography.sizes.xl;
      case 'medium':
      default:
        return typography.sizes.lg;
    }
  };

  return (
    <View style={[styles.container, style]}>
      {showIcon && (
        <Ionicons
          name="time-outline"
          size={size === 'small' ? 16 : size === 'large' ? 24 : 20}
          color={colors.primary}
          style={styles.icon}
        />
      )}
      <View>
        <Text
          style={[
            styles.timeText,
            {
              color: colors.text,
              fontSize: getFontSize(),
            },
          ]}
        >
          {getFormattedTime()}
        </Text>
        {showDate && (
          <Text
            style={[
              styles.dateText,
              {
                color: colors.textSecondary,
                fontSize: size === 'small' ? typography.sizes.xs : typography.sizes.sm,
              },
            ]}
          >
            {getFormattedDate()}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  timeText: {
    fontFamily: typography.fontFamily.medium,
  },
  dateText: {
    fontFamily: typography.fontFamily.regular,
    marginTop: 2,
  },
});

export default TimeDisplay;
