import React, { useEffect, useState, useCallback } from 'react';
import { TouchableOpacity, BackHandler, Platform } from 'react-native';
import { initializeApp, InitStatus, InitResult } from '../services/AppInitializer';
import EnhancedLoadingScreen from './EnhancedLoadingScreen';

interface AppLoaderProps {
  children: React.ReactNode;
  onInitialized?: (result: InitResult) => void;
  onInitError?: (error: Error) => void;
  timeout?: number;
}

const AppLoader: React.FC<AppLoaderProps> = ({
  children,
  onInitialized,
  onInitError,
  timeout = 15000
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Loading...');
  const [statusMessages, setStatusMessages] = useState<string[]>([]);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  // Function to start initialization
  const startInitialization = useCallback(async (forceRefresh = false) => {
    setIsLoading(true);
    setIsError(false);
    setErrorMessage(undefined);
    setProgress(0);
    setMessage('Loading...');
    setStatusMessages(['Preparing your experience...']);

    // Track critical errors that would prevent app from functioning
    let hasCriticalError = false;

    try {
      // Disable back button during initialization on Android
      let backHandler: any = null;
      if (Platform.OS === 'android') {
        backHandler = BackHandler.addEventListener('hardwareBackPress', () => true);
      }

      // Start initialization with optimized timeout
      const result = await initializeApp({
        onProgress: (status: InitStatus) => {
          // Update progress
          setProgress(status.progress);

          // Update messages for non-complete, non-error steps
          if (status.step !== 'complete' && status.step !== 'error') {
            setMessage(status.message);

            // Add status message to the list (keep last 3)
            setStatusMessages(prev => {
              const newMessages = [...prev, status.message];
              return newMessages.slice(-3);
            });
          }

          // Log errors but continue loading
          if (status.error) {
            console.warn(`Error in initialization step ${status.step}:`, status.error);

            // Only consider network and auth as critical errors
            if (status.step === 'network' || status.step === 'auth') {
              hasCriticalError = true;
            }
          }

          // When complete, finish loading quickly
          if (status.isComplete) {
            // If we have critical errors, show error UI
            if (hasCriticalError) {
              setIsError(true);
              setErrorMessage('Error connecting to server. Tap to retry.');
            } else {
              // Otherwise, complete loading immediately
              setMessage('Ready');
              
              // Quickly complete the loading process
              Promise.resolve().then(async () => {
                try {
                  // Clear the loading flag
                  const AsyncStorage = require('@react-native-async-storage/async-storage').default;
                  await AsyncStorage.setItem('app_loading', 'false');
                  await AsyncStorage.setItem('app_ready', 'true');
                } catch (error) {
                  console.error('Error clearing loading flag:', error);
                }

                setIsLoading(false);
                setIsInitialized(true);

                // Clean up back handler
                if (backHandler) {
                  backHandler.remove();
                }
              });
            }
          }
        },
        onComplete: (result: InitResult) => {
          if (onInitialized) {
            onInitialized(result);
          }

          // If initialization failed, show error UI
          if (!result.success) {
            setIsError(true);
            setErrorMessage('Error initializing app. Tap to retry.');
            console.error('Initialization failed:', result.errors);

            // Clean up back handler
            if (backHandler) {
              backHandler.remove();
            }
          }
        },
        onError: (error: Error, step: string) => {
          console.error(`Error in initialization step ${step}:`, error);

          if (onInitError) {
            onInitError(error);
          }

          // Only show error UI for truly critical errors
          if (step === 'network' || step === 'auth' || step === 'global') {
            setIsError(true);
            setErrorMessage(`Error connecting to server. Tap to retry.`);
            hasCriticalError = true;
          } else {
            // For API errors, just log and continue
            console.log(`Non-critical error in ${step}, continuing initialization`);

            // Clean up back handler
            if (backHandler) {
              backHandler.remove();
            }
          }
        },
        timeout, // Use the passed timeout
        forceRefresh
      });

      return result;
    } catch (error) {
      console.error('Unexpected error during initialization:', error);
      setIsError(true);
      setErrorMessage('Unexpected error. Tap to retry.');

      if (onInitError) {
        if (error instanceof Error) {
          onInitError(error);
        } else {
          onInitError(new Error(String(error)));
        }
      }

      return {
        success: false,
        data: {},
        errors: { 
          global: error instanceof Error ? error.message : String(error) 
        },
        skipped: []
      };
    }
  }, [onInitialized, onInitError, timeout]);

  // Start initialization on mount
  useEffect(() => {
    // Set a flag in AsyncStorage to indicate app is starting
    const markAppStarting = async () => {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;

        // Set app start time and loading flag
        await AsyncStorage.setItem('app_start_time', Date.now().toString());
        await AsyncStorage.setItem('app_loading', 'true');
        await AsyncStorage.setItem('app_ready', 'false');

        // Clear any cached API responses that might be causing errors
        const { clearAllCache } = require('../services/apiRequestManager');
        await clearAllCache();

        // Clear any error flags
        await AsyncStorage.removeItem('app_error');

        // Configure API client for initialization
        const { configureApiClient } = require('../services/apiClient');
        configureApiClient({
          timeout: 15000, // Reduced from 30000
          retries: 2,     // Reduced from 5
          logRequests: true
        });
      } catch (error) {
        console.error('Error setting app start time:', error);
      }

      // Start initialization immediately
      startInitialization();
    };

    markAppStarting();
  }, [startInitialization]);

  // Handle retry
  const handleRetry = () => {
    if (isError) {
      startInitialization(true);
    }
  };

  // If loading is complete, render children
  if (!isLoading && isInitialized) {
    return <>{children}</>;
  }

  // Otherwise, render loading screen
  return (
    <TouchableOpacity
      activeOpacity={isError ? 0.8 : 1}
      onPress={handleRetry}
      style={{ flex: 1 }}
    >
      <EnhancedLoadingScreen
        progress={progress}
        message={message}
        statusMessages={statusMessages}
        showLogo={true}
        showProgressBar={true}
        isError={isError}
        errorMessage={errorMessage}
      />
    </TouchableOpacity>
  );
};

export default AppLoader;
