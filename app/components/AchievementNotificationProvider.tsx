import React, { useState, useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import AchievementNotification from './AchievementNotification';
import { 
  getNextAchievementNotification, 
  markAchievementAsSeen,
  removeAchievementFromNotificationQueue,
  AchievementNotificationItem
} from '../services/achievementNotificationService';
import { useNavigation } from '@react-navigation/native';

interface AchievementNotificationProviderProps {
  children: React.ReactNode;
}

const AchievementNotificationProvider: React.FC<AchievementNotificationProviderProps> = ({ 
  children 
}) => {
  const [currentNotification, setCurrentNotification] = useState<AchievementNotificationItem | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const appState = useRef(AppState.currentState);
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const navigation = useNavigation();
  
  // Check for new notifications
  const checkForNotifications = async () => {
    if (isVisible) return; // Don't check if already showing a notification
    
    const nextNotification = await getNextAchievementNotification();
    if (nextNotification) {
      setCurrentNotification(nextNotification);
      setIsVisible(true);
    }
  };
  
  // Handle app state changes
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      checkForNotifications();
    }
    
    appState.current = nextAppState;
  };
  
  // Set up notification checking
  useEffect(() => {
    // Check for notifications when component mounts
    checkForNotifications();
    
    // Set up interval to check for new notifications
    checkIntervalRef.current = setInterval(checkForNotifications, 30000); // Check every 30 seconds
    
    // Set up app state change listener
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      // Clean up
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
      }
      subscription.remove();
    };
  }, [isVisible]);
  
  // Handle notification press
  const handleNotificationPress = () => {
    if (currentNotification) {
      // Mark as seen
      markAchievementAsSeen(currentNotification.achievement.id);
      
      // Hide notification
      setIsVisible(false);
      
      // Navigate to achievements screen
      // @ts-ignore - Navigation typing is complex
      navigation.navigate('Profile', { showAchievements: true });
    }
  };
  
  // Handle notification dismiss
  const handleNotificationDismiss = () => {
    if (currentNotification) {
      // Mark as seen
      markAchievementAsSeen(currentNotification.achievement.id);
      
      // Remove from queue
      removeAchievementFromNotificationQueue(currentNotification.achievement.id);
      
      // Hide notification
      setIsVisible(false);
      
      // Check for more notifications after a short delay
      setTimeout(checkForNotifications, 500);
    }
  };
  
  return (
    <>
      {children}
      
      {isVisible && currentNotification && (
        <AchievementNotification
          title="Achievement Unlocked!"
          description={currentNotification.achievement.title}
          icon={currentNotification.achievement.icon}
          onPress={handleNotificationPress}
          onDismiss={handleNotificationDismiss}
          autoHide={true}
          duration={7000}
        />
      )}
    </>
  );
};

export default AchievementNotificationProvider;
