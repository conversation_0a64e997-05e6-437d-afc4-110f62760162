import React, { ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInRight,
  SlideOutLeft,
  SlideInUp,
  SlideOutDown
} from 'react-native-reanimated';

type TransitionType = 'fade' | 'slide-horizontal' | 'slide-vertical';

interface AnimatedTransitionProps {
  children: ReactNode;
  type?: TransitionType;
  duration?: number;
  delay?: number;
  style?: any;
}

const AnimatedTransition: React.FC<AnimatedTransitionProps> = ({
  children,
  type = 'fade',
  duration = 300,
  delay = 0,
  style
}) => {
  const getEnteringAnimation = () => {
    switch (type) {
      case 'slide-horizontal':
        return SlideInRight.duration(duration).delay(delay);
      case 'slide-vertical':
        return SlideInUp.duration(duration).delay(delay);
      case 'fade':
      default:
        return FadeIn.duration(duration).delay(delay);
    }
  };

  const getExitingAnimation = () => {
    switch (type) {
      case 'slide-horizontal':
        return SlideOutLeft.duration(duration);
      case 'slide-vertical':
        return SlideOutDown.duration(duration);
      case 'fade':
      default:
        return FadeOut.duration(duration);
    }
  };

  return (
    <Animated.View
      style={[styles.container, style]}
      entering={getEnteringAnimation()}
      exiting={getExitingAnimation()}
    >
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default AnimatedTransition;
