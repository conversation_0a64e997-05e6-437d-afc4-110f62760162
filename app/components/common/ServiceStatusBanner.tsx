import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';

interface ServiceStatusBannerProps {
  visible: boolean;
  onDismiss?: () => void;
  message?: string;
}

const ServiceStatusBanner: React.FC<ServiceStatusBannerProps> = ({
  visible,
  onDismiss,
  message = 'AI service is currently unavailable. Some features may be limited.',
}) => {
  const { colors } = useTheme();

  if (!visible) return null;

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: colors.error + '20', borderColor: colors.error + '40' },
      ]}
    >
      <Ionicons name="alert-circle" size={20} color={colors.error} style={styles.icon} />
      <Text style={[styles.message, { color: colors.text }]}>{message}</Text>
      {onDismiss && (
        <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
          <Ionicons name="close" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 15,
    marginVertical: 10,
  },
  icon: {
    marginRight: 8,
  },
  message: {
    flex: 1,
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  dismissButton: {
    padding: 5,
  },
});

export default ServiceStatusBanner;
