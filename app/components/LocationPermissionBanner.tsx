import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Linking } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme as useNavigationTheme } from '@react-navigation/native';
import { useTheme } from '../theme/ThemeProvider';
import { PermissionStatus } from '../services/locationService';

interface LocationPermissionBannerProps {
  permissionStatus?: PermissionStatus;
  locationServicesEnabled?: boolean;
  onRequestPermission: () => Promise<void>;
  onOpenSettings?: () => void;
  onDismiss?: () => void;
}

const LocationPermissionBanner: React.FC<LocationPermissionBannerProps> = ({
  permissionStatus = 'undetermined',
  locationServicesEnabled = true,
  onRequestPermission,
  onOpenSettings,
  onDismiss
}) => {
  // Use our app's theme which has textSecondary property
  const { colors } = useTheme();
  
  const handleOpenSettings = async () => {
    if (onOpenSettings) {
      onOpenSettings();
    } else {
      if (Platform.OS === 'ios') {
        await Linking.openSettings();
      } else {
        await Linking.openSettings();
      }
    }
  };

  // Determine message based on permission status and location services
  const getMessage = () => {
    if (!locationServicesEnabled) {
      return "Location services are disabled on your device. Please enable them in settings for accurate weather data.";
    }
    
    switch (permissionStatus) {
      case 'denied':
        return "Location permission was denied. Please enable location access in settings for accurate weather data.";
      case 'blocked':
        return "Location permission is blocked. Please update permission settings for accurate weather data.";
      default:
        return "Enable location access for accurate weather data and personalized meal suggestions.";
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: `${colors.primary}20` }]}>
      <View style={styles.iconContainer}>
        <Ionicons name="location-outline" size={24} color={colors.primary} />
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.title, { color: colors.text }]}>
          Location Access Required
        </Text>
        <Text style={[styles.message, { color: colors.textSecondary }]}>
          {getMessage()}
        </Text>
      </View>
      <View style={styles.buttonContainer}>
        {(permissionStatus === 'undetermined' || permissionStatus === 'denied') && (
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={onRequestPermission}
          >
            <Text style={[styles.buttonText, { color: colors.background }]}>
              Enable
            </Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.settingsButton, { borderColor: colors.primary }]}
          onPress={handleOpenSettings}
        >
          <Text style={[styles.settingsButtonText, { color: colors.primary }]}>
            Settings
          </Text>
        </TouchableOpacity>
        {onDismiss && (
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onDismiss}
          >
            <Ionicons name="close" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginLeft: 8,
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginBottom: 8,
  },
  buttonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  settingsButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
  },
  settingsButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  dismissButton: {
    marginTop: 8,
    padding: 4,
  },
});

export default LocationPermissionBanner;
