import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { useDigest } from '../context/DigestContext';
import { useTheme } from '../context/ThemeContext';

interface DigestResetButtonProps {
  label?: string;
}

const DigestResetButton: React.FC<DigestResetButtonProps> = ({ 
  label = 'Reset Digest Loading'
}) => {
  const { resetDigestLoadingFlag } = useDigest();
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  const handleReset = async () => {
    Alert.alert(
      'Reset Digest Loading',
      'This will reset the digest loading system and attempt to reload your daily digest. Continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          onPress: async () => {
            setIsLoading(true);
            try {
              const success = await resetDigestLoadingFlag();
              if (success) {
                Alert.alert(
                  'Success',
                  'Digest loading has been reset. Please restart the app for changes to take effect.',
                  [{ text: 'OK' }]
                );
              } else {
                Alert.alert(
                  'Error',
                  'Failed to reset digest loading. Please try again.',
                  [{ text: 'OK' }]
                );
              }
            } catch (error) {
              console.error('Error resetting digest loading:', error);
              Alert.alert(
                'Error',
                'An unexpected error occurred. Please try again.',
                [{ text: 'OK' }]
              );
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  return (
    <TouchableOpacity 
      style={[styles.button, { backgroundColor: colors.primary }]}
      onPress={handleReset}
      disabled={isLoading}
    >
      {isLoading ? (
        <ActivityIndicator color="#fff" size="small" />
      ) : (
        <Text style={styles.buttonText}>{label}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  }
});

export default DigestResetButton;
