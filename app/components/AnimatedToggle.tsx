import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableWithoutFeedback, Animated } from 'react-native';

interface AnimatedToggleProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  activeColor?: string;
  inactiveColor?: string;
  thumbColor?: string;
  disabled?: boolean;
}

const AnimatedToggle: React.FC<AnimatedToggleProps> = ({
  value,
  onValueChange,
  activeColor = '#4CD964',
  inactiveColor = '#E5E5EA',
  thumbColor = '#FFFFFF',
  disabled = false,
}) => {
  // Animation values
  const translateX = useRef(new Animated.Value(value ? 22 : 2)).current;
  const toggleScale = useRef(new Animated.Value(1)).current;
  const backgroundColorAnim = useRef(new Animated.Value(value ? 1 : 0)).current;

  // Update animation when value changes
  useEffect(() => {
    Animated.parallel([
      Animated.spring(translateX, {
        toValue: value ? 22 : 2,
        friction: 6,
        tension: 100,
        useNativeDriver: false,
      }),
      Animated.timing(backgroundColorAnim, {
        toValue: value ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  }, [value, translateX, backgroundColorAnim]);

  // Handle toggle press
  const handleToggle = () => {
    if (disabled) return;
    
    // Animate the thumb scale for a "press" effect
    Animated.sequence([
      Animated.timing(toggleScale, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: false,
      }),
      Animated.timing(toggleScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: false,
      }),
    ]).start();
    
    onValueChange(!value);
  };

  // Interpolate background color
  const backgroundColor = backgroundColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [inactiveColor, activeColor],
  });

  return (
    <TouchableWithoutFeedback onPress={handleToggle} disabled={disabled}>
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor,
            opacity: disabled ? 0.6 : 1,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.thumb,
            {
              transform: [
                { translateX },
                { scale: toggleScale },
              ],
              backgroundColor: thumbColor,
            },
          ]}
        />
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 50,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  thumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default AnimatedToggle;
