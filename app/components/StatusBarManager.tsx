import React, { useEffect } from 'react';
import { StatusBar as RNStatusBar, StatusBarStyle, View, Platform, Animated } from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { useAnimatedTheme } from '../theme/AnimatedThemeProvider';

interface StatusBarManagerProps {
  backgroundColor?: string;
  barStyle?: StatusBarStyle;
}

export default function StatusBarManager({
  backgroundColor,
  barStyle
}: StatusBarManagerProps) {
  const { colors, isDark } = useTheme();
  const { animatedColors } = useAnimatedTheme();

  // Use provided backgroundColor or default from theme
  const bgColor = backgroundColor || colors.background;

  // Use provided barStyle or determine based on theme
  const statusBarStyle = barStyle || (isDark ? 'light-content' : 'dark-content');

  // Update status bar style when theme changes
  useEffect(() => {
    RNStatusBar.setBarStyle(statusBarStyle);
    if (Platform.OS === 'android') {
      RNStatusBar.setBackgroundColor('transparent');
    }
  }, [isDark, statusBarStyle]);

  return (
    <Animated.View style={{
      height: Platform.OS === 'ios' ? 50 : RNStatusBar.currentHeight,
      backgroundColor: animatedColors.background,
      zIndex: 999
    }}>
      <RNStatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle={statusBarStyle}
      />
    </Animated.View>
  );
}