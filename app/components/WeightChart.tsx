import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, ActivityIndicator } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';

// Use dynamic import with error handling for chart library
let LineChart: any = null;
try {
  // Try to import the LineChart component
  const ChartKit = require('react-native-chart-kit');
  LineChart = ChartKit.LineChart;
} catch (error) {
  console.error('Error importing react-native-chart-kit:', error);
}
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { WeightEntry, getWeightEntriesForRange } from '../services/weightTracking';

interface WeightChartProps {
  onPointSelect?: (entry: WeightEntry) => void;
  refreshTrigger?: number; // A value that changes to trigger refresh
  enhancedUX?: boolean; // Flag to use enhanced UX styling
}

const WeightChart: React.FC<WeightChartProps> = ({
  onPointSelect,
  refreshTrigger,
  enhancedUX = false
}) => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation();
  const [entries, setEntries] = useState<WeightEntry[]>([]);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year' | 'all'>('month');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPoint, setSelectedPoint] = useState<number | null>(null);
  const [newDataAdded, setNewDataAdded] = useState(false);
  const [chartPopover, setChartPopover] = useState<{
    visible: boolean;
    value: number;
    date: string;
    x: number;
    y: number;
  } | null>(null);
  const previousEntriesLength = useRef<number>(0);
  const chartRef = useRef<any>(null);

  // Animation values
  const tabTranslateX = useSharedValue(
    timeRange === 'week' ? 0 :
    timeRange === 'month' ? 1 :
    timeRange === 'year' ? 2 : 3
  );

  // Calculate width accounting for padding and margins
  const windowWidth = Dimensions.get('window').width;
  const screenWidth = windowWidth - 120; // Increase padding for y-axis labels

  useEffect(() => {
    loadData();
  }, [timeRange]);

  useEffect(() => {
    const unsubscribe = navigation?.addListener('focus', () => {
      loadData();
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (refreshTrigger !== undefined) {
      console.log('WeightChart: Refresh triggered');
      loadData();
    }
  }, [refreshTrigger]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const data = await getWeightEntriesForRange(timeRange);

      // Check if new data was added by comparing with previous entries
      const hasNewData = data.length > previousEntriesLength.current;
      if (hasNewData) {
        console.log('New weight data detected, updating chart');
        setNewDataAdded(true);
        setTimeout(() => setNewDataAdded(false), 3000);
      }

      // Update the previous entries length reference
      previousEntriesLength.current = data.length;

      // Update the entries state
      setEntries(data);

      // Force a re-render of the chart
      if (hasNewData) {
        setSelectedPoint(null);
      }
    } catch (error) {
      console.error('Error loading weight data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);

    switch (timeRange) {
      case 'week':
        // For week view, show 3-letter day name (more readable than single letters)
        return date.toLocaleDateString('en-US', { weekday: 'short' });

      case 'month':
        // For month view, show month/day in a consistent format
        const month = date.getMonth() + 1;
        const day = date.getDate();
        // Use consistent width for single-digit days and months
        return `${month}/${day}`;

      case 'year':
        // For year view, show abbreviated month name
        return date.toLocaleDateString('en-US', { month: 'short' });

      case 'all':
        // For all time view, show month and year
        const thisYear = new Date().getFullYear();
        const entryYear = date.getFullYear();

        // If it's the current year, just show the month
        if (entryYear === thisYear) {
          return date.toLocaleDateString('en-US', { month: 'short' });
        }
        // Otherwise show month and year
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });

      default:
        return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  };

  const getChartData = () => {
    if (entries.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{ data: [0] }]
      };
    }

    // Calculate min and max values for better y-axis scaling
    const values = entries.map(entry => entry.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    let range = maxValue - minValue;

    // Improve y-axis scaling logic - make consistent across all tabs
    let yAxisRange;

    // Determine min and max for y-axis with reasonable padding
    // For consistent display across all tabs, use the same approach

    // Define standard padding as a percentage of the range or minimum value
    const minPadding = 3; // Minimum padding in units (lbs)
    let paddingPercentage = 0.10; // 10% padding by default

    // If all values are the same or very close, create a reasonable range
    if (range < 2) {
      // For flat or nearly flat data, create a meaningful visual range
      // Center the value and provide context around it
      const padding = Math.max(minPadding, Math.ceil(minValue * 0.05));
      yAxisRange = [
        Math.floor(minValue - padding),
        Math.ceil(minValue + padding)
      ];
    }
    // For normal ranges, use consistent padding for all views
    else {
      const padding = Math.max(minPadding, Math.ceil(range * paddingPercentage));

      // Round values to create clean boundaries
      yAxisRange = [
        Math.floor(minValue - padding),
        Math.ceil(maxValue + padding)
      ];
    }

    // Calculate how many labels can fit based on screen width
    // Assuming each label needs ~50px of space to avoid overlap
    const labelWidth = timeRange === 'week' ? 40 : 50; // Week view can have smaller spacing
    const maxLabelsBasedOnWidth = Math.floor(screenWidth / labelWidth);

    let displayEntries: WeightEntry[] = [];

    // For week view, show one point per day (max 7 days)
    if (timeRange === 'week') {
      // Group entries by day and select the newest entry for each day
      const entriesByDay: Record<string, WeightEntry> = {};

      // Create a set of the last 7 days (to ensure we have consistent days)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const lastWeekDays: string[] = [];

      // Generate the last 7 days in YYYY-MM-DD format
      // Start with oldest day first (for consistent left-to-right ordering)
      for (let i = 6; i >= 0; i--) {
        const day = new Date(today);
        day.setDate(today.getDate() - i);
        lastWeekDays.push(day.toISOString().split('T')[0]); // Add in chronological order (oldest to newest)
      }

      // Process entries and group by day
      entries.forEach(entry => {
        const date = new Date(entry.date);
        // Use YYYY-MM-DD format as key to group by day
        const dayKey = date.toISOString().split('T')[0];

        // Keep the newest entry for each day
        if (!entriesByDay[dayKey] || new Date(entry.date) > new Date(entriesByDay[dayKey].date)) {
          entriesByDay[dayKey] = entry;
        }
      });

      // Create a consistent array with entries for the last 7 days
      // If a day has no entry, we'll use the previous day's value
      displayEntries = [];
      let lastValue: number | null = null;

      // First, find the first value in our week range to use as a starting point
      // Sort entries chronologically
      const sortedEntries = [...entries].sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // Find the first entry within our week range
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);

      for (const entry of sortedEntries) {
        const entryDate = new Date(entry.date);
        if (entryDate >= weekAgo) {
          lastValue = entry.value;
          break;
        }
      }

      // If no entries in range, use the most recent entry before this week
      if (lastValue === null && sortedEntries.length > 0) {
        // Find the most recent entry before our week range
        for (let i = sortedEntries.length - 1; i >= 0; i--) {
          const entryDate = new Date(sortedEntries[i].date);
          if (entryDate < weekAgo) {
            lastValue = sortedEntries[i].value;
            break;
          }
        }

        // If still no value, use the oldest entry
        if (lastValue === null) {
          lastValue = sortedEntries[0].value;
        }
      }

      // Now process each day of the week
      lastWeekDays.forEach(dayKey => {
        if (entriesByDay[dayKey]) {
          // We have an entry for this day
          displayEntries.push(entriesByDay[dayKey]);
          lastValue = entriesByDay[dayKey].value;
        } else if (lastValue !== null) {
          // No entry for this day, create a placeholder with the last known value
          // This ensures we have a continuous line on the chart
          displayEntries.push({
            value: lastValue,
            date: `${dayKey}T00:00:00.000Z`
          });
        }
      });

      // Make sure we have at least 2 points for the week view
      if (displayEntries.length === 1) {
        // If we have just one entry, duplicate it to create a flat line
        const entry = displayEntries[0];
        // Create a second point with the same value but a different date
        const nextDay = new Date(entry.date);
        nextDay.setDate(nextDay.getDate() + 1);

        displayEntries.push({
          value: entry.value,
          date: nextDay.toISOString()
        });
      } else if (displayEntries.length === 0 && entries.length > 0) {
        // If no entries in the last 7 days but we have other entries
        // Use the most recent entry and create a flat line for the week
        const mostRecentEntry = [...entries].sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];

        // Create two points with the same value to show a flat line
        const today = new Date();
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 6);

        displayEntries = [
          {
            value: mostRecentEntry.value,
            date: weekAgo.toISOString()
          },
          {
            value: mostRecentEntry.value,
            date: today.toISOString()
          }
        ];
      }
    }
    // For month view, select strategic points
    else if (timeRange === 'month') {
      // Group entries by day to avoid duplicates on the same day
      const entriesByDay: Record<string, WeightEntry> = {};

      entries.forEach(entry => {
        const date = new Date(entry.date);
        const dayKey = date.toISOString().split('T')[0];

        // Keep the newest entry for each day
        if (!entriesByDay[dayKey] || new Date(entry.date) > new Date(entriesByDay[dayKey].date)) {
          entriesByDay[dayKey] = entry;
        }
      });

      // Convert to array and sort by date (oldest first)
      const sortedDayEntries = Object.values(entriesByDay).sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // Limit to max labels that can fit on screen
      const maxPoints = Math.min(maxLabelsBasedOnWidth, 8);

      if (sortedDayEntries.length <= maxPoints) {
        displayEntries = [...sortedDayEntries];
      } else {
        // Always include first and last points
        displayEntries = [sortedDayEntries[0]]; // Oldest entry

        // Add evenly distributed points in the middle
        const step = Math.floor(sortedDayEntries.length / (maxPoints - 2));
        for (let i = step; i < sortedDayEntries.length - step; i += step) {
          displayEntries.push(sortedDayEntries[i]);
        }

        // Add the newest entry
        if (sortedDayEntries.length > 1) {
          displayEntries.push(sortedDayEntries[sortedDayEntries.length - 1]);
        }
      }
    }
    // For year view, aim to show one point per month with consistent month display
    else if (timeRange === 'year') {
      // Create a map to store one entry per month
      const entriesByMonth: Record<string, WeightEntry> = {};

      // Get current date for reference
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      // Generate keys for the last 12 months in chronological order (oldest first)
      const monthKeys: string[] = [];
      for (let i = 11; i >= 0; i--) {
        const month = currentMonth - i;
        const year = currentYear + Math.floor(month / 12);
        const adjustedMonth = ((month % 12) + 12) % 12; // Handle negative month values
        monthKeys.push(`${year}-${adjustedMonth}`);
      }

      // First populate with actual entries (newest entry per month)
      entries.forEach(entry => {
        const date = new Date(entry.date);
        const monthKey = `${date.getFullYear()}-${date.getMonth()}`;

        // Only consider entries from the last 12 months
        if (monthKeys.includes(monthKey)) {
          // Keep the newest entry for each month
          if (!entriesByMonth[monthKey] || new Date(entry.date) > new Date(entriesByMonth[monthKey].date)) {
            entriesByMonth[monthKey] = entry;
          }
        }
      });

      // Now create a consistent array with all months represented
      displayEntries = [];
      let lastValue: number | null = null;

      // Find the first known weight to use for months without data
      if (entries.length > 0) {
        // Sort entries chronologically
        const sortedEntries = [...entries].sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        // Find the oldest entry within our year range
        for (const entry of sortedEntries) {
          const entryDate = new Date(entry.date);
          const yearAgo = new Date();
          yearAgo.setFullYear(yearAgo.getFullYear() - 1);

          if (entryDate >= yearAgo) {
            lastValue = entry.value;
            break;
          }
        }

        // If no entries in range, use the oldest available
        if (lastValue === null && sortedEntries.length > 0) {
          lastValue = sortedEntries[0].value;
        }
      }

      // Process each month in order (oldest to newest)
      monthKeys.forEach(monthKey => {
        if (entriesByMonth[monthKey]) {
          // We have an entry for this month
          displayEntries.push(entriesByMonth[monthKey]);
          lastValue = entriesByMonth[monthKey].value;
        } else if (lastValue !== null) {
          // No entry for this month, create placeholder with last known value
          // Extract year and month from the key
          const [year, month] = monthKey.split('-').map(Number);
          const date = new Date(year, month, 15); // Middle of the month

          displayEntries.push({
            value: lastValue,
            date: date.toISOString()
          });
        }
      });

      // If we still have too many points, select strategic ones
      if (displayEntries.length > maxLabelsBasedOnWidth) {
        // Always include first and last points, and evenly distribute others
        const firstPoint = displayEntries[0];
        const lastPoint = displayEntries[displayEntries.length - 1];

        // Select additional points
        const maxMiddlePoints = maxLabelsBasedOnWidth - 2;
        const step = Math.ceil((displayEntries.length - 2) / maxMiddlePoints);

        // Start with first point
        const sampledEntries = [firstPoint];

        // Add strategic middle points
        for (let i = step; i < displayEntries.length - 1; i += step) {
          sampledEntries.push(displayEntries[i]);
        }

        // Add the last point and update displayEntries
        sampledEntries.push(lastPoint);
        displayEntries = sampledEntries;
      }
    }
    // For all time view, select strategic points
    else {
      // Sort entries by date (oldest first)
      const sortedEntries = [...entries].sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // For all time, limit to a reasonable number of points
      const maxPoints = Math.min(maxLabelsBasedOnWidth, 12);

      if (sortedEntries.length <= maxPoints) {
        displayEntries = [...sortedEntries];
      } else {
        // Select first, last, and evenly distributed points
        displayEntries = [sortedEntries[0]]; // Oldest entry

        const step = Math.floor(sortedEntries.length / (maxPoints - 2));
        for (let i = step; i < sortedEntries.length - step; i += step) {
          displayEntries.push(sortedEntries[i]);
        }

        // Add the newest entry
        if (sortedEntries.length > 1) {
          displayEntries.push(sortedEntries[sortedEntries.length - 1]);
        }
      }
    }

    return {
      labels: displayEntries.map(entry => formatDate(entry.date)),
      datasets: [
        {
          data: displayEntries.map(entry => entry.value),
          color: (opacity = 1) => colors.primary,
          strokeWidth: 2
        }
      ],
      // Add y-axis min/max if we calculated a custom range
      ...(yAxisRange ? { yAxisRange } : {})
    };
  };

  const handleDataPointClick = (data: any) => {
    if (entries.length === 0) return;

    const index = data.index;
    setSelectedPoint(index);

    const chartData = getChartData();
    if (onPointSelect && chartData.datasets[0].data[index]) {
      // Get the actual entry from the filtered display entries
      // This is more reliable than matching by formatted date string
      const value = chartData.datasets[0].data[index];
      const label = chartData.labels[index];

      // Find the entry that matches both the value and the formatted date
      const matchingEntries = entries.filter(e => {
        return e.value === value && formatDate(e.date) === label;
      });

      // If we found a match, use it
      if (matchingEntries.length > 0) {
        // Use the newest matching entry if there are multiple with the same value
        const entry = matchingEntries.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
        onPointSelect(entry);
      }
    }
  };

  // Animation for time range selector with improved spring dynamics
  const updateTimeRange = (range: 'week' | 'month' | 'year' | 'all') => {
    const targetValue =
      range === 'week' ? 0 :
      range === 'month' ? 1 :
      range === 'year' ? 2 : 3;

    // Using withTiming with improved animation parameters following guidelines
    tabTranslateX.value = withTiming(targetValue, {
      duration: 350, // Standard transition timing per guidelines (250-350ms)
      easing: Easing.out(Easing.cubic) // Smoother easing per animation guidelines
    });

    setTimeRange(range);
    // Clear any selected point
    setSelectedPoint(null);
    setChartPopover(null);
  };

  // Animation style for the selection indicator
  const indicatorStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: tabTranslateX.value * (windowWidth - 40) / 4 }
      ]
    };
  });

  return (
    <View style={[
      styles.container,
      enhancedUX && {
        borderRadius: 0,
        paddingTop: 0,
        paddingHorizontal: 0,
      }
    ]}>
      {enhancedUX ? (
        // Smooth sliding tab selector for enhanced UX
        <View style={styles.enhancedHeader}>
          <View style={styles.enhancedTabContainer}>
            <Animated.View
              style={[
                styles.tabIndicator,
                { backgroundColor: colors.primary },
                indicatorStyle
              ]}
            />
            {['week', 'month', 'year', 'all'].map((range, index) => (
              <TouchableOpacity
                key={range}
                style={styles.enhancedTab}
                onPress={() => updateTimeRange(range as any)}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.enhancedTabText,
                    {
                      color: timeRange === ['week', 'month', 'year', 'all'][index] ?
                        colors.primary : colors.textSecondary,
                      fontFamily: timeRange === ['week', 'month', 'year', 'all'][index] ?
                        typography.fontFamily.semibold : typography.fontFamily.regular
                    }
                  ]}
                >
                  {range.charAt(0).toUpperCase() + range.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ) : (
        // Standard header with title and range buttons
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Weight Tracking</Text>
          <View style={styles.timeRangeSelector}>
            {['week', 'month', 'year', 'all'].map((range) => (
              <TouchableOpacity
                key={range}
                style={[
                  styles.rangeButton,
                  timeRange === range && { backgroundColor: colors.primaryLight }
                ]}
                onPress={() => setTimeRange(range as any)}
              >
                <Text
                  style={[
                    styles.rangeButtonText,
                    { color: timeRange === range ? colors.primary : colors.textSecondary }
                  ]}
                >
                  {range.charAt(0).toUpperCase() + range.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Chart content area */}
      {isLoading ? (
        <View style={[
          styles.loadingContainer,
          enhancedUX && { paddingVertical: 50 }
        ]}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading weight data...
          </Text>
        </View>
      ) : entries.length === 0 ? (
        <View style={[
          styles.emptyContainer,
          enhancedUX && { paddingVertical: 50 }
        ]}>
          <Ionicons name="analytics-outline" size={40} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No weight data available
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textTertiary }]}>
            Add your weight to start tracking
          </Text>
        </View>
      ) : !LineChart ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={40} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            Chart component could not be loaded
          </Text>
        </View>
      ) : (
        <View
          style={[
            styles.chartWrapper,
            enhancedUX && {
              paddingBottom: 15,
              paddingTop: 0,
              marginTop: 10
            }
          ]}
        >
          {newDataAdded && (
            <View
              style={[
                styles.newDataBadge,
                {
                  backgroundColor: colors.success,
                  shadowColor: colors.success,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 3,
                  elevation: 3,
                }
              ]}
            >
              <Ionicons name="checkmark-circle" size={16} color="#fff" />
              <Text style={styles.newDataText}>Updated</Text>
            </View>
          )}

          <LineChart
            ref={chartRef}
            data={getChartData()}
            width={screenWidth}
            height={enhancedUX ? 240 : 220}
            chartConfig={{
              backgroundColor: colors.card,
              backgroundGradientFrom: colors.card,
              backgroundGradientTo: colors.card,
              decimalPlaces: 0, // Use whole numbers for weight
              color: (opacity = 1) => `rgba(${isDark ? '173, 216, 230' : '0, 122, 255'}, ${opacity})`,
              labelColor: (opacity = 1) => colors.text,
              // Format y-axis values to be more readable
              formatYLabel: (value: string) => {
                // Round to nearest whole number for cleaner display
                return Math.round(parseFloat(value)).toString();
              },
              propsForLabels: {
                fontSize: 11,
                fontWeight: '500',
                // Rotate labels slightly to prevent overlap in month view
                rotation: timeRange === 'month' && entries.length > 8 ? -45 : 0,
                // For week view, show day names clearly
                ...(timeRange === 'week' ? { fontSize: 12, fontWeight: '600' } : {}),
              },
              style: {
                borderRadius: enhancedUX ? 0 : 16,
              },
              propsForDots: {
                r: selectedPoint !== null ? 6 : 4,
                strokeWidth: 2,
                stroke: colors.primary,
              },
              propsForBackgroundLines: {
                strokeDasharray: '',
                stroke: colors.border,
                strokeWidth: 1,
              },
              // Consistent padding for all tabs to ensure y-axis labels are visible
              paddingLeft: 65,
              paddingRight: 20,
              paddingTop: 25,
              // Add more bottom padding when labels are rotated
              paddingBottom: (timeRange === 'month' && entries.length > 8) ? 30 : 15,
            }}
            withDots
            withShadow={false}
            style={{
              marginVertical: enhancedUX ? 0 : 12,
              marginHorizontal: 'auto',
              alignSelf: 'center',
              borderRadius: enhancedUX ? 0 : 16,
            }}
            onDataPointClick={handleDataPointClick}
            fromZero={false}
            yAxisSuffix=" lbs"
            yAxisInterval={1} // Consistent y-axis intervals
            segments={5} // Limit the number of horizontal grid lines
          />

          {/* Chart popover for data point details - could be implemented in a future update */}
          {chartPopover && chartPopover.visible && (
            <View
              style={[
                styles.chartPopover,
                {
                  backgroundColor: colors.card,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.15,
                  shadowRadius: 3,
                  elevation: 3,
                  borderColor: colors.border,
                  left: chartPopover.x - 75, // Center the popover
                  top: chartPopover.y - 60, // Position above the point
                }
              ]}
            >
              <Text style={[styles.popoverValue, { color: colors.text }]}>
                {chartPopover.value.toFixed(1)} lbs
              </Text>
              <Text style={[styles.popoverDate, { color: colors.textSecondary }]}>
                {chartPopover.date}
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    padding: 15,
    paddingHorizontal: 5,
    paddingTop: 20,
    borderRadius: 16,
    alignItems: 'center',
  },
  header: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginBottom: 15,
    width: '100%',
    alignItems: 'stretch',
  },
  // Enhanced header styling for modern UI
  enhancedHeader: {
    width: '100%',
    paddingTop: 0,
    paddingHorizontal: 10,
    alignItems: 'center',
  },
  enhancedTabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    height: 40,
    position: 'relative',
    marginBottom: 5,
  },
  enhancedTab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 40,
    zIndex: 2,
  },
  enhancedTabText: {
    fontSize: typography.sizes.sm,
    textAlign: 'center',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '25%',
    height: 3,
    borderRadius: 1.5,
    zIndex: 1,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 10,
  },
  timeRangeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  chartWrapper: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 10,
    paddingTop: 10,
    marginBottom: 10,
  },
  rangeButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  rangeButtonText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  loadingContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginTop: 8,
  },
  emptyContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 5,
  },
  errorContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginTop: 10,
    textAlign: 'center',
  },
  newDataBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    zIndex: 10,
  },
  newDataText: {
    color: '#fff',
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  chartPopover: {
    position: 'absolute',
    width: 150,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 100,
  },
  popoverValue: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
  },
  popoverDate: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
});

export default WeightChart;
