import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Modal,
  ScrollView,
  Alert,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInDown, FadeInDown } from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import CollapsibleContainer from './CollapsibleContainer';
import DashboardSection from './DashboardSection';
import { useNavigation } from '@react-navigation/native';
import { useProfile } from '../../context/ProfileContext';
import { getCurrentWeather, getMealSuggestions, getNutritionTargets } from '../../services/nutritionService';
import { useUserContext } from '../../context/UserContextProvider';
import { ContextType } from '../../services/contextService';
import { getUserStats } from '../../services/userStatsService';

// Define the height constants
const EXPANDED_HEIGHT = 550; // Increased to accommodate more content
const COLLAPSED_HEIGHT = 60;

// Meal time types
type MealTime = 'breakfast' | 'lunch' | 'dinner' | 'snack';

// Meal suggestion interface
interface MealSuggestion {
  title: string;
  description: string;
  mealTime: MealTime;
}

import { Highlight } from '../../services/dashboardService';

interface DailyDashboardCardProps {
  highlights: Highlight[];
  onWorkoutPress?: () => void;
  onMealPress?: () => void;
  onWorkoutModalOpen?: (workout: any) => void;
  onMealModalOpen?: (meal: any) => void;
}

const DailyDashboardCard: React.FC<DailyDashboardCardProps> = ({
  highlights,
  onWorkoutPress,
  onMealPress,
  onWorkoutModalOpen,
  onMealModalOpen
}) => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation();
  const { profile } = useProfile();
  const { contextData, getContextByType } = useUserContext();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [weather, setWeather] = useState<any>(null);
  const [workoutSuggestion, setWorkoutSuggestion] = useState<string | null>(null);
  const [mealSuggestions, setMealSuggestions] = useState<MealSuggestion[]>([]);
  const [dailyTip, setDailyTip] = useState<string | null>(null);
  const [userStats, setUserStats] = useState<any>(null);
  const [nutritionTargets, setNutritionTargets] = useState<any>(null);
  const [dietaryRestrictions, setDietaryRestrictions] = useState<string[]>([]);
  const [injuries, setInjuries] = useState<string[]>([]);
  const [goals, setGoals] = useState<string[]>([]);

  // Modal states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editType, setEditType] = useState<'workout' | 'meal' | null>(null);
  const [editMealTime, setEditMealTime] = useState<MealTime | null>(null);
  const [editInput, setEditInput] = useState('');
  const [editingItem, setEditingItem] = useState<any>(null);

  // Get current date in a readable format
  const today = new Date();
  const dateOptions: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  };
  const formattedDate = today.toLocaleDateString('en-US', dateOptions);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);

      try {
        // Load user context data
        const dietaryRestrictionsData = getContextByType(ContextType.DIETARY_RESTRICTION);
        const injuriesData = getContextByType(ContextType.INJURY);
        const goalsData = getContextByType(ContextType.GOAL);

        // Extract values from context data
        const restrictions = dietaryRestrictionsData.map(item => item.value);
        const userInjuries = injuriesData.map(item => item.value);
        const userGoals = goalsData.map(item => item.value);

        setDietaryRestrictions(restrictions);
        setInjuries(userInjuries);
        setGoals(userGoals);

        // Load weather data
        const weatherData = await getCurrentWeather();
        setWeather(weatherData);

        // Get user stats
        const stats = await getUserStats();
        setUserStats(stats);

        // Get nutrition targets
        const targets = await getNutritionTargets();
        setNutritionTargets(targets);

        // Get meal suggestions - in a real implementation, these would be personalized
        // based on the user's dietary restrictions, goals, etc.
        const mealData = await getMealSuggestions(true);

          // Create personalized meal suggestions based on user data
        const personalizedMeals: MealSuggestion[] = [
          {
            title: "Personalized breakfast",
            description: generateMealDescription("breakfast", restrictions, userGoals),
            mealTime: "breakfast" as MealTime
          },
          {
            title: "Personalized lunch",
            description: generateMealDescription("lunch", restrictions, userGoals),
            mealTime: "lunch" as MealTime
          },
          {
            title: "Personalized dinner",
            description: generateMealDescription("dinner", restrictions, userGoals),
            mealTime: "dinner" as MealTime
          },
          {
            title: "Healthy snack",
            description: generateMealDescription("snack", restrictions, userGoals),
            mealTime: "snack" as MealTime
          }
        ];

        setMealSuggestions(personalizedMeals);

        // Generate personalized workout suggestion based on injuries and goals
        setWorkoutSuggestion(generateWorkoutSuggestion(userInjuries, userGoals));

        // Generate personalized daily tip
        setDailyTip(generateDailyTip(userGoals, weatherData));

      } catch (error) {
        console.error('Error loading dashboard data:', error);

        // Fallback data if there's an error
        setMealSuggestions([
          {
            title: "Protein-rich breakfast",
            description: "Eggs with whole grain toast and avocado",
            mealTime: "breakfast"
          },
          {
            title: "Balanced lunch",
            description: "Grilled chicken salad with mixed vegetables and quinoa",
            mealTime: "lunch"
          },
          {
            title: "Nutritious dinner",
            description: "Baked salmon with roasted vegetables and brown rice",
            mealTime: "dinner"
          },
          {
            title: "Healthy snack",
            description: "Greek yogurt with berries and a drizzle of honey",
            mealTime: "snack"
          }
        ]);

        setWorkoutSuggestion("Upper body strength training focusing on chest and shoulders");
        setDailyTip("Stay hydrated throughout the day for optimal recovery");
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [getContextByType]);

  // Helper function to generate personalized meal descriptions
  const generateMealDescription = (mealTime: MealTime, restrictions: string[], goals: string[]): string => {
    // Default meals for each time
    const defaultMeals = {
      breakfast: "Eggs with whole grain toast and avocado",
      lunch: "Grilled chicken salad with mixed vegetables and quinoa",
      dinner: "Baked salmon with roasted vegetables and brown rice",
      snack: "Greek yogurt with berries and a drizzle of honey"
    };

    // Check for dietary restrictions
    if (restrictions.length > 0) {
      // Handle vegetarian/vegan
      if (restrictions.some(r => r.toLowerCase().includes('vegetarian') || r.toLowerCase().includes('vegan'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Plant-based yogurt with granola, fresh berries, and a drizzle of maple syrup";
          case 'lunch':
            return "Quinoa bowl with roasted vegetables, chickpeas, and tahini dressing";
          case 'dinner':
            return "Lentil and vegetable curry with brown rice and steamed greens";
          case 'snack':
            return "Mixed nuts and dried fruits with dark chocolate pieces";
        }
      }

      // Handle gluten-free
      if (restrictions.some(r => r.toLowerCase().includes('gluten'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Gluten-free oatmeal with almond butter, banana, and cinnamon";
          case 'lunch':
            return "Rice bowl with grilled chicken, avocado, and roasted vegetables";
          case 'dinner':
            return "Baked fish with quinoa and steamed seasonal vegetables";
          case 'snack':
            return "Rice cakes with almond butter and sliced apple";
        }
      }

      // Handle dairy-free
      if (restrictions.some(r => r.toLowerCase().includes('dairy') || r.toLowerCase().includes('lactose'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Coconut yogurt parfait with grain-free granola and mixed berries";
          case 'lunch':
            return "Chicken and vegetable stir-fry with coconut aminos and brown rice";
          case 'dinner':
            return "Grilled steak with sweet potato and roasted Brussels sprouts";
          case 'snack':
            return "Apple slices with almond butter and cinnamon";
        }
      }
    }

    // Check for fitness goals
    if (goals.length > 0) {
      // Handle muscle building
      if (goals.some(g => g.toLowerCase().includes('muscle') || g.toLowerCase().includes('strength'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Protein pancakes with Greek yogurt and mixed berries";
          case 'lunch':
            return "Lean steak with quinoa and roasted vegetables";
          case 'dinner':
            return "Grilled chicken breast with sweet potato and steamed broccoli";
          case 'snack':
            return "Protein shake with banana and almond butter";
        }
      }

      // Handle weight loss
      if (goals.some(g => g.toLowerCase().includes('weight loss') || g.toLowerCase().includes('fat loss'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Egg white omelet with spinach, tomatoes, and feta cheese";
          case 'lunch':
            return "Large salad with grilled chicken, light dressing, and a variety of vegetables";
          case 'dinner':
            return "Baked white fish with steamed vegetables and a small portion of brown rice";
          case 'snack':
            return "Celery sticks with hummus or a small handful of almonds";
        }
      }

      // Handle endurance
      if (goals.some(g => g.toLowerCase().includes('endurance') || g.toLowerCase().includes('cardio'))) {
        switch (mealTime) {
          case 'breakfast':
            return "Oatmeal with banana, honey, and a sprinkle of chia seeds";
          case 'lunch':
            return "Whole grain wrap with turkey, avocado, and plenty of vegetables";
          case 'dinner':
            return "Pasta with lean ground turkey, tomato sauce, and a side salad";
          case 'snack':
            return "Whole grain toast with banana and a drizzle of honey";
        }
      }
    }

    // Return default if no specific customizations apply
    return defaultMeals[mealTime];
  };

  // Helper function to generate personalized workout suggestions
  const generateWorkoutSuggestion = (injuries: string[], goals: string[]): string => {
    // Check for injuries first
    if (injuries.length > 0) {
      // Handle shoulder injuries
      if (injuries.some(i => i.toLowerCase().includes('shoulder'))) {
        return "Lower body focus with leg press, lunges, and core exercises that avoid shoulder strain";
      }

      // Handle knee injuries
      if (injuries.some(i => i.toLowerCase().includes('knee'))) {
        return "Upper body and core workout with seated exercises and low-impact cardio on the elliptical";
      }

      // Handle back injuries
      if (injuries.some(i => i.toLowerCase().includes('back'))) {
        return "Gentle mobility routine with swimming and carefully guided core strengthening exercises";
      }
    }

    // If no injuries, check goals
    if (goals.length > 0) {
      // Handle strength goals
      if (goals.some(g => g.toLowerCase().includes('strength') || g.toLowerCase().includes('muscle'))) {
        return "Compound strength training with squats, deadlifts, bench press, and overhead press";
      }

      // Handle weight loss goals
      if (goals.some(g => g.toLowerCase().includes('weight loss') || g.toLowerCase().includes('fat loss'))) {
        return "HIIT circuit with 30 seconds work/15 seconds rest: burpees, mountain climbers, jumping jacks, and bodyweight squats";
      }

      // Handle endurance goals
      if (goals.some(g => g.toLowerCase().includes('endurance') || g.toLowerCase().includes('cardio'))) {
        return "45-minute zone 2 cardio session followed by dynamic stretching and mobility work";
      }
    }

    // Default suggestion if no specific customizations apply
    return "Full-body workout with a mix of strength training and cardio intervals";
  };

  // Helper function to generate exercises from workout suggestion
  const generateExercisesFromSuggestion = (suggestion: string, injuries: string[]): any[] => {
    // Default exercises if we can't parse the suggestion
    const defaultExercises = [
      {
        name: "Recommended Exercise",
        sets: "3-4 sets",
        reps: "8-12 reps",
        notes: "Perform with proper form"
      }
    ];

    // Try to extract exercises from the suggestion
    if (!suggestion) return defaultExercises;

    // Check for common workout types
    if (suggestion.toLowerCase().includes("strength training") ||
        suggestion.toLowerCase().includes("weight") ||
        suggestion.toLowerCase().includes("resistance")) {

      // Strength training exercises
      if (suggestion.toLowerCase().includes("upper body") ||
          suggestion.toLowerCase().includes("chest") ||
          suggestion.toLowerCase().includes("shoulder")) {
        return [
          {
            name: "Bench Press",
            sets: "3 sets",
            reps: "8-10 reps",
            notes: "Focus on controlled movement"
          },
          {
            name: "Shoulder Press",
            sets: "3 sets",
            reps: "10-12 reps",
            notes: "Keep core engaged"
          },
          {
            name: "Tricep Dips",
            sets: "3 sets",
            reps: "12-15 reps",
            notes: "Use assisted machine if needed"
          }
        ];
      }

      if (suggestion.toLowerCase().includes("lower body") ||
          suggestion.toLowerCase().includes("leg") ||
          suggestion.toLowerCase().includes("squat")) {
        return [
          {
            name: "Squats",
            sets: "4 sets",
            reps: "10-12 reps",
            notes: "Keep weight in heels"
          },
          {
            name: "Lunges",
            sets: "3 sets",
            reps: "12 reps each leg",
            notes: "Step forward with control"
          },
          {
            name: "Leg Press",
            sets: "3 sets",
            reps: "12-15 reps",
            notes: "Don't lock knees at extension"
          }
        ];
      }

      // Full body default
      return [
        {
          name: "Squats",
          sets: "3 sets",
          reps: "10-12 reps",
          notes: "Keep weight in heels"
        },
        {
          name: "Push-ups",
          sets: "3 sets",
          reps: "As many as possible with good form",
          notes: "Modify on knees if needed"
        },
        {
          name: "Dumbbell Rows",
          sets: "3 sets",
          reps: "12 reps each arm",
          notes: "Keep back straight"
        }
      ];
    }

    // HIIT or circuit training
    if (suggestion.toLowerCase().includes("hiit") ||
        suggestion.toLowerCase().includes("circuit") ||
        suggestion.toLowerCase().includes("interval")) {
      return [
        {
          name: "Burpees",
          sets: "4 rounds",
          duration: "30 seconds",
          notes: "15 seconds rest between rounds"
        },
        {
          name: "Mountain Climbers",
          sets: "4 rounds",
          duration: "30 seconds",
          notes: "15 seconds rest between rounds"
        },
        {
          name: "Jump Squats",
          sets: "4 rounds",
          duration: "30 seconds",
          notes: "15 seconds rest between rounds"
        },
        {
          name: "Plank",
          sets: "4 rounds",
          duration: "30 seconds",
          notes: "15 seconds rest between rounds"
        }
      ];
    }

    // Cardio
    if (suggestion.toLowerCase().includes("cardio") ||
        suggestion.toLowerCase().includes("endurance") ||
        suggestion.toLowerCase().includes("running") ||
        suggestion.toLowerCase().includes("zone 2")) {
      return [
        {
          name: "Warm-up",
          duration: "5-10 minutes",
          notes: "Light jog or brisk walk"
        },
        {
          name: "Main Cardio Session",
          duration: "20-30 minutes",
          notes: suggestion.toLowerCase().includes("zone 2") ?
            "Keep heart rate in zone 2 (60-70% of max)" :
            "Moderate intensity, able to talk but not sing"
        },
        {
          name: "Cool Down",
          duration: "5 minutes",
          notes: "Gradually reduce intensity"
        }
      ];
    }

    // Check for injury accommodations
    if (injuries.length > 0) {
      if (injuries.some(i => i.toLowerCase().includes("shoulder"))) {
        return [
          {
            name: "Leg Press",
            sets: "3 sets",
            reps: "12-15 reps",
            notes: "Shoulder-friendly exercise"
          },
          {
            name: "Leg Curls",
            sets: "3 sets",
            reps: "12 reps",
            notes: "Focus on hamstrings"
          },
          {
            name: "Core Exercises",
            sets: "3 sets",
            duration: "30-60 seconds",
            notes: "Avoid positions that strain shoulders"
          }
        ];
      }

      if (injuries.some(i => i.toLowerCase().includes("knee"))) {
        return [
          {
            name: "Seated Row",
            sets: "3 sets",
            reps: "12 reps",
            notes: "Focus on upper body"
          },
          {
            name: "Lat Pulldown",
            sets: "3 sets",
            reps: "12 reps",
            notes: "Engage core throughout"
          },
          {
            name: "Swimming",
            duration: "20 minutes",
            notes: "Low impact on joints"
          }
        ];
      }
    }

    // Default full-body workout if we can't categorize
    return [
      {
        name: "Bodyweight Squats",
        sets: "3 sets",
        reps: "15 reps",
        notes: "Focus on form"
      },
      {
        name: "Push-ups",
        sets: "3 sets",
        reps: "10-12 reps",
        notes: "Modify as needed"
      },
      {
        name: "Plank",
        sets: "3 sets",
        duration: "30-60 seconds",
        notes: "Keep body in straight line"
      }
    ];
  };

  // Helper function to generate personalized daily tips
  const generateDailyTip = (goals: string[], weatherData: any): string => {
    // Weather-based tips
    if (weatherData && !weatherData.isUnavailable) {
      // Hot weather tips
      if (weatherData.temperatureF > 85) {
        return "It's hot today! Remember to stay hydrated and consider working out during cooler parts of the day.";
      }

      // Cold weather tips
      if (weatherData.temperatureF < 45) {
        return "It's cold today! Warm up thoroughly before exercise and consider layering your workout clothes.";
      }

      // Rainy weather tips
      if (weatherData.condition && weatherData.condition.toLowerCase().includes('rain')) {
        return "Rainy day ahead! Consider an indoor workout or make sure you have appropriate gear if heading outside.";
      }
    }

    // Goal-based tips
    if (goals.length > 0) {
      // Strength tips
      if (goals.some(g => g.toLowerCase().includes('strength') || g.toLowerCase().includes('muscle'))) {
        return "For optimal muscle recovery, aim for 7-9 hours of quality sleep tonight and include protein in your post-workout meal.";
      }

      // Weight loss tips
      if (goals.some(g => g.toLowerCase().includes('weight loss') || g.toLowerCase().includes('fat loss'))) {
        return "Stay consistent with your nutrition today. Consider tracking your meals and drinking water before each meal to help with portion control.";
      }

      // Endurance tips
      if (goals.some(g => g.toLowerCase().includes('endurance') || g.toLowerCase().includes('cardio'))) {
        return "Focus on proper breathing during your cardio sessions today. Try nasal breathing to improve oxygen efficiency.";
      }
    }

    // Default tips if no specific customizations apply
    const defaultTips = [
      "Consistency is key! Small daily actions lead to significant results over time.",
      "Remember to stay hydrated throughout the day for optimal performance and recovery.",
      "Take a moment for mindfulness today - even 5 minutes of deep breathing can reduce stress.",
      "Don't forget to stretch after your workout to improve flexibility and reduce soreness.",
      "Quality sleep is essential for recovery - aim for 7-9 hours tonight."
    ];

    return defaultTips[Math.floor(Math.random() * defaultTips.length)];
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Navigate to specific screens
  const navigateToStrength = () => {
    navigation.navigate('Strength' as never);
  };

  const navigateToDiet = () => {
    navigation.navigate('Diet' as never);
  };

  // Open edit modal
  const openEditModal = (type: 'workout' | 'meal', item: any = null, mealTime: MealTime | null = null) => {
    setEditType(type);
    setEditMealTime(mealTime);
    setEditingItem(item);

    if (type === 'workout') {
      setEditInput(workoutSuggestion || '');
    } else if (type === 'meal' && item) {
      setEditInput(item.description || '');
    } else {
      setEditInput('');
    }

    setShowEditModal(true);
  };

  // Handle edit submission
  const handleEditSubmit = () => {
    if (!editInput.trim()) {
      Alert.alert('Error', 'Please enter some text');
      return;
    }

    if (editType === 'workout') {
      setWorkoutSuggestion(editInput);
      Alert.alert('Success', 'Workout plan updated');
    } else if (editType === 'meal' && editMealTime) {
      // Update the specific meal suggestion
      setMealSuggestions(prev =>
        prev.map(meal =>
          meal.mealTime === editMealTime
            ? { ...meal, description: editInput }
            : meal
        )
      );
      Alert.alert('Success', `${editMealTime.charAt(0).toUpperCase() + editMealTime.slice(1)} updated`);
    }

    setShowEditModal(false);
    setEditInput('');
    setEditType(null);
    setEditMealTime(null);
    setEditingItem(null);
  };

  // Render weather information
  const renderWeather = () => {
    if (!weather || weather.isUnavailable) {
      return (
        <Text style={[styles.weatherText, { color: colors.textSecondary }]}>
          Weather data unavailable
        </Text>
      );
    }

    return (
      <View style={styles.weatherContainer}>
        <Ionicons
          name={weather.isDay ? "sunny-outline" : "moon-outline"}
          size={18}
          color={colors.primary}
        />
        <Text style={[styles.weatherText, { color: colors.text }]}>
          {Math.round(weather.temperatureF)}°F, {weather.condition}
        </Text>
      </View>
    );
  };

  // Render edit modal
  const renderEditModal = () => {
    return (
      <Modal
        visible={showEditModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => setShowEditModal(false)}
      >
        <Animated.View
          style={styles.modalOverlay}
          entering={FadeIn.duration(300)}
        >
          <Animated.View
            style={[styles.modalContainer, { backgroundColor: colors.background }]}
            entering={SlideInDown.duration(400).springify()}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {editType === 'workout'
                  ? 'Edit Workout Plan'
                  : `Edit ${editMealTime?.charAt(0).toUpperCase() + editMealTime?.slice(1) || 'Meal'}`}
              </Text>
              <TouchableOpacity onPress={() => setShowEditModal(false)}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={[styles.modalLabel, { color: colors.textSecondary }]}>
                {editType === 'workout'
                  ? 'Describe your preferred workout:'
                  : 'Describe your preferred meal:'}
              </Text>
              <TextInput
                style={[
                  styles.modalInput,
                  {
                    color: colors.text,
                    backgroundColor: colors.card,
                    borderColor: colors.border
                  }
                ]}
                value={editInput}
                onChangeText={setEditInput}
                placeholder={editType === 'workout'
                  ? 'e.g., 30-minute cardio followed by core exercises'
                  : 'e.g., Grilled chicken salad with avocado'
                }
                placeholderTextColor={colors.textTertiary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />

              <View style={styles.modalButtonRow}>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: colors.card }]}
                  onPress={() => setShowEditModal(false)}
                >
                  <Text style={[styles.modalButtonText, { color: colors.text }]}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: colors.primary }]}
                  onPress={handleEditSubmit}
                >
                  <Text style={[styles.modalButtonText, { color: '#fff' }]}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </Animated.View>
      </Modal>
    );
  };

  // Render meal suggestion by time
  const renderMealByTime = (mealTime: MealTime) => {
    const meal = mealSuggestions.find(m => m.mealTime === mealTime);

    if (!meal) return null;

    const mealIcons = {
      breakfast: 'cafe-outline',
      lunch: 'restaurant-outline',
      dinner: 'nutrition-outline',
      snack: 'fast-food-outline'
    };

    const mealTitles = {
      breakfast: 'Breakfast',
      lunch: 'Lunch',
      dinner: 'Dinner',
      snack: 'Snack'
    };

    // Generate a full meal object for the modal
    const generateMealObject = (meal: MealSuggestion, mealTime: MealTime) => {
      // Create a more detailed meal object for the modal
      const title = mealTitles[mealTime] || "Meal";
      const mealTitle = meal?.title || "Recommended Meal";
      return {
        title: `${title}: ${mealTitle}`,
        description: meal.description,
        ingredients: generateIngredientsFromDescription(meal.description),
        steps: generateInstructionsFromDescription(meal.description),
        calories: estimateCalories(meal.description, mealTime),
        protein: estimateProtein(meal.description),
        carbs: estimateCarbs(meal.description),
        fat: estimateFat(meal.description),
        tags: generateTagsFromDescription(meal.description, dietaryRestrictions)
      };
    };

    return (
      <View style={styles.mealItem}>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            if (onMealModalOpen && meal) {
              const mealObject = generateMealObject(meal, mealTime);
              onMealModalOpen(mealObject);
            }
          }}
        >
          <View style={styles.mealHeader}>
            <View style={styles.mealTitleContainer}>
              <Ionicons
                name={mealIcons[mealTime] || 'restaurant-outline'}
                size={18}
                color={colors.primary}
                style={{ marginRight: 6 }}
              />
              <Text style={[styles.mealTitle, { color: colors.text }]}>
                {mealTitles[mealTime] || 'Meal'}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => openEditModal('meal', meal, mealTime)}
              style={[styles.editButton, { backgroundColor: colors.primaryLight + '30' }]}
            >
              <Ionicons name="pencil-outline" size={14} color={colors.primary} />
            </TouchableOpacity>
          </View>
          <Text style={[styles.mealDescription, { color: colors.textSecondary }]}>
            {meal.description}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Helper function to estimate calories based on meal description and time
  const estimateCalories = (description: string, mealTime: MealTime): number => {
    // Base calories by meal time
    const baseCalories = {
      breakfast: 400,
      lunch: 600,
      dinner: 700,
      snack: 200
    };

    // Adjust based on content
    let calories = baseCalories[mealTime];

    if (description.toLowerCase().includes("protein") ||
        description.toLowerCase().includes("chicken") ||
        description.toLowerCase().includes("beef") ||
        description.toLowerCase().includes("fish")) {
      calories += 50;
    }

    if (description.toLowerCase().includes("salad") ||
        description.toLowerCase().includes("vegetable")) {
      calories -= 100;
    }

    if (description.toLowerCase().includes("rice") ||
        description.toLowerCase().includes("pasta") ||
        description.toLowerCase().includes("bread")) {
      calories += 100;
    }

    return calories;
  };

  // Helper function to estimate protein
  const estimateProtein = (description: string): number => {
    let protein = 20; // Base protein

    if (description.toLowerCase().includes("protein") ||
        description.toLowerCase().includes("chicken") ||
        description.toLowerCase().includes("beef") ||
        description.toLowerCase().includes("fish")) {
      protein += 15;
    }

    if (description.toLowerCase().includes("egg")) {
      protein += 6;
    }

    if (description.toLowerCase().includes("yogurt") ||
        description.toLowerCase().includes("milk") ||
        description.toLowerCase().includes("cheese")) {
      protein += 8;
    }

    return protein;
  };

  // Helper function to estimate carbs
  const estimateCarbs = (description: string): number => {
    let carbs = 30; // Base carbs

    if (description.toLowerCase().includes("rice") ||
        description.toLowerCase().includes("pasta") ||
        description.toLowerCase().includes("bread") ||
        description.toLowerCase().includes("toast")) {
      carbs += 30;
    }

    if (description.toLowerCase().includes("fruit") ||
        description.toLowerCase().includes("berries") ||
        description.toLowerCase().includes("banana")) {
      carbs += 15;
    }

    if (description.toLowerCase().includes("vegetable") ||
        description.toLowerCase().includes("salad")) {
      carbs += 10;
    }

    return carbs;
  };

  // Helper function to estimate fat
  const estimateFat = (description: string): number => {
    let fat = 10; // Base fat

    if (description.toLowerCase().includes("avocado") ||
        description.toLowerCase().includes("olive oil") ||
        description.toLowerCase().includes("nuts")) {
      fat += 10;
    }

    if (description.toLowerCase().includes("cheese") ||
        description.toLowerCase().includes("cream")) {
      fat += 8;
    }

    if (description.toLowerCase().includes("salmon") ||
        description.toLowerCase().includes("fatty fish")) {
      fat += 12;
    }

    return fat;
  };

  // Helper function to generate ingredients from description
  const generateIngredientsFromDescription = (description: string): string[] => {
    const ingredients: string[] = [];

    // Extract potential ingredients from the description
    const words = description.split(/\s+/);
    const commonIngredients = [
      "chicken", "beef", "fish", "salmon", "tuna", "eggs", "egg", "avocado",
      "spinach", "kale", "lettuce", "tomato", "tomatoes", "cucumber", "rice",
      "quinoa", "pasta", "bread", "toast", "oatmeal", "yogurt", "milk",
      "cheese", "nuts", "berries", "banana", "apple", "olive oil", "honey"
    ];

    // Check for common ingredients in the description
    commonIngredients.forEach(ingredient => {
      if (description.toLowerCase().includes(ingredient)) {
        // Format the ingredient with quantity
        if (ingredient === "eggs" || ingredient === "egg") {
          ingredients.push("2 large eggs");
        } else if (ingredient === "chicken" || ingredient === "beef" || ingredient === "fish" || ingredient === "salmon" || ingredient === "tuna") {
          ingredients.push(`4 oz ${ingredient}`);
        } else if (ingredient === "rice" || ingredient === "quinoa" || ingredient === "pasta") {
          ingredients.push(`1/2 cup cooked ${ingredient}`);
        } else if (ingredient === "olive oil") {
          ingredients.push("1 tbsp olive oil");
        } else if (ingredient === "honey") {
          ingredients.push("1 tsp honey");
        } else {
          ingredients.push(`1 ${ingredient}`);
        }
      }
    });

    // Add some basic ingredients if we don't have enough
    if (ingredients.length < 3) {
      if (description.toLowerCase().includes("salad")) {
        ingredients.push("2 cups mixed greens");
        ingredients.push("1/4 cup cherry tomatoes");
        ingredients.push("1 tbsp olive oil");
        ingredients.push("1 tsp balsamic vinegar");
      } else if (description.toLowerCase().includes("sandwich")) {
        ingredients.push("2 slices whole grain bread");
        ingredients.push("2 slices turkey or ham");
        ingredients.push("1 slice cheese");
        ingredients.push("1 tsp mustard");
      } else if (description.toLowerCase().includes("smoothie")) {
        ingredients.push("1 banana");
        ingredients.push("1 cup spinach");
        ingredients.push("1 cup almond milk");
        ingredients.push("1 tbsp protein powder");
      } else {
        ingredients.push("Salt and pepper to taste");
        ingredients.push("Herbs and spices as needed");
      }
    }

    return ingredients;
  };

  // Helper function to generate instructions from description
  const generateInstructionsFromDescription = (description: string): string[] => {
    const instructions: string[] = [];

    // Generate basic instructions based on the meal type
    if (description.toLowerCase().includes("salad")) {
      instructions.push("Wash and chop all vegetables.");
      instructions.push("Combine all ingredients in a large bowl.");
      instructions.push("Drizzle with olive oil and vinegar or your preferred dressing.");
      instructions.push("Toss until well combined and serve immediately.");
    } else if (description.toLowerCase().includes("sandwich") || description.toLowerCase().includes("toast")) {
      instructions.push("Toast the bread if desired.");
      instructions.push("Layer all ingredients on the bread.");
      instructions.push("Add condiments to taste.");
      instructions.push("Cut in half and serve.");
    } else if (description.toLowerCase().includes("smoothie")) {
      instructions.push("Add all ingredients to a blender.");
      instructions.push("Blend until smooth, about 30-60 seconds.");
      instructions.push("Pour into a glass and serve immediately.");
    } else if (description.toLowerCase().includes("chicken") || description.toLowerCase().includes("beef") || description.toLowerCase().includes("fish")) {
      instructions.push("Season the protein with salt, pepper, and desired spices.");
      instructions.push("Heat a pan over medium-high heat with a small amount of oil.");
      instructions.push("Cook the protein until done, about 4-6 minutes per side depending on thickness.");
      instructions.push("Let rest for a few minutes before serving with sides.");
    } else if (description.toLowerCase().includes("bowl") || description.toLowerCase().includes("rice") || description.toLowerCase().includes("quinoa")) {
      instructions.push("Cook the grain according to package instructions.");
      instructions.push("Prepare all vegetables and proteins while the grain cooks.");
      instructions.push("Combine all ingredients in a bowl.");
      instructions.push("Add sauce or dressing and mix if desired.");
    } else if (description.toLowerCase().includes("oatmeal") || description.toLowerCase().includes("porridge")) {
      instructions.push("Combine oats and liquid in a pot or microwave-safe bowl.");
      instructions.push("Cook until desired consistency is reached, stirring occasionally.");
      instructions.push("Add toppings like fruit, nuts, or sweeteners.");
      instructions.push("Serve hot.");
    } else {
      instructions.push("Prepare all ingredients as needed.");
      instructions.push("Combine ingredients according to the recipe.");
      instructions.push("Cook until done, ensuring proper food safety temperatures.");
      instructions.push("Serve and enjoy!");
    }

    return instructions;
  };

  // Helper function to generate tags from description and dietary restrictions
  const generateTagsFromDescription = (description: string, restrictions: string[]): string[] => {
    const tags: string[] = [];

    // Add tags based on dietary restrictions
    if (restrictions.length > 0) {
      restrictions.forEach(restriction => {
        tags.push(restriction);
      });
    }

    // Add tags based on meal content
    if (description.toLowerCase().includes("vegetable") ||
        description.toLowerCase().includes("salad") ||
        description.toLowerCase().includes("greens")) {
      tags.push("Vegetables");
    }

    if (description.toLowerCase().includes("protein") ||
        description.toLowerCase().includes("chicken") ||
        description.toLowerCase().includes("beef") ||
        description.toLowerCase().includes("fish")) {
      tags.push("High Protein");
    }

    if (description.toLowerCase().includes("quick") ||
        description.toLowerCase().includes("easy") ||
        description.toLowerCase().includes("simple")) {
      tags.push("Quick & Easy");
    }

    if (description.toLowerCase().includes("breakfast")) {
      tags.push("Breakfast");
    } else if (description.toLowerCase().includes("lunch")) {
      tags.push("Lunch");
    } else if (description.toLowerCase().includes("dinner")) {
      tags.push("Dinner");
    } else if (description.toLowerCase().includes("snack")) {
      tags.push("Snack");
    }

    return tags;
  };

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your daily overview...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {highlights.map((highlight, index) => (
        <Animated.View 
          key={highlight.id}
          entering={FadeInDown.delay(index * 100).duration(400).springify()}
        >
          <TouchableOpacity
            style={[styles.highlightCard, { backgroundColor: colors.card }]}
            onPress={() => navigation.navigate(highlight.screen as never)}
            activeOpacity={0.7}
          >
            <Ionicons 
              name={'star-outline'} 
              size={24} 
              color={colors.primary} 
            />
            <View style={styles.highlightTextContainer}>
              <Text style={[styles.highlightTitle, { color: colors.text }]}>{highlight.title}</Text>
              <Text style={[styles.highlightSubtitle, { color: colors.textSecondary }]}>{highlight.subtitle}</Text>
            </View>
            <Ionicons name="chevron-forward-outline" size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </Animated.View>
      ))}
      {highlights.length === 0 && (
        <Animated.View
          entering={FadeIn.duration(400)}
          style={styles.emptyContainer}
        >
          <Ionicons name="alert-circle-outline" size={40} color={colors.textSecondary} />
          <Text style={[styles.emptyText, { color: colors.text }]}>No highlights available</Text>
          <Text style={[styles.emptySubText, { color: colors.textSecondary }]}>
            Complete more activities to see your highlights
          </Text>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  highlightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  highlightTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  highlightTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
  },
  highlightSubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    opacity: 0.9,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginTop: 10,
    marginBottom: 5,
  },
  emptySubText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  scrollContent: {
    paddingBottom: 24,
    paddingTop: 12,
  },
  scrollViewContainer: {
    paddingHorizontal: 16,
  },
  dateWeatherRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
  },
  weatherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  weatherText: {
    fontSize: 14,
    marginLeft: 6,
    fontFamily: typography.fontFamily.regular,
  },
  greeting: {
            fontSize: 24,
            fontWeight: '600',
            marginBottom: 12,
            fontFamily: typography.fontFamily.semibold,
  },
  contextIndicators: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  contextBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  contextBadgeText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  suggestionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 12,
    marginVertical: 4,
  },
  suggestionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  suggestionIcon: {
    marginRight: 10,
  },
  suggestionText: {
    fontSize: 15,
    flex: 1,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 22,
  },
  tipCard: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 10,
    marginHorizontal: 6,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  tipIconContainer: {
    marginRight: 12,
  },
  tipIconCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tipText: {
    fontSize: 15,
    fontStyle: 'italic',
    fontFamily: typography.fontFamily.regular,
    lineHeight: 22,
    flex: 1,
  },
  workoutCard: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
    marginHorizontal: 6,
  },
  workoutContent: {
    flexDirection: 'column',
  },
  workoutTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  injuryWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 18,
    marginHorizontal: 4,
  },
  injuryWarningText: {
    fontSize: 13,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
    flex: 1,
  },
  workoutButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 2,
  },
  workoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginLeft: 10,
    marginVertical: 2,
  },
  workoutButtonText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  mealsContainer: {
    marginTop: 6,
    marginHorizontal: 6,
    paddingHorizontal: 4,
  },
  dietaryNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 18,
    marginHorizontal: 4,
  },
  dietaryNoticeText: {
    fontSize: 13,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
    flex: 1,
  },
  mealItem: {
    marginBottom: 16,
    paddingBottom: 16,
    paddingHorizontal: 2,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  mealTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mealTitle: {
    fontSize: 15,
    fontFamily: typography.fontFamily.medium,
  },
  mealDescription: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
    paddingLeft: 24, // Align with the icon
  },
  editButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewAllMealsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 18,
    borderRadius: 8,
    marginTop: 14,
    marginBottom: 4,
    marginHorizontal: 4,
  },
  viewAllMealsText: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginRight: 4,
  },
  nutritionCard: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 10,
    marginHorizontal: 6,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  nutritionItem: {
    flex: 1,
    paddingHorizontal: 8,
  },
  nutritionLabel: {
    fontSize: 13,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 4,
  },
  nutritionValue: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: typography.fontFamily.semibold,
  },
  modalContent: {
    padding: 16,
  },
  modalLabel: {
    fontSize: 14,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: typography.fontFamily.regular,
    minHeight: 120,
    marginBottom: 16,
  },
  modalButtonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 10,
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: typography.fontFamily.medium,
  },
});

export default DailyDashboardCard;
