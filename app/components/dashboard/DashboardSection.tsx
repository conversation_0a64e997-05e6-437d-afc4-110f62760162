import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';

interface DashboardSectionProps {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  children: React.ReactNode;
  onPress?: () => void;
  delay?: number;
}

const DashboardSection: React.FC<DashboardSectionProps> = ({
  title,
  icon,
  children,
  onPress,
  delay = 0
}) => {
  const { colors } = useTheme();

  return (
    <Animated.View
      style={[styles.container, { backgroundColor: colors.background }]}
      entering={FadeIn.duration(400).delay(delay)}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Ionicons name={icon} size={18} color={colors.primary} style={styles.icon} />
          <Text style={[styles.title, { color: colors.text }]}>
            {title}
          </Text>
        </View>

        {onPress && (
          <TouchableOpacity
            onPress={onPress}
            style={[styles.actionButton, { backgroundColor: colors.primaryLight }]}
            activeOpacity={0.7}
          >
            <Text style={[styles.actionText, { color: colors.primary }]}>
              View
            </Text>
            <Ionicons name="chevron-forward" size={14} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.content}>
        {children}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingTop: 6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
    paddingHorizontal: 2,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: typography.fontFamily.semiBold,
  },
  content: {
    marginTop: 8,
    paddingRight: 4,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    marginVertical: 2,
    marginRight: 2,
  },
  actionText: {
    fontSize: 13,
    fontWeight: '500',
    marginRight: 4,
  }
});

export default DashboardSection;
