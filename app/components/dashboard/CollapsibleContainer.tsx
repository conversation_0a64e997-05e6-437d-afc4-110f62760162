import React, { useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  interpolate,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';

interface CollapsibleContainerProps {
  title: string;
  children: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  height: number;
  collapsedHeight: number;
  showBadge?: boolean;
}

const CollapsibleContainer: React.FC<CollapsibleContainerProps> = ({
  title,
  children,
  isExpanded,
  onToggle,
  height,
  collapsedHeight,
  showBadge = false
}) => {
  const { colors } = useTheme();
  const animatedHeight = useSharedValue(isExpanded ? height : collapsedHeight);
  const rotateValue = useSharedValue(isExpanded ? 180 : 0);

  useEffect(() => {
    animatedHeight.value = withTiming(
      isExpanded ? height : collapsedHeight,
      { duration: 300, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }
    );

    rotateValue.value = withTiming(
      isExpanded ? 180 : 0,
      { duration: 300, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }
    );
  }, [isExpanded, height, collapsedHeight]);

  const containerStyle = useAnimatedStyle(() => {
    return {
      height: animatedHeight.value,
      overflow: 'hidden'
    };
  });

  const arrowStyle = useAnimatedStyle(() => {
    return {
      transform: [{
        rotate: `${interpolate(rotateValue.value, [0, 180], [0, 180])}deg`
      }]
    };
  });

  return (
    <Animated.View
      style={[
        styles.container,
        containerStyle,
        { backgroundColor: colors.card }
      ]}
      entering={FadeIn.duration(400)}
    >
      <TouchableOpacity
        style={[
          styles.header,
          { borderBottomColor: isExpanded ? colors.border : 'transparent' }
        ]}
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            {title}
          </Text>
          {showBadge && (
            <View style={[styles.badge, { backgroundColor: colors.primary }]}>
              <Text style={styles.badgeText}>New</Text>
            </View>
          )}
        </View>
        <Animated.View style={arrowStyle}>
          <Ionicons
            name="chevron-down"
            size={20}
            color={colors.text}
          />
        </Animated.View>
      </TouchableOpacity>

      <View style={styles.content}>
        {children}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderBottomWidth: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
    fontFamily: typography.fontFamily.semiBold,
  },
  content: {
    padding: 24,
    paddingTop: 20,
  },
  badge: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  }
});

export default CollapsibleContainer;
