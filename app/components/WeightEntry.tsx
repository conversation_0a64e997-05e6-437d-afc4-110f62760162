import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  ScrollView,
  Keyboard,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withDelay,
  withSpring,
  Easing,
  SlideInUp,
  FadeIn,
  FadeOut,
} from 'react-native-reanimated';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import { saveWeightEntry } from '../services/weightTracking';

interface WeightEntryProps {
  onWeightAdded: () => void;
  isUpdate?: boolean; // Flag to indicate if this is an update to an existing weight
  floatingStyle?: boolean; // Flag to use a compact floating button style
  hideButton?: boolean; // Flag to hide the button and only show modal functionality
}

export interface WeightEntryRef {
  openModal: () => void;
}

const WeightEntry = forwardRef<WeightEntryRef, WeightEntryProps>(({
  onWeightAdded,
  isUpdate = false,
  floatingStyle = false
}, ref) => {
  const { colors, isDark } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [weight, setWeight] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [showDateOptions, setShowDateOptions] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Animation values
  const successOpacity = useSharedValue(0);
  const successScale = useSharedValue(0.8);
  const buttonScale = useSharedValue(1);
  const modalScale = useSharedValue(0.95);
  const dateOptionsHeight = useSharedValue(0);

  // Expose openModal function to parent component
  useImperativeHandle(ref, () => ({
    openModal: () => {
      setModalVisible(true);
      setError('');
      setSelectedDate(new Date());
    }
  }));

  // Add keyboard detection effects with smooth animation
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        // Animate the transition when keyboard shows
        setKeyboardVisible(true);

        // On iOS we can get the keyboard height and animate based on it
        const keyboardHeight = event.endCoordinates?.height || 0;
        modalScale.value = withTiming(1, { duration: 300 });
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        // Animate the transition when keyboard hides
        setKeyboardVisible(false);
        modalScale.value = withTiming(1, { duration: 300 });
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  const handleAddWeight = async () => {
    if (!weight.trim()) {
      setError('Please enter a weight value');
      return;
    }

    const weightValue = parseFloat(weight);
    if (isNaN(weightValue) || weightValue <= 0 || weightValue > 1000) {
      setError('Please enter a valid weight between 0 and 1000 lbs');
      return;
    }

    // Dismiss keyboard for smoother animation
    Keyboard.dismiss();

    setIsLoading(true);
    setError('');

    try {
      // Show partial success message early for better UX
      setSuccessMessage('Saving weight...');
      setShowSuccess(true);

      // Partial success animation
      successOpacity.value = withTiming(0.7, { duration: 150 });
      successScale.value = withTiming(1, { duration: 150 });

      // STEP 1: IMMEDIATE LOCAL UPDATE - Use our new function for instant UI updates
      try {
        // Import the function that updates all local data sources
        const { updateLocalWeightData } = require('../services/weightTracking');

        // Update all local data sources with the new weight
        await updateLocalWeightData(weightValue);

        // Notify parent component to refresh UI with new weight (only once)
        onWeightAdded();

        console.log('All local data sources updated with new weight:', weightValue);
      } catch (error) {
        console.error('Error updating local data sources:', error);
      }

      // BACKGROUND SYNC - Save to backend asynchronously
      saveWeightEntry(weightValue, undefined, selectedDate)
        .then(entryId => {
          if (entryId) {
            console.log('Weight entry saved to backend with ID:', entryId);
          } else {
            console.error('Failed to save weight entry to backend');
          }
        })
        .catch(error => {
          console.error('Error saving weight to backend:', error);
        });

      // Update success message and complete the animation
      setSuccessMessage(isUpdate ? 'Weight updated successfully!' : 'Weight added successfully!');

      // Animate success message with spring for more natural feel
      successOpacity.value = withSequence(
        withSpring(1, {
          damping: 15,
          stiffness: 120,
          mass: 0.8
        }),
        withDelay(1200, withTiming(0, { duration: 300 }))
      );

      successScale.value = withSequence(
        withSpring(1.1, {
          damping: 15,
          stiffness: 120
        }),
        withSpring(1, {
          damping: 12,
          stiffness: 100
        }),
        withDelay(1200, withTiming(0.8, { duration: 300 }))
      );

      // Close modal and reset form after animation
      setTimeout(() => {
        // Start modal dismiss animation
        modalScale.value = withTiming(0.95, { duration: 200 });

        // Actually close the modal slightly later for smooth exit
        setTimeout(() => {
          setModalVisible(false);
          setWeight('');
          setSelectedDate(new Date()); // Reset to today
          setShowSuccess(false);
          setShowDateOptions(false);
          dateOptionsHeight.value = 0;
        }, 200);
      }, 1500);
    } catch (error) {
      console.error('Error saving weight:', error);
      setError('Failed to save weight. Please try again.');
      setShowSuccess(false);
    } finally {
      setIsLoading(false);
    }
  };

  const openModal = () => {
    setModalVisible(true);
    setError('');
    // Reset to today when opening modal
    setSelectedDate(new Date());
  };

  const handleDateChange = (event: any, date?: Date) => {
    // For Android, automatically close the picker after selection
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    // If a date was selected, update the state
    if (date) {
      setSelectedDate(date);

      // For iOS we don't automatically close - user will use "Done" button
      // But we can provide feedback that the date was selected
      if (Platform.OS === 'ios') {
        // Visual feedback that date was selected (subtle animation)
        successScale.value = withSequence(
          withTiming(1.03, { duration: 100 }),
          withTiming(1, { duration: 100 })
        );
      }
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Improved toggle date options with more stable animations
  const toggleDateOptions = () => {
    // First close date picker if it's open
    if (showDatePicker) {
      setShowDatePicker(false);
    }

    if (showDateOptions) {
      // Hide date options with animation
      dateOptionsHeight.value = withTiming(0, {
        duration: 250,
        easing: Easing.inOut(Easing.quad)
      });
      setTimeout(() => setShowDateOptions(false), 260);
    } else {
      // Show date options with animation
      setShowDateOptions(true);
      setTimeout(() => {
        dateOptionsHeight.value = withTiming(140, {
          duration: 300,
          easing: Easing.out(Easing.quad)
        });
      }, 10);
    }
  };

  // Success message animation style
  const successAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: successOpacity.value,
      transform: [{ scale: successScale.value }]
    };
  });

  // Handle button press animation
  const handleButtonPress = () => {
    buttonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    openModal();
  };

  // Button animation style
  const buttonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }]
    };
  });

  // Modal animation style
  const modalAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: modalScale.value }]
    };
  });

  // Date options animation style
  const dateOptionsAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: dateOptionsHeight.value,
      opacity: dateOptionsHeight.value > 0 ? withTiming(1, { duration: 200 }) : withTiming(0, { duration: 100 }),
      overflow: 'hidden'
    };
  });


  // Enhanced data submission with haptic feedback and animations
  const enhancedAddWeight = async () => {
    // Dismiss keyboard first for smoother animation
    Keyboard.dismiss();

    // Add slight feedback animation
    buttonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 150 })
    );

    await handleAddWeight();
  };

  // Effect to handle modal animation
  useEffect(() => {
    if (modalVisible) {
      // Animate modal in
      modalScale.value = withSpring(1, {
        damping: 15,
        stiffness: 150,
        mass: 0.8
      });

      // Reset date options when modal opens
      setShowDateOptions(false);
      dateOptionsHeight.value = 0;
    }
  }, [modalVisible]);

  return (
    <>
      {floatingStyle ? (
        <Animated.View style={buttonAnimatedStyle}>
          <TouchableOpacity
            style={[
              styles.floatingButton,
              {
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOpacity: isDark ? 0.4 : 0.2,
              }
            ]}
            onPress={handleButtonPress}
            activeOpacity={0.8}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.floatingButtonText}>Add Weight</Text>
          </TouchableOpacity>
        </Animated.View>
      ) : (
        <Animated.View style={buttonAnimatedStyle}>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={handleButtonPress}
            activeOpacity={0.8}
          >
            <Ionicons name="add" size={24} color="#fff" />
            <Text style={styles.addButtonText}>Add Weight</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      <Modal
        transparent={true}
        visible={modalVisible}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
        statusBarTranslucent={true}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
          keyboardVerticalOffset={10}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                backgroundColor: colors.card,
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: -2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 5,
              },
              modalAnimatedStyle
            ]}
            entering={SlideInUp.springify().damping(15)}
          >
            {/* Pull indicator at top of modal */}
            <View style={[styles.pullIndicator, { backgroundColor: colors.border }]} />

            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Today's Weight
              </Text>
              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: colors.background }]}
                onPress={() => setModalVisible(false)}
                hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
              >
                <Ionicons name="close" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContentContainer}>
              {/* Simple weight input with large font for easier entry */}
              <View style={styles.inputContainer}>
                <View style={[
                  styles.weightInputContainer,
                  {
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    borderRadius: 16,
                    marginBottom: 8
                  }
                ]}>
                  <TextInput
                    style={[
                      styles.input,
                      styles.weightInput,
                      {
                        color: colors.text,
                        fontSize: typography.sizes.xxxl,
                      }
                    ]}
                    value={weight}
                    onChangeText={setWeight}
                    placeholder="0.0"
                    placeholderTextColor={colors.textTertiary}
                    keyboardType="decimal-pad"
                    returnKeyType="done"
                    selectionColor={colors.primary}
                  />
                  <Text style={[styles.weightUnit, { color: colors.textSecondary }]}>lbs</Text>
                </View>

                {/* Current date display (subtle) */}
                <View style={styles.currentDateContainer}>
                  <Ionicons
                    name="today-outline"
                    size={16}
                    color={colors.textSecondary}
                    style={{marginRight: 6}}
                  />
                  <Text style={[styles.currentDateText, { color: colors.textSecondary }]}>
                    {formatDate(selectedDate)}
                  </Text>

                  {/* Change date button */}
                  <TouchableOpacity
                    style={styles.changeDateButton}
                    onPress={() => {
                      // Toggle date options visibility
                      if (showDateOptions) {
                        // Hide date options with animation
                        dateOptionsHeight.value = withTiming(0, {
                          duration: 250,
                          easing: Easing.inOut(Easing.quad)
                        });
                        setTimeout(() => setShowDateOptions(false), 260);
                      } else {
                        // Show date options with animation
                        setShowDateOptions(true);
                        setTimeout(() => {
                          dateOptionsHeight.value = withTiming(140, {
                            duration: 300,
                            easing: Easing.out(Easing.quad)
                          });
                        }, 10);
                      }
                    }}
                    hitSlop={{ top: 15, right: 15, bottom: 15, left: 15 }}
                  >
                    <Text style={[styles.changeDateText, { color: colors.primary }]}>
                      {showDateOptions ? "Hide options" : "Change date"}
                    </Text>
                    <Ionicons
                      name={showDateOptions ? "chevron-up" : "chevron-down"}
                      size={14}
                      color={colors.primary}
                      style={{marginLeft: 4}}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Expandable date options */}
              {showDateOptions && (
                <Animated.View style={[styles.dateOptionsContainer, dateOptionsAnimatedStyle]}>
                  <TouchableOpacity
                    style={[
                      styles.dateSelector,
                      {
                        backgroundColor: colors.background,
                        borderColor: colors.border,
                        borderRadius: 12,
                        marginBottom: 12,
                        shadowColor: colors.primary,
                        shadowOffset: { width: 0, height: 1 },
                        shadowOpacity: 0.1,
                        shadowRadius: 2,
                        elevation: 1,
                      }
                    ]}
                    onPress={() => {
                      setShowDatePicker(true);
                      // Hide date options when showing the picker for cleaner UI
                      if (Platform.OS === 'ios') {
                        Keyboard.dismiss(); // Dismiss keyboard on iOS for better date picker display
                      }
                    }}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name="calendar-outline"
                      size={20}
                      color={colors.primary}
                      style={styles.calendarIcon}
                    />
                    <Text style={[styles.dateText, { color: colors.text }]}>
                      Select different date
                    </Text>
                  </TouchableOpacity>

                  {/* Quick date buttons */}
                  <View style={styles.quickDateButtons}>
                    <TouchableOpacity
                      style={[styles.quickDateButton, {backgroundColor: colors.primaryLight + '30'}]}
                      onPress={() => {
                        const yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        setSelectedDate(yesterday);
                      }}
                    >
                      <Text style={[styles.quickDateButtonText, {color: colors.primary}]}>Yesterday</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.quickDateButton, {backgroundColor: colors.primaryLight + '50'}]}
                      onPress={() => setSelectedDate(new Date())}
                    >
                      <Text style={[styles.quickDateButtonText, {color: colors.primary}]}>Today</Text>
                    </TouchableOpacity>
                  </View>
                </Animated.View>
              )}

              {/* Date picker displayed outside the animated container */}
              {showDatePicker && (
                <Animated.View
                  style={[
                    styles.datePickerContainer,
                    {
                      backgroundColor: colors.card,
                      borderColor: colors.border,
                      borderWidth: 1
                    }
                  ]}
                  entering={FadeIn.duration(200)}
                >
                  {Platform.OS === 'ios' && (
                    <View style={styles.datePickerHeader}>
                      <TouchableOpacity
                        style={styles.datePickerDoneButton}
                        onPress={() => setShowDatePicker(false)}
                      >
                        <Text style={[styles.datePickerDoneText, { color: colors.primary }]}>
                          Done
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}

                  <DateTimePicker
                    value={selectedDate}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={handleDateChange}
                    maximumDate={new Date()}
                    themeVariant={isDark ? 'dark' : 'light'}
                    textColor={colors.text}
                    style={{
                      width: Platform.OS === 'ios' ? '100%' : undefined,
                      backgroundColor: colors.card
                    }}
                  />
                </Animated.View>
              )}

              {/* Error display with icon */}
              {error ? (
                <Animated.View
                  style={[styles.errorContainer, { backgroundColor: `${colors.error}20` }]}
                  entering={FadeIn.duration(200)}
                  exiting={FadeOut.duration(200)}
                >
                  <Ionicons name="alert-circle" size={18} color={colors.error} />
                  <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
                </Animated.View>
              ) : null}

              {/* Success message with animation */}
              {showSuccess && (
                <Animated.View style={[styles.successContainer, successAnimatedStyle]}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  <Text style={[styles.successText, { color: colors.success }]}>
                    {successMessage}
                  </Text>
                </Animated.View>
              )}

              {/* Action buttons - adjusted for keyboard visibility */}
              <Animated.View style={[
                styles.buttonContainer,
                keyboardVisible && {
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  backgroundColor: colors.card,
                  borderTopWidth: 1,
                  borderTopColor: colors.border,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: -2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 2,
                  elevation: 4,
                }
              ]}>
                <View style={styles.buttonRow}>
                  <TouchableOpacity
                    style={[
                      styles.cancelButton,
                      { backgroundColor: colors.background, borderColor: colors.border }
                    ]}
                    onPress={() => {
                      Keyboard.dismiss();
                      setModalVisible(false);
                    }}
                    disabled={isLoading || showSuccess}
                  >
                    <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.saveButton,
                      {
                        backgroundColor: colors.primary,
                        shadowColor: colors.primary,
                        shadowOpacity: 0.3,
                        shadowRadius: 5,
                        elevation: 3,
                      },
                      isLoading && { opacity: 0.7 }
                    ]}
                    onPress={enhancedAddWeight}
                    disabled={isLoading || showSuccess}
                  >
                    {isLoading ? (
                      <ActivityIndicator color="#fff" size="small" />
                    ) : (
                      <>
                        <Ionicons
                          name="checkmark"
                          size={20}
                          color="#fff"
                          style={styles.saveButtonIcon}
                        />
                        <Text style={styles.saveButtonText}>
                          Save Weight
                        </Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </Animated.View>
            </View>
          </Animated.View>
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
});

const styles = StyleSheet.create({
  // Date picker specific styles
  datePickerContainer: {
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    overflow: 'hidden',
    paddingBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  datePickerHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.2)',
  },
  datePickerDoneButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  datePickerDoneText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  // Standard add button
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    marginVertical: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  addButtonText: {
    color: '#fff',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
  },

  // Floating compact style button
  floatingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 30,
    elevation: 3,
    shadowOffset: { width: 0, height: 3 },
    shadowRadius: 4,
  },
  floatingButtonText: {
    color: '#fff',
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 6,
  },

  // Modal container
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    maxHeight: '80%', // Reduce max height to work better with keyboard
  },
  modalContentContainer: {
    position: 'relative',
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
    minHeight: 300,
  },
  pullIndicator: {
    width: 36,
    height: 5,
    borderRadius: 3,
    marginTop: 8,
    marginBottom: 16,
    alignSelf: 'center',
    opacity: 0.2,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Input fields
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    height: 56,
    borderWidth: 1,
    paddingHorizontal: 16,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  weightInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 100,
    borderWidth: 1,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  weightInput: {
    flex: 1,
    height: 100,
    borderWidth: 0,
    paddingHorizontal: 0,
    paddingVertical: 12,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
  },
  weightUnit: {
    fontSize: typography.sizes.xxxl,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
    width: 65,
    opacity: 0.7,
  },

  // New date display and selection
  currentDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingHorizontal: 4,
  },
  currentDateText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  changeDateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  changeDateText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  dateOptionsContainer: {
    marginBottom: 16,
  },
  quickDateButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  quickDateButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  quickDateButtonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
  },
  dateSelector: {
    height: 56,
    borderWidth: 1,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  calendarIcon: {
    marginRight: 12,
  },
  dateText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },

  // Error and success states
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
    flex: 1,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(46, 204, 113, 0.1)',
  },
  successText: {
    marginLeft: 8,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },

  // Action buttons
  buttonContainer: {
    paddingTop: 16,
    paddingBottom: Platform.OS === 'ios' ? 20 : 16,
    width: '100%',
    zIndex: 5,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  saveButton: {
    flex: 2,
    height: 56,
    borderRadius: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: 3 },
  },
  saveButtonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default WeightEntry;
