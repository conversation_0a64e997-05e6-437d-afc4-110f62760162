import React, { useEffect } from 'react';
import { View, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useTheme } from '../theme/ThemeProvider';
import { spacing } from '../theme/spacing';

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  animated?: boolean;
}

/**
 * Individual skeleton loading element
 */
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  animated = true,
}) => {
  const { colors, isDark } = useTheme();
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    if (animated) {
      shimmerValue.value = withRepeat(
        withTiming(1, { duration: 1500 }),
        -1,
        true
      );
    }
  }, [animated]);

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      shimmerValue.value,
      [0, 0.5, 1],
      [0.3, 0.7, 0.3]
    );

    return {
      opacity: animated ? opacity : 0.3,
    };
  });

  const baseColor = isDark ? colors.border : colors.card;
  const highlightColor = isDark ? colors.card : colors.background;

  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor: baseColor,
          overflow: 'hidden',
        },
        style,
      ]}
    >
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: highlightColor,
          },
          animatedStyle,
        ]}
      />
    </View>
  );
};

/**
 * Pre-built skeleton patterns for common UI elements
 */

interface SkeletonCardProps {
  showAvatar?: boolean;
  lines?: number;
  style?: ViewStyle;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  showAvatar = false,
  lines = 3,
  style,
}) => {
  return (
    <View
      style={[
        {
          padding: spacing.padding.card,
          marginBottom: spacing.margin.component,
        },
        style,
      ]}
    >
      <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
        {showAvatar && (
          <SkeletonLoader
            width={40}
            height={40}
            borderRadius={20}
            style={{ marginRight: spacing.margin.element }}
          />
        )}
        <View style={{ flex: 1 }}>
          <SkeletonLoader
            height={16}
            style={{ marginBottom: spacing.margin.tight }}
          />
          {Array.from({ length: lines - 1 }).map((_, index) => (
            <SkeletonLoader
              key={index}
              height={14}
              width={index === lines - 2 ? '70%' : '100%'}
              style={{ marginBottom: spacing.margin.tight }}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

interface SkeletonListProps {
  items?: number;
  showAvatar?: boolean;
  style?: ViewStyle;
}

export const SkeletonList: React.FC<SkeletonListProps> = ({
  items = 5,
  showAvatar = false,
  style,
}) => {
  return (
    <View style={style}>
      {Array.from({ length: items }).map((_, index) => (
        <SkeletonCard key={index} showAvatar={showAvatar} lines={2} />
      ))}
    </View>
  );
};

interface SkeletonHeaderProps {
  showBackButton?: boolean;
  style?: ViewStyle;
}

export const SkeletonHeader: React.FC<SkeletonHeaderProps> = ({
  showBackButton = false,
  style,
}) => {
  return (
    <View
      style={[
        {
          flexDirection: 'row',
          alignItems: 'center',
          padding: spacing.padding.container,
          justifyContent: 'space-between',
        },
        style,
      ]}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        {showBackButton && (
          <SkeletonLoader
            width={24}
            height={24}
            borderRadius={12}
            style={{ marginRight: spacing.margin.element }}
          />
        )}
        <SkeletonLoader width={120} height={20} />
      </View>
      <SkeletonLoader width={24} height={24} borderRadius={12} />
    </View>
  );
};

interface SkeletonButtonProps {
  width?: number | string;
  style?: ViewStyle;
}

export const SkeletonButton: React.FC<SkeletonButtonProps> = ({
  width = '100%',
  style,
}) => {
  return (
    <SkeletonLoader
      width={width}
      height={spacing.layout.buttonHeight}
      borderRadius={spacing.layout.buttonRadius}
      style={style}
    />
  );
};

interface SkeletonInputProps {
  style?: ViewStyle;
}

export const SkeletonInput: React.FC<SkeletonInputProps> = ({ style }) => {
  return (
    <SkeletonLoader
      height={spacing.layout.inputHeight}
      borderRadius={spacing.layout.inputRadius}
      style={style}
    />
  );
};

/**
 * Skeleton screen wrapper for full-screen loading states
 */
interface SkeletonScreenProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const SkeletonScreen: React.FC<SkeletonScreenProps> = ({
  children,
  style,
}) => {
  return (
    <View
      style={[
        {
          flex: 1,
          padding: spacing.padding.screen,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
};

export default SkeletonLoader;
