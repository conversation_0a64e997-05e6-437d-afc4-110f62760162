import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing } from '../theme/spacing';
import { typography } from '../theme/typography';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Enhanced Error Boundary with better UX and error reporting
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[ErrorBoundary] Caught error:', error);
    console.error('[ErrorBoundary] Error info:', errorInfo);
    
    this.setState({ error, errorInfo });
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // TODO: Send error to crash reporting service
    // Example: Crashlytics.recordError(error);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  onRetry: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onRetry }) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Ionicons 
          name="warning-outline" 
          size={64} 
          color="#FF6B6B" 
          style={styles.icon}
        />
        
        <Text style={styles.title}>Oops! Something went wrong</Text>
        
        <Text style={styles.message}>
          We encountered an unexpected error. Don't worry, your data is safe.
        </Text>
        
        {__DEV__ && error && (
          <View style={styles.errorDetails}>
            <Text style={styles.errorTitle}>Error Details (Dev Mode):</Text>
            <Text style={styles.errorText}>{error.message}</Text>
            {error.stack && (
              <Text style={styles.errorStack} numberOfLines={10}>
                {error.stack}
              </Text>
            )}
          </View>
        )}
        
        <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
          <Ionicons name="refresh" size={20} color="#FFFFFF" />
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
        
        <Text style={styles.helpText}>
          If this problem persists, please contact support.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.padding.screen,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  icon: {
    marginBottom: spacing.margin.section,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: spacing.margin.element,
  },
  message: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    color: '#5D6D7E',
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed,
    marginBottom: spacing.margin.section,
  },
  errorDetails: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FEB2B2',
    borderWidth: 1,
    borderRadius: spacing.layout.inputRadius,
    padding: spacing.padding.component,
    marginBottom: spacing.margin.section,
    width: '100%',
  },
  errorTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    color: '#C53030',
    marginBottom: spacing.margin.tight,
  },
  errorText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.mono || typography.fontFamily.regular,
    color: '#E53E3E',
    marginBottom: spacing.margin.tight,
  },
  errorStack: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.mono || typography.fontFamily.regular,
    color: '#C53030',
    lineHeight: 14,
  },
  retryButton: {
    backgroundColor: '#3498DB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.padding.section,
    paddingVertical: spacing.padding.component,
    borderRadius: spacing.layout.buttonRadius,
    marginBottom: spacing.margin.component,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: spacing.margin.tight,
  },
  helpText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    color: '#85929E',
    textAlign: 'center',
  },
});

/**
 * Hook for handling errors in functional components
 */
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('[useErrorHandler] Error caught:', error);
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      // TODO: Send error to crash reporting service
      console.error('[useErrorHandler] Error state set:', error);
    }
  }, [error]);

  return {
    error,
    resetError,
    handleError,
  };
};

/**
 * Higher-order component for wrapping components with error boundary
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default ErrorBoundary;
