import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { typography } from '../theme/typography';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { WeatherData } from '../services/nutritionService';

interface MiniWeatherDisplayProps {
  weatherData: WeatherData | null;
  isLoading: boolean;
  onRefresh: () => void;
}

const MiniWeatherDisplay: React.FC<MiniWeatherDisplayProps> = ({
  weatherData,
  isLoading,
  onRefresh,
}) => {
  const { colors, isDark } = useTheme();
  const [showModal, setShowModal] = useState(false);

  // Get appropriate weather icon
  const getWeatherIcon = () => {
    if (!weatherData) return 'partly-sunny-outline';

    // If weather data is unavailable, show alert icon
    if (weatherData.unavailable) return 'alert-circle-outline';

    const condition = weatherData.condition?.toLowerCase() || '';
    if (condition.includes('clear') || condition.includes('sun')) {
      return 'sunny-outline';
    } else if (condition.includes('cloud')) {
      return 'cloudy-outline';
    } else if (condition.includes('rain') || condition.includes('drizzle')) {
      return 'rainy-outline';
    } else if (condition.includes('snow')) {
      return 'snow-outline';
    } else if (condition.includes('thunder') || condition.includes('storm')) {
      return 'thunderstorm-outline';
    } else {
      return 'partly-sunny-outline';
    }
  };

  // Convert wind speed from km/h to mph
  const getWindSpeedInMph = (kmh: number | null): string => {
    if (kmh === null) return 'N/A';
    const mph = Math.round(kmh * 0.621371);
    return `${mph} mph`;
  };

  // Render mini weather display
  const renderMiniWeather = () => {
    if (isLoading) {
      return (
        <View style={styles.miniContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      );
    }

    if (!weatherData || weatherData.unavailable) {
      return (
        <View style={styles.miniContainer}>
          <Ionicons name="alert-circle-outline" size={16} color={colors.error} />
        </View>
      );
    }

    return (
      <View style={styles.miniContainer}>
        <Ionicons name={getWeatherIcon()} size={16} color={colors.primary} />
        <Text style={[styles.miniTemp, { color: colors.text }]}>
          {Math.round(weatherData.temperatureF || 0)}°F
        </Text>
      </View>
    );
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.container, { backgroundColor: colors.card }]}
        onPress={() => setShowModal(true)}
        activeOpacity={0.7}
      >
        {renderMiniWeather()}
      </TouchableOpacity>

      {/* Detailed Weather Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => setShowModal(false)}
      >
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
          style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
        >
          <Animated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={SlideOutDown.duration(300)}
            style={[styles.modalContent, { backgroundColor: colors.card }]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Weather Details</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Loading weather data...
                </Text>
              </View>
            ) : !weatherData || weatherData.unavailable ? (
              <View style={styles.unavailableContainer}>
                <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
                <Text style={[styles.unavailableText, { color: colors.text }]}>
                  Weather data unavailable
                </Text>
                <TouchableOpacity
                  style={[styles.refreshButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    onRefresh();
                  }}
                >
                  <Text style={[styles.refreshButtonText, { color: colors.background }]}>
                    Refresh
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.weatherDetails}>
                <View style={styles.weatherHeader}>
                  {weatherData.iconUrl ? (
                    <Image
                      source={{ uri: `https:${weatherData.iconUrl}` }}
                      style={styles.weatherIcon}
                      resizeMode="contain"
                    />
                  ) : (
                    <Ionicons name={getWeatherIcon()} size={64} color={colors.primary} />
                  )}
                  <View style={styles.weatherHeaderInfo}>
                    <Text style={[styles.location, { color: colors.text }]}>
                      {weatherData.location}
                    </Text>
                    <Text style={[styles.temperature, { color: colors.text }]}>
                      {Math.round(weatherData.temperatureF || 0)}°F
                    </Text>
                    <Text style={[styles.condition, { color: colors.textSecondary }]}>
                      {weatherData.condition}
                    </Text>
                  </View>
                </View>

                <View style={styles.weatherGrid}>
                  <View style={styles.weatherGridItem}>
                    <Ionicons name="thermometer-outline" size={24} color={colors.primary} />
                    <Text style={[styles.gridLabel, { color: colors.textSecondary }]}>Feels Like</Text>
                    <Text style={[styles.gridValue, { color: colors.text }]}>
                      {weatherData.feelsLikeF ? `${Math.round(weatherData.feelsLikeF)}°F` : 'N/A'}
                    </Text>
                  </View>

                  <View style={styles.weatherGridItem}>
                    <Ionicons name="water-outline" size={24} color={colors.primary} />
                    <Text style={[styles.gridLabel, { color: colors.textSecondary }]}>Humidity</Text>
                    <Text style={[styles.gridValue, { color: colors.text }]}>
                      {weatherData.humidity ? `${weatherData.humidity}%` : 'N/A'}
                    </Text>
                  </View>

                  <View style={styles.weatherGridItem}>
                    <Ionicons name="speedometer-outline" size={24} color={colors.primary} />
                    <Text style={[styles.gridLabel, { color: colors.textSecondary }]}>Wind</Text>
                    <Text style={[styles.gridValue, { color: colors.text }]}>
                      {weatherData.windSpeed ? getWindSpeedInMph(weatherData.windSpeed) : 'N/A'}
                    </Text>
                  </View>

                  <View style={styles.weatherGridItem}>
                    <Ionicons name="compass-outline" size={24} color={colors.primary} />
                    <Text style={[styles.gridLabel, { color: colors.textSecondary }]}>Direction</Text>
                    <Text style={[styles.gridValue, { color: colors.text }]}>
                      {weatherData.windDirection || 'N/A'}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={[styles.refreshButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    onRefresh();
                  }}
                >
                  <Text style={[styles.refreshButtonText, { color: colors.background }]}>
                    Refresh Weather
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </Animated.View>
        </Animated.View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8,
  },
  miniContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniTemp: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '90%',
    borderRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  unavailableContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  unavailableText: {
    marginTop: 16,
    marginBottom: 20,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  weatherDetails: {
    alignItems: 'stretch',
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  weatherIcon: {
    width: 80,
    height: 80,
    marginRight: 16,
  },
  weatherHeaderInfo: {
    flex: 1,
  },
  location: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  temperature: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  condition: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  weatherGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
  },
  weatherGridItem: {
    width: '50%',
    alignItems: 'center',
    marginBottom: 20,
  },
  gridLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  gridValue: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    marginTop: 2,
  },
  refreshButton: {
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  refreshButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default MiniWeatherDisplay;
