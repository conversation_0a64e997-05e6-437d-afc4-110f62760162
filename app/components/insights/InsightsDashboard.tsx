import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  FadeIn,
  SlideInRight
} from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import {
  Insight,
  InsightSummary,
  InsightType,
  InsightPriority,
  generatePersonalizedInsights,
  getInsightsSummary
} from '../../services/personalizedInsightsService';

interface InsightsDashboardProps {
  onInsightPress?: (insight: Insight) => void;
  showHeader?: boolean;
  maxInsights?: number;
}

const { width } = Dimensions.get('window');

const InsightsDashboard: React.FC<InsightsDashboardProps> = ({
  onInsightPress,
  showHeader = true,
  maxInsights = 10
}) => {
  const { colors, isDark } = useTheme();
  const [insights, setInsights] = useState<Insight[]>([]);
  const [summary, setSummary] = useState<InsightSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadInsights();
  }, []);

  const loadInsights = async (forceRefresh = false) => {
    try {
      if (forceRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const [insightsData, summaryData] = await Promise.all([
        generatePersonalizedInsights(forceRefresh),
        getInsightsSummary()
      ]);

      setInsights(insightsData.slice(0, maxInsights));
      setSummary(summaryData);

    } catch (error) {
      console.error('[InsightsDashboard] Error loading insights:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadInsights(true);
  };

  const getPriorityColor = (priority: InsightPriority): string => {
    switch (priority) {
      case InsightPriority.CRITICAL:
        return '#FF3B30';
      case InsightPriority.HIGH:
        return '#FF9500';
      case InsightPriority.MEDIUM:
        return '#007AFF';
      case InsightPriority.LOW:
        return colors.textSecondary;
      default:
        return colors.textSecondary;
    }
  };

  const getInsightIcon = (type: InsightType): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case InsightType.PERFORMANCE_TREND:
        return 'trending-up';
      case InsightType.NUTRITION_PATTERN:
        return 'restaurant';
      case InsightType.WORKOUT_OPTIMIZATION:
        return 'barbell';
      case InsightType.RECOVERY_ANALYSIS:
        return 'bed';
      case InsightType.GOAL_PROGRESS:
        return 'flag';
      case InsightType.BEHAVIORAL_PATTERN:
        return 'analytics';
      case InsightType.PREDICTION:
        return 'eye';
      case InsightType.MILESTONE_ACHIEVEMENT:
        return 'trophy';
      default:
        return 'bulb';
    }
  };

  const renderInsightCard = (insight: Insight, index: number) => (
    <Animated.View
      key={insight.id}
      entering={SlideInRight.delay(index * 100)}
      style={[styles.insightCard, { backgroundColor: colors.card }]}
    >
      <TouchableOpacity
        style={styles.insightContent}
        onPress={() => onInsightPress?.(insight)}
        activeOpacity={0.7}
      >
        {/* Header */}
        <View style={styles.insightHeader}>
          <View style={[styles.iconContainer, { backgroundColor: `${getPriorityColor(insight.priority)}20` }]}>
            <Ionicons
              name={getInsightIcon(insight.type)}
              size={20}
              color={getPriorityColor(insight.priority)}
            />
          </View>
          <View style={styles.headerText}>
            <Text style={[styles.insightTitle, { color: colors.text }]} numberOfLines={2}>
              {insight.title}
            </Text>
            <View style={styles.metaContainer}>
              <Text style={[styles.category, { color: colors.textSecondary }]}>
                {insight.category}
              </Text>
              <View style={styles.confidenceContainer}>
                <Ionicons name="checkmark-circle" size={12} color={colors.primary} />
                <Text style={[styles.confidence, { color: colors.textSecondary }]}>
                  {insight.confidence}%
                </Text>
              </View>
            </View>
          </View>
          {insight.actionable && (
            <View style={[styles.actionableBadge, { backgroundColor: colors.primary }]}>
              <Ionicons name="flash" size={12} color={colors.background} />
            </View>
          )}
        </View>

        {/* Description */}
        <Text style={[styles.insightDescription, { color: colors.textSecondary }]} numberOfLines={3}>
          {insight.description}
        </Text>

        {/* Recommendations Preview */}
        {insight.recommendations.length > 0 && (
          <View style={styles.recommendationsPreview}>
            <Text style={[styles.recommendationText, { color: colors.text }]} numberOfLines={1}>
              💡 {insight.recommendations[0]}
            </Text>
            {insight.recommendations.length > 1 && (
              <Text style={[styles.moreRecommendations, { color: colors.textSecondary }]}>
                +{insight.recommendations.length - 1} more
              </Text>
            )}
          </View>
        )}

        {/* Tags */}
        {insight.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {insight.tags.slice(0, 3).map((tag, tagIndex) => (
              <View key={tagIndex} style={[styles.tag, { backgroundColor: colors.border }]}>
                <Text style={[styles.tagText, { color: colors.textSecondary }]}>
                  {tag}
                </Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );

  const renderSummaryCard = () => {
    if (!summary) return null;

    return (
      <Animated.View
        entering={FadeIn}
        style={[styles.summaryCard, { backgroundColor: colors.card }]}
      >
        <Text style={[styles.summaryTitle, { color: colors.text }]}>
          Insights Summary
        </Text>
        
        <View style={styles.summaryStats}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>
              {summary.totalInsights}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Total
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#FF9500' }]}>
              {summary.highPriorityInsights}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              High Priority
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: '#34C759' }]}>
              {summary.actionableInsights}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Actionable
            </Text>
          </View>
        </View>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Analyzing your data...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
      >
        {showHeader && (
          <View style={styles.header}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Personalized Insights
            </Text>
            <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              Data-driven recommendations for your fitness journey
            </Text>
          </View>
        )}

        {renderSummaryCard()}

        {insights.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="analytics-outline" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No Insights Yet
            </Text>
            <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
              Keep logging your workouts and nutrition to get personalized insights!
            </Text>
          </View>
        ) : (
          <View style={styles.insightsContainer}>
            {insights.map((insight, index) => renderInsightCard(insight, index))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  summaryCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  insightsContainer: {
    paddingBottom: 20,
  },
  insightCard: {
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  insightContent: {
    padding: 20,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  insightTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    lineHeight: 22,
    marginBottom: 4,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  category: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  confidence: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  actionableBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  insightDescription: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
    marginBottom: 12,
  },
  recommendationsPreview: {
    marginBottom: 12,
  },
  recommendationText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  moreRecommendations: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginTop: 4,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default InsightsDashboard;
