import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown
} from 'react-native-reanimated';
import { Recommendation } from '../../services/recommendationService';
import { format, parseISO } from 'date-fns';

interface RecommendationDetailModalProps {
  visible: boolean;
  onClose: () => void;
  recommendation: Recommendation | null;
  type: 'workout' | 'meal';
  onStartWorkout?: () => void;
}

const RecommendationDetailModal: React.FC<RecommendationDetailModalProps> = ({
  visible,
  onClose,
  recommendation,
  type,
  onStartWorkout,
}) => {
  const { colors, isDark } = useTheme();

  // Enhanced debug logs
  console.log(`RecommendationDetailModal - visible: ${visible}, type: ${type}`);
  console.log(`RecommendationDetailModal - recommendation: ${recommendation ? 'exists' : 'null'}`);
  if (recommendation) {
    console.log(`RecommendationDetailModal - title: ${recommendation.title}`);
    console.log(`RecommendationDetailModal - metadata: ${JSON.stringify(recommendation.metadata)}`);
  } else {
    console.log('RecommendationDetailModal - recommendation is null or undefined');
  }

  // Don't return null when recommendation is null, just don't render the content
  // This allows the modal to be visible even if recommendation is not set yet

  // Determine icon and accent color based on type
  const icon = type === 'workout' ? 'barbell-outline' : 'restaurant-outline';
  const accentColor = type === 'workout' ? colors.primary : '#FF9500';

  // Only process these if recommendation exists
  const formattedTime = recommendation?.scheduledTime
    ? format(parseISO(recommendation.scheduledTime), 'h:mm a')
    : '';

  // Extract metadata based on type
  const workoutDetails = recommendation && type === 'workout' ? recommendation.metadata?.workoutDetails : null;
  const mealDetails = recommendation && type === 'meal' ? recommendation.metadata?.mealDetails : null;

  // Log when the component renders
  console.log(`RecommendationDetailModal - rendering with visible=${visible}`);

  // Add a debug log right before rendering
  console.log(`RecommendationDetailModal - About to render modal with visible=${visible}`);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide" // Changed from fade to slide for better visibility
      onRequestClose={() => {
        console.log('Modal onRequestClose called');
        onClose();
      }}
      statusBarTranslucent={true}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          {/* Add a close button at the top right for easier closing */}
          <TouchableOpacity
            style={styles.closeButtonAbsolute}
            onPress={() => {
              console.log('Close button pressed');
              onClose();
            }}
          >
            <Ionicons name="close-circle" size={32} color={colors.primary} />
          </TouchableOpacity>
          <SafeAreaView style={{ flex: 1 }}>
            <View style={styles.modalHeader}>
              <View style={styles.headerContent}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: accentColor + '20' }
                  ]}
                >
                  <Ionicons name={icon} size={22} color={accentColor} />
                </View>
                <Text style={[styles.modalTitle, { color: colors.text }]}>
                  {recommendation?.title || `${type === 'workout' ? 'Workout' : 'Meal'} Details`}
                </Text>
              </View>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.scrollContent}
              contentContainerStyle={styles.scrollContentContainer}
              showsVerticalScrollIndicator={false}
            >
              {!recommendation ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={accentColor} />
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    Loading {type === 'workout' ? 'workout' : 'meal'} details...
                  </Text>
                </View>
              ) : (
                <>
                  {/* Time */}
                  {formattedTime && (
                    <View style={styles.timeContainer}>
                      <Ionicons name="time-outline" size={16} color={colors.textSecondary} />
                      <Text style={[styles.timeText, { color: colors.textSecondary }]}>
                        {formattedTime}
                      </Text>
                    </View>
                  )}

                  {/* Description */}
                  <Text style={[styles.description, { color: colors.text }]}>
                    {recommendation.description}
                  </Text>
                </>
              )}

              {/* Workout Details */}
              {recommendation && type === 'workout' && workoutDetails && (
                <View style={styles.detailsSection}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    Workout Details
                  </Text>

                  {workoutDetails.duration && (
                    <View style={styles.detailRow}>
                      <Ionicons name="timer-outline" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.text }]}>
                        Duration: {workoutDetails.duration} minutes
                      </Text>
                    </View>
                  )}

                  {workoutDetails.notes && (
                    <View style={[styles.notesContainer, { backgroundColor: colors.surfaceLight }]}>
                      <Text style={[styles.notesText, { color: colors.text }]}>
                        {workoutDetails.notes}
                      </Text>
                    </View>
                  )}

                  {workoutDetails.exercises && workoutDetails.exercises.length > 0 && (
                    <View style={styles.exercisesContainer}>
                      <Text style={[styles.exercisesTitle, { color: colors.text }]}>
                        Exercises
                      </Text>

                      {workoutDetails.exercises.map((exercise, index) => (
                        <View
                          key={index}
                          style={[
                            styles.exerciseItem,
                            {
                              backgroundColor: colors.surfaceLight,
                              borderLeftColor: accentColor
                            }
                          ]}
                        >
                          <Text style={[styles.exerciseName, { color: colors.text }]}>
                            {exercise.name}
                          </Text>
                          <Text style={[styles.exerciseDetails, { color: colors.textSecondary }]}>
                            {exercise.sets} sets × {exercise.reps} reps • {exercise.weight}
                          </Text>
                          {exercise.notes && (
                            <Text style={[styles.exerciseNotes, { color: colors.textSecondary }]}>
                              Note: {exercise.notes}
                            </Text>
                          )}
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}

              {/* Meal Details */}
              {recommendation && type === 'meal' && mealDetails && (
                <View style={styles.detailsSection}>
                  {mealDetails.nutrition && (
                    <View style={styles.nutritionSection}>
                      <Text style={[styles.sectionTitle, { color: colors.text }]}>
                        Nutrition
                      </Text>
                      <View style={styles.nutritionRow}>
                        <View style={styles.nutritionItem}>
                          <Text style={[styles.nutritionValue, { color: colors.text }]}>
                            {mealDetails.nutrition.calories}
                          </Text>
                          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                            Calories
                          </Text>
                        </View>
                        <View style={styles.nutritionItem}>
                          <Text style={[styles.nutritionValue, { color: colors.text }]}>
                            {mealDetails.nutrition.protein}g
                          </Text>
                          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                            Protein
                          </Text>
                        </View>
                        <View style={styles.nutritionItem}>
                          <Text style={[styles.nutritionValue, { color: colors.text }]}>
                            {mealDetails.nutrition.carbs}g
                          </Text>
                          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                            Carbs
                          </Text>
                        </View>
                        <View style={styles.nutritionItem}>
                          <Text style={[styles.nutritionValue, { color: colors.text }]}>
                            {mealDetails.nutrition.fat}g
                          </Text>
                          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                            Fat
                          </Text>
                        </View>
                      </View>
                    </View>
                  )}

                  {mealDetails.ingredients && mealDetails.ingredients.length > 0 && (
                    <View style={styles.ingredientsSection}>
                      <Text style={[styles.sectionTitle, { color: colors.text }]}>
                        Ingredients
                      </Text>
                      <View style={[styles.listContainer, { backgroundColor: colors.surfaceLight }]}>
                        {mealDetails.ingredients.map((ingredient, index) => (
                          <View key={index} style={styles.listItem}>
                            <View style={[styles.bullet, { backgroundColor: accentColor }]} />
                            <Text style={[styles.listItemText, { color: colors.text }]}>
                              {ingredient}
                            </Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}

                  {mealDetails.instructions && mealDetails.instructions.length > 0 && (
                    <View style={styles.instructionsSection}>
                      <Text style={[styles.sectionTitle, { color: colors.text }]}>
                        Instructions
                      </Text>
                      <View style={[styles.listContainer, { backgroundColor: colors.surfaceLight }]}>
                        {mealDetails.instructions.map((instruction, index) => (
                          <View key={index} style={styles.listItem}>
                            <Text style={[styles.stepNumber, { color: accentColor }]}>
                              {index + 1}.
                            </Text>
                            <Text style={[styles.listItemText, { color: colors.text }]}>
                              {instruction}
                            </Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}

                  {mealDetails.tags && mealDetails.tags.length > 0 && (
                    <View style={styles.tagsContainer}>
                      {mealDetails.tags.map((tag, index) => (
                        <View
                          key={index}
                          style={[
                            styles.tag,
                            { backgroundColor: accentColor + '20' }
                          ]}
                        >
                          <Text style={[styles.tagText, { color: accentColor }]}>
                            {tag}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}
            </ScrollView>

            {/* Action Button */}
            {type === 'workout' && recommendation && (
              <View style={styles.actionContainer}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: accentColor }]}
                  onPress={onStartWorkout}
                >
                  <Ionicons name="play" size={18} color="#FFFFFF" style={styles.actionIcon} />
                  <Text style={styles.actionText}>Start Workout</Text>
                </TouchableOpacity>
              </View>
            )}
          </SafeAreaView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    backgroundColor: 'white', // Ensure there's a default background color
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  modalTitle: {
    ...typography.h3,
    fontWeight: '600',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  scrollContent: {
    maxHeight: '100%',
  },
  scrollContentContainer: {
    padding: 16,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeText: {
    ...typography.caption,
    marginLeft: 6,
  },
  description: {
    ...typography.body,
    marginBottom: 20,
    lineHeight: 22,
  },
  detailsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...typography.h4,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    ...typography.body,
    marginLeft: 8,
  },
  notesContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  notesText: {
    ...typography.body,
    fontStyle: 'italic',
  },
  exercisesContainer: {
    marginTop: 8,
  },
  exercisesTitle: {
    ...typography.subtitle,
    fontWeight: '600',
    marginBottom: 8,
  },
  exerciseItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
  },
  exerciseName: {
    ...typography.subtitle,
    fontWeight: '600',
    marginBottom: 4,
  },
  exerciseDetails: {
    ...typography.caption,
    marginBottom: 4,
  },
  exerciseNotes: {
    ...typography.caption,
    fontStyle: 'italic',
  },
  nutritionSection: {
    marginBottom: 20,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    ...typography.h3,
    fontWeight: '600',
  },
  nutritionLabel: {
    ...typography.caption,
  },
  ingredientsSection: {
    marginBottom: 20,
  },
  instructionsSection: {
    marginBottom: 20,
  },
  listContainer: {
    padding: 12,
    borderRadius: 8,
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 7,
    marginRight: 8,
  },
  stepNumber: {
    ...typography.body,
    fontWeight: '600',
    marginRight: 8,
    width: 20,
  },
  listItemText: {
    ...typography.body,
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    ...typography.caption,
    fontWeight: '600',
  },
  actionContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    ...typography.button,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    ...typography.body,
    marginTop: 16,
    textAlign: 'center',
  },
  closeButtonAbsolute: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 10,
    padding: 5,
  },
});

export default RecommendationDetailModal;
