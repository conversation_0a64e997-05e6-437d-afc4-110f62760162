import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import Animated, { FadeIn } from 'react-native-reanimated';

interface RecommendationPanelProps {
  type: 'workout' | 'meal';
  title: string;
  description: string;
  time: string;
  isLoading: boolean;
  onPress: () => void;
}

const RecommendationPanel: React.FC<RecommendationPanelProps> = ({
  type,
  title,
  description,
  time,
  isLoading,
  onPress,
}) => {
  const { colors, isDark } = useTheme();

  // Determine icon based on type
  const icon = type === 'workout'
    ? 'barbell-outline'
    : 'restaurant-outline';

  // Determine accent color based on type
  const accentColor = type === 'workout'
    ? colors.primary
    : '#FF9500'; // Orange for meals

  return (
    <Animated.View
      entering={FadeIn.duration(600).delay(300)}
      style={styles.container}
    >
      <TouchableOpacity
        style={[
          styles.panel,
          {
            backgroundColor: isDark
              ? 'rgba(30, 30, 30, 0.8)'
              : 'rgba(255, 255, 255, 0.8)',
            borderColor: accentColor,
          }
        ]}
        onPress={() => {
          console.log('RecommendationPanel - TouchableOpacity pressed');
          onPress();
        }}
        activeOpacity={0.8}
        disabled={isLoading || !title} // Disable if loading or no title (no recommendation)
      >
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={accentColor} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading {type === 'workout' ? 'workout' : 'meal'}...
            </Text>
          </View>
        ) : (
          // Only render content if not loading AND title exists (meaning recommendation data is available)
          title ? (
            <>
              <View style={styles.header}>
                <View style={[styles.iconContainer, { backgroundColor: accentColor + '20' }]}>
                  <Ionicons name={icon} size={12} color={accentColor} />
                </View>
                <Text style={[styles.title, { color: colors.text }]}>
                  {type === 'workout' ? 'Next Workout' : 'Next Meal'}
                </Text>
              </View>

              <View style={styles.content}>
                <Text style={[styles.activityTitle, { color: colors.text }]} numberOfLines={1}>
                  {title}
                </Text>
                <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
                  {description}
                </Text>
              </View>

              <View style={styles.footer}>
                <View style={styles.timeContainer}>
                  <Ionicons name="time-outline" size={10} color={colors.textSecondary} />
                  <Text style={[styles.time, { color: colors.textSecondary }]}>
                    {time}
                  </Text>
                </View>
                <View style={[styles.actionButton, { backgroundColor: accentColor + '20' }]}>
                  <Text style={[styles.actionText, { color: accentColor }]}>
                    {type === 'workout' ? 'Start' : 'View'}
                  </Text>
                  <Ionicons
                    name={type === 'workout' ? 'play' : 'eye-outline'}
                    size={10}
                    color={accentColor}
                    style={styles.actionIcon}
                  />
                </View>
              </View>
            </>
          ) : (
            // Render nothing if not loading and no title (no recommendation available)
            null
          )
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '48%',
    marginBottom: 8,
  },
  panel: {
    borderRadius: 10,
    padding: 8,
    borderLeftWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  iconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  title: {
    fontSize: 11,
    fontWeight: '600',
    fontFamily: typography.fontFamily.semibold, // Use semibold as per fontWeight
  },
  content: {
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: typography.sizes.sm, // Use sm size as a reasonable default
    fontWeight: '600',
    fontFamily: typography.fontFamily.semibold, // Use semibold as per fontWeight
    marginBottom: 2,
  },
  description: {
    fontSize: 11,
    lineHeight: 14,
    fontFamily: typography.fontFamily.regular, // Use regular for description
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  time: {
    fontSize: 10,
    marginLeft: 2,
    fontFamily: typography.fontFamily.regular, // Use regular for time
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 8,
  },
  actionText: {
    fontSize: 10,
    fontWeight: '600',
    fontFamily: typography.fontFamily.semibold, // Use semibold as per fontWeight
  },
  actionIcon: {
    marginLeft: 2,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  loadingText: {
    fontSize: 10,
    marginTop: 6,
    fontFamily: typography.fontFamily.regular, // Use regular for loading text
  },
});

export default RecommendationPanel;
