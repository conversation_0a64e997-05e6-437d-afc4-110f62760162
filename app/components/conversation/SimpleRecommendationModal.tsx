import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { Recommendation } from '../../services/recommendationService';
import { format, parseISO } from 'date-fns';

interface SimpleRecommendationModalProps {
  visible: boolean;
  onClose: () => void;
  recommendation: Recommendation | null;
  type: 'workout' | 'meal';
  onStartWorkout?: () => void;
}

const SimpleRecommendationModal: React.FC<SimpleRecommendationModalProps> = ({
  visible,
  onClose,
  recommendation,
  type,
  onStartWorkout,
}) => {
  const { colors } = useTheme();

  // Debug logs
  console.log(`SimpleRecommendationModal - visible: ${visible}, type: ${type}`);
  if (recommendation) {
    console.log(`SimpleRecommendationModal - title: ${recommendation.title}`);
  } else {
    console.log('SimpleRecommendationModal - recommendation is null');
  }

  // Determine icon and accent color based on type
  const icon = type === 'workout' ? 'barbell-outline' : 'restaurant-outline';
  const accentColor = type === 'workout' ? colors.primary : '#FF9500';

  // Only process these if recommendation exists
  const formattedTime = recommendation?.scheduledTime
    ? format(parseISO(recommendation.scheduledTime), 'h:mm a')
    : '';

  // Extract metadata based on type
  const workoutDetails = recommendation && type === 'workout' ? recommendation.metadata?.workoutDetails : null;
  const mealDetails = recommendation && type === 'meal' ? recommendation.metadata?.mealDetails : null;

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => {
              console.log('Close button pressed');
              onClose();
            }}
          >
            <Ionicons name="close-circle" size={32} color={colors.primary} />
          </TouchableOpacity>

          <SafeAreaView style={{ flex: 1 }}>
            <View style={styles.modalHeader}>
              <View style={styles.headerContent}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: accentColor + '20' }
                  ]}
                >
                  <Ionicons name={icon} size={22} color={accentColor} />
                </View>
                <Text style={[styles.modalTitle, { color: colors.text }]}>
                  {recommendation?.title || `${type === 'workout' ? 'Workout' : 'Meal'} Details`}
                </Text>
              </View>
            </View>

            <ScrollView
              style={styles.scrollContent}
              contentContainerStyle={styles.scrollContentContainer}
              showsVerticalScrollIndicator={false}
            >
              {!recommendation ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={accentColor} />
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    Loading {type === 'workout' ? 'workout' : 'meal'} details...
                  </Text>
                </View>
              ) : (
                <>
                  {/* Time */}
                  {formattedTime && (
                    <View style={styles.timeContainer}>
                      <Ionicons name="time-outline" size={16} color={colors.textSecondary} />
                      <Text style={[styles.timeText, { color: colors.textSecondary }]}>
                        {formattedTime}
                      </Text>
                    </View>
                  )}

                  {/* Description */}
                  <Text style={[styles.description, { color: colors.text }]}>
                    {recommendation.description}
                  </Text>

                  {/* Workout Details */}
                  {type === 'workout' && workoutDetails && (
                    <View style={styles.detailsSection}>
                      <Text style={[styles.sectionTitle, { color: colors.text }]}>
                        Workout Details
                      </Text>

                      {workoutDetails.duration && (
                        <View style={styles.detailRow}>
                          <Ionicons name="timer-outline" size={16} color={colors.textSecondary} />
                          <Text style={[styles.detailText, { color: colors.text }]}>
                            Duration: {workoutDetails.duration} minutes
                          </Text>
                        </View>
                      )}

                      {workoutDetails.notes && (
                        <View style={[styles.notesContainer, { backgroundColor: colors.surfaceLight }]}>
                          <Text style={[styles.notesText, { color: colors.text }]}>
                            {workoutDetails.notes}
                          </Text>
                        </View>
                      )}

                      {workoutDetails.exercises && workoutDetails.exercises.length > 0 && (
                        <View style={styles.exercisesContainer}>
                          <Text style={[styles.exercisesTitle, { color: colors.text }]}>
                            Exercises
                          </Text>

                          {workoutDetails.exercises.map((exercise, index) => (
                            <View
                              key={index}
                              style={[
                                styles.exerciseItem,
                                {
                                  backgroundColor: colors.surfaceLight,
                                  borderLeftColor: accentColor
                                }
                              ]}
                            >
                              <Text style={[styles.exerciseName, { color: colors.text }]}>
                                {exercise.name}
                              </Text>
                              <Text style={[styles.exerciseDetails, { color: colors.textSecondary }]}>
                                {exercise.sets} sets × {exercise.reps} reps • {exercise.weight}
                              </Text>
                              {exercise.notes && (
                                <Text style={[styles.exerciseNotes, { color: colors.textSecondary }]}>
                                  Note: {exercise.notes}
                                </Text>
                              )}
                            </View>
                          ))}
                        </View>
                      )}
                    </View>
                  )}

                  {/* Meal Details */}
                  {type === 'meal' && mealDetails && (
                    <View style={styles.detailsSection}>
                      {mealDetails.nutrition && (
                        <View style={styles.nutritionSection}>
                          <Text style={[styles.sectionTitle, { color: colors.text }]}>
                            Nutrition
                          </Text>
                          <View style={styles.nutritionRow}>
                            <View style={styles.nutritionItem}>
                              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                                {mealDetails.nutrition.calories}
                              </Text>
                              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                                Calories
                              </Text>
                            </View>
                            <View style={styles.nutritionItem}>
                              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                                {mealDetails.nutrition.protein}g
                              </Text>
                              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                                Protein
                              </Text>
                            </View>
                            <View style={styles.nutritionItem}>
                              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                                {mealDetails.nutrition.carbs}g
                              </Text>
                              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                                Carbs
                              </Text>
                            </View>
                            <View style={styles.nutritionItem}>
                              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                                {mealDetails.nutrition.fat}g
                              </Text>
                              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                                Fat
                              </Text>
                            </View>
                          </View>
                        </View>
                      )}
                    </View>
                  )}
                </>
              )}
            </ScrollView>

            {/* Action Button */}
            {type === 'workout' && recommendation && onStartWorkout && (
              <View style={styles.actionContainer}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: accentColor }]}
                  onPress={onStartWorkout}
                >
                  <Ionicons name="play" size={18} color="#FFFFFF" style={styles.actionIcon} />
                  <Text style={styles.actionText}>Start Workout</Text>
                </TouchableOpacity>
              </View>
            )}
          </SafeAreaView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    backgroundColor: 'white',
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 40 : 10,
    right: 10,
    zIndex: 10,
    padding: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    marginTop: Platform.OS === 'ios' ? 30 : 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  modalTitle: {
    ...typography.h3,
    fontWeight: '600',
    flex: 1,
  },
  scrollContent: {
    maxHeight: '100%',
  },
  scrollContentContainer: {
    padding: 16,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeText: {
    ...typography.caption,
    marginLeft: 6,
  },
  description: {
    ...typography.body,
    marginBottom: 20,
    lineHeight: 22,
  },
  detailsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...typography.h4,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    ...typography.body,
    marginLeft: 8,
  },
  notesContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  notesText: {
    ...typography.body,
    fontStyle: 'italic',
  },
  exercisesContainer: {
    marginTop: 8,
  },
  exercisesTitle: {
    ...typography.subtitle,
    fontWeight: '600',
    marginBottom: 8,
  },
  exerciseItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
  },
  exerciseName: {
    ...typography.subtitle,
    fontWeight: '600',
    marginBottom: 4,
  },
  exerciseDetails: {
    ...typography.caption,
    marginBottom: 4,
  },
  exerciseNotes: {
    ...typography.caption,
    fontStyle: 'italic',
  },
  nutritionSection: {
    marginBottom: 20,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    ...typography.h3,
    fontWeight: '600',
  },
  nutritionLabel: {
    ...typography.caption,
  },
  actionContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    ...typography.button,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    ...typography.body,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default SimpleRecommendationModal;
