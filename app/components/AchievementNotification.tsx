import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import { useTheme } from '../theme/ThemeProvider';

interface AchievementNotificationProps {
  title: string;
  description: string;
  icon: string;
  onPress?: () => void;
  onDismiss?: () => void;
  autoHide?: boolean;
  duration?: number;
}

const { width } = Dimensions.get('window');

const AchievementNotification: React.FC<AchievementNotificationProps> = ({
  title,
  description,
  icon,
  onPress,
  onDismiss,
  autoHide = true,
  duration = 5000,
}) => {
  const { colors, isDark } = useTheme();
  
  // Animation values
  const translateY = useRef(new Animated.Value(-150)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.9)).current;
  const iconRotate = useRef(new Animated.Value(0)).current;
  
  // Auto-hide timer
  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Animation configurations
  const showAnimation = Animated.parallel([
    Animated.timing(translateY, {
      toValue: 0,
      duration: 500,
      easing: Easing.out(Easing.back(1.5)),
      useNativeDriver: true,
    }),
    Animated.timing(opacity, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    }),
    Animated.timing(scale, {
      toValue: 1,
      duration: 500,
      easing: Easing.out(Easing.back(1.5)),
      useNativeDriver: true,
    }),
    Animated.timing(iconRotate, {
      toValue: 1,
      duration: 700,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }),
  ]);
  
  const hideAnimation = Animated.parallel([
    Animated.timing(translateY, {
      toValue: -150,
      duration: 300,
      easing: Easing.in(Easing.ease),
      useNativeDriver: true,
    }),
    Animated.timing(opacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }),
  ]);
  
  // Show notification on mount
  useEffect(() => {
    showAnimation.start();
    
    // Set up auto-hide if enabled
    if (autoHide) {
      hideTimerRef.current = setTimeout(() => {
        hideNotification();
      }, duration);
    }
    
    return () => {
      if (hideTimerRef.current) {
        clearTimeout(hideTimerRef.current);
      }
    };
  }, []);
  
  // Hide notification and call onDismiss
  const hideNotification = () => {
    hideAnimation.start(() => {
      if (onDismiss) {
        onDismiss();
      }
    });
  };
  
  // Handle notification press
  const handlePress = () => {
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
    }
    
    hideAnimation.start(() => {
      if (onPress) {
        onPress();
      }
    });
  };
  
  // Convert rotation value to degrees
  const rotate = iconRotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: colors.card,
          borderColor: colors.primary,
          shadowColor: isDark ? colors.primary : '#000',
          transform: [
            { translateY },
            { scale },
          ],
          opacity,
        },
      ]}
    >
      <TouchableOpacity
        style={styles.content}
        activeOpacity={0.8}
        onPress={handlePress}
      >
        <View style={styles.header}>
          <Animated.View
            style={[
              styles.iconContainer,
              {
                backgroundColor: colors.primary,
                transform: [{ rotate }],
              },
            ]}
          >
            <Ionicons name={icon as any} size={24} color="#FFFFFF" />
          </Animated.View>
          
          <View style={styles.textContainer}>
            <Text style={[styles.title, { color: colors.text }]}>
              {title}
            </Text>
            <Text
              style={[styles.description, { color: colors.textSecondary }]}
              numberOfLines={2}
            >
              {description}
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.closeButton}
            onPress={hideNotification}
            hitSlop={{ top: 15, right: 15, bottom: 15, left: 15 }}
          >
            <Ionicons name="close" size={20} color={colors.textTertiary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.footer}>
          <Text style={[styles.tapText, { color: colors.textTertiary }]}>
            Tap to view achievement
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    borderRadius: 16,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1000,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  closeButton: {
    padding: 4,
  },
  footer: {
    marginTop: 12,
    alignItems: 'center',
  },
  tapText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.regular,
    opacity: 0.7,
  },
});

export default AchievementNotification;
