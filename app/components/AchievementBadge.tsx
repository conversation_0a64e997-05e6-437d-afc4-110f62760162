import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Easing,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';

interface AchievementBadgeProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  unlocked: boolean;
  progress?: number; // 0 to 1
  color?: string;
  backgroundColor?: string;
  textColor?: string;
  onPress?: () => void;
  delay?: number;
}

const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  title,
  description,
  icon,
  unlocked,
  progress = 0,
  color = '#007AFF',
  backgroundColor = '#FFFFFF',
  textColor = '#000000',
  onPress,
  delay = 0,
}) => {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  // Track if this is a newly unlocked achievement
  const [isNewlyUnlocked, setIsNewlyUnlocked] = useState(false);

  // Animate in on mount
  useEffect(() => {
    // Update progress animation value
    progressAnim.setValue(unlocked ? 1 : progress);

    // Check if this is a newly unlocked achievement (for special effects)
    // In a real app, you would track this with a timestamp or flag in the achievement data
    const checkIfNewlyUnlocked = () => {
      if (unlocked) {
        // For demo purposes, we'll randomly mark some unlocked achievements as "new"
        // In a real app, you would compare the unlocked timestamp with the last viewed timestamp
        const isNew = Math.random() > 0.5;
        setIsNewlyUnlocked(isNew);
        return isNew;
      }
      return false;
    };

    const isNew = checkIfNewlyUnlocked();

    // Start entrance animation with delay
    const animationTimeout = setTimeout(() => {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
          easing: Easing.out(Easing.back(1.5)),
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
      ]).start();

      // Start shimmer animation for unlocked achievements
      if (unlocked) {
        Animated.loop(
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
            easing: Easing.inOut(Easing.ease),
          })
        ).start();
      }

      // Start pulse animation for newly unlocked achievements
      if (isNew) {
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.05,
              duration: 800,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease),
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
              easing: Easing.inOut(Easing.ease),
            }),
          ])
        ).start();
      }
    }, delay);

    return () => clearTimeout(animationTimeout);
  }, [unlocked, progress, delay]);

  // Handle press animation
  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Calculate rotation for the badge
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Calculate shimmer position for the gradient effect
  const shimmerTranslate = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
      disabled={!onPress}
    >
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor,
            transform: [
              { scale: scaleAnim },
              { scale: unlocked && isNewlyUnlocked ? pulseAnim : 1 }
            ],
            opacity: opacityAnim,
            borderColor: unlocked ? color : 'transparent',
            borderWidth: unlocked ? 1 : 0,
          },
        ]}
      >
        {unlocked && (
          <Animated.View
            style={[
              styles.shimmerOverlay,
              {
                transform: [{ translateX: shimmerTranslate }],
                backgroundColor: 'rgba(255,255,255,0.05)',
              },
            ]}
          />
        )}

        {/* Add a subtle glow effect for unlocked achievements */}
        {unlocked && (
          <View style={[
            styles.glowEffect,
            {
              backgroundColor: color,
              shadowColor: color,
            }
          ]} />
        )}

        {isNewlyUnlocked && (
          <View style={[styles.newBadge, { backgroundColor: color }]}>
            <Text style={styles.newBadgeText}>NEW</Text>
          </View>
        )}

        <View style={styles.badgeRow}>
          <Animated.View
            style={[
              styles.iconContainer,
              {
                backgroundColor: unlocked ? color : color + '40',
                transform: [{ rotate }],
                shadowColor: unlocked ? color : 'transparent',
                shadowOpacity: unlocked ? 0.5 : 0,
              },
            ]}
          >
            <Ionicons
              name={icon}
              size={24}
              color={unlocked ? '#FFFFFF' : color}
            />
          </Animated.View>

          <View style={styles.textContainer}>
            <Text
              style={[
                styles.title,
                {
                  color: textColor,
                  opacity: unlocked ? 1 : 0.7,
                },
              ]}
            >
              {title}
            </Text>
            <Text
              style={[
                styles.description,
                {
                  color: textColor + '99',
                  opacity: unlocked ? 0.8 : 0.5,
                },
              ]}
              numberOfLines={2}
            >
              {description}
            </Text>
          </View>

          {unlocked ? (
            <View style={[styles.unlockedBadge, { backgroundColor: color }]}>
              <Ionicons name="trophy" size={12} color="#FFFFFF" />
            </View>
          ) : progress > 0 ? (
            <Text style={[styles.progressPercentage, { color }]}>
              {Math.round(progress * 100)}%
            </Text>
          ) : (
            <View style={[styles.lockedBadge, { backgroundColor: color + '40' }]}>
              <Ionicons name="lock-closed" size={12} color={color} />
            </View>
          )}
        </View>

        {!unlocked && progress > 0 && (
          <View style={styles.progressContainer}>
            <View style={[styles.progressBackground, { backgroundColor: color + '20' }]}>
              <Animated.View
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: color,
                    width: `${progress * 100}%`,
                  },
                ]}
              />
            </View>
            <Text style={[styles.progressText, { color: textColor + '99' }]}>
              {Math.round(progress * 100)}%
            </Text>
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden', // For shimmer effect
    position: 'relative', // For positioning the NEW badge
  },
  badgeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 3,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  unlockedBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  progressContainer: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBackground: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.medium,
    width: 36,
    textAlign: 'right',
  },
  progressPercentage: {
    fontSize: 12,
    fontFamily: typography.fontFamily.bold,
    marginLeft: 8,
  },
  // New styles for enhanced visuals
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 100,
    height: '100%',
    zIndex: 1,
    transform: [{ skewX: '-20deg' }],
  },
  newBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
    zIndex: 2,
  },
  newBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontFamily: typography.fontFamily.bold,
  },
  glowEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    opacity: 0.05,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 5,
  },
  lockedBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default AchievementBadge;
