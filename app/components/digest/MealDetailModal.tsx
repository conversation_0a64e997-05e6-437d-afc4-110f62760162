import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { DigestActivity } from '../../services/digestService';
import { sendMessageToChat } from '../../services/chatService';

interface MealDetailModalProps {
  visible: boolean;
  onClose: () => void;
  activity: DigestActivity | null;
}

const MealDetailModal: React.FC<MealDetailModalProps> = ({
  visible,
  onClose,
  activity
}) => {
  const { colors, isDark } = useTheme();
  const [mealDetails, setMealDetails] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (visible && activity) {
      // Log the activity to debug
      console.log('MealDetailModal - Activity:', JSON.stringify(activity, null, 2));

      // Check if activity type is meal (case insensitive)
      const isMeal = activity.type?.toLowerCase() === 'meal';

      if (isMeal) {
        console.log('MealDetailModal - Activity is a meal, fetching details');
        fetchMealDetails();
      } else {
        console.warn(`MealDetailModal - Activity type is not 'meal', it's '${activity.type}'`);
        setError(`This activity is not a meal (type: ${activity.type})`);
      }
    }
  }, [visible, activity]);

  const fetchMealDetails = async () => {
    if (!activity) return;

    setIsLoading(true);
    setError(null);

    try {
      // First check if the activity already has detailed information
      if (activity.metadata?.mealDetails) {
        console.log('Using existing meal details from activity metadata:', JSON.stringify(activity.metadata.mealDetails, null, 2));

        // Normalize the data to handle both field naming conventions
        const normalizedDetails = {
          ...activity.metadata.mealDetails,
          // Ensure we have both instructions and steps fields
          instructions: activity.metadata.mealDetails.instructions || activity.metadata.mealDetails.steps || [],
          steps: activity.metadata.mealDetails.steps || activity.metadata.mealDetails.instructions || []
        };

        setMealDetails(normalizedDetails);
        setIsLoading(false);
        return;
      }

      // Log if metadata exists but no mealDetails
      if (activity.metadata) {
        console.log('Activity has metadata but no mealDetails:', JSON.stringify(activity.metadata, null, 2));
      } else {
        console.log('Activity has no metadata at all');
      }

      // Request detailed meal information from the LLM using the same format as chat page
      const prompt = `
        Please provide detailed information about this meal: "${activity.title}".
        Consider the meal description: "${activity.description}"

        Create a complete recipe with the following:
        1. A detailed description of the meal
        2. Nutritional information (calories, protein, carbs, fat)
        3. Complete list of ingredients with measurements
        4. Step-by-step preparation instructions
        5. Relevant tags (e.g., "high-protein", "vegetarian", "quick", etc.)
        6. Prep time and cook time
        7. Number of servings

        IMPORTANT: Return ONLY valid JSON, no other text or formatting.

        Format as JSON with fields:
        {
          "title": "Meal title",
          "description": "Detailed description",
          "nutrition": {
            "calories": 000,
            "protein": 00,
            "carbs": 00,
            "fat": 00
          },
          "ingredients": ["ingredient 1 with measurement", "ingredient 2 with measurement", ...],
          "instructions": ["step 1", "step 2", ...],
          "tags": ["tag1", "tag2", ...],
          "prepTime": "15 minutes",
          "cookTime": "20 minutes",
          "servings": 2
        }
      `;

      const response = await sendMessageToChat(prompt, true);

      // Enhanced JSON parsing similar to nutritionService
      let mealData = null;

      try {
        // Try multiple parsing approaches
        const parseAttempts = [
          // Attempt 1: Direct parsing
          () => JSON.parse(response),

          // Attempt 2: Extract JSON from code blocks
          () => {
            const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) ||
                             response.match(/```\n([\s\S]*?)\n```/) ||
                             response.match(/```([\s\S]*?)```/) ||
                             response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonString = jsonMatch[1] || jsonMatch[0];
              return JSON.parse(jsonString);
            }
            throw new Error('No JSON found');
          },

          // Attempt 3: Clean and parse
          () => {
            let cleaned = response
              .replace(/```json/g, '')
              .replace(/```/g, '')
              .replace(/^\s*[\w\s]*?(?=\{)/, '') // Remove text before first {
              .replace(/\}[\w\s]*$/, '}') // Remove text after last }
              .trim();
            return JSON.parse(cleaned);
          }
        ];

        for (const attempt of parseAttempts) {
          try {
            mealData = attempt();
            if (mealData && typeof mealData === 'object') {
              console.log('Successfully parsed meal data');
              break;
            }
          } catch (e) {
            continue;
          }
        }

        if (mealData) {
          setMealDetails(mealData);

          // Save the meal details to the activity metadata for future use
          try {
            const { saveActivityOverride } = require('../../services/digestService');
            await saveActivityOverride(activity, {
              metadata: {
                ...activity.metadata,
                mealDetails: mealData
              }
            });
            console.log('Saved meal details to activity metadata');
          } catch (saveError) {
            console.error('Error saving meal details to activity:', saveError);
            // Continue anyway since we have the data for this session
          }
        } else {
          // If no JSON found, use the text response as description
          console.log('Could not parse JSON, using fallback data');
          const fallbackData = {
            title: activity.title,
            description: response.length > 500 ? response.substring(0, 500) + '...' : response,
            nutrition: {
              calories: 400,
              protein: 25,
              carbs: 45,
              fat: 15
            },
            ingredients: [
              'Fresh ingredients as needed',
              'Seasonings to taste'
            ],
            instructions: [
              'Follow the description above',
              'Adjust ingredients to your preference',
              'Cook until done and enjoy'
            ],
            tags: ['healthy', 'nutritious'],
            prepTime: '15 minutes',
            cookTime: '20 minutes',
            servings: 2
          };

          setMealDetails(fallbackData);

          // Save the fallback data to the activity metadata
          try {
            const { saveActivityOverride } = require('../../services/digestService');
            await saveActivityOverride(activity, {
              metadata: {
                ...activity.metadata,
                mealDetails: fallbackData
              }
            });
          } catch (saveError) {
            console.error('Error saving fallback meal details:', saveError);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching meal details:', error);
      setError('Failed to load meal details');
    } finally {
      setIsLoading(false);
    }
  };

  if (!activity) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
        style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
      >
        <Animated.View
          entering={SlideInDown.duration(400).springify()}
          exiting={SlideOutDown.duration(300)}
          style={[styles.modalContent, { backgroundColor: colors.card }]}
        >
          <View style={styles.modalHeader}>
            <View style={styles.headerContent}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: colors.primary + '20' }
                ]}
              >
                <Ionicons
                  name="restaurant-outline"
                  size={24}
                  color={colors.primary}
                />
              </View>
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                {activity.title}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Loading meal details...
                </Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle-outline" size={40} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {error}
                </Text>
              </View>
            ) : mealDetails ? (
              <View style={styles.detailsContainer}>
                <Text style={[styles.description, { color: colors.textSecondary }]}>
                  {mealDetails.description}
                </Text>

                {/* Nutrition Information */}
                <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    Nutrition Information
                  </Text>
                  <View style={styles.nutritionGrid}>
                    <View style={styles.nutritionItem}>
                      <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                        {typeof mealDetails.nutrition.calories === 'number'
                          ? mealDetails.nutrition.calories
                          : 'N/A'}
                      </Text>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Calories
                      </Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                        {typeof mealDetails.nutrition.protein === 'number'
                          ? `${mealDetails.nutrition.protein}g`
                          : 'N/A'}
                      </Text>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Protein
                      </Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                        {typeof mealDetails.nutrition.carbs === 'number'
                          ? `${mealDetails.nutrition.carbs}g`
                          : 'N/A'}
                      </Text>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Carbs
                      </Text>
                    </View>
                    <View style={styles.nutritionItem}>
                      <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                        {typeof mealDetails.nutrition.fat === 'number'
                          ? `${mealDetails.nutrition.fat}g`
                          : 'N/A'}
                      </Text>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Fat
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Ingredients */}
                {mealDetails.ingredients && mealDetails.ingredients.length > 0 && (
                  <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>
                      Ingredients
                    </Text>
                    <View style={styles.listContainer}>
                      {mealDetails.ingredients.map((ingredient: string, index: number) => (
                        <View key={index} style={styles.listItem}>
                          <View style={[styles.bullet, { backgroundColor: colors.primary }]} />
                          <Text style={[styles.listText, { color: colors.text }]}>
                            {ingredient}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                )}

                {/* Instructions - handle both 'instructions' and 'steps' field names */}
                {(mealDetails.instructions || mealDetails.steps) && (
                  <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>
                      Preparation
                    </Text>
                    <View style={styles.listContainer}>
                      {(mealDetails.instructions || mealDetails.steps || []).map((instruction: string, index: number) => (
                        <View key={index} style={styles.listItem}>
                          <Text style={[styles.stepNumber, { color: colors.primary }]}>
                            {index + 1}.
                          </Text>
                          <Text style={[styles.listText, { color: colors.text }]}>
                            {instruction}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                )}

                {/* Tags */}
                {mealDetails.tags && mealDetails.tags.length > 0 && (
                  <View style={styles.tagsContainer}>
                    {mealDetails.tags.map((tag: string, index: number) => (
                      <View
                        key={index}
                        style={[
                          styles.tag,
                          { backgroundColor: colors.primaryLight + '30' }
                        ]}
                      >
                        <Text style={[styles.tagText, { color: colors.primary }]}>
                          {tag}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.errorContainer}>
                <Text style={[styles.errorText, { color: colors.textSecondary }]}>
                  No meal details available
                </Text>
              </View>
            )}
          </ScrollView>

          <View style={[styles.footer, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.footerButton, { backgroundColor: colors.primary }]}
              onPress={onClose}
            >
              <Text style={[styles.footerButtonText, { color: colors.background }]}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    ...typography.h2,
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    ...typography.body,
    marginTop: 12,
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    ...typography.body,
    marginTop: 12,
    textAlign: 'center',
  },
  detailsContainer: {
    paddingBottom: 16,
  },
  description: {
    ...typography.body,
    marginBottom: 20,
  },
  sectionContainer: {
    marginBottom: 20,
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    ...typography.h3,
    marginBottom: 12,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
  },
  nutritionValue: {
    ...typography.h2,
    marginBottom: 4,
  },
  nutritionLabel: {
    ...typography.caption,
  },
  listContainer: {
    marginTop: 8,
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 6,
    marginRight: 8,
  },
  stepNumber: {
    ...typography.subtitle,
    marginRight: 8,
    width: 20,
  },
  listText: {
    ...typography.body,
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    ...typography.caption,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  footerButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  footerButtonText: {
    ...typography.button,
  },
});

export default MealDetailModal;
