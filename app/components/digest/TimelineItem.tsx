import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { DigestActivity, DigestActivityType, saveActivityOverride } from '../../services/digestService';
import { format, parseISO } from 'date-fns';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface TimelineItemProps {
  activity: DigestActivity;
  isFirst?: boolean;
  isLast?: boolean;
  onPress?: (activity: DigestActivity) => void;
  onEdit?: (activity: DigestActivity) => void;
  onComplete?: (activity: DigestActivity, completed: boolean) => void;
  onDelete?: (activity: DigestActivity) => void;
  onRefresh?: () => void;
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  activity,
  isFirst = false,
  isLast = false,
  onPress,
  onEdit,
  onComplete,
  onDelete,
  onRefresh
}) => {
  const { colors, isDark } = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);

  // Format the time from the ISO string
  const formatTime = (isoString: string) => {
    return format(parseISO(isoString), 'h:mm a');
  };

  // Get icon based on activity type
  const getActivityIcon = () => {
    switch (activity.type) {
      case DigestActivityType.WORKOUT:
        return 'barbell-outline';
      case DigestActivityType.MEAL:
        return 'restaurant-outline';
      case DigestActivityType.WATER:
        return 'water-outline';
      case DigestActivityType.SLEEP:
        return 'bed-outline';
      case DigestActivityType.REMINDER:
        return 'alarm-outline';
      default:
        return 'calendar-outline';
    }
  };

  // Get color based on activity type
  const getActivityColor = () => {
    switch (activity.type) {
      case DigestActivityType.WORKOUT:
        return isDark ? '#5e92f3' : '#2979ff';
      case DigestActivityType.MEAL:
        return isDark ? '#66bb6a' : '#43a047';
      case DigestActivityType.WATER:
        return isDark ? '#4fc3f7' : '#29b6f6';
      case DigestActivityType.SLEEP:
        return isDark ? '#9575cd' : '#7e57c2';
      case DigestActivityType.REMINDER:
        return isDark ? '#ffb74d' : '#ff9800';
      default:
        return colors.primary;
    }
  };

  // Handle marking activity as complete
  const handleComplete = async () => {
    if (!onComplete) return;

    setIsCompleting(true);

    try {
      // Call the onComplete callback
      onComplete(activity, !activity.completed);

      // If this isn't already an override, save it as one
      if (!activity.isUserOverride) {
        await saveActivityOverride(activity, {
          completed: !activity.completed
        });

        // Refresh the digest if needed
        if (onRefresh) {
          onRefresh();
        }
      }
    } catch (error) {
      console.error('Error completing activity:', error);
      Alert.alert('Error', 'Failed to update activity status');
    } finally {
      setIsCompleting(false);
    }
  };

  // Handle editing the activity
  const handleEdit = () => {
    if (onEdit) {
      onEdit(activity);
    }
  };

  // Handle deleting the activity
  const handleDelete = () => {
    if (onDelete) {
      Alert.alert(
        'Delete Activity',
        'Are you sure you want to delete this activity?',
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => onDelete(activity)
          }
        ]
      );
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        { backgroundColor: colors.card }
      ]}
      entering={FadeIn.duration(300)}
      exiting={FadeOut.duration(300)}
    >
      {/* Time column */}
      <View style={styles.timeColumn}>
        <Text style={[styles.timeText, { color: colors.text }]}>
          {formatTime(activity.scheduledTime)}
        </Text>

        {/* Timeline line */}
        <View style={styles.timelineContainer}>
          {!isFirst && (
            <View
              style={[
                styles.timelineLine,
                styles.timelineLineTop,
                { backgroundColor: colors.border }
              ]}
            />
          )}

          <View
            style={[
              styles.timelineDot,
              {
                backgroundColor: getActivityColor(),
                borderColor: colors.card
              }
            ]}
          />

          {!isLast && (
            <View
              style={[
                styles.timelineLine,
                styles.timelineLineBottom,
                { backgroundColor: colors.border }
              ]}
            />
          )}
        </View>
      </View>

      {/* Content column */}
      <TouchableOpacity
        style={styles.contentColumn}
        onPress={() => {
          // For meal and workout activities, always call onPress if provided
          if ((activity.type === DigestActivityType.MEAL ||
               activity.type === DigestActivityType.WORKOUT) &&
              onPress) {
            onPress(activity);
          } else if (onPress) {
            onPress(activity);
          } else {
            setExpanded(!expanded);
          }
        }}
        activeOpacity={0.7}
      >
        {/* Header row with icon, title and status */}
        <View style={styles.headerRow}>
          <View style={styles.titleContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: getActivityColor() + '20' }
              ]}
            >
              <Ionicons
                name={getActivityIcon()}
                size={16}
                color={getActivityColor()}
              />
            </View>

            <Text
              style={[
                styles.titleText,
                {
                  color: colors.text,
                  textDecorationLine: activity.completed ? 'line-through' : 'none'
                }
              ]}
            >
              {activity.title}
            </Text>

            {activity.isUserOverride && (
              <View
                style={[
                  styles.overrideBadge,
                  { backgroundColor: colors.primary + '30' }
                ]}
              >
                <Text
                  style={[
                    styles.overrideText,
                    { color: colors.primary }
                  ]}
                >
                  Modified
                </Text>
              </View>
            )}
          </View>

          <TouchableOpacity
            style={[
              styles.completeButton,
              {
                backgroundColor: activity.completed
                  ? colors.success + '20'
                  : colors.border
              }
            ]}
            onPress={handleComplete}
            disabled={isCompleting}
          >
            <Ionicons
              name={activity.completed ? 'checkmark-circle' : 'checkmark-circle-outline'}
              size={20}
              color={activity.completed ? colors.success : colors.textSecondary}
            />
          </TouchableOpacity>
        </View>

        {/* Description */}
        <Text
          style={[
            styles.descriptionText,
            {
              color: colors.textSecondary,
              textDecorationLine: activity.completed ? 'line-through' : 'none'
            }
          ]}
          numberOfLines={expanded ? undefined : 2}
        >
          {activity.description}
        </Text>

        {/* Expanded content */}
        {expanded && (
          <View style={styles.expandedContent}>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[
                  styles.editButton,
                  { backgroundColor: colors.primary }
                ]}
                onPress={handleEdit}
              >
                <Ionicons name="pencil-outline" size={16} color="#fff" />
                <Text style={styles.editButtonText}>
                  {activity.isUserOverride ? 'Edit Changes' : 'Customize'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.deleteButton,
                  { backgroundColor: colors.error }
                ]}
                onPress={handleDelete}
              >
                <Ionicons name="trash-outline" size={16} color="#fff" />
                <Text style={styles.editButtonText}>
                  Delete
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeColumn: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 16,
  },
  timeText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    marginBottom: 8,
  },
  timelineContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
  },
  timelineLineTop: {
    marginBottom: 4,
  },
  timelineLineBottom: {
    marginTop: 4,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
  },
  contentColumn: {
    flex: 1,
    padding: 12,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  titleText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    flex: 1,
    lineHeight: 22,
  },
  overrideBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  overrideText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.xs,
  },
  completeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  descriptionText: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    lineHeight: 20,
    opacity: 0.8,
  },
  expandedContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
  },
  editButtonText: {
    color: '#fff',
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    marginLeft: 6,
  },
});

export default TimelineItem;
