import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { DigestActivity } from '../../services/digestService';
import { WorkoutData, WorkoutExercise, saveLog } from '../../services/conversationService';
import { generateUUID } from '../../utils/uuid';
import { useNavigation } from '@react-navigation/native';

interface WorkoutDetailModalProps {
  visible: boolean;
  onClose: () => void;
  activity: DigestActivity | null;
}

const WorkoutDetailModal: React.FC<WorkoutDetailModalProps> = ({
  visible,
  onClose,
  activity
}) => {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [workoutData, setWorkoutData] = useState<WorkoutData | undefined>(undefined);

  // Initialize workout data from activity metadata
  useEffect(() => {
    if (activity?.metadata?.workoutDetails) {
      setWorkoutData(activity.metadata.workoutDetails);
    } else {
      setWorkoutData(undefined);
    }
  }, [activity]);

  useEffect(() => {
    const handleWorkoutData = async () => {
      if (visible && activity) {
        // Log the activity to debug
        console.log('WorkoutDetailModal - Activity:', JSON.stringify(activity, null, 2));

        // Check if activity type is workout (case insensitive)
        const isWorkout = activity.type?.toLowerCase() === 'workout';

        if (isWorkout) {
          console.log('WorkoutDetailModal - Activity is a workout');

          // Check if workout data is available
          if (activity.metadata?.workoutDetails) {
            console.log('WorkoutDetailModal - Workout details found:',
              JSON.stringify(activity.metadata.workoutDetails, null, 2));
          } else {
            console.log('WorkoutDetailModal - No workout details found, fetching from Groq');
            await fetchWorkoutDetails();
          }
        } else {
          console.warn(`WorkoutDetailModal - Activity type is not 'workout', it's '${activity.type}'`);
          setError(`This activity is not a workout (type: ${activity.type})`);
        }
      }
    };

    handleWorkoutData();
  }, [visible, activity]);

  const fetchWorkoutDetails = async () => {
    if (!activity) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching workout details from Groq for:', activity.title);

      // Import sendMessageToChat
      const { sendMessageToChat } = require('../../services/chatService');

      // Request detailed workout information from the LLM
      const prompt = `
        Please provide detailed information about this workout: "${activity.title}".
        Consider the workout description: "${activity.description}"

        Create a complete workout plan with the following:
        1. A detailed description of the workout
        2. List of exercises with sets, reps, and descriptions
        3. Estimated duration
        4. Target body parts
        5. Difficulty level
        6. Required equipment

        IMPORTANT: Return ONLY valid JSON, no other text or formatting.

        Format as JSON with fields:
        {
          "duration": 45,
          "exercises": [
            {
              "name": "Exercise name",
              "sets": 3,
              "reps": "10-12",
              "description": "How to perform the exercise",
              "restTime": "60 seconds"
            }
          ],
          "bodyParts": ["chest", "back", "legs"],
          "difficulty": "beginner|intermediate|advanced",
          "equipment": ["dumbbells", "barbell"],
          "notes": "Additional workout notes"
        }
      `;

      const response = await sendMessageToChat(prompt, true);

      // Enhanced JSON parsing
      let workoutDetails = null;

      try {
        // Try multiple parsing approaches
        const parseAttempts = [
          // Attempt 1: Direct parsing
          () => JSON.parse(response),

          // Attempt 2: Extract JSON from code blocks
          () => {
            const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) ||
                             response.match(/```\n([\s\S]*?)\n```/) ||
                             response.match(/```([\s\S]*?)```/) ||
                             response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonString = jsonMatch[1] || jsonMatch[0];
              return JSON.parse(jsonString);
            }
            throw new Error('No JSON found');
          },

          // Attempt 3: Clean and parse
          () => {
            let cleaned = response
              .replace(/```json/g, '')
              .replace(/```/g, '')
              .replace(/^\s*[\w\s]*?(?=\{)/, '') // Remove text before first {
              .replace(/\}[\w\s]*$/, '}') // Remove text after last }
              .trim();
            return JSON.parse(cleaned);
          }
        ];

        for (const attempt of parseAttempts) {
          try {
            workoutDetails = attempt();
            if (workoutDetails && typeof workoutDetails === 'object') {
              console.log('Successfully parsed workout data');
              break;
            }
          } catch (e) {
            continue;
          }
        }

        if (workoutDetails) {
          setWorkoutData(workoutDetails);

          // Save the workout details to the activity metadata for future use
          try {
            const { saveActivityOverride } = require('../../services/digestService');
            await saveActivityOverride(activity, {
              metadata: {
                ...activity.metadata,
                workoutDetails: workoutDetails
              }
            });
            console.log('Saved workout details to activity metadata');
          } catch (saveError) {
            console.error('Error saving workout details to activity:', saveError);
            // Continue anyway since we have the data for this session
          }
        } else {
          // If no JSON found, create fallback data
          console.log('Could not parse JSON, using fallback data');
          const fallbackData = {
            duration: 45,
            exercises: [
              {
                name: activity.title,
                sets: 3,
                reps: '10-12',
                description: activity.description,
                restTime: '60 seconds'
              }
            ],
            bodyParts: ['full-body'],
            difficulty: 'intermediate',
            equipment: ['bodyweight'],
            notes: 'Follow the workout description and adjust as needed'
          };

          setWorkoutData(fallbackData);

          // Save the fallback data to the activity metadata
          try {
            const { saveActivityOverride } = require('../../services/digestService');
            await saveActivityOverride(activity, {
              metadata: {
                ...activity.metadata,
                workoutDetails: fallbackData
              }
            });
          } catch (saveError) {
            console.error('Error saving fallback workout details:', saveError);
          }
        }
      } catch (error) {
        console.error('Error parsing workout JSON:', error);
        setError('Could not parse workout details');
      }
    } catch (error) {
      console.error('Error fetching workout details:', error);
      setError('Failed to load workout details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartWorkout = () => {
    if (!workoutData) return;

    // Close this modal
    onClose();

    // Navigate to the Strength tab and open the active workout modal
    // We need to use setTimeout to ensure the modal is closed before navigating
    setTimeout(() => {
      // @ts-ignore - Navigation typing is complex
      navigation.navigate('Strength', {
        screen: 'StrengthScreen',
        params: {
          startWorkout: true,
          workoutData: workoutData
        }
      });
    }, 300);
  };

  const handleSaveWorkout = async () => {
    if (!workoutData) return;

    setIsLoading(true);
    try {
      // Save workout to logs
      const logId = generateUUID();
      await saveLog({
        id: logId,
        type: 'workout',
        timestamp: new Date().toISOString(),
        description: workoutData.title,
        workoutData: workoutData,
        metrics: {
          workout: workoutData
        }
      });

      Alert.alert('Success', 'Workout saved to your templates!');
      onClose();
    } catch (error) {
      console.error('Error saving workout:', error);
      setError('Failed to save workout template');
    } finally {
      setIsLoading(false);
    }
  };

  if (!activity) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
        style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
      >
        <Animated.View
          entering={SlideInDown.duration(400).springify()}
          exiting={SlideOutDown.duration(300)}
          style={[styles.modalContent, { backgroundColor: colors.card }]}
        >
          <View style={styles.modalHeader}>
            <View style={styles.headerContent}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: colors.primary + '20' }
                ]}
              >
                <Ionicons
                  name="barbell-outline"
                  size={24}
                  color={colors.primary}
                />
              </View>
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                {activity.title}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Processing workout...
                </Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle-outline" size={40} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {error}
                </Text>
              </View>
            ) : workoutData ? (
              <View style={styles.detailsContainer}>
                <Text style={[styles.description, { color: colors.textSecondary }]}>
                  {activity.description}
                </Text>

                {/* Workout Details */}
                <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    Workout Details
                  </Text>

                  <View style={styles.workoutInfoRow}>
                    <View style={styles.workoutInfoItem}>
                      <Ionicons name="time-outline" size={18} color={colors.primary} />
                      <Text style={[styles.workoutInfoText, { color: colors.text }]}>
                        {workoutData.duration || 45} min
                      </Text>
                    </View>

                    <View style={styles.workoutInfoItem}>
                      <Ionicons name="barbell-outline" size={18} color={colors.primary} />
                      <Text style={[styles.workoutInfoText, { color: colors.text }]}>
                        {workoutData.exercises?.length || 0} exercises
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Exercises */}
                {workoutData.exercises && workoutData.exercises.length > 0 && (
                  <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>
                      Exercises
                    </Text>

                    {workoutData.exercises.map((exercise: WorkoutExercise, index: number) => (
                      <View key={index} style={[styles.exerciseItem, { borderBottomColor: colors.border }]}>
                        <View style={styles.exerciseHeader}>
                          <Text style={[styles.exerciseName, { color: colors.text }]}>
                            {exercise.name}
                          </Text>
                          <Text style={[styles.exerciseIndex, { color: colors.textSecondary }]}>
                            {index + 1}/{workoutData.exercises.length}
                          </Text>
                        </View>

                        <View style={styles.exerciseDetails}>
                          {/* Handle both structured sets and direct properties */}
                          {exercise.sets && Array.isArray(exercise.sets) ? (
                            // Structured format with sets array
                            <View style={styles.exerciseDetail}>
                              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                                Sets
                              </Text>
                              <Text style={[styles.detailValue, { color: colors.text }]}>
                                {exercise.sets.length} x {exercise.sets[0]?.reps || '?'} reps
                                {exercise.sets[0]?.weight ? ` @ ${exercise.sets[0].weight}` : ''}
                              </Text>
                            </View>
                          ) : (
                            // Direct properties format (from Lambda)
                            <>
                              {(exercise as any).sets && (
                                <View style={styles.exerciseDetail}>
                                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                                    Sets
                                  </Text>
                                  <Text style={[styles.detailValue, { color: colors.text }]}>
                                    {(exercise as any).sets}
                                  </Text>
                                </View>
                              )}

                              {(exercise as any).reps && (
                                <View style={styles.exerciseDetail}>
                                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                                    Reps
                                  </Text>
                                  <Text style={[styles.detailValue, { color: colors.text }]}>
                                    {typeof (exercise as any).reps === 'object' ? 
                                      JSON.stringify((exercise as any).reps) : 
                                      (exercise as any).reps}
                                  </Text>
                                </View>
                              )}

                              {(exercise as any).weight && (
                                <View style={styles.exerciseDetail}>
                                  <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                                    Weight
                                  </Text>
                                  <Text style={[styles.detailValue, { color: colors.text }]}>
                                    {(exercise as any).weight}
                                  </Text>
                                </View>
                              )}
                            </>
                          )}
                        </View>
                      </View>
                    ))}
                  </View>
                )}

                {/* Notes */}
                {workoutData.notes && (
                  <View style={[styles.sectionContainer, { borderColor: colors.border }]}>
                    <Text style={[styles.sectionTitle, { color: colors.text }]}>
                      Notes
                    </Text>
                    <Text style={[styles.notesText, { color: colors.textSecondary }]}>
                      {workoutData.notes}
                    </Text>
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.errorContainer}>
                <Text style={[styles.errorText, { color: colors.textSecondary }]}>
                  No workout details available
                </Text>
              </View>
            )}
          </ScrollView>

          {workoutData && (
            <View style={[styles.footer, { borderTopColor: colors.border }]}>
              <TouchableOpacity
                style={[styles.footerButton, styles.saveButton, { borderColor: colors.primary }]}
                onPress={handleSaveWorkout}
              >
                <Ionicons name="save-outline" size={18} color={colors.primary} style={styles.buttonIcon} />
                <Text style={[styles.saveButtonText, { color: colors.primary }]}>
                  Save Template
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.footerButton, styles.startButton, { backgroundColor: colors.primary }]}
                onPress={handleStartWorkout}
              >
                <Ionicons name="play" size={18} color="#fff" style={styles.buttonIcon} />
                <Text style={[styles.startButtonText, { color: '#fff' }]}>
                  Start Workout
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 12,
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 12,
    textAlign: 'center',
  },
  detailsContainer: {
    paddingBottom: 16,
  },
  description: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 20,
  },
  sectionContainer: {
    marginBottom: 20,
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 12,
  },
  workoutInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  workoutInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  workoutInfoText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginLeft: 8,
  },
  exerciseItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    marginBottom: 12,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  exerciseName: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    flex: 1,
  },
  exerciseIndex: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  exerciseDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  exerciseDetail: {
    marginRight: 24,
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  notesText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  saveButton: {
    borderWidth: 1,
    marginRight: 8,
  },
  startButton: {
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  startButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
});

export default WorkoutDetailModal;
