import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

interface ActivityItemProps {
  activity: {
    id: string;
    type: string;
    title: string;
    description: string;
    scheduledTime: string;
    completed: boolean;
    metadata?: any;
  };
  onToggleComplete: (id: string) => void;
  onViewDetails: (id: string) => void;
}

const ActivityItem: React.FC<ActivityItemProps> = ({ 
  activity, 
  onToggleComplete, 
  onViewDetails 
}) => {
  const { colors } = useTheme();
  
  // Format time from ISO string
  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return format(date, 'h:mm a');
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'No time';
    }
  };
  
  // Get appropriate icon based on activity type
  const getIconName = (type: string) => {
    switch (type.toLowerCase()) {
      case 'meal':
        return 'restaurant-outline';
      case 'workout':
        return 'barbell-outline';
      case 'water':
        return 'water-outline';
      case 'sleep':
        return 'bed-outline';
      case 'reminder':
        return 'alarm-outline';
      default:
        return 'calendar-outline';
    }
  };
  
  // Get type-specific color
  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'meal':
        return '#f9a825'; // Amber
      case 'workout':
        return '#7986cb'; // Indigo
      case 'water':
        return '#4fc3f7'; // Light Blue
      case 'sleep':
        return '#9575cd'; // Deep Purple
      case 'reminder':
        return '#4db6ac'; // Teal
      default:
        return colors.primary;
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: colors.card }]}
      onPress={() => onViewDetails(activity.id)}
      activeOpacity={0.7}
    >
      <View style={[styles.topRow, { borderBottomColor: colors.border }]}>
        <View style={styles.timeContainer}>
          <Text style={[styles.time, { color: colors.text }]}>
            {formatTime(activity.scheduledTime)}
          </Text>
        </View>
        
        <View style={[
          styles.typeContainer, 
          { backgroundColor: getTypeColor(activity.type) + '20' }
        ]}>
          <Ionicons 
            name={getIconName(activity.type)} 
            size={14} 
            color={getTypeColor(activity.type)} 
            style={styles.typeIcon} 
          />
          <Text style={[
            styles.type, 
            { color: getTypeColor(activity.type) }
          ]}>
            {activity.type}
          </Text>
        </View>
      </View>
      
      <View style={styles.contentContainer}>
        <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
          {activity.title}
        </Text>
        <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
          {activity.description}
        </Text>
      </View>
      
      <TouchableOpacity 
        style={[
          styles.completeButton, 
          activity.completed 
            ? { backgroundColor: '#e6f7e6', borderColor: '#4caf50' } 
            : { backgroundColor: colors.card, borderColor: colors.border }
        ]}
        onPress={() => onToggleComplete(activity.id)}
      >
        {activity.completed ? (
          <View style={styles.completedContainer}>
            <Ionicons name="checkmark-circle" size={16} color="#4caf50" />
            <Text style={[styles.buttonText, { color: '#4caf50' }]}>
              Completed
            </Text>
          </View>
        ) : (
          <Text style={[styles.buttonText, { color: colors.text }]}>
            Mark as complete
          </Text>
        )}
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  time: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeIcon: {
    marginRight: 4,
  },
  type: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.xs,
    textTransform: 'capitalize',
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    marginBottom: 6,
  },
  description: {
    fontFamily: typography.fontFamily.regular,
    fontSize: typography.sizes.sm,
    lineHeight: typography.sizes.md * 1.4,
  },
  completeButton: {
    padding: 12,
    alignItems: 'center',
    borderTopWidth: 1,
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    marginLeft: 4,
  },
});

export default ActivityItem; 