import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  FadeIn,
  SlideInRight,
  Layout
} from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import {
  EnhancedDayDigest,
  EnhancedDigestActivity,
  DigestTrend,
  generateEnhancedDigest,
  getDigestPreferences,
  DigestPreferences
} from '../../services/enhancedDigestService';
import { format, parseISO } from 'date-fns';

interface EnhancedDailyTimelineProps {
  date: string;
  onActivityPress?: (activity: EnhancedDigestActivity) => void;
  onRefresh?: () => void;
  onCustomize?: () => void;
  compactView?: boolean;
}

const { width } = Dimensions.get('window');

const EnhancedDailyTimeline: React.FC<EnhancedDailyTimelineProps> = ({
  date,
  onActivityPress,
  onRefresh,
  onCustomize,
  compactView = false
}) => {
  const { colors, isDark } = useTheme();
  const [digest, setDigest] = useState<EnhancedDayDigest | null>(null);
  const [preferences, setPreferences] = useState<DigestPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['activities']));

  // Animation values
  const headerOpacity = useSharedValue(1);
  const contentScale = useSharedValue(1);

  useEffect(() => {
    loadDigest();
    loadPreferences();
  }, [date]);

  const loadDigest = async (forceRefresh = false) => {
    try {
      if (forceRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const enhancedDigest = await generateEnhancedDigest(date, forceRefresh);
      setDigest(enhancedDigest);

    } catch (err) {
      console.error('[EnhancedTimeline] Error loading digest:', err);
      setError('Failed to load enhanced digest');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const loadPreferences = async () => {
    try {
      const prefs = await getDigestPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error('[EnhancedTimeline] Error loading preferences:', error);
    }
  };

  const handleRefresh = () => {
    loadDigest(true);
    onRefresh?.();
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Memoized calculations
  const timelineStats = useMemo(() => {
    if (!digest) return null;

    const totalActivities = digest.activities.length;
    const completedActivities = digest.activities.filter(a => a.completed).length;
    const highPriorityActivities = digest.activities.filter(a => a.priority === 'high' || a.priority === 'critical').length;
    const totalDuration = digest.activities.reduce((sum, a) => sum + a.estimatedDuration, 0);

    return {
      totalActivities,
      completedActivities,
      highPriorityActivities,
      totalDuration,
      completionRate: totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0
    };
  }, [digest]);

  const groupedActivities = useMemo(() => {
    if (!digest) return {};

    const groups: Record<string, EnhancedDigestActivity[]> = {};
    
    digest.activities.forEach(activity => {
      const hour = activity.scheduledTime ? 
        format(parseISO(activity.scheduledTime), 'HH:00') : 'Unscheduled';
      
      if (!groups[hour]) groups[hour] = [];
      groups[hour].push(activity);
    });

    return groups;
  }, [digest]);

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: contentScale.value }]
  }));

  const renderHeader = () => (
    <Animated.View style={[styles.header, headerAnimatedStyle]}>
      <View style={styles.headerTop}>
        <View style={styles.headerLeft}>
          <Text style={[styles.dateText, { color: colors.text }]}>
            {format(parseISO(date), 'EEEE, MMM d')}
          </Text>
          {digest?.insights.dailyFocus && (
            <Text style={[styles.focusText, { color: colors.textSecondary }]}>
              Focus: {digest.insights.dailyFocus}
            </Text>
          )}
        </View>
        
        <View style={styles.headerRight}>
          {onCustomize && (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.card }]}
              onPress={onCustomize}
            >
              <Ionicons name="options-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.card }]}
            onPress={handleRefresh}
          >
            <Ionicons name="refresh-outline" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {timelineStats && !compactView && (
        <View style={[styles.statsContainer, { backgroundColor: colors.card }]}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>
              {timelineStats.totalActivities}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Activities
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.success || '#34C759' }]}>
              {timelineStats.completionRate.toFixed(0)}%
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Complete
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.warning || '#FF9500' }]}>
              {timelineStats.highPriorityActivities}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Priority
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.text }]}>
              {Math.round(timelineStats.totalDuration / 60)}h
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Duration
            </Text>
          </View>
        </View>
      )}

      {digest?.insights.motivationalMessage && preferences?.customization.showMotivation && (
        <View style={[styles.motivationContainer, { backgroundColor: colors.primaryLight + '20' }]}>
          <Ionicons name="heart" size={16} color={colors.primary} />
          <Text style={[styles.motivationText, { color: colors.text }]}>
            {digest.insights.motivationalMessage}
          </Text>
        </View>
      )}
    </Animated.View>
  );

  const renderTrendSection = () => {
    if (!digest?.trends.length || compactView) return null;

    return (
      <Animated.View
        style={[styles.section, { backgroundColor: colors.card }]}
        entering={FadeIn.delay(200)}
        layout={Layout.springify()}
      >
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection('trends')}
        >
          <View style={styles.sectionHeaderLeft}>
            <Ionicons name="trending-up" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Trends & Insights
            </Text>
          </View>
          <Ionicons
            name={expandedSections.has('trends') ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('trends') && (
          <Animated.View entering={FadeIn.duration(300)}>
            {digest.trends.map((trend, index) => (
              <View key={`${trend.type}_${trend.period}`} style={styles.trendItem}>
                <View style={styles.trendHeader}>
                  <Text style={[styles.trendType, { color: colors.text }]}>
                    {trend.type.charAt(0).toUpperCase() + trend.type.slice(1)}
                  </Text>
                  <View style={[
                    styles.trendBadge,
                    { backgroundColor: getTrendColor(trend.trend) + '20' }
                  ]}>
                    <Ionicons
                      name={getTrendIcon(trend.trend)}
                      size={12}
                      color={getTrendColor(trend.trend)}
                    />
                    <Text style={[
                      styles.trendBadgeText,
                      { color: getTrendColor(trend.trend) }
                    ]}>
                      {trend.changePercentage > 0 ? '+' : ''}{trend.changePercentage}%
                    </Text>
                  </View>
                </View>
                
                {trend.insights.length > 0 && (
                  <Text style={[styles.trendInsight, { color: colors.textSecondary }]}>
                    {trend.insights[0]}
                  </Text>
                )}
              </View>
            ))}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const renderActivitiesSection = () => {
    if (!digest?.activities.length) {
      return (
        <Animated.View
          style={[styles.emptyContainer, { backgroundColor: colors.card }]}
          entering={FadeIn.delay(300)}
        >
          <Ionicons name="calendar-outline" size={48} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No Activities Planned
          </Text>
          <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
            Tap refresh to generate personalized activities for today
          </Text>
          <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: colors.primary }]}
            onPress={handleRefresh}
          >
            <Ionicons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.refreshButtonText}>Generate Activities</Text>
          </TouchableOpacity>
        </Animated.View>
      );
    }

    return (
      <Animated.View
        style={[styles.section, { backgroundColor: colors.card }]}
        entering={FadeIn.delay(400)}
        layout={Layout.springify()}
      >
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection('activities')}
        >
          <View style={styles.sectionHeaderLeft}>
            <Ionicons name="list" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Today's Activities
            </Text>
          </View>
          <Ionicons
            name={expandedSections.has('activities') ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('activities') && (
          <Animated.View entering={FadeIn.duration(300)}>
            {Object.entries(groupedActivities).map(([timeSlot, activities]) => (
              <View key={timeSlot} style={styles.timeSlotContainer}>
                <Text style={[styles.timeSlotHeader, { color: colors.textSecondary }]}>
                  {timeSlot}
                </Text>
                
                {activities.map((activity, index) => (
                  <Animated.View
                    key={activity.id}
                    entering={SlideInRight.delay(index * 100)}
                    layout={Layout.springify()}
                  >
                    <TouchableOpacity
                      style={[
                        styles.activityCard,
                        { backgroundColor: colors.background },
                        activity.completed && styles.completedActivity
                      ]}
                      onPress={() => onActivityPress?.(activity)}
                    >
                      <View style={styles.activityLeft}>
                        <View style={[
                          styles.priorityIndicator,
                          { backgroundColor: getPriorityColor(activity.priority) }
                        ]} />
                        
                        <View style={styles.activityContent}>
                          <Text style={[
                            styles.activityTitle,
                            { color: colors.text },
                            activity.completed && styles.completedText
                          ]}>
                            {activity.title}
                          </Text>
                          
                          {activity.description && (
                            <Text style={[
                              styles.activityDescription,
                              { color: colors.textSecondary }
                            ]}>
                              {activity.description}
                            </Text>
                          )}
                          
                          <View style={styles.activityMeta}>
                            <View style={styles.metaItem}>
                              <Ionicons name="time-outline" size={12} color={colors.textSecondary} />
                              <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                                {activity.estimatedDuration}m
                              </Text>
                            </View>
                            
                            <View style={styles.metaItem}>
                              <Ionicons name="flash-outline" size={12} color={colors.textSecondary} />
                              <Text style={[styles.metaText, { color: colors.textSecondary }]}>
                                {activity.energyLevel}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                      
                      <View style={styles.activityRight}>
                        <Ionicons
                          name={activity.completed ? 'checkmark-circle' : 'ellipse-outline'}
                          size={24}
                          color={activity.completed ? colors.success || '#34C759' : colors.textSecondary}
                        />
                      </View>
                    </TouchableOpacity>
                  </Animated.View>
                ))}
              </View>
            ))}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const renderInsightsSection = () => {
    if (!digest?.insights.recommendations.length || compactView) return null;

    return (
      <Animated.View
        style={[styles.section, { backgroundColor: colors.card }]}
        entering={FadeIn.delay(500)}
        layout={Layout.springify()}
      >
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection('insights')}
        >
          <View style={styles.sectionHeaderLeft}>
            <Ionicons name="bulb-outline" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Recommendations
            </Text>
          </View>
          <Ionicons
            name={expandedSections.has('insights') ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>

        {expandedSections.has('insights') && (
          <Animated.View entering={FadeIn.duration(300)}>
            {digest.insights.recommendations.map((recommendation, index) => (
              <View key={index} style={styles.recommendationItem}>
                <Ionicons name="arrow-forward" size={16} color={colors.primary} />
                <Text style={[styles.recommendationText, { color: colors.text }]}>
                  {recommendation}
                </Text>
              </View>
            ))}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  // Helper functions
  const getTrendColor = (trend: DigestTrend['trend']): string => {
    switch (trend) {
      case 'improving': return '#34C759';
      case 'declining': return '#FF3B30';
      case 'stable': return '#007AFF';
      default: return colors.textSecondary;
    }
  };

  const getTrendIcon = (trend: DigestTrend['trend']): keyof typeof Ionicons.glyphMap => {
    switch (trend) {
      case 'improving': return 'trending-up';
      case 'declining': return 'trending-down';
      case 'stable': return 'remove';
      default: return 'help';
    }
  };

  const getPriorityColor = (priority: EnhancedDigestActivity['priority']): string => {
    switch (priority) {
      case 'critical': return '#FF3B30';
      case 'high': return '#FF9500';
      case 'medium': return '#007AFF';
      case 'low': return '#8E8E93';
      default: return colors.textSecondary;
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Animated.View entering={FadeIn}>
          <Ionicons name="hourglass-outline" size={48} color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Crafting your perfect day...
          </Text>
        </Animated.View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Ionicons name="alert-circle-outline" size={48} color={colors.error || '#FF3B30'} />
        <Text style={[styles.errorText, { color: colors.error || '#FF3B30' }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={() => loadDigest(true)}
        >
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Animated.View style={[styles.container, contentAnimatedStyle]}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
      >
        {renderHeader()}
        {renderTrendSection()}
        {renderActivitiesSection()}
        {renderInsightsSection()}
        
        <View style={styles.bottomSpacer} />
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    gap: 8,
  },
  dateText: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  focusText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  motivationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  motivationText: {
    flex: 1,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    fontStyle: 'italic',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  trendItem: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  trendHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  trendType: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  trendBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  trendBadgeText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  trendInsight: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  timeSlotContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  timeSlotHeader: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
    marginLeft: 8,
  },
  activityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  completedActivity: {
    opacity: 0.7,
  },
  activityLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityIndicator: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  completedText: {
    textDecorationLine: 'line-through',
  },
  activityDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 8,
    lineHeight: 18,
  },
  activityMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  activityRight: {
    marginLeft: 12,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    gap: 8,
  },
  recommendationText: {
    flex: 1,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  refreshButtonText: {
    color: '#FFFFFF',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  bottomSpacer: {
    height: 20,
  },
});

export default EnhancedDailyTimeline;
