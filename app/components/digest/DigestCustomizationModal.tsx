import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import {
  DigestPreferences,
  getDigestPreferences,
  saveDigestPreferences
} from '../../services/enhancedDigestService';

interface DigestCustomizationModalProps {
  visible: boolean;
  onClose: () => void;
  onSave?: (preferences: DigestPreferences) => void;
}

const DigestCustomizationModal: React.FC<DigestCustomizationModalProps> = ({
  visible,
  onClose,
  onSave
}) => {
  const { colors, isDark } = useTheme();
  const [preferences, setPreferences] = useState<DigestPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (visible) {
      loadPreferences();
    }
  }, [visible]);

  const loadPreferences = async () => {
    try {
      setIsLoading(true);
      const prefs = await getDigestPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error('[DigestCustomization] Error loading preferences:', error);
      Alert.alert('Error', 'Failed to load preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!preferences) return;

    try {
      setIsSaving(true);
      await saveDigestPreferences(preferences);
      onSave?.(preferences);
      onClose();
    } catch (error) {
      console.error('[DigestCustomization] Error saving preferences:', error);
      Alert.alert('Error', 'Failed to save preferences');
    } finally {
      setIsSaving(false);
    }
  };

  const updatePreferences = (updates: Partial<DigestPreferences>) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      ...updates,
      lastUpdated: new Date().toISOString()
    });
  };

  const updateWorkoutPreferences = (updates: Partial<DigestPreferences['workoutPreferences']>) => {
    if (!preferences) return;
    
    updatePreferences({
      workoutPreferences: {
        ...preferences.workoutPreferences,
        ...updates
      }
    });
  };

  const updateMealPreferences = (updates: Partial<DigestPreferences['mealPreferences']>) => {
    if (!preferences) return;
    
    updatePreferences({
      mealPreferences: {
        ...preferences.mealPreferences,
        ...updates
      }
    });
  };

  const updateCustomization = (updates: Partial<DigestPreferences['customization']>) => {
    if (!preferences) return;
    
    updatePreferences({
      customization: {
        ...preferences.customization,
        ...updates
      }
    });
  };

  const renderTimeInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void
  ) => (
    <View style={styles.inputGroup}>
      <Text style={[styles.inputLabel, { color: colors.text }]}>{label}</Text>
      <TextInput
        style={[
          styles.timeInput,
          { 
            backgroundColor: colors.card,
            color: colors.text,
            borderColor: colors.border
          }
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder="HH:MM"
        placeholderTextColor={colors.textSecondary}
        keyboardType="numeric"
        maxLength={5}
      />
    </View>
  );

  const renderSwitchRow = (
    label: string,
    description: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    icon?: keyof typeof Ionicons.glyphMap
  ) => (
    <View style={styles.switchRow}>
      <View style={styles.switchLeft}>
        {icon && (
          <Ionicons name={icon} size={20} color={colors.primary} style={styles.switchIcon} />
        )}
        <View style={styles.switchTextContainer}>
          <Text style={[styles.switchLabel, { color: colors.text }]}>{label}</Text>
          <Text style={[styles.switchDescription, { color: colors.textSecondary }]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: colors.border, true: colors.primaryLight }}
        thumbColor={value ? colors.primary : colors.textSecondary}
      />
    </View>
  );

  const renderSection = (title: string, children: React.ReactNode, icon?: keyof typeof Ionicons.glyphMap) => (
    <View style={[styles.section, { backgroundColor: colors.card }]}>
      <View style={styles.sectionHeader}>
        {icon && <Ionicons name={icon} size={20} color={colors.primary} />}
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{title}</Text>
      </View>
      {children}
    </View>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent
      statusBarTranslucent
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[styles.modal, { backgroundColor: colors.background }]}
          entering={SlideInDown.duration(300)}
        >
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: colors.border }]}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Customize Digest
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Ionicons name="close" size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Ionicons name="hourglass-outline" size={48} color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Loading preferences...
              </Text>
            </View>
          ) : preferences ? (
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Schedule Settings */}
              {renderSection('Schedule', (
                <View style={styles.sectionContent}>
                  {renderTimeInput(
                    'Wake Up Time',
                    preferences.wakeUpTime,
                    (text) => updatePreferences({ wakeUpTime: text })
                  )}
                  {renderTimeInput(
                    'Bed Time',
                    preferences.bedTime,
                    (text) => updatePreferences({ bedTime: text })
                  )}
                </View>
              ), 'time-outline')}

              {/* Workout Preferences */}
              {renderSection('Workout Preferences', (
                <View style={styles.sectionContent}>
                  <View style={styles.inputGroup}>
                    <Text style={[styles.inputLabel, { color: colors.text }]}>
                      Preferred Duration (minutes)
                    </Text>
                    <TextInput
                      style={[
                        styles.numberInput,
                        { 
                          backgroundColor: colors.card,
                          color: colors.text,
                          borderColor: colors.border
                        }
                      ]}
                      value={preferences.workoutPreferences.duration.toString()}
                      onChangeText={(text) => updateWorkoutPreferences({ 
                        duration: parseInt(text) || 45 
                      })}
                      keyboardType="numeric"
                      placeholder="45"
                      placeholderTextColor={colors.textSecondary}
                    />
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.inputLabel, { color: colors.text }]}>
                      Intensity Level
                    </Text>
                    <View style={styles.segmentedControl}>
                      {(['low', 'medium', 'high'] as const).map((intensity) => (
                        <TouchableOpacity
                          key={intensity}
                          style={[
                            styles.segmentButton,
                            {
                              backgroundColor: preferences.workoutPreferences.intensity === intensity
                                ? colors.primary
                                : colors.card,
                              borderColor: colors.border
                            }
                          ]}
                          onPress={() => updateWorkoutPreferences({ intensity })}
                        >
                          <Text
                            style={[
                              styles.segmentText,
                              {
                                color: preferences.workoutPreferences.intensity === intensity
                                  ? '#FFFFFF'
                                  : colors.text
                              }
                            ]}
                          >
                            {intensity.charAt(0).toUpperCase() + intensity.slice(1)}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                </View>
              ), 'barbell-outline')}

              {/* Meal Preferences */}
              {renderSection('Meal Preferences', (
                <View style={styles.sectionContent}>
                  <View style={styles.inputGroup}>
                    <Text style={[styles.inputLabel, { color: colors.text }]}>
                      Meals Per Day
                    </Text>
                    <TextInput
                      style={[
                        styles.numberInput,
                        { 
                          backgroundColor: colors.card,
                          color: colors.text,
                          borderColor: colors.border
                        }
                      ]}
                      value={preferences.mealPreferences.mealsPerDay.toString()}
                      onChangeText={(text) => updateMealPreferences({ 
                        mealsPerDay: parseInt(text) || 3 
                      })}
                      keyboardType="numeric"
                      placeholder="3"
                      placeholderTextColor={colors.textSecondary}
                    />
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.inputLabel, { color: colors.text }]}>
                      Snack Frequency
                    </Text>
                    <TextInput
                      style={[
                        styles.numberInput,
                        { 
                          backgroundColor: colors.card,
                          color: colors.text,
                          borderColor: colors.border
                        }
                      ]}
                      value={preferences.mealPreferences.snackFrequency.toString()}
                      onChangeText={(text) => updateMealPreferences({ 
                        snackFrequency: parseInt(text) || 2 
                      })}
                      keyboardType="numeric"
                      placeholder="2"
                      placeholderTextColor={colors.textSecondary}
                    />
                  </View>
                </View>
              ), 'restaurant-outline')}

              {/* Display Options */}
              {renderSection('Display Options', (
                <View style={styles.sectionContent}>
                  {renderSwitchRow(
                    'Show Weather',
                    'Display weather information in digest',
                    preferences.customization.showWeather,
                    (value) => updateCustomization({ showWeather: value }),
                    'partly-sunny-outline'
                  )}

                  {renderSwitchRow(
                    'Show Motivation',
                    'Include motivational messages',
                    preferences.customization.showMotivation,
                    (value) => updateCustomization({ showMotivation: value }),
                    'heart-outline'
                  )}

                  {renderSwitchRow(
                    'Show Progress',
                    'Display progress tracking information',
                    preferences.customization.showProgress,
                    (value) => updateCustomization({ showProgress: value }),
                    'trending-up-outline'
                  )}

                  {renderSwitchRow(
                    'Compact View',
                    'Use a more condensed layout',
                    preferences.customization.compactView,
                    (value) => updateCustomization({ compactView: value }),
                    'contract-outline'
                  )}

                  {renderSwitchRow(
                    'Auto Refresh',
                    'Automatically refresh digest data',
                    preferences.customization.autoRefresh,
                    (value) => updateCustomization({ autoRefresh: value }),
                    'refresh-outline'
                  )}

                  {renderSwitchRow(
                    'Notifications',
                    'Enable digest notifications',
                    preferences.customization.notificationsEnabled,
                    (value) => updateCustomization({ notificationsEnabled: value }),
                    'notifications-outline'
                  )}
                </View>
              ), 'options-outline')}

              <View style={styles.bottomSpacer} />
            </ScrollView>
          ) : null}

          {/* Footer */}
          <View style={[styles.footer, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.footerButton, styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.footerButton,
                styles.saveButton,
                { backgroundColor: colors.primary },
                isSaving && styles.disabledButton
              ]}
              onPress={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <Ionicons name="hourglass-outline" size={20} color="#FFFFFF" />
              ) : (
                <Text style={styles.saveButtonText}>Save</Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 16,
  },
  section: {
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
  },
  timeInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  numberInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
  },
  segmentButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderRightWidth: 0,
  },
  segmentText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  switchLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchIcon: {
    marginRight: 12,
  },
  switchTextContainer: {
    flex: 1,
  },
  switchLabel: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
  },
  switchDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    gap: 12,
  },
  footerButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  saveButton: {
    minHeight: 44,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  disabledButton: {
    opacity: 0.6,
  },
  bottomSpacer: {
    height: 20,
  },
});

export default DigestCustomizationModal;
