import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, FlatList, TouchableOpacity } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import { DayDigest, DigestActivity, getDayDigest, saveActivityOverride, deleteActivityOverride } from '../../services/digestService';
import TimelineItem from './TimelineItem';
import { format } from 'date-fns';
import Animated, { FadeIn } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';

interface DailyTimelineProps {
  date: Date;
  onEditActivity?: (activity: DigestActivity) => void;
  onSelectActivity?: (activity: DigestActivity) => void;
  onRefresh?: () => void;
  onAddContext?: () => void;
  onResetFuturePlans?: () => void;
}

const DailyTimeline: React.FC<DailyTimelineProps> = ({
  date,
  onEditActivity,
  onSelectActivity,
  onRefresh,
  onAddContext,
  onResetFuturePlans
}) => {
  const { colors } = useTheme();
  const [digest, setDigest] = useState<DayDigest | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load digest data for the selected date
  useEffect(() => {
    fetchDigest();
  }, [date]);

  const fetchDigest = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const dayDigest = await getDayDigest(date);
      setDigest(dayDigest);
    } catch (err) {
      console.error('Error loading digest:', err);
      setError('Failed to load daily digest');
    } finally {
      setIsLoading(false);
    }
  };

  // Alias for backward compatibility
  const loadDigest = fetchDigest;

  // Handle marking an activity as complete
  const handleCompleteActivity = async (activity: DigestActivity, completed: boolean) => {
    if (!digest) return;

    // Update the local state immediately for better UX
    const updatedActivities = digest.activities.map(a =>
      a.id === activity.id ? { ...a, completed } : a
    );

    setDigest({
      ...digest,
      activities: updatedActivities
    });

    // Save the change to the server
    try {
      await saveActivityOverride(activity, { completed });
    } catch (error) {
      console.error('Error saving activity completion:', error);
      // Revert the change if there was an error
      setDigest(digest);
    }
  };

  // Handle deleting an activity
  const handleDeleteActivity = async (activity: DigestActivity) => {
    if (!digest) return;

    try {
      // Update the local state immediately for better UX
      const updatedActivities = digest.activities.filter(a => a.id !== activity.id);

      setDigest({
        ...digest,
        activities: updatedActivities
      });

      // Delete the activity on the server
      await deleteActivityOverride(activity.id, date);
    } catch (error) {
      console.error('Error deleting activity:', error);
      // Revert the change if there was an error
      fetchDigest();
    }
  };

  // Format the date for display
  const formatDate = (date: Date) => {
    return format(date, 'EEEE, MMMM d, yyyy');
  };

  // Render a timeline item
  const renderTimelineItem = ({ item, index }: { item: DigestActivity; index: number }) => {
    return (
      <TimelineItem
        activity={item}
        isFirst={index === 0}
        isLast={index === digest?.activities.length! - 1}
        onPress={onSelectActivity}
        onEdit={onEditActivity}
        onComplete={handleCompleteActivity}
        onDelete={handleDeleteActivity}
        onRefresh={fetchDigest}
      />
    );
  };

  // Render loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your day...
        </Text>
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error}
        </Text>
      </View>
    );
  }

  // Render empty state
  if (!digest || digest.activities.length === 0) {
    // Different empty states based on reason
    if (digest?.noDataReason === 'before-join') {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <Ionicons name="calendar-outline" size={48} color={colors.textTertiary} style={styles.emptyIcon} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No Data Available
          </Text>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            This date is before you joined Lotus.
          </Text>
        </View>
      );
    } else if (digest?.noDataReason === 'no-data-collected') {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <Ionicons name="analytics-outline" size={48} color={colors.textTertiary} style={styles.emptyIcon} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No Data Collected
          </Text>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No data was collected for this date. Start tracking your activities to see them here.
          </Text>
        </View>
      );
    } else {
      // Check if there's an error
      const hasError = digest?.error || digest?.noDataReason === 'api-error';

      return (
        <View style={[styles.container, styles.centerContent]}>
          {hasError ? (
            // Error state
            <>
              <Ionicons name="alert-circle-outline" size={48} color={colors.error} style={styles.emptyIcon} />
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                Error Loading Activities
              </Text>
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                {digest?.error || 'There was an error generating activities for this day.'}
              </Text>
            </>
          ) : (
            // Empty state
            <>
              <Ionicons name="calendar-outline" size={48} color={colors.textTertiary} style={styles.emptyIcon} />
              <Text style={[styles.emptyTitle, { color: colors.text }]}>
                No Activities Scheduled
              </Text>
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                There are no activities scheduled for this day.
              </Text>
            </>
          )}

          {/* Add a prominent refresh button */}
          {onRefresh && (
            <TouchableOpacity
              style={[styles.emptyRefreshButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                console.log('Refreshing digest...');
                if (onRefresh) onRefresh();
              }}
            >
              <Ionicons name="refresh-outline" size={20} color="#FFFFFF" />
              <Text style={[styles.emptyRefreshButtonText, { color: "#FFFFFF" }]}>
                Refresh
              </Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }
  }

  // Render the timeline
  return (
    <Animated.View
      style={styles.container}
      entering={FadeIn.duration(300)}
    >
      <View style={styles.headerContainer}>
        <Text style={[styles.dateHeader, { color: colors.text }]}>
          {formatDate(date)}
        </Text>

        <View style={styles.actionButtons}>
          {onRefresh && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primaryLight + '30' }]}
              onPress={() => {
                fetchDigest();
                if (onRefresh) onRefresh();
              }}
            >
              <Ionicons name="refresh-outline" size={18} color={colors.primary} />
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>Refresh</Text>
            </TouchableOpacity>
          )}

          {onAddContext && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primaryLight + '30' }]}
              onPress={onAddContext}
            >
              <Ionicons name="create-outline" size={18} color={colors.primary} />
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>Customize</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <FlatList
        data={digest.activities}
        renderItem={renderTimelineItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.timelineList}
        showsVerticalScrollIndicator={false}
      />

      {onResetFuturePlans && (
        <TouchableOpacity
          style={[styles.resetButton, { backgroundColor: colors.primaryLight + '20' }]}
          onPress={onResetFuturePlans}
        >
          <Ionicons name="calendar-outline" size={18} color={colors.primary} />
          <Text style={[styles.resetButtonText, { color: colors.primary }]}>
            Reset Future Plans
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  dateHeader: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.lg,
    flex: 1,
    color: '#333333',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginLeft: 8,
  },
  actionButtonText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.sm,
    marginLeft: 4,
  },
  timelineList: {
    paddingBottom: 24,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 16,
  },
  resetButtonText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    marginLeft: 8,
  },
  loadingText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.md,
    marginTop: 16,
  },
  errorText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.md,
    textAlign: 'center',
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.lg,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyText: {
    fontFamily: typography.fontFamily.medium,
    fontSize: typography.sizes.md,
    textAlign: 'center',
    maxWidth: '80%',
    marginBottom: 24,
  },
  emptyRefreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 28,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emptyRefreshButtonText: {
    fontFamily: typography.fontFamily.semibold,
    fontSize: typography.sizes.md,
    marginLeft: 8,
  },
});

export default DailyTimeline;
