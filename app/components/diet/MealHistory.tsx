import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { MealData } from '../../services/conversationService';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import AllMealsModal from './AllMealsModal';
import { SkeletonMealHistory } from '../common/SkeletonPlaceholder';

interface MealHistoryProps {
  meals: {
    id: string;
    title: string;
    date: string;
    description: string;
    nutrition: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    ingredients?: string[];
    instructions?: string[];
    tags?: string[];
  }[];
  onSelectMeal?: (meal: any) => void;
  isLoading?: boolean;
  isInsideScrollView?: boolean; // Flag to prevent nested virtualized lists
}

const MealHistory: React.FC<MealHistoryProps> = ({ meals, onSelectMeal, isLoading = false, isInsideScrollView = true }) => {
  const { colors, isDark } = useTheme();
  const [expandedMeal, setExpandedMeal] = useState<string | null>(null);
  const initialExpansionDone = useRef<boolean>(false);
  const [showAllMealsModal, setShowAllMealsModal] = useState(false);

  // Get only the 5 most recent meals for the main view
  const recentMeals = meals.slice(0, 5);
  const hasMoreMeals = meals.length > 5;

  // Toggle meal expansion
  const toggleMeal = (id: string) => {
    setExpandedMeal(expandedMeal === id ? null : id);
    // Mark that user has manually toggled a meal
    initialExpansionDone.current = true;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Render a meal item
  const renderMealItem = ({ item }: { item: MealHistoryProps['meals'][0] }) => {
    const isExpanded = expandedMeal === item.id;

    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(200)}
        style={[
          styles.mealItem,
          {
            backgroundColor: colors.card,
            borderColor: isDark ? colors.border : 'transparent',
            borderWidth: isDark ? 1 : 0,
          }
        ]}
      >
        <TouchableOpacity
          style={styles.mealHeader}
          onPress={() => toggleMeal(item.id)}
          activeOpacity={0.7}
        >
          <View style={styles.mealTitleContainer}>
            <Text style={[styles.mealTitle, { color: colors.text }]}>
              {item.title}
            </Text>
            <Text style={[styles.mealDate, { color: colors.textSecondary }]}>
              {formatDate(item.date)}
            </Text>
          </View>

          <View style={styles.mealActions}>
            <Text style={[styles.calorieCount, { color: colors.textSecondary }]}>
              {item.nutrition.calories} kcal
            </Text>
            <Ionicons
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={colors.textSecondary}
              style={styles.expandIcon}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <Animated.View
            entering={FadeIn.duration(200)}
            style={styles.mealDetails}
          >
            <Text style={[styles.mealDescription, { color: colors.text }]}>
              {item.description}
            </Text>

            <View style={styles.nutritionContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition</Text>
              <View style={styles.nutritionGrid}>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.calories}</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.protein}g</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.carbs}g</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.fat}g</Text>
                </View>
              </View>
            </View>

            {item.ingredients && item.ingredients.length > 0 && (
              <View style={styles.ingredientsContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
                {item.ingredients.map((ingredient, index) => (
                  <Text
                    key={`ingredient-${index}`}
                    style={[styles.ingredientItem, { color: colors.textSecondary }]}
                  >
                    • {ingredient}
                  </Text>
                ))}
              </View>
            )}

            {onSelectMeal && (
              <TouchableOpacity
                style={[styles.cookButton, { backgroundColor: colors.primary }]}
                onPress={() => onSelectMeal(item)}
              >
                <Text style={[styles.cookButtonText, { color: colors.background }]}>
                  Cook This Meal
                </Text>
              </TouchableOpacity>
            )}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  if (isLoading) {
    return <SkeletonMealHistory />;
  }

  if (!meals || meals.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.card }]}>
        <Ionicons name="restaurant-outline" size={40} color={colors.textSecondary} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          No meal history
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
          Log your meals to see your history here
        </Text>
      </View>
    );
  }

  // We no longer auto-expand the first meal
  useEffect(() => {
    // Mark as done to prevent auto-expansion
    initialExpansionDone.current = true;
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="restaurant-outline" size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>Recent Meals</Text>
        </View>

        {hasMoreMeals && (
          <TouchableOpacity
            style={styles.showAllButton}
            onPress={() => setShowAllMealsModal(true)}
          >
            <Text style={[styles.showAllText, { color: colors.primary }]}>
              Show All
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {isInsideScrollView ? (
        // When inside a ScrollView, use a simple non-virtualized list
        <View style={styles.listContent}>
          {recentMeals.map((meal) => (
            <View key={meal.id}>
              {renderMealItem({ item: meal })}
            </View>
          ))}
        </View>
      ) : (
        // When not inside a ScrollView, use FlatList for virtualization
        <FlatList
          data={recentMeals}
          renderItem={renderMealItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          scrollEnabled={false}
        />
      )}

      {hasMoreMeals && meals.length > 0 && (
        <AllMealsModal
          visible={showAllMealsModal}
          onClose={() => setShowAllMealsModal(false)}
          meals={meals}
          onSelectMeal={onSelectMeal || (() => {})}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 8,
  },
  showAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  showAllText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  listContent: {
    paddingBottom: 5,
  },
  mealItem: {
    borderRadius: 12,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  mealTitleContainer: {
    flex: 1,
  },
  mealTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  mealDate: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  mealActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  calorieCount: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginRight: 5,
  },
  expandIcon: {
    marginLeft: 5,
  },
  mealDetails: {
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  mealDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
    lineHeight: 20,
  },
  nutritionContainer: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  nutritionItem: {
    width: '25%',
    paddingHorizontal: 5,
    marginBottom: 10,
  },
  nutritionLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  },
  nutritionValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  ingredientsContainer: {
    marginBottom: 15,
  },
  ingredientItem: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 5,
    paddingLeft: 5,
  },
  cookButton: {
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 5,
  },
  cookButtonText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyContainer: {
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 15,
    marginBottom: 5,
  },
  emptySubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
});

export default MealHistory;
