import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  StatusBar,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';

interface MealItem {
  id: string;
  title: string;
  date: string;
  description: string;
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  ingredients?: string[];
  instructions?: string[];
  tags?: string[];
}

interface AllMealsModalProps {
  visible: boolean;
  onClose: () => void;
  meals: MealItem[];
  onSelectMeal: (meal: MealItem) => void;
}

const AllMealsModal: React.FC<AllMealsModalProps> = ({
  visible,
  onClose,
  meals,
  onSelectMeal
}) => {
  const { colors, isDark } = useTheme();
  const [expandedMeal, setExpandedMeal] = useState<string | null>(null);

  // Toggle meal expansion
  const toggleMeal = (id: string) => {
    setExpandedMeal(expandedMeal === id ? null : id);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Render a meal item
  const renderMealItem = ({ item }: { item: MealItem }) => {
    const isExpanded = expandedMeal === item.id;

    return (
      <View
        style={[
          styles.mealItem,
          {
            backgroundColor: colors.card,
            borderColor: isDark ? colors.border : 'transparent',
            borderWidth: isDark ? 1 : 0,
          }
        ]}
      >
        <TouchableOpacity
          style={styles.mealHeader}
          onPress={() => toggleMeal(item.id)}
          activeOpacity={0.7}
        >
          <View style={styles.mealTitleContainer}>
            <Text style={[styles.mealTitle, { color: colors.text }]}>
              {item.title}
            </Text>
            <Text style={[styles.mealDate, { color: colors.textSecondary }]}>
              {formatDate(item.date)}
            </Text>
          </View>

          <View style={styles.mealActions}>
            <Text style={[styles.calorieCount, { color: colors.textSecondary }]}>
              {item.nutrition.calories} kcal
            </Text>
            <Ionicons
              name={isExpanded ? 'chevron-up' : 'chevron-down'}
              size={20}
              color={colors.textSecondary}
              style={styles.expandIcon}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.mealDetails}
          >
            <Text style={[styles.mealDescription, { color: colors.text }]}>
              {item.description}
            </Text>

            <View style={styles.nutritionContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition</Text>
              <View style={styles.nutritionGrid}>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.calories}</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.protein}g</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.carbs}g</Text>
                </View>
                <View style={styles.nutritionItem}>
                  <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>{item.nutrition.fat}g</Text>
                </View>
              </View>
            </View>

            {item.ingredients && item.ingredients.length > 0 && (
              <View style={styles.ingredientsContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
                {item.ingredients.map((ingredient, index) => (
                  <Text
                    key={`ingredient-${index}`}
                    style={[styles.ingredientItem, { color: colors.textSecondary }]}
                  >
                    • {ingredient}
                  </Text>
                ))}
              </View>
            )}

            <TouchableOpacity
              style={[styles.cookButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                onSelectMeal(item);
                onClose();
              }}
            >
              <Text style={[styles.cookButtonText, { color: colors.background }]}>
                Cook This Meal
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: isDark ? colors.border : 'rgba(0,0,0,0.1)' }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="arrow-back" size={24} color={colors.primary} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>All Meals</Text>
          <View style={styles.headerRight} />
        </View>

        {meals.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={40} color={colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>
              No meal history
            </Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              Log your meals to see your history here
            </Text>
          </View>
        ) : (
          <FlatList
            data={meals}
            renderItem={renderMealItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  closeButton: {
    padding: 8,
  },
  headerRight: {
    width: 40, // For balance with close button
  },
  listContent: {
    padding: 16,
    paddingBottom: 30,
  },
  mealItem: {
    borderRadius: 12,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  mealTitleContainer: {
    flex: 1,
  },
  mealTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  mealDate: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  mealActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  calorieCount: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginRight: 5,
  },
  expandIcon: {
    marginLeft: 5,
  },
  mealDetails: {
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  mealDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 15,
    lineHeight: 20,
  },
  nutritionContainer: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  nutritionItem: {
    width: '25%',
    paddingHorizontal: 5,
    marginBottom: 10,
  },
  nutritionLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  },
  nutritionValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  ingredientsContainer: {
    marginBottom: 15,
  },
  ingredientItem: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 5,
    paddingLeft: 5,
  },
  cookButton: {
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 5,
  },
  cookButtonText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 15,
    marginBottom: 5,
  },
  emptySubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },

});

export default AllMealsModal;
