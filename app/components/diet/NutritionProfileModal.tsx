import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  FlatList,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import {
  getNutritionProfile,
  saveNutritionProfile,
  NutritionProfile
} from '../../services/nutritionService';
import { getProfileFromStorage, saveProfile, UserProfile } from '../../services/profile';
import { useProfile } from '../../context/ProfileContext';

interface NutritionProfileModalProps {
  visible: boolean;
  onClose: () => void;
  onProfileUpdated: () => void;
}

const NutritionProfileModal: React.FC<NutritionProfileModalProps> = ({
  visible,
  onClose,
  onProfileUpdated,
}) => {
  const { colors, isDark } = useTheme();
  const { profile: userProfile, setProfile: updateUserProfile } = useProfile();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState<'male' | 'female' | 'other'>('male');
  const [activityLevel, setActivityLevel] = useState<string>('moderatelyActive');
  const [goal, setGoal] = useState<'maintain' | 'lose' | 'gain'>('maintain');
  const [dietaryPreferences, setDietaryPreferences] = useState<string[]>([]);

  // Common dietary preferences
  const commonDietaryPreferences = [
    { id: 'vegan', label: 'Vegan' },
    { id: 'vegetarian', label: 'Vegetarian' },
    { id: 'gluten-free', label: 'Gluten-Free' },
    { id: 'dairy-free', label: 'Dairy-Free' },
    { id: 'nut-free', label: 'Nut-Free' },
    { id: 'keto', label: 'Keto' },
    { id: 'paleo', label: 'Paleo' },
  ];

  // Activity level options
  const activityLevelOptions = [
    { value: 'sedentary', label: 'Sedentary (little or no exercise)' },
    { value: 'lightlyActive', label: 'Lightly Active (light exercise 1-3 days/week)' },
    { value: 'moderatelyActive', label: 'Moderately Active (moderate exercise 3-5 days/week)' },
    { value: 'veryActive', label: 'Very Active (hard exercise 6-7 days/week)' },
    { value: 'extraActive', label: 'Extra Active (very hard exercise & physical job)' },
  ];

  // Goal options
  const goalOptions = [
    { value: 'lose', label: 'Lose Weight' },
    { value: 'maintain', label: 'Maintain Weight' },
    { value: 'gain', label: 'Gain Weight' },
  ];

  // Load profile when modal opens
  useEffect(() => {
    if (visible) {
      loadProfile();
    }
  }, [visible]);

  // Calculate age from birthday
  const calculateAge = (birthday: string): number => {
    if (!birthday) {
      console.log('No birthday provided for age calculation');
      return 0;
    }

    console.log('Calculating age from birthday:', birthday);

    try {
      // Try different date formats
      let birthDate: Date;

      // Check if it's in ISO format (YYYY-MM-DD)
      if (birthday.includes('-')) {
        birthDate = new Date(birthday);
      }
      // Check if it's in MM/DD/YYYY format
      else if (birthday.includes('/')) {
        const parts = birthday.split('/');
        if (parts.length === 3) {
          // Convert MM/DD/YYYY to YYYY-MM-DD for more reliable parsing
          birthDate = new Date(`${parts[2]}-${parts[0]}-${parts[1]}`);
        } else {
          birthDate = new Date(birthday);
        }
      }
      // Try direct parsing as a fallback
      else {
        birthDate = new Date(birthday);
      }

      // Check if date is valid
      if (isNaN(birthDate.getTime())) {
        console.log('Invalid birthday date format:', birthday);
        return 0;
      }

      const today = new Date();

      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // If birthday hasn't occurred yet this year, subtract one year
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      console.log('Calculated age:', age, 'from birthdate:', birthDate.toISOString());
      return age;
    } catch (error) {
      console.error('Error calculating age:', error);
      return 0;
    }
  };

  const loadProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Get nutrition profile
      const nutritionProfile = await getNutritionProfile();

      // Get user profile to sync data - this is the source of truth
      const userProfileData = await getProfileFromStorage();

      if (!userProfileData) {
        setError('Please complete your profile first to use nutrition features');
        setIsLoading(false);
        return;
      }

      // Get the latest weight from weight entries if available
      let latestWeight = '';
      try {
        // First check if we have the latest weight in the nutrition profile cache
        const nutritionService = require('../../services/nutritionService');
        if (nutritionService.nutritionProfileCache?.profile?.weight) {
          latestWeight = nutritionService.nutritionProfileCache.profile.weight.toString();
          console.log('Using latest weight from nutrition profile cache:', latestWeight);
        } else {
          // If not in cache, get from weight entries
          const { getWeightEntries } = require('../../services/weightTracking');
          const weightEntries = await getWeightEntries();
          if (weightEntries.length > 0) {
            // Sort by date (newest first)
            const sortedEntries = [...weightEntries].sort(
              (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
            );
            latestWeight = sortedEntries[0].value.toString();
            console.log('Using latest weight from weight tracker:', latestWeight);
          }
        }
      } catch (weightError) {
        console.error('Error getting latest weight:', weightError);
      }

      // Get weight from latest weight entry, user profile, or nutrition profile
      const profileWeight = latestWeight || userProfileData.weight || nutritionProfile.weight?.toString() || '';
      console.log('Final weight value for nutrition profile modal:', profileWeight);

      // Get height from user profile
      const profileHeight = userProfileData.height || nutritionProfile.height?.toString() || '';

      // Calculate age from birthday in profile
      let profileAge = '';
      console.log('User profile birthday:', userProfileData.birthday);

      if (userProfileData.birthday) {
        const calculatedAge = calculateAge(userProfileData.birthday);
        console.log('Calculated age result:', calculatedAge);

        if (calculatedAge > 0) {
          profileAge = calculatedAge.toString();
          console.log('Setting profile age to:', profileAge);
        } else {
          console.log('Calculated age is 0 or negative, not setting age');
        }
      } else {
        console.log('No birthday found in user profile');
      }

      // Determine gender from user profile if available
      let profileGender: 'male' | 'female' | 'other' = nutritionProfile.gender || 'male';
      if (userProfileData.gender) {
        const gender = userProfileData.gender.toLowerCase();
        if (gender === 'male' || gender === 'female') {
          profileGender = gender as 'male' | 'female';
        } else {
          profileGender = 'other';
        }
      }

      // Set form values
      setWeight(profileWeight);
      setHeight(profileHeight);
      setAge(profileAge);
      setGender(profileGender);
      setActivityLevel(nutritionProfile.activityLevel || 'moderatelyActive');
      setGoal(nutritionProfile.goal || 'maintain');
      setDietaryPreferences(nutritionProfile.dietaryPreferences || []);
    } catch (error) {
      console.error('Error loading nutrition profile:', error);
      setError('Failed to load your nutrition profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);
    setSuccess(false);

    try {
      // Get current user profile for weight and other shared data
      const userProfileData = await getProfileFromStorage();

      if (!userProfileData) {
        setError('Please complete your profile first to use nutrition features');
        setIsSaving(false);
        return;
      }

      // Validate inputs
      if (!userProfileData.weight) {
        setError('Please update your weight in the Weight Tracker first');
        setIsSaving(false);
        return;
      }

      if (!height) {
        setError('Please enter your height');
        setIsSaving(false);
        return;
      }

      // Create nutrition profile object
      const nutritionProfile: NutritionProfile = {
        // Use weight from user profile
        weight: parseFloat(userProfileData.weight),
        activityLevel: activityLevel as any,
        goal: goal as 'maintain' | 'lose' | 'gain',
        dietaryPreferences,
      };

      // Log the weight value for debugging
      console.log(`Using weight value for nutrition profile: ${nutritionProfile.weight} (type: ${typeof nutritionProfile.weight})`);

      // Ensure weight is a valid number
      if (isNaN(nutritionProfile.weight) || nutritionProfile.weight <= 0) {
        setError('Please enter a valid weight value');
        setIsSaving(false);
        return;
      }

      // Add height from form
      nutritionProfile.height = parseFloat(height);

      // Calculate age from birthday in profile
      if (userProfileData.birthday) {
        const calculatedAge = calculateAge(userProfileData.birthday);
        if (calculatedAge > 0) {
          nutritionProfile.age = calculatedAge;
        }
      }

      // Add gender
      nutritionProfile.gender = gender;

      // Save nutrition profile
      await saveNutritionProfile(nutritionProfile);

      // Also update the main user profile to keep height in sync
      try {
        // Get current user profile
        const currentUserProfile = { ...userProfile };

        // Update height field
        if (height && height !== currentUserProfile.height) {
          currentUserProfile.height = height;

          // Save updated user profile
          await updateUserProfile(currentUserProfile);
        }
      } catch (profileError) {
        console.error('Error updating user profile:', profileError);
        // Continue even if user profile update fails
      }

      setSuccess(true);

      // Trigger immediate recalculation of nutrition targets
      try {
        const { getNutritionTargets } = require('../../services/nutritionService');
        // Force recalculation of nutrition targets with the new profile data
        await getNutritionTargets(true); // Pass true to force refresh
        console.log('Nutrition targets recalculated with new profile data');
      } catch (recalcError) {
        console.error('Error recalculating nutrition targets:', recalcError);
      }

      // Wait a moment to show success before closing
      setTimeout(() => {
        onProfileUpdated();
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error saving nutrition profile:', error);
      setError('An error occurred while saving your profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const toggleDietaryPreference = (preference: string) => {
    if (dietaryPreferences.includes(preference)) {
      setDietaryPreferences(dietaryPreferences.filter(p => p !== preference));
    } else {
      setDietaryPreferences([...dietaryPreferences, preference]);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
          <Animated.View
            entering={FadeIn.duration(300).springify()}
            style={[styles.modalContent, { backgroundColor: colors.card }]}
          >
            <View style={[
              styles.modalHeader,
              { borderBottomColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.06)' }
            ]}>
              <View style={styles.headerContent}>
                <Ionicons name="nutrition-outline" size={24} color={colors.primary} style={styles.headerIcon} />
                <Text style={[styles.modalTitle, { color: colors.text }]}>Nutrition Profile</Text>
              </View>
              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)' }]}
                onPress={onClose}
              >
                <Ionicons name="close" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <View style={[styles.loadingIconContainer, { backgroundColor: `${colors.primary}10` }]}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
                <Text style={[styles.loadingTitle, { color: colors.text }]}>
                  Loading Profile
                </Text>
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                  Please wait while we retrieve your nutrition profile...
                </Text>
              </View>
            ) : (
              <ScrollView style={styles.modalScrollContent}>
                {error && (
                  <Animated.View
                    entering={FadeIn.duration(300)}
                    style={[styles.statusContainer, { backgroundColor: 'rgba(255,59,48,0.08)', borderColor: 'rgba(255,59,48,0.2)' }]}
                  >
                    <View style={[styles.statusIconContainer, { backgroundColor: 'rgba(255,59,48,0.15)' }]}>
                      <Ionicons name="alert-circle" size={20} color="#FF3B30" />
                    </View>
                    <View style={styles.statusContent}>
                      <Text style={[styles.statusTitle, { color: '#FF3B30' }]}>Error</Text>
                      <Text style={[styles.statusText, { color: isDark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)' }]}>
                        {error}
                      </Text>
                    </View>
                  </Animated.View>
                )}

                {success && (
                  <Animated.View
                    entering={FadeIn.duration(300)}
                    style={[styles.statusContainer, { backgroundColor: `${colors.primary}08`, borderColor: `${colors.primary}20` }]}
                  >
                    <View style={[styles.statusIconContainer, { backgroundColor: `${colors.primary}15` }]}>
                      <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
                    </View>
                    <View style={styles.statusContent}>
                      <Text style={[styles.statusTitle, { color: colors.primary }]}>Success</Text>
                      <Text style={[styles.statusText, { color: isDark ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)' }]}>
                        Profile updated successfully!
                      </Text>
                    </View>
                  </Animated.View>
                )}

                <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Weight (lbs)</Text>
                  <View style={[
                    styles.inputContainer,
                    { backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)' }
                  ]}>
                    <Ionicons name="fitness-outline" size={20} color={colors.textSecondary} style={styles.inputIcon} />
                    <Text
                      style={[
                        styles.readOnlyInput,
                        {
                          color: weight ? colors.text : colors.textSecondary,
                          textAlign: 'left'
                        }
                      ]}
                    >
                      {weight ? `${weight} lbs` : 'No weight data available - update in Weight Tracker'}
                    </Text>
                  </View>
                  <View style={[styles.infoContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)' }]}>
                    <Ionicons name="information-circle-outline" size={16} color={colors.textSecondary} style={{ marginRight: 6 }} />
                    <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                      Weight can be updated in the Weight Tracker section
                    </Text>
                  </View>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Height (inches)</Text>
                  <View style={styles.inputContainer}>
                    <Ionicons name="resize-outline" size={20} color={colors.primary} style={styles.inputIcon} />
                    <TextInput
                      style={[
                        styles.input,
                        {
                          color: colors.text,
                          backgroundColor: 'transparent',
                        }
                      ]}
                      value={height}
                      onChangeText={setHeight}
                      placeholder="Enter height in inches"
                      placeholderTextColor={colors.textTertiary}
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={[styles.infoContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)' }]}>
                    <Ionicons name="sync-outline" size={16} color={colors.textSecondary} style={{ marginRight: 6 }} />
                    <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                      Height is synchronized with your profile
                    </Text>
                  </View>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Age</Text>
                  <View style={[
                    styles.inputContainer,
                    { backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)' }
                  ]}>
                    <Ionicons name="calendar-outline" size={20} color={colors.textSecondary} style={styles.inputIcon} />
                    <Text
                      style={[
                        styles.readOnlyInput,
                        {
                          color: age ? colors.text : colors.textSecondary,
                          textAlign: 'left'
                        }
                      ]}
                    >
                      {age || 'No age data available - update birthday in profile'}
                    </Text>
                  </View>
                  <View style={[styles.infoContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)' }]}>
                    <Ionicons name="information-circle-outline" size={16} color={colors.textSecondary} style={{ marginRight: 6 }} />
                    <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                      Age is automatically calculated from your birthday in profile
                    </Text>
                  </View>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Gender</Text>
                  <View style={[
                    styles.segmentedControl,
                    { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.03)' }
                  ]}>
                    <TouchableOpacity
                      style={[
                        styles.segmentedOption,
                        gender === 'male' && [
                          styles.segmentedOptionActive,
                          {
                            borderColor: colors.primary,
                            backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.9)'
                          }
                        ]
                      ]}
                      onPress={() => setGender('male')}
                    >
                      <Ionicons
                        name="male"
                        size={18}
                        color={gender === 'male' ? colors.primary : colors.textSecondary}
                        style={styles.segmentedOptionIcon}
                      />
                      <Text style={[
                        styles.segmentedOptionText,
                        { color: gender === 'male' ? colors.primary : colors.text }
                      ]}>
                        Male
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.segmentedOption,
                        gender === 'female' && [
                          styles.segmentedOptionActive,
                          {
                            borderColor: colors.primary,
                            backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.9)'
                          }
                        ]
                      ]}
                      onPress={() => setGender('female')}
                    >
                      <Ionicons
                        name="female"
                        size={18}
                        color={gender === 'female' ? colors.primary : colors.textSecondary}
                        style={styles.segmentedOptionIcon}
                      />
                      <Text style={[
                        styles.segmentedOptionText,
                        { color: gender === 'female' ? colors.primary : colors.text }
                      ]}>
                        Female
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.segmentedOption,
                        gender === 'other' && [
                          styles.segmentedOptionActive,
                          {
                            borderColor: colors.primary,
                            backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.9)'
                          }
                        ]
                      ]}
                      onPress={() => setGender('other')}
                    >
                      <Ionicons
                        name="person"
                        size={18}
                        color={gender === 'other' ? colors.primary : colors.textSecondary}
                        style={styles.segmentedOptionIcon}
                      />
                      <Text style={[
                        styles.segmentedOptionText,
                        { color: gender === 'other' ? colors.primary : colors.text }
                      ]}>
                        Other
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 20 }]}>Activity & Goals</Text>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Activity Level</Text>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.activityLevelContainer}
                  >
                    {activityLevelOptions.map(option => {
                      const isSelected = activityLevel === option.value;
                      let iconName = 'body-outline';

                      // Choose appropriate icon based on activity level
                      if (option.value === 'sedentary') iconName = 'bed-outline';
                      else if (option.value === 'lightlyActive') iconName = 'walk-outline';
                      else if (option.value === 'moderatelyActive') iconName = 'bicycle-outline';
                      else if (option.value === 'veryActive') iconName = 'barbell-outline';
                      else if (option.value === 'extraActive') iconName = 'fitness-outline';

                      return (
                        <TouchableOpacity
                          key={option.value}
                          style={[
                            styles.activityCard,
                            {
                              borderColor: isSelected ? colors.primary : 'transparent',
                              backgroundColor: isDark
                                ? isSelected ? `${colors.primary}15` : 'rgba(255,255,255,0.05)'
                                : isSelected ? `${colors.primary}10` : 'rgba(0,0,0,0.03)',
                            }
                          ]}
                          onPress={() => setActivityLevel(option.value)}
                        >
                          <View style={[
                            styles.activityIconContainer,
                            {
                              backgroundColor: isSelected
                                ? `${colors.primary}20`
                                : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                            }
                          ]}>
                            <Ionicons
                              name={iconName}
                              size={22}
                              color={isSelected ? colors.primary : colors.textSecondary}
                            />
                          </View>
                          <Text
                            style={[
                              styles.activityCardTitle,
                              { color: isSelected ? colors.primary : colors.text }
                            ]}
                            numberOfLines={2}
                          >
                            {option.label}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                </View>

                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Goal</Text>
                  <View style={styles.goalContainer}>
                    {goalOptions.map(option => {
                      const isSelected = goal === option.value;
                      let iconName = 'trending-up';

                      // Choose appropriate icon based on goal
                      if (option.value === 'lose') iconName = 'trending-down';
                      else if (option.value === 'maintain') iconName = 'remove-outline';
                      else if (option.value === 'gain') iconName = 'trending-up';

                      return (
                        <TouchableOpacity
                          key={option.value}
                          style={[
                            styles.goalCard,
                            {
                              borderColor: isSelected ? colors.primary : 'transparent',
                              backgroundColor: isDark
                                ? isSelected ? `${colors.primary}15` : 'rgba(255,255,255,0.05)'
                                : isSelected ? `${colors.primary}10` : 'rgba(0,0,0,0.03)',
                            }
                          ]}
                          onPress={() => setGoal(option.value as any)}
                        >
                          <View style={[
                            styles.goalIconContainer,
                            {
                              backgroundColor: isSelected
                                ? `${colors.primary}20`
                                : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                            }
                          ]}>
                            <Ionicons
                              name={iconName}
                              size={22}
                              color={isSelected ? colors.primary : colors.textSecondary}
                            />
                          </View>
                          <Text
                            style={[
                              styles.goalCardTitle,
                              { color: isSelected ? colors.primary : colors.text }
                            ]}
                          >
                            {option.label}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}
                  </View>
                </View>

                <View style={[styles.noteContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }]}>
                  <View style={[styles.noteIconContainer, { backgroundColor: `${colors.primary}15` }]}>
                    <Ionicons name="chatbubble-ellipses-outline" size={20} color={colors.primary} />
                  </View>
                  <View style={styles.noteContent}>
                    <Text style={[styles.noteTitle, { color: colors.text }]}>
                      Chat with Lotus
                    </Text>
                    <Text style={[styles.noteText, { color: colors.textSecondary }]}>
                      To add dietary restrictions, define specific goals, or customize your nutrition plan further,
                      simply tell Lotus in a conversation.
                    </Text>
                  </View>
                </View>

                {isSaving ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                    <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                      Saving your profile...
                    </Text>
                  </View>
                ) : (
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity
                      style={[styles.saveButton, { backgroundColor: colors.primary }]}
                      onPress={handleSave}
                    >
                      <Ionicons name="checkmark-circle-outline" size={20} color={colors.background} style={styles.buttonIcon} />
                      <Text style={[styles.saveButtonText, { color: colors.background }]}>
                        Save Profile
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.cancelButton,
                        {
                          backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                          borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                        }
                      ]}
                      onPress={onClose}
                    >
                      <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </ScrollView>
            )}
          </Animated.View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 24,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 10,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalScrollContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 10,
    opacity: 0.8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    borderRadius: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: 56,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  segmentedControl: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 16,
    padding: 4,
  },
  segmentedOption: {
    flex: 1,
    flexDirection: 'row',
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginHorizontal: 2,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  segmentedOptionActive: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  segmentedOptionIcon: {
    marginRight: 6,
  },
  segmentedOptionText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  activityLevelContainer: {
    paddingVertical: 8,
  },
  activityCard: {
    padding: 16,
    borderRadius: 16,
    marginRight: 12,
    width: 160,
    borderWidth: 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  activityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  activityCardTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
  goalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  goalCard: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    marginHorizontal: 4,
    borderWidth: 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  goalIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  goalCardTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    textAlign: 'center',
  },
  noteContainer: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 16,
    marginBottom: 24,
    marginTop: 8,
    alignItems: 'flex-start',
  },
  noteIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  noteContent: {
    flex: 1,
  },
  noteTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  noteText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 12,
  },
  saveButton: {
    height: 56,
    borderRadius: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  cancelButton: {
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
  },
  statusIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statusContent: {
    flex: 1,
  },
  statusTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    lineHeight: 18,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  loadingIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loadingTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 8,
  },
  loadingText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: 22,
  },
  readOnlyInput: {
    flex: 1,
    height: 56,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    paddingVertical: 16,
    alignSelf: 'center',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 10,
    borderRadius: 8,
  },
  infoText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    flex: 1,
  },
});

export default NutritionProfileModal;
