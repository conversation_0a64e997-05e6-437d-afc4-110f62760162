import React, { useState } from 'react';
import { View, Text, StyleSheet, Dimensions, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTheme } from '@react-navigation/native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeIn } from 'react-native-reanimated';
import InfoModal from '../ui/InfoModal';

// We'll use a simplified approach without victory-native for now
// This will be a placeholder until we can properly integrate the charts
const VictoryChart = null; // Setting to null to trigger our fallback UI

interface IntakeBurnChartProps {
  intake: { date: string; calories: number }[];
  burn: { date: string; calories: number }[];
  isLoading: boolean;
}

const IntakeBurnChart: React.FC<IntakeBurnChartProps> = ({
  intake,
  burn,
  isLoading
}) => {
  const { colors } = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [showInfoModal, setShowInfoModal] = useState(false);
  const screenWidth = Dimensions.get('window').width;

  // Check if we have enough data (at least 3 days of data for a meaningful chart)
  const hasEnoughData = intake && burn && intake.length >= 3 && burn.length >= 3;

  // We'll keep the component but show a message if there's not enough data

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Prepare data for chart
  const chartData = intake && burn ? intake.map((item, index) => {
    const burnValue = burn[index]?.calories || 0;
    return {
      date: item.date,
      formattedDate: formatDate(item.date),
      intake: item.calories,
      burn: burnValue,
      // Calculate net calories (intake - burn)
      net: item.calories - burnValue
    };
  }) : [];

  // Sort by date
  chartData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Limit to last 7 days unless expanded
  const displayData = expanded ? chartData : chartData.slice(-7);

  // Calculate domain padding based on data
  const maxValue = displayData.length > 0 ? Math.max(
    ...displayData.map(d => Math.max(d.intake, d.burn)),
    1000 // Minimum value for better visualization
  ) : 1000;

  return (
    <Animated.View
      entering={FadeIn.duration(600).delay(200)}
      style={[
        styles.container,
        { backgroundColor: colors.card }
      ]}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            Intake vs. Burn
          </Text>
          <TouchableOpacity
            onPress={() => setShowInfoModal(true)}
            style={styles.infoButton}
          >
            <Ionicons name="information-circle-outline" size={18} color={colors.primary} />
          </TouchableOpacity>
        </View>
        {hasEnoughData && (
          <TouchableOpacity
            onPress={() => setExpanded(!expanded)}
            style={styles.expandButton}
          >
            <Text style={[styles.expandText, { color: colors.primary }]}>
              {expanded ? 'Show Less' : 'Show More'}
            </Text>
            <Ionicons
              name={expanded ? 'chevron-up' : 'chevron-down'}
              size={16}
              color={colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>

      <View style={[styles.chartContainer, !hasEnoughData && !isLoading ? styles.compactContainer : null]}>
        {isLoading ? (
          <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
        ) : !hasEnoughData ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: colors.text }]}>
              More data required
            </Text>
            <Text style={[styles.noDataSubtext, { color: colors.primary }]}>
              Log meals for at least 3 days
            </Text>
          </View>
        ) : !VictoryChart ? (
          <View style={styles.fallbackContainer}>
            <Text style={[styles.fallbackText, { color: colors.text }]}>
              Chart visualization unavailable
            </Text>
            <Text style={[styles.fallbackSubtext, { color: colors.text, opacity: 0.7 }]}>
              Latest intake: {displayData[displayData.length - 1]?.intake || 0} kcal
            </Text>
          </View>
        ) : (
          <VictoryChart
            width={screenWidth - 48}
            height={expanded ? 300 : 220}
            padding={{ top: 20, bottom: 40, left: 50, right: 20 }}
            domainPadding={{ x: 20, y: [0, 20] }}
            containerComponent={
              <VictoryVoronoiContainer
                voronoiDimension="x"
                labels={({ datum }) => `${datum.formattedDate}\nIntake: ${Math.round(datum.intake)} kcal\nBurn: ${Math.round(datum.burn)} kcal`}
                labelComponent={
                  <VictoryTooltip
                    cornerRadius={5}
                    flyoutStyle={{
                      fill: colors.card,
                      stroke: colors.border,
                      strokeWidth: 1,
                    }}
                    style={{ fill: colors.text, fontSize: 10 }}
                  />
                }
              />
            }
          >
            {/* X-axis (dates) */}
            <VictoryAxis
              tickFormat={(t, i) => {
                // Show fewer ticks on smaller charts
                const interval = expanded ? 1 : 2;
                return i % interval === 0 ? displayData[i]?.formattedDate.split(' ')[1] : '';
              }}
              style={{
                axis: { stroke: colors.border },
                ticks: { stroke: colors.border, size: 5 },
                tickLabels: { fill: colors.text, fontSize: 10 }
              }}
            />

            {/* Y-axis (calories) */}
            <VictoryAxis
              dependentAxis
              tickFormat={(t) => `${Math.round(t / 1000)}k`}
              style={{
                axis: { stroke: colors.border },
                ticks: { stroke: colors.border, size: 5 },
                tickLabels: { fill: colors.text, fontSize: 10 }
              }}
              domain={[0, maxValue * 1.1]}
            />

            {/* Intake bars */}
            <VictoryBar
              data={displayData}
              x="formattedDate"
              y="intake"
              style={{
                data: {
                  fill: colors.primary,
                  width: 12
                }
              }}
              animate={{
                duration: 500,
                onLoad: { duration: 500 }
              }}
            />

            {/* Burn line */}
            <VictoryLine
              data={displayData}
              x="formattedDate"
              y="burn"
              style={{
                data: {
                  stroke: colors.notification,
                  strokeWidth: 2
                }
              }}
              animate={{
                duration: 500,
                onLoad: { duration: 500 }
              }}
            />
          </VictoryChart>
        )}
      </View>

      {hasEnoughData && (
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>Intake</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendLine, { backgroundColor: colors.notification }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>Burn</Text>
          </View>
        </View>
      )}

      {/* Info Modal */}
      <InfoModal
        visible={showInfoModal}
        onClose={() => setShowInfoModal(false)}
        title="Intake vs. Burn"
        icon="bar-chart-outline"
        content={
          <View>
            <Text style={[styles.modalText, { color: colors.text }]}>
              The Intake vs. Burn chart shows your daily calorie consumption compared to your estimated calorie expenditure.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              <Text style={{ fontWeight: 'bold' }}>Calorie Intake (bars):</Text> The total calories from all meals logged on each day.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              <Text style={{ fontWeight: 'bold' }}>Calorie Burn (line):</Text> Your estimated maintenance calories for each day, representing how many calories your body burns.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 16 }]}>
              <Text style={{ fontWeight: 'bold' }}>How to interpret this chart:</Text>
            </Text>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                When the bar is higher than the line (intake {'>'} burn), you're in a calorie surplus, which may lead to weight gain
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                When the bar is lower than the line (intake {'<'} burn), you're in a calorie deficit, which may lead to weight loss
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                When the bar and line are approximately equal, you're maintaining your current weight
              </Text>
            </View>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 16 }]}>
              <Text style={{ fontWeight: 'bold' }}>Relationship to TDEE:</Text>
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 8 }]}>
              Your Total Daily Energy Expenditure (TDEE) is represented by the "Burn" line. This value is calculated based on your activity level, body composition, and metabolic rate.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              Consistent patterns in this chart help determine your maintenance calories and can guide your nutrition strategy for weight management goals.
            </Text>
          </View>
        }
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoButton: {
    marginLeft: 6,
    padding: 2,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandText: {
    fontSize: 12,
    marginRight: 4,
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 220,
  },
  compactContainer: {
    height: 70, // Even smaller height when no data
  },
  loader: {
    marginVertical: 60,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  noDataSubtext: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
    textAlign: 'center',
  },
  fallbackContainer: {
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fallbackText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  fallbackSubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 2,
    marginRight: 6,
  },
  legendLine: {
    width: 16,
    height: 2,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
  },
  modalText: {
    fontSize: 15,
    lineHeight: 22,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginTop: 6,
    paddingLeft: 8,
  },
  bulletPoint: {
    fontSize: 15,
    marginRight: 8,
  },
  bulletPointText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
});

export default IntakeBurnChart;
