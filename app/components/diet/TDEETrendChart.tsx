import React, { useState } from 'react';
import { View, Text, StyleSheet, Dimensions, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTheme } from '@react-navigation/native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeIn } from 'react-native-reanimated';
import InfoModal from '../ui/InfoModal';

// We'll use a simplified approach without victory-native for now
// This will be a placeholder until we can properly integrate the charts
const VictoryChart = null; // Setting to null to trigger our fallback UI

interface TDEETrendChartProps {
  trend: { date: string; tdee: number }[];
  isLoading: boolean;
}

const TDEETrendChart: React.FC<TDEETrendChartProps> = ({
  trend,
  isLoading
}) => {
  const { colors } = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [showInfoModal, setShowInfoModal] = useState(false);
  const screenWidth = Dimensions.get('window').width;

  // Check if we have enough data (at least 7 days of data for a meaningful trend)
  const hasEnoughData = trend && trend.length >= 7;

  // We'll keep the component but show a message if there's not enough data

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Prepare data for chart
  const chartData = trend ? trend.map(item => ({
    date: item.date,
    formattedDate: formatDate(item.date),
    tdee: item.tdee
  })) : [];

  // Sort by date
  chartData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Limit to last 14 days unless expanded
  const displayData = expanded ? chartData : chartData.slice(-14);

  // Calculate domain for y-axis
  const tdeeValues = displayData.map(d => d.tdee);
  const minTDEE = tdeeValues.length > 0 ? Math.min(...tdeeValues, 1500) : 1500;
  const maxTDEE = tdeeValues.length > 0 ? Math.max(...tdeeValues, 2500) : 2500;
  const padding = (maxTDEE - minTDEE) * 0.1;

  return (
    <Animated.View
      entering={FadeIn.duration(600).delay(300)}
      style={[
        styles.container,
        { backgroundColor: colors.card }
      ]}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            TDEE Trend
          </Text>
          <TouchableOpacity
            onPress={() => setShowInfoModal(true)}
            style={styles.infoButton}
          >
            <Ionicons name="information-circle-outline" size={18} color={colors.primary} />
          </TouchableOpacity>
        </View>
        {hasEnoughData && (
          <TouchableOpacity
            onPress={() => setExpanded(!expanded)}
            style={styles.expandButton}
          >
            <Text style={[styles.expandText, { color: colors.primary }]}>
              {expanded ? 'Show Less' : 'Show More'}
            </Text>
            <Ionicons
              name={expanded ? 'chevron-up' : 'chevron-down'}
              size={16}
              color={colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>

      <View style={[styles.chartContainer, !hasEnoughData && !isLoading ? styles.compactContainer : null]}>
        {isLoading ? (
          <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
        ) : !hasEnoughData ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: colors.text }]}>
              More data required
            </Text>
            <Text style={[styles.noDataSubtext, { color: colors.primary }]}>
              Log meals and weight for at least 7 days
            </Text>
          </View>
        ) : !VictoryChart ? (
          <View style={styles.fallbackContainer}>
            <Text style={[styles.fallbackText, { color: colors.text }]}>
              Chart visualization unavailable
            </Text>
            <Text style={[styles.fallbackSubtext, { color: colors.text, opacity: 0.7 }]}>
              Current TDEE: {displayData[displayData.length - 1]?.tdee || 0} kcal
            </Text>
          </View>
        ) : (
          <VictoryChart
            width={screenWidth - 48}
            height={expanded ? 250 : 180}
            padding={{ top: 20, bottom: 40, left: 50, right: 20 }}
            domainPadding={{ x: 10, y: 10 }}
            containerComponent={
              <VictoryVoronoiContainer
                voronoiDimension="x"
                labels={({ datum }) => `${datum.formattedDate}\nTDEE: ${Math.round(datum.tdee)} kcal`}
                labelComponent={
                  <VictoryTooltip
                    cornerRadius={5}
                    flyoutStyle={{
                      fill: colors.card,
                      stroke: colors.border,
                      strokeWidth: 1,
                    }}
                    style={{ fill: colors.text, fontSize: 10 }}
                  />
                }
              />
            }
          >
            {/* X-axis (dates) */}
            <VictoryAxis
              tickFormat={(t, i) => {
                // Show fewer ticks on smaller charts
                const interval = expanded ? 2 : 3;
                return i % interval === 0 ? displayData[i]?.formattedDate.split(' ')[1] : '';
              }}
              style={{
                axis: { stroke: colors.border },
                ticks: { stroke: colors.border, size: 5 },
                tickLabels: { fill: colors.text, fontSize: 10 }
              }}
            />

            {/* Y-axis (TDEE) */}
            <VictoryAxis
              dependentAxis
              tickFormat={(t) => `${Math.round(t / 1000)}k`}
              style={{
                axis: { stroke: colors.border },
                ticks: { stroke: colors.border, size: 5 },
                tickLabels: { fill: colors.text, fontSize: 10 }
              }}
              domain={[
                Math.max(minTDEE - padding, 0),
                maxTDEE + padding
              ]}
            />

            {/* TDEE line */}
            <VictoryLine
              data={displayData}
              x="formattedDate"
              y="tdee"
              style={{
                data: {
                  stroke: colors.primary,
                  strokeWidth: 2
                }
              }}
              animate={{
                duration: 500,
                onLoad: { duration: 500 }
              }}
            />
          </VictoryChart>
        )}
      </View>

      <Text style={[styles.subtitle, { color: colors.text }]}>
        {hasEnoughData
          ? 'Your metabolism changes over time based on activity, diet, and other factors'
          : 'For accurate TDEE trend analysis'}
      </Text>

      {/* Info Modal */}
      <InfoModal
        visible={showInfoModal}
        onClose={() => setShowInfoModal(false)}
        title="TDEE Trend"
        icon="trending-up-outline"
        content={
          <View>
            <Text style={[styles.modalText, { color: colors.text }]}>
              TDEE (Total Daily Energy Expenditure) is the total number of calories your body burns each day, including resting metabolism, daily activities, and exercise.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              <Text style={{ fontWeight: 'bold' }}>How TDEE is calculated:</Text>
            </Text>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Your calorie intake from logged meals
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Changes in your weight over time
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                The formula: TDEE = Average Intake - Weight Change (in calories)
              </Text>
            </View>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 16 }]}>
              <Text style={{ fontWeight: 'bold' }}>Why TDEE changes over time:</Text>
            </Text>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Changes in activity level (more/less exercise)
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Changes in body composition (more muscle burns more calories)
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Metabolic adaptation to diet changes
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Hormonal changes and aging
              </Text>
            </View>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 16 }]}>
              <Text style={{ fontWeight: 'bold' }}>Relationship to other metrics:</Text>
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 8 }]}>
              Your TDEE is the same as your "Maintenance Calories" - the amount you need to eat to maintain your current weight. The "Burn" line in the Intake vs. Burn chart represents your TDEE for each day.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              Tracking your TDEE trend helps you understand how your metabolism responds to changes in diet, exercise, and lifestyle, allowing for more accurate nutrition planning.
            </Text>
          </View>
        }
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoButton: {
    marginLeft: 6,
    padding: 2,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandText: {
    fontSize: 12,
    marginRight: 4,
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 180,
  },
  compactContainer: {
    height: 70, // Even smaller height when no data
  },
  loader: {
    marginVertical: 60,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  noDataSubtext: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
    textAlign: 'center',
  },
  fallbackContainer: {
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fallbackText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  fallbackSubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  subtitle: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.6,
    marginTop: 8,
  },
  modalText: {
    fontSize: 15,
    lineHeight: 22,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginTop: 6,
    paddingLeft: 8,
  },
  bulletPoint: {
    fontSize: 15,
    marginRight: 8,
  },
  bulletPointText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
});

export default TDEETrendChart;
