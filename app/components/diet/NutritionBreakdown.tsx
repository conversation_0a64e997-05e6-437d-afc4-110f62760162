import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import EditMealModal from './EditMealModal';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface NutritionBreakdownProps {
  macros: {
    calories: { current: number; target: number };
    protein: { current: number; target: number };
    carbs: { current: number; target: number };
    fat: { current: number; target: number };
  };
  meals: {
    id: string;
    title: string;
    timestamp: string;
    nutrition: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    ingredients?: string[];
    steps?: string[];
    description?: string;
  }[];
  onMealUpdated?: () => void;
  isInsideScrollView?: boolean; // Flag to prevent nested virtualized lists
}

const NutritionBreakdown: React.FC<NutritionBreakdownProps> = ({ macros, meals, onMealUpdated, isInsideScrollView = true }) => {
  const { colors, isDark } = useTheme();
  const [expanded, setExpanded] = useState(false); // Start collapsed by default
  const [selectedMeal, setSelectedMeal] = useState<any>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  const toggleExpanded = () => {
    // Remove LayoutAnimation to prevent layout shifts
    setExpanded(!expanded);
  };

  const handleMealSelect = (meal: any) => {
    setSelectedMeal(meal);
    setShowEditModal(true);
  };

  const handleMealUpdated = () => {
    if (onMealUpdated) {
      onMealUpdated();
    }
  };

  const formatTime = (timestamp: string) => {
    try {
      // Check if timestamp is valid
      if (!timestamp) {
        console.warn('Invalid timestamp provided to formatTime:', timestamp);
        return 'Unknown time';
      }

      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date created from timestamp:', timestamp);
        return 'Unknown time';
      }

      // Format the time
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      console.error('Error formatting time:', error, timestamp);
      return 'Unknown time';
    }
  };

  const formatDate = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Today';
      }

      // Check if it's today
      const today = new Date();
      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      }

      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    } catch (error) {
      console.error('Error formatting date:', error, timestamp);
      return 'Today';
    }
  };

  // Render a macro ring
  const renderMacroRing = (
    label: string,
    current: number,
    target: number,
    color: string,
    size: number = 70
  ) => {
    const percentage = Math.min(current / target, 1);
    const radius = size / 2 - 10;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference * (1 - percentage);

    return (
      <View style={styles.macroRingContainer}>
        <View style={styles.ringContainer}>
          <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            {/* Background circle */}
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke={isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}
              strokeWidth={5}
              fill="transparent"
            />

            {/* Progress circle */}
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke={color}
              strokeWidth={5}
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              fill="transparent"
              transform={`rotate(-90, ${size / 2}, ${size / 2})`}
            />

            {/* Value text */}
            <SvgText
              x={size / 2}
              y={size / 2 + 5}
              fontSize="14"
              fontWeight="bold"
              fill={colors.text}
              textAnchor="middle"
            >
              {Math.round(percentage * 100)}
            </SvgText>
          </Svg>
        </View>

        <Text style={[styles.macroLabel, { color: colors.text }]}>{label}</Text>
        <Text style={[styles.macroValue, { color: colors.textSecondary }]}>
          {current}/{target}
        </Text>
      </View>
    );
  };

  const renderMealItem = ({ item }: { item: NutritionBreakdownProps['meals'][0] }) => {
    // Ensure nutrition values are numbers
    const calories = typeof item.nutrition.calories === 'number' ? item.nutrition.calories : 0;
    const protein = typeof item.nutrition.protein === 'number' ? item.nutrition.protein : 0;
    const carbs = typeof item.nutrition.carbs === 'number' ? item.nutrition.carbs : 0;
    const fat = typeof item.nutrition.fat === 'number' ? item.nutrition.fat : 0;

    return (
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => handleMealSelect(item)}
      >
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(200)}
          style={[
            styles.mealItem,
            {
              backgroundColor: isDark ? colors.background : colors.card,
              borderColor: `${colors.border}80`,
            },
          ]}
        >
          <View style={styles.mealItemContent}>
            {/* Left side - Meal icon and info */}
            <View style={styles.mealItemLeft}>
              <View style={[styles.mealIconContainer, { backgroundColor: `${colors.primary}15` }]}>
                <Ionicons name="restaurant" size={18} color={colors.primary} />
              </View>
              <View style={styles.mealInfo}>
                <Text style={[styles.mealTitle, { color: colors.text }]} numberOfLines={1}>
                  {item.title || 'Meal'}
                </Text>
                <Text style={[styles.mealTime, { color: colors.textSecondary }]}>
                  {formatTime(item.timestamp)}
                </Text>
              </View>
            </View>

            {/* Right side - Calories and edit icon */}
            <View style={styles.mealItemRight}>
              <Text style={[styles.mealCalories, { color: colors.text }]}>
                {calories} <Text style={{ fontSize: typography.sizes.xs, color: colors.textSecondary }}>kcal</Text>
              </Text>
              <Ionicons
                name="pencil"
                size={16}
                color={colors.primary}
                style={{ marginLeft: 8, opacity: 0.7 }}
              />
            </View>
          </View>

          {/* Nutrition bar */}
          <View style={styles.nutritionBarContainer}>
            {/* Protein bar */}
            {protein > 0 && (
              <View
                style={[
                  styles.nutritionBar,
                  {
                    backgroundColor: '#4ECDC4',
                    flex: Math.max(protein, 0.1),
                    marginRight: (carbs > 0 || fat > 0) ? 1 : 0
                  }
                ]}
              />
            )}

            {/* Carbs bar */}
            {carbs > 0 && (
              <View
                style={[
                  styles.nutritionBar,
                  {
                    backgroundColor: '#FFD166',
                    flex: Math.max(carbs, 0.1),
                    marginRight: fat > 0 ? 1 : 0
                  }
                ]}
              />
            )}

            {/* Fat bar */}
            {fat > 0 && (
              <View
                style={[
                  styles.nutritionBar,
                  {
                    backgroundColor: '#6B5CA5',
                    flex: Math.max(fat, 0.1)
                  }
                ]}
              />
            )}

            {/* Show empty bar if all values are 0 */}
            {protein === 0 && carbs === 0 && fat === 0 && (
              <View
                style={[
                  styles.nutritionBar,
                  {
                    backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                    flex: 1
                  }
                ]}
              />
            )}
          </View>

          {/* Nutrition details */}
          <View style={styles.mealNutrition}>
            <View style={[styles.nutritionItem, { flex: 1 }]}>
              <View style={[styles.nutritionDot, { backgroundColor: '#4ECDC4' }]} />
              <Text style={[styles.nutritionValue, { color: colors.text }]} numberOfLines={1}>
                {protein}g <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>protein</Text>
              </Text>
            </View>

            <View style={[styles.nutritionItem, { flex: 1, marginHorizontal: 4 }]}>
              <View style={[styles.nutritionDot, { backgroundColor: '#FFD166' }]} />
              <Text style={[styles.nutritionValue, { color: colors.text }]} numberOfLines={1}>
                {carbs}g <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>carbs</Text>
              </Text>
            </View>

            <View style={[styles.nutritionItem, { flex: 1 }]}>
              <View style={[styles.nutritionDot, { backgroundColor: '#6B5CA5' }]} />
              <Text style={[styles.nutritionValue, { color: colors.text }]} numberOfLines={1}>
                {fat}g <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>fat</Text>
              </Text>
            </View>
          </View>
        </Animated.View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Edit Meal Modal */}
      <EditMealModal
        visible={showEditModal}
        onClose={() => setShowEditModal(false)}
        onMealUpdated={handleMealUpdated}
        meal={selectedMeal}
      />

      <Animated.View
        style={[
          styles.card,
          {
            backgroundColor: colors.card,
            shadowColor: colors.text,
            transform: [{ scale: expanded ? 1 : 0.99 }]
          }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.header,
            {
              borderBottomColor: expanded ? colors.border : 'transparent',
              borderBottomWidth: expanded ? 1 : 0,
            },
          ]}
          onPress={toggleExpanded}
          activeOpacity={0.7}
        >
          <View style={styles.headerContent}>
            <Animated.View
              style={{
                backgroundColor: `${colors.primary}20`,
                padding: 8,
                borderRadius: 12,
                transform: [{ rotate: expanded ? '0deg' : '0deg' }]
              }}
            >
              <Ionicons
                name="nutrition-outline"
                size={20}
                color={colors.primary}
              />
            </Animated.View>
            <Text style={[styles.headerTitle, { color: colors.text }]}>Daily Nutrition</Text>
          </View>

          <View style={styles.headerRight}>
            <Text style={[styles.calorieText, { color: colors.textSecondary }]}>
              {macros.calories.current} / {macros.calories.target} kcal
            </Text>
            <Animated.View
              style={{
                transform: [{ rotate: expanded ? '180deg' : '0deg' }]
              }}
            >
              <Ionicons
                name="chevron-down"
                size={20}
                color={colors.textSecondary}
              />
            </Animated.View>
          </View>
        </TouchableOpacity>

        {/* Always visible macro rings */}
        <View style={styles.macroRingsContainer}>
          <View style={styles.macroRings}>
            {renderMacroRing('Calories', macros.calories.current, macros.calories.target, '#FF6B6B')}
            {renderMacroRing('Protein', macros.protein.current, macros.protein.target, '#4ECDC4')}
            {renderMacroRing('Carbs', macros.carbs.current, macros.carbs.target, '#FFD166')}
            {renderMacroRing('Fat', macros.fat.current, macros.fat.target, '#6B5CA5')}
          </View>
        </View>
      </Animated.View>

      {expanded ? (
        <Animated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(200)}
          style={[
            styles.expandedContent,
            {
              backgroundColor: colors.card,
              borderBottomLeftRadius: 15,
              borderBottomRightRadius: 15,
              shadowColor: colors.text,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3,
            }
          ]}
        >
          <View style={styles.macroSummary}>
            <Animated.View
              entering={FadeIn.duration(400).delay(150)}
              style={styles.macroItem}
            >
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Protein</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {macros.protein.current}g / {macros.protein.target}g
              </Text>
              <View style={[styles.progressBarContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                <Animated.View
                  entering={FadeIn.duration(300).delay(200)}
                  style={[
                    styles.progressBar,
                    {
                      backgroundColor: '#4ECDC4',
                      width: `${Math.min(
                        (macros.protein.current / macros.protein.target) * 100,
                        100
                      )}%`,
                    },
                  ]}
                />
              </View>
            </Animated.View>

            <Animated.View
              entering={FadeIn.duration(400).delay(200)}
              style={styles.macroItem}
            >
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Carbs</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {macros.carbs.current}g / {macros.carbs.target}g
              </Text>
              <View style={[styles.progressBarContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                <Animated.View
                  entering={FadeIn.duration(300).delay(250)}
                  style={[
                    styles.progressBar,
                    {
                      backgroundColor: '#FFD166',
                      width: `${Math.min(
                        (macros.carbs.current / macros.carbs.target) * 100,
                        100
                      )}%`,
                    },
                  ]}
                />
              </View>
            </Animated.View>

            <Animated.View
              entering={FadeIn.duration(400).delay(250)}
              style={styles.macroItem}
            >
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Fat</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {macros.fat.current}g / {macros.fat.target}g
              </Text>
              <View style={[styles.progressBarContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                <Animated.View
                  entering={FadeIn.duration(300).delay(300)}
                  style={[
                    styles.progressBar,
                    {
                      backgroundColor: '#6B5CA5',
                      width: `${Math.min((macros.fat.current / macros.fat.target) * 100, 100)}%`,
                    },
                  ]}
                />
              </View>

              {/* Add a clear end to the macro section */}
              <View style={{ height: 10 }} />
            </Animated.View>
          </View>

          <View style={{ height: 20 }} />

          <View style={[
            styles.sectionDivider,
            { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
          ]} />

          <View style={{ height: 10 }} />

          {/* Today's Meals Section - Only visible when expanded */}
          <Animated.View
            entering={FadeIn.duration(400).delay(300)}
            exiting={FadeOut.duration(200)}
            style={[
              styles.mealsContainer,
              { backgroundColor: colors.card }
            ]}
          >
            <View style={styles.mealsTitleContainer}>
              <Ionicons name="restaurant-outline" size={18} color={colors.primary} style={{ marginRight: 8 }} />
              <Text style={[styles.mealsTitle, { color: colors.text }]}>Today's Meals</Text>
            </View>

            {meals.length === 0 ? (
              <Animated.View
                entering={FadeIn.duration(300).delay(350)}
                exiting={FadeOut.duration(200)}
                style={styles.noMealsContainer}
              >
                <Ionicons name="restaurant-outline" size={30} color={colors.textTertiary} style={{ marginBottom: 10 }} />
                <Text style={[styles.noMealsText, { color: colors.text }]}>
                  No meals logged today
                </Text>
                <Text style={[styles.noMealsSubText, { color: colors.textTertiary, marginTop: 5 }]}>
                  Tap "Log Meal" to add your first meal
                </Text>
              </Animated.View>
            ) : isInsideScrollView ? (
              // When inside a ScrollView, use a simple non-virtualized list
              <View style={styles.mealsList}>
                {meals.map((meal, index) => (
                  <View key={meal.id || `meal-${Math.random()}`}>
                    {renderMealItem({ item: meal })}
                    {index < meals.length - 1 && <View style={{ height: 12 }} />}
                  </View>
                ))}
              </View>
            ) : (
              // When not inside a ScrollView, use FlatList for virtualization
              <FlatList
                data={meals}
                renderItem={renderMealItem}
                keyExtractor={(item) => item.id || `meal-${Math.random()}`}
                scrollEnabled={false}
                contentContainerStyle={styles.mealsList}
                ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
              />
            )}
          </Animated.View>
        </Animated.View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  card: {
    borderRadius: 15,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginLeft: 4,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  calorieText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  macroRingsContainer: {
    padding: 15,
    paddingTop: 5,
  },
  macroRings: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  macroRingContainer: {
    alignItems: 'center',
    width: '25%',
    marginBottom: 10,
  },
  ringContainer: {
    marginBottom: 8,
  },
  macroLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 2,
    textAlign: 'center',
  },
  macroValue: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  expandedContent: {
    padding: 15,
    marginTop: 0,
    borderTopWidth: 1,
    borderTopColor: 'transparent',
  },
  macroSummary: {
    marginBottom: 10,
    backgroundColor: 'transparent',
    paddingBottom: 10,
  },
  macroItem: {
    marginBottom: 16,
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 6,
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  mealsContainer: {
    marginTop: 20,
  },
  mealsTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  mealsTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  noMealsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  noMealsText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  mealsList: {
    paddingBottom: 5,
  },
  mealItem: {
    borderRadius: 12,
    padding: 14,
    borderWidth: 1,
    overflow: 'hidden',
    marginHorizontal: 1,
  },
  mealItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  mealItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  mealIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  mealInfo: {
    flex: 1,
  },
  mealItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  mealTitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  mealTime: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    marginTop: 2,
  },
  mealCalories: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  nutritionBarContainer: {
    height: 6,
    flexDirection: 'row',
    marginBottom: 10,
    borderRadius: 3,
    overflow: 'hidden',
  },
  nutritionBar: {
    height: '100%',
  },
  mealNutrition: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: -4,
  },
  nutritionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 0,
  },
  nutritionDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  nutritionValue: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
    flexShrink: 1,
  },
  nutritionLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
  noMealsSubText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  sectionDivider: {
    height: 1,
    marginVertical: 15,
    width: '100%',
  },
});

export default NutritionBreakdown;
