import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  TextInput,
  SafeAreaView,
  Platform,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../../theme/typography';
import { useTheme } from '../../theme/ThemeProvider';
import { getLogs, Log, MealData } from '../../services/conversationService';

interface MealLibraryModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectMeal: (meal: any) => void;
}

const MealLibraryModal: React.FC<MealLibraryModalProps> = ({
  visible,
  onClose,
  onSelectMeal,
}) => {
  const { colors, isDark } = useTheme();
  const [meals, setMeals] = useState<any[]>([]);
  const [filteredMeals, setFilteredMeals] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    if (visible) {
      loadMeals();
    }
  }, [visible]);

  const loadMeals = async () => {
    setIsLoading(true);
    try {
      const logs = await getLogs();
      const mealLogs = logs.filter((log) => log.type === 'meal');

      // Process meal logs to extract unique meals
      const processedMeals = mealLogs.map((log) => {
        // Check both data and metrics.meal for meal data
        const mealData = (log.data as MealData) || (log.metrics?.meal as MealData);

        // Skip logs with no meal data
        if (!mealData) {
          console.log(`Skipping meal log ${log.id} - no meal data found`);
          return null;
        }

        return {
          id: log.id || `meal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: mealData.title || 'Untitled Meal',
          description: mealData.description || '',
          nutrition: {
            calories: mealData.calories || 0,
            protein: mealData.protein || 0,
            carbs: mealData.carbs || 0,
            fat: mealData.fat || 0
          },
          ingredients: mealData.ingredients || [],
          instructions: mealData.steps || [],
          tags: mealData.tags || [],
          timestamp: log.timestamp,
          category: getCategoryFromMeal(mealData),
        };
      }).filter(Boolean);

      // Sort by most recent first
      const sortedMeals = processedMeals.sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      // Remove duplicates based on title
      const uniqueMeals = sortedMeals.filter(
        (meal, index, self) => index === self.findIndex((m) => m.title === meal.title)
      );

      setMeals(uniqueMeals);
      setFilteredMeals(uniqueMeals);
    } catch (error) {
      console.error('Error loading meals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getCategoryFromMeal = (meal: MealData): string => {
    if (!meal || !meal.tags || !Array.isArray(meal.tags) || meal.tags.length === 0) {
      // Try to infer category from title if available
      if (meal?.title) {
        const lowerTitle = meal.title.toLowerCase();
        if (lowerTitle.includes('breakfast')) return 'Breakfast';
        if (lowerTitle.includes('lunch')) return 'Lunch';
        if (lowerTitle.includes('dinner')) return 'Dinner';
        if (lowerTitle.includes('snack')) return 'Snack';
        if (lowerTitle.includes('dessert')) return 'Dessert';
      }
      return 'Other';
    }

    // Check for common meal categories in tags
    const lowerTags = meal.tags.map(tag => tag.toLowerCase());

    if (lowerTags.some(tag => tag.includes('breakfast'))) return 'Breakfast';
    if (lowerTags.some(tag => tag.includes('lunch'))) return 'Lunch';
    if (lowerTags.some(tag => tag.includes('dinner'))) return 'Dinner';
    if (lowerTags.some(tag => tag.includes('snack'))) return 'Snack';
    if (lowerTags.some(tag => tag.includes('dessert'))) return 'Dessert';

    return 'Other';
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    filterMeals(text, selectedCategory);
  };

  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    filterMeals(searchQuery, category);
  };

  const filterMeals = (query: string, category: string | null) => {
    let filtered = meals;

    // Filter by search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      filtered = filtered.filter(
        (meal) =>
          meal.title.toLowerCase().includes(lowerQuery) ||
          meal.description.toLowerCase().includes(lowerQuery) ||
          (meal.ingredients && meal.ingredients.some((ing: string) => ing.toLowerCase().includes(lowerQuery)))
      );
    }

    // Filter by category
    if (category) {
      filtered = filtered.filter((meal) => meal.category === category);
    }

    setFilteredMeals(filtered);
  };

  const renderMealItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.mealItem, { backgroundColor: colors.card }]}
      onPress={() => onSelectMeal(item)}
      activeOpacity={0.7}
    >
      <View style={styles.mealHeader}>
        <Text style={[styles.mealTitle, { color: colors.text }]}>{item.title}</Text>
        <View style={[styles.categoryTag, { backgroundColor: `${colors.primary}20` }]}>
          <Text style={[styles.categoryText, { color: colors.primary }]}>{item.category}</Text>
        </View>
      </View>

      <Text
        style={[styles.mealDescription, { color: colors.textSecondary }]}
        numberOfLines={2}
      >
        {item.description}
      </Text>

      <View style={styles.nutritionRow}>
        <View style={styles.nutritionItem}>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>
            {item.nutrition.calories}
          </Text>
          <Text style={[styles.nutritionLabel, { color: colors.textTertiary }]}>kcal</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>
            {item.nutrition.protein}g
          </Text>
          <Text style={[styles.nutritionLabel, { color: colors.textTertiary }]}>protein</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>
            {item.nutrition.carbs}g
          </Text>
          <Text style={[styles.nutritionLabel, { color: colors.textTertiary }]}>carbs</Text>
        </View>
        <View style={styles.nutritionItem}>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>
            {item.nutrition.fat}g
          </Text>
          <Text style={[styles.nutritionLabel, { color: colors.textTertiary }]}>fat</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryPill = (category: string) => (
    <TouchableOpacity
      style={[
        styles.categoryPill,
        {
          backgroundColor:
            selectedCategory === category ? colors.primary : isDark ? colors.background : '#f0f0f0',
        },
      ]}
      onPress={() => handleCategorySelect(selectedCategory === category ? null : category)}
    >
      <Text
        style={[
          styles.categoryPillText,
          {
            color: selectedCategory === category ? colors.background : colors.textSecondary,
          },
        ]}
      >
        {category}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.text }]}>Cook Book</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: isDark ? colors.card : '#f0f0f0' }]}>
            <Ionicons name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search meals..."
              placeholderTextColor={colors.textTertiary}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesScrollContent}
          >
            {renderCategoryPill('All')}
            {renderCategoryPill('Breakfast')}
            {renderCategoryPill('Lunch')}
            {renderCategoryPill('Dinner')}
            {renderCategoryPill('Snack')}
            {renderCategoryPill('Dessert')}
            {renderCategoryPill('Other')}
          </ScrollView>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading meals...
            </Text>
          </View>
        ) : filteredMeals.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={60} color={colors.textTertiary} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>No meals found</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              {searchQuery
                ? 'Try a different search term'
                : 'Log meals to build your library'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredMeals}
            renderItem={renderMealItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.mealsList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  closeButton: {
    padding: 5,
  },
  placeholder: {
    width: 34, // Same width as close button for centering
  },
  title: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  categoriesContainer: {
    marginBottom: 15,
  },
  categoriesScrollContent: {
    paddingHorizontal: 15,
  },
  categoryPill: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  categoryPillText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: 15,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
  },
  mealsList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  mealItem: {
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  mealTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    flex: 1,
  },
  categoryTag: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 10,
  },
  categoryText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.medium,
  },
  mealDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginBottom: 12,
    lineHeight: 20,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  nutritionLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fontFamily.regular,
  },
});

export default MealLibraryModal;
