import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { typography } from '../../theme/typography';
import { saveLog, Log } from '../../services/conversationService';

interface EditMealModalProps {
  visible: boolean;
  onClose: () => void;
  onMealUpdated: () => void;
  meal: any;
}

const EditMealModal: React.FC<EditMealModalProps> = ({
  visible,
  onClose,
  onMealUpdated,
  meal,
}) => {
  const { colors, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [title, setTitle] = useState('');
  const [calories, setCalories] = useState('');
  const [protein, setProtein] = useState('');
  const [carbs, setCarbs] = useState('');
  const [fat, setFat] = useState('');

  useEffect(() => {
    if (meal && visible) {
      setTitle(meal.title || '');
      setCalories(meal.nutrition?.calories?.toString() || '0');
      setProtein(meal.nutrition?.protein?.toString() || '0');
      setCarbs(meal.nutrition?.carbs?.toString() || '0');
      setFat(meal.nutrition?.fat?.toString() || '0');
      setError(null);
      setSuccess(false);
    }
  }, [meal, visible]);

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Validate inputs
      if (!title.trim()) {
        setError('Please enter a meal title');
        setIsLoading(false);
        return;
      }

      // Parse numeric values
      const caloriesNum = parseInt(calories) || 0;
      const proteinNum = parseInt(protein) || 0;
      const carbsNum = parseInt(carbs) || 0;
      const fatNum = parseInt(fat) || 0;

      // Create updated meal object
      const updatedMeal = {
        ...meal,
        title: title.trim(),
        nutrition: {
          calories: caloriesNum,
          protein: proteinNum,
          carbs: carbsNum,
          fat: fatNum,
        }
      };

      // Create log object for saving
      const mealLog: Log = {
        id: meal.id,
        type: 'meal',
        description: title.trim(),
        timestamp: meal.timestamp || new Date().toISOString(),
        contextDate: meal.contextDate || new Date().toISOString(),
        metrics: {
          meal: {
            title: title.trim(),
            calories: caloriesNum,
            protein: proteinNum,
            carbs: carbsNum,
            fat: fatNum,
            ingredients: meal.ingredients || [],
            steps: meal.steps || [],
            description: meal.description || '',
          }
        }
      };

      // Save the updated meal
      await saveLog(mealLog);

      setSuccess(true);

      // Wait a moment to show success before closing
      setTimeout(() => {
        onMealUpdated();
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error updating meal:', error);
      setError('An error occurred while updating the meal. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
        style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1, justifyContent: 'center' }}
        >
          <Animated.View
            entering={SlideInDown.duration(400).springify()}
            exiting={SlideOutDown.duration(300)}
            style={[styles.modalContent, { backgroundColor: colors.card }]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Edit Meal</Text>
              <TouchableOpacity onPress={onClose}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollContent}>
              {error && (
                <View style={[styles.errorContainer, { backgroundColor: 'rgba(255,59,48,0.1)' }]}>
                  <Ionicons name="alert-circle" size={20} color="#FF3B30" />
                  <Text style={[styles.errorText, { color: '#FF3B30' }]}>{error}</Text>
                </View>
              )}

              {success && (
                <View style={[styles.successContainer, { backgroundColor: `${colors.primary}20` }]}>
                  <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
                  <Text style={[styles.successText, { color: colors.text }]}>
                    Meal updated successfully!
                  </Text>
                </View>
              )}

              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>Meal Title</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                      borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                    }
                  ]}
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Enter meal title"
                  placeholderTextColor={colors.textTertiary}
                />
              </View>

              <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Information</Text>

              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>Calories</Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: colors.text,
                      backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                      borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                    }
                  ]}
                  value={calories}
                  onChangeText={setCalories}
                  placeholder="Enter calories"
                  placeholderTextColor={colors.textTertiary}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.macroInputRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Protein (g)</Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                        borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                      }
                    ]}
                    value={protein}
                    onChangeText={setProtein}
                    placeholder="Protein"
                    placeholderTextColor={colors.textTertiary}
                    keyboardType="numeric"
                  />
                </View>

                <View style={[styles.formGroup, { flex: 1, marginHorizontal: 4 }]}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Carbs (g)</Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                        borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                      }
                    ]}
                    value={carbs}
                    onChangeText={setCarbs}
                    placeholder="Carbs"
                    placeholderTextColor={colors.textTertiary}
                    keyboardType="numeric"
                  />
                </View>

                <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                  <Text style={[styles.label, { color: colors.textSecondary }]}>Fat (g)</Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                        borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                      }
                    ]}
                    value={fat}
                    onChangeText={setFat}
                    placeholder="Fat"
                    placeholderTextColor={colors.textTertiary}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    Updating meal...
                  </Text>
                </View>
              ) : (
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.saveButton, { backgroundColor: colors.primary }]}
                    onPress={handleSave}
                  >
                    <Text style={[styles.saveButtonText, { color: colors.background }]}>
                      Save Changes
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.cancelButton, { borderColor: colors.border }]}
                    onPress={onClose}
                  >
                    <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
          </Animated.View>
        </KeyboardAvoidingView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 15,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  modalScrollContent: {
    padding: 15,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginBottom: 6,
  },
  input: {
    height: 45,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    marginTop: 8,
    marginBottom: 16,
  },
  macroInputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 20,
  },
  saveButton: {
    height: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  saveButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  cancelButton: {
    height: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  successText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginLeft: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
    marginTop: 10,
  },
});

export default EditMealModal;
