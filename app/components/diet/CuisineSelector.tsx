import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInUp,
  SlideOutDown
} from 'react-native-reanimated';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Available cuisine options
const CUISINE_OPTIONS = [
  { id: 'italian', name: 'Italian', icon: 'pizza-outline' },
  { id: 'mexican', name: 'Mexican', icon: 'restaurant-outline' },
  { id: 'asian', name: 'Asian', icon: 'restaurant-outline' },
  { id: 'mediterranean', name: 'Mediterranean', icon: 'restaurant-outline' },
  { id: 'indian', name: 'Indian', icon: 'restaurant-outline' },
  { id: 'american', name: 'American', icon: 'fast-food-outline' },
  { id: 'french', name: 'French', icon: 'wine-outline' },
  { id: 'middle_eastern', name: 'Middle Eastern', icon: 'restaurant-outline' },
  { id: 'thai', name: 'Thai', icon: 'restaurant-outline' },
  { id: 'japanese', name: 'Japanese', icon: 'restaurant-outline' },
  { id: 'chinese', name: 'Chinese', icon: 'restaurant-outline' },
  { id: 'greek', name: 'Greek', icon: 'restaurant-outline' },
  { id: 'vegetarian', name: 'Vegetarian', icon: 'leaf-outline' },
  { id: 'vegan', name: 'Vegan', icon: 'leaf-outline' },
  { id: 'gluten_free', name: 'Gluten-Free', icon: 'nutrition-outline' },
  { id: 'keto', name: 'Keto', icon: 'nutrition-outline' },
  { id: 'paleo', name: 'Paleo', icon: 'nutrition-outline' },
];

// Storage key for cuisine preferences
const CUISINE_PREFERENCES_KEY = 'CUISINE_PREFERENCES_KEY';

interface CuisineSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelectCuisines: (cuisines: string[]) => void;
  initialCuisines?: string[];
}

const CuisineSelector: React.FC<CuisineSelectorProps> = ({
  visible,
  onClose,
  onSelectCuisines,
  initialCuisines = []
}) => {
  const { colors } = useTheme();
  const [selectedCuisines, setSelectedCuisines] = useState<string[]>(initialCuisines);

  // Update selected cuisines when initialCuisines changes
  useEffect(() => {
    if (initialCuisines.length > 0) {
      setSelectedCuisines(initialCuisines);
    } else {
      loadCuisinePreferences();
    }
  }, [visible, initialCuisines]);

  // Load cuisine preferences from storage
  const loadCuisinePreferences = async () => {
    try {
      const savedPreferences = await AsyncStorage.getItem(CUISINE_PREFERENCES_KEY);
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        setSelectedCuisines(preferences);

        // If the modal is becoming visible, we don't want to trigger a refresh
        // This prevents unnecessary API calls when just opening the modal
        if (!visible) {
          return;
        }
      }
    } catch (error) {
      console.error('Error loading cuisine preferences:', error);
    }
  };

  // Save cuisine preferences to storage
  const saveCuisinePreferences = async (cuisines: string[]) => {
    try {
      await AsyncStorage.setItem(CUISINE_PREFERENCES_KEY, JSON.stringify(cuisines));
    } catch (error) {
      console.error('Error saving cuisine preferences:', error);
    }
  };

  // Toggle cuisine selection
  const toggleCuisine = (cuisineId: string) => {
    setSelectedCuisines(prev => {
      if (prev.includes(cuisineId)) {
        return prev.filter(id => id !== cuisineId);
      } else {
        return [...prev, cuisineId];
      }
    });
  };

  // Apply selected cuisines and close modal
  const applySelection = () => {
    console.log('🍽️ Applying cuisine selection:', selectedCuisines);

    // Save to AsyncStorage
    saveCuisinePreferences(selectedCuisines);

    // Notify parent component and pass the selected cuisines
    onSelectCuisines(selectedCuisines);

    // Close the modal
    onClose();
  };

  // Clear all selections
  const clearAll = () => {
    console.log('🧹 Clearing all cuisine selections');
    setSelectedCuisines([]);

    // Note: Changes are not applied until the user clicks "Apply"
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn.duration(300)}
        exiting={FadeOut.duration(300)}
        style={[styles.modalOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
      >
        <Animated.View
          entering={SlideInUp.duration(400).springify()}
          exiting={SlideOutDown.duration(300)}
          style={[styles.modalContent, { backgroundColor: colors.card }]}
        >
          <SafeAreaView style={{ flex: 1 }}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Cuisine Preferences
              </Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
              Select cuisines for meal suggestions
            </Text>

            <ScrollView style={styles.scrollContent}>
              <View style={styles.cuisineGrid}>
                {CUISINE_OPTIONS.map(cuisine => (
                  <TouchableOpacity
                    key={cuisine.id}
                    style={[
                      styles.cuisineItem,
                      selectedCuisines.includes(cuisine.id) &&
                      { backgroundColor: `${colors.primary}20` }
                    ]}
                    onPress={() => toggleCuisine(cuisine.id)}
                  >
                    <Ionicons
                      name={cuisine.icon as any}
                      size={24}
                      color={selectedCuisines.includes(cuisine.id) ? colors.primary : colors.text}
                    />
                    <Text
                      style={[
                        styles.cuisineName,
                        {
                          color: selectedCuisines.includes(cuisine.id) ? colors.primary : colors.text
                        }
                      ]}
                    >
                      {cuisine.name}
                    </Text>
                    {selectedCuisines.includes(cuisine.id) && (
                      <View style={styles.checkmarkContainer}>
                        <Ionicons name="checkmark-circle" size={18} color={colors.primary} />
                      </View>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.clearButton, { borderColor: colors.border }]}
                onPress={clearAll}
              >
                <Text style={[styles.clearButtonText, { color: colors.text }]}>Clear All</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.applyButton, { backgroundColor: colors.primary }]}
                onPress={applySelection}
              >
                <Text style={styles.applyButtonText}>Apply</Text>
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  modalContent: {
    width: '100%',
    height: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  modalSubtitle: {
    fontSize: 14,
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 8,
  },
  closeButton: {
    padding: 4,
  },
  scrollContent: {
    flex: 1,
  },
  cuisineGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 12,
  },
  cuisineItem: {
    width: (width - 48) / 3,
    height: 100,
    margin: 4,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  cuisineName: {
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center',
  },
  checkmarkContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  clearButton: {
    flex: 1,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    borderWidth: 1,
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  applyButton: {
    flex: 2,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
});

export default CuisineSelector;
