import React, { useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTheme } from '@react-navigation/native';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeIn } from 'react-native-reanimated';
import InfoModal from '../ui/InfoModal';

interface MaintenanceCaloriesCardProps {
  maintenance: number | null;
  isLoading: boolean;
}

const MaintenanceCaloriesCard: React.FC<MaintenanceCaloriesCardProps> = ({
  maintenance,
  isLoading
}) => {
  const { colors } = useTheme();
  const [showInfoModal, setShowInfoModal] = useState(false);

  // Check if we have enough data
  const hasEnoughData = !!maintenance;

  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      style={[
        styles.container,
        { backgroundColor: colors.card }
      ]}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            Maintenance Calories
          </Text>
          <TouchableOpacity
            onPress={() => setShowInfoModal(true)}
            style={styles.infoButton}
          >
            <Ionicons name="information-circle-outline" size={18} color={colors.primary} />
          </TouchableOpacity>
        </View>
        <Ionicons name="flame-outline" size={20} color={colors.primary} />
      </View>

      <View style={styles.content}>
        {isLoading ? (
          <ActivityIndicator size="large" color={colors.primary} />
        ) : !hasEnoughData ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: colors.text }]}>
              More data required
            </Text>
            <Text style={[styles.noDataSubtext, { color: colors.primary }]}>
              Log meals and weight for 2 weeks
            </Text>
          </View>
        ) : (
          <>
            <Text style={[styles.maintenanceValue, { color: colors.text }]}>
              {maintenance ? maintenance.toLocaleString() : '—'}
            </Text>
            <Text style={[styles.unit, { color: colors.text }]}>
              kcal/day
            </Text>
          </>
        )}
      </View>

      <Text style={[styles.subtitle, { color: colors.text }]}>
        {hasEnoughData ? 'based on 2-week trend' : 'for accurate calculations'}
      </Text>

      {/* Info Modal */}
      <InfoModal
        visible={showInfoModal}
        onClose={() => setShowInfoModal(false)}
        title="Maintenance Calories"
        icon="flame-outline"
        content={
          <View>
            <Text style={[styles.modalText, { color: colors.text }]}>
              Maintenance calories represent the number of calories your body needs daily to maintain your current weight.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              This value is calculated based on:
            </Text>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Your recent calorie intake from logged meals
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Changes in your weight over time
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Your activity level and metabolic rate
              </Text>
            </View>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              Your maintenance calories may change over time as your body composition, activity level, and metabolism change.
            </Text>

            <Text style={[styles.modalText, { color: colors.text, marginTop: 12 }]}>
              <Text style={{ fontWeight: 'bold' }}>How it relates to weight management:</Text>
            </Text>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Eating more than your maintenance calories will generally lead to weight gain
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Eating less than your maintenance calories will generally lead to weight loss
              </Text>
            </View>

            <View style={styles.bulletPointContainer}>
              <Text style={[styles.bulletPoint, { color: colors.text }]}>•</Text>
              <Text style={[styles.bulletPointText, { color: colors.text }]}>
                Eating close to your maintenance calories will help maintain your current weight
              </Text>
            </View>
          </View>
        }
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoButton: {
    marginLeft: 6,
    padding: 2,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    flexDirection: 'row',
  },
  maintenanceValue: {
    fontSize: 32,
    fontWeight: 'bold',
    marginRight: 4,
  },
  unit: {
    fontSize: 16,
    fontWeight: '400',
    opacity: 0.7,
    alignSelf: 'flex-end',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.6,
    marginTop: 4,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  noDataSubtext: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.7,
  },
  modalText: {
    fontSize: 15,
    lineHeight: 22,
  },
  bulletPointContainer: {
    flexDirection: 'row',
    marginTop: 6,
    paddingLeft: 8,
  },
  bulletPoint: {
    fontSize: 15,
    marginRight: 8,
  },
  bulletPointText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
});

export default MaintenanceCaloriesCard;
