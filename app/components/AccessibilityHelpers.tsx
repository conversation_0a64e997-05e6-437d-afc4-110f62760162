import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  AccessibilityInfo,
  AccessibilityRole,
  AccessibilityState,
  ViewProps,
  TextProps,
  TouchableOpacityProps,
} from 'react-native';
import * as Haptics from 'expo-haptics';

/**
 * Enhanced TouchableOpacity with better accessibility support
 */
interface AccessibleTouchableProps extends TouchableOpacityProps {
  accessibilityLabel: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessibilityState?: AccessibilityState;
  hapticFeedback?: 'light' | 'medium' | 'heavy' | 'selection' | 'none';
  children: React.ReactNode;
}

export const AccessibleTouchable: React.FC<AccessibleTouchableProps> = ({
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  accessibilityState,
  hapticFeedback = 'light',
  onPress,
  children,
  ...props
}) => {
  const handlePress = async (event: any) => {
    // Provide haptic feedback
    if (hapticFeedback !== 'none') {
      switch (hapticFeedback) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'selection':
          await Haptics.selectionAsync();
          break;
      }
    }

    // Call the original onPress handler
    if (onPress) {
      onPress(event);
    }
  };

  return (
    <TouchableOpacity
      {...props}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={accessibilityRole}
      accessibilityState={accessibilityState}
      onPress={handlePress}
    >
      {children}
    </TouchableOpacity>
  );
};

/**
 * Accessible heading component with proper semantic structure
 */
interface AccessibleHeadingProps extends TextProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
}

export const AccessibleHeading: React.FC<AccessibleHeadingProps> = ({
  level,
  children,
  ...props
}) => {
  return (
    <Text
      {...props}
      accessibilityRole="header"
      accessibilityLevel={level}
    >
      {children}
    </Text>
  );
};

/**
 * Screen reader announcement helper
 */
export const announceForAccessibility = (message: string, priority: 'low' | 'high' = 'low') => {
  if (priority === 'high') {
    // For high priority announcements, use a more immediate method
    AccessibilityInfo.announceForAccessibility(message);
  } else {
    // For low priority, use a slight delay to avoid interrupting other announcements
    setTimeout(() => {
      AccessibilityInfo.announceForAccessibility(message);
    }, 100);
  }
};

/**
 * Hook to check if screen reader is enabled
 */
export const useScreenReader = () => {
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = React.useState(false);

  React.useEffect(() => {
    const checkScreenReader = async () => {
      const enabled = await AccessibilityInfo.isScreenReaderEnabled();
      setIsScreenReaderEnabled(enabled);
    };

    checkScreenReader();

    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    return () => subscription?.remove();
  }, []);

  return isScreenReaderEnabled;
};

/**
 * Hook to check if reduce motion is enabled
 */
export const useReduceMotion = () => {
  const [isReduceMotionEnabled, setIsReduceMotionEnabled] = React.useState(false);

  React.useEffect(() => {
    const checkReduceMotion = async () => {
      const enabled = await AccessibilityInfo.isReduceMotionEnabled();
      setIsReduceMotionEnabled(enabled);
    };

    checkReduceMotion();

    const subscription = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      setIsReduceMotionEnabled
    );

    return () => subscription?.remove();
  }, []);

  return isReduceMotionEnabled;
};

/**
 * Accessible form field wrapper
 */
interface AccessibleFieldProps extends ViewProps {
  label: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
}

export const AccessibleField: React.FC<AccessibleFieldProps> = ({
  label,
  error,
  required = false,
  children,
  ...props
}) => {
  const fieldId = React.useId();
  const labelId = `${fieldId}-label`;
  const errorId = `${fieldId}-error`;

  return (
    <View {...props}>
      <Text
        id={labelId}
        accessibilityRole="text"
        style={{ marginBottom: 4 }}
      >
        {label}
        {required && (
          <Text style={{ color: 'red' }} accessibilityLabel="required">
            {' *'}
          </Text>
        )}
      </Text>
      
      <View
        accessibilityLabelledBy={labelId}
        accessibilityDescribedBy={error ? errorId : undefined}
      >
        {children}
      </View>
      
      {error && (
        <Text
          id={errorId}
          accessibilityRole="alert"
          accessibilityLiveRegion="polite"
          style={{ color: 'red', fontSize: 12, marginTop: 4 }}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

/**
 * Loading state announcer
 */
interface LoadingAnnouncerProps {
  isLoading: boolean;
  loadingMessage?: string;
  completedMessage?: string;
}

export const LoadingAnnouncer: React.FC<LoadingAnnouncerProps> = ({
  isLoading,
  loadingMessage = 'Loading',
  completedMessage = 'Loading completed',
}) => {
  const prevLoading = React.useRef(isLoading);

  React.useEffect(() => {
    if (isLoading && !prevLoading.current) {
      // Started loading
      announceForAccessibility(loadingMessage);
    } else if (!isLoading && prevLoading.current) {
      // Finished loading
      announceForAccessibility(completedMessage);
    }
    
    prevLoading.current = isLoading;
  }, [isLoading, loadingMessage, completedMessage]);

  return null;
};

/**
 * Focus management helper
 */
export const useFocusManagement = () => {
  const focusRef = React.useRef<any>(null);

  const setFocus = React.useCallback(() => {
    if (focusRef.current) {
      AccessibilityInfo.setAccessibilityFocus(focusRef.current);
    }
  }, []);

  const setFocusDelayed = React.useCallback((delay: number = 100) => {
    setTimeout(setFocus, delay);
  }, [setFocus]);

  return {
    focusRef,
    setFocus,
    setFocusDelayed,
  };
};

/**
 * Accessible modal wrapper
 */
interface AccessibleModalProps extends ViewProps {
  isVisible: boolean;
  title: string;
  onClose: () => void;
  children: React.ReactNode;
}

export const AccessibleModal: React.FC<AccessibleModalProps> = ({
  isVisible,
  title,
  onClose,
  children,
  ...props
}) => {
  const { focusRef, setFocusDelayed } = useFocusManagement();

  React.useEffect(() => {
    if (isVisible) {
      // Announce modal opening and set focus
      announceForAccessibility(`${title} dialog opened`);
      setFocusDelayed(300);
    }
  }, [isVisible, title, setFocusDelayed]);

  if (!isVisible) {
    return null;
  }

  return (
    <View
      {...props}
      ref={focusRef}
      accessibilityRole="dialog"
      accessibilityLabel={title}
      accessibilityModal={true}
      accessibilityViewIsModal={true}
    >
      {children}
    </View>
  );
};

/**
 * Accessibility testing helpers (for development)
 */
export const AccessibilityTestHelpers = {
  logAccessibilityTree: () => {
    if (__DEV__) {
      AccessibilityInfo.getRecommendedTimeoutMillis(1000).then((timeout) => {
        console.log('[A11Y] Recommended timeout:', timeout);
      });
    }
  },
  
  testScreenReader: async () => {
    if (__DEV__) {
      const isEnabled = await AccessibilityInfo.isScreenReaderEnabled();
      console.log('[A11Y] Screen reader enabled:', isEnabled);
      return isEnabled;
    }
    return false;
  },
  
  testReduceMotion: async () => {
    if (__DEV__) {
      const isEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      console.log('[A11Y] Reduce motion enabled:', isEnabled);
      return isEnabled;
    }
    return false;
  },
};

export default {
  AccessibleTouchable,
  AccessibleHeading,
  AccessibleField,
  AccessibleModal,
  LoadingAnnouncer,
  announceForAccessibility,
  useScreenReader,
  useReduceMotion,
  useFocusManagement,
  AccessibilityTestHelpers,
};
