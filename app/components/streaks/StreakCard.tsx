import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  interpolate
} from 'react-native-reanimated';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';
import {
  StreakType,
  StreakData,
  getStreakData,
  recordStreakActivity
} from '../../services/streakService';
import { shareStreakMilestone } from '../../services/socialSharingService';

interface StreakCardProps {
  type: StreakType;
  onStreakUpdate?: (streak: StreakData) => void;
  showShareButton?: boolean;
  compact?: boolean;
}

const { width } = Dimensions.get('window');

const StreakCard: React.FC<StreakCardProps> = ({
  type,
  onStreakUpdate,
  showShareButton = true,
  compact = false
}) => {
  const { colors, isDark } = useTheme();
  const [streak, setStreak] = useState<StreakData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Animation values
  const scale = useSharedValue(1);
  const fireScale = useSharedValue(1);
  const progressWidth = useSharedValue(0);

  useEffect(() => {
    loadStreakData();
  }, [type]);

  const loadStreakData = async () => {
    try {
      setIsLoading(true);
      const streakData = await getStreakData(type);
      setStreak(streakData);
      
      // Animate progress bar
      const progressPercentage = Math.min(100, (streakData.currentStreak / getNextMilestone(streakData.currentStreak)) * 100);
      progressWidth.value = withTiming(progressPercentage, { duration: 1000 });
      
      // Animate fire icon if streak is active
      if (streakData.currentStreak > 0) {
        fireScale.value = withSequence(
          withSpring(1.2),
          withSpring(1)
        );
      }
    } catch (error) {
      console.error('[StreakCard] Error loading streak data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecordActivity = async () => {
    if (!streak) return;

    try {
      scale.value = withSequence(
        withSpring(0.95),
        withSpring(1)
      );

      const updatedStreak = await recordStreakActivity({
        type,
        date: new Date().toISOString()
      });

      setStreak(updatedStreak);
      onStreakUpdate?.(updatedStreak);

      // Check for milestone achievement
      const newMilestones = updatedStreak.milestones.filter(m => 
        !streak.milestones.some(existing => existing.days === m.days)
      );

      if (newMilestones.length > 0) {
        const milestone = newMilestones[0];
        Alert.alert(
          '🎉 Milestone Achieved!',
          milestone.title,
          [
            { text: 'Share', onPress: () => handleShareMilestone(milestone) },
            { text: 'OK', style: 'default' }
          ]
        );
      }

      // Animate fire icon
      fireScale.value = withSequence(
        withSpring(1.3),
        withSpring(1)
      );

    } catch (error) {
      console.error('[StreakCard] Error recording activity:', error);
      Alert.alert('Error', 'Failed to record activity. Please try again.');
    }
  };

  const handleShareMilestone = async (milestone: any) => {
    try {
      await shareStreakMilestone({
        type: getStreakDisplayName(type),
        days: milestone.days,
        category: getStreakDisplayName(type)
      });
    } catch (error) {
      console.error('[StreakCard] Error sharing milestone:', error);
    }
  };

  const getStreakDisplayName = (streakType: StreakType): string => {
    const names = {
      [StreakType.WORKOUT]: 'Workout',
      [StreakType.NUTRITION_LOGGING]: 'Nutrition',
      [StreakType.WEIGHT_LOGGING]: 'Weight Tracking',
      [StreakType.DAILY_CHECK_IN]: 'Daily Check-in',
      [StreakType.MEAL_PLANNING]: 'Meal Planning',
      [StreakType.HYDRATION]: 'Hydration',
      [StreakType.SLEEP_LOGGING]: 'Sleep',
      [StreakType.STEP_GOAL]: 'Steps'
    };
    return names[streakType] || 'Activity';
  };

  const getStreakIcon = (streakType: StreakType): keyof typeof Ionicons.glyphMap => {
    const icons = {
      [StreakType.WORKOUT]: 'barbell',
      [StreakType.NUTRITION_LOGGING]: 'restaurant',
      [StreakType.WEIGHT_LOGGING]: 'scale',
      [StreakType.DAILY_CHECK_IN]: 'checkmark-circle',
      [StreakType.MEAL_PLANNING]: 'calendar',
      [StreakType.HYDRATION]: 'water',
      [StreakType.SLEEP_LOGGING]: 'moon',
      [StreakType.STEP_GOAL]: 'walk'
    };
    return icons[streakType] || 'flash';
  };

  const getNextMilestone = (currentStreak: number): number => {
    const milestones = [3, 7, 14, 30, 50, 75, 100, 150, 200, 365];
    return milestones.find(m => m > currentStreak) || currentStreak + 50;
  };

  const canRecordToday = (): boolean => {
    if (!streak) return false;
    const today = new Date().toISOString().split('T')[0];
    const lastActivity = streak.lastActivityDate.split('T')[0];
    return lastActivity !== today;
  };

  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }]
  }));

  const fireAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: fireScale.value }]
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`
  }));

  if (isLoading || !streak) {
    return (
      <View style={[styles.card, { backgroundColor: colors.card }, compact && styles.compactCard]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading...</Text>
        </View>
      </View>
    );
  }

  const nextMilestone = getNextMilestone(streak.currentStreak);
  const progressToNext = streak.currentStreak;
  const isActive = streak.currentStreak > 0;

  return (
    <Animated.View style={[styles.card, { backgroundColor: colors.card }, compact && styles.compactCard, cardAnimatedStyle]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons 
            name={getStreakIcon(type)} 
            size={compact ? 20 : 24} 
            color={isActive ? colors.primary : colors.textSecondary} 
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }, compact && styles.compactTitle]}>
            {getStreakDisplayName(type)}
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }, compact && styles.compactSubtitle]}>
            {streak.totalActiveDays} total days
          </Text>
        </View>
        {isActive && (
          <Animated.View style={fireAnimatedStyle}>
            <Ionicons name="flame" size={compact ? 20 : 24} color="#FF6B35" />
          </Animated.View>
        )}
      </View>

      {/* Current Streak */}
      <View style={styles.streakContainer}>
        <Text style={[styles.streakNumber, { color: isActive ? colors.primary : colors.textSecondary }]}>
          {streak.currentStreak}
        </Text>
        <Text style={[styles.streakLabel, { color: colors.textSecondary }]}>
          {streak.currentStreak === 1 ? 'day' : 'days'}
        </Text>
      </View>

      {/* Progress to Next Milestone */}
      {!compact && (
        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: colors.textSecondary }]}>
              Next milestone: {nextMilestone} days
            </Text>
            <Text style={[styles.progressValue, { color: colors.text }]}>
              {progressToNext}/{nextMilestone}
            </Text>
          </View>
          <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
            <Animated.View 
              style={[
                styles.progressFill, 
                { backgroundColor: colors.primary },
                progressAnimatedStyle
              ]} 
            />
          </View>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[
            styles.recordButton,
            { 
              backgroundColor: canRecordToday() ? colors.primary : colors.border,
              opacity: canRecordToday() ? 1 : 0.5
            },
            compact && styles.compactButton
          ]}
          onPress={handleRecordActivity}
          disabled={!canRecordToday()}
        >
          <Ionicons 
            name={canRecordToday() ? "add" : "checkmark"} 
            size={compact ? 16 : 20} 
            color={canRecordToday() ? colors.background : colors.textSecondary} 
          />
          <Text style={[
            styles.recordButtonText, 
            { color: canRecordToday() ? colors.background : colors.textSecondary },
            compact && styles.compactButtonText
          ]}>
            {canRecordToday() ? 'Record' : 'Done Today'}
          </Text>
        </TouchableOpacity>

        {showShareButton && streak.currentStreak >= 3 && (
          <TouchableOpacity
            style={[styles.shareButton, compact && styles.compactButton]}
            onPress={() => handleShareMilestone({ days: streak.currentStreak })}
          >
            <Ionicons name="share-outline" size={compact ? 16 : 20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Best Streak */}
      {!compact && streak.longestStreak > streak.currentStreak && (
        <View style={styles.bestStreakContainer}>
          <Text style={[styles.bestStreakText, { color: colors.textSecondary }]}>
            Best: {streak.longestStreak} days
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  compactCard: {
    padding: 16,
    marginVertical: 4,
  },
  loadingContainer: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
  },
  compactTitle: {
    fontSize: typography.sizes.md,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
    marginTop: 2,
  },
  compactSubtitle: {
    fontSize: typography.sizes.xs,
  },
  streakContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  streakNumber: {
    fontSize: 48,
    fontFamily: typography.fontFamily.bold,
    lineHeight: 56,
  },
  streakLabel: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    marginTop: -4,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
  progressValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recordButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  compactButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  recordButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
  },
  compactButtonText: {
    fontSize: typography.sizes.sm,
  },
  shareButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  bestStreakContainer: {
    marginTop: 12,
    alignItems: 'center',
  },
  bestStreakText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.regular,
  },
});

export default StreakCard;
