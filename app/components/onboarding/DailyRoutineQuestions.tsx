import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { format } from 'date-fns';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';

export interface DailyRoutineData {
  wakeUpTime: string;
  bedTime: string;
  differentWeekendSchedule: boolean;
  weekendWakeUpTime?: string;
  weekendBedTime?: string;
  preferredWorkoutTime: string;
  preferredMealTimes: {
    breakfast: string;
    lunch: string;
    dinner: string;
  };
  snacks: boolean;
  waterReminders: boolean;
  workSchedule: string;
}

interface DailyRoutineQuestionsProps {
  onComplete: (data: DailyRoutineData) => void;
  onBack: () => void;
}

export default function DailyRoutineQuestions({ onComplete, onBack }: DailyRoutineQuestionsProps) {
  const { colors } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [timePickerVisible, setTimePickerVisible] = useState(false);
  const [currentTimeField, setCurrentTimeField] = useState<string>('');

  // Initialize with default values
  const [routineData, setRoutineData] = useState<DailyRoutineData>({
    wakeUpTime: '07:00',
    bedTime: '22:30',
    differentWeekendSchedule: false,
    weekendWakeUpTime: '08:30',
    weekendBedTime: '23:30',
    preferredWorkoutTime: '17:30',
    preferredMealTimes: {
      breakfast: '08:00',
      lunch: '12:30',
      dinner: '19:00',
    },
    snacks: true,
    waterReminders: true,
    workSchedule: 'Standard 9-5'
  });

  const steps = [
    {
      id: 'sleep',
      title: "What's your sleep schedule?",
      description: "This helps us schedule activities at the right times.",
      fields: ['wakeUpTime', 'bedTime', 'differentWeekendSchedule', 'weekendWakeUpTime', 'weekendBedTime']
    },
    {
      id: 'workout',
      title: "When do you prefer to work out?",
      description: "We'll schedule workouts at your preferred time.",
      fields: ['preferredWorkoutTime']
    },
    {
      id: 'meals',
      title: "When do you usually eat?",
      description: "This helps us time meal recommendations.",
      fields: ['preferredMealTimes', 'snacks']
    },
    {
      id: 'work',
      title: "What's your work schedule?",
      description: "This helps us understand your availability.",
      fields: ['workSchedule', 'waterReminders']
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(routineData);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const showTimePicker = (field: string) => {
    setCurrentTimeField(field);
    setTimePickerVisible(true);
  };

  const hideTimePicker = () => {
    setTimePickerVisible(false);
  };

  const handleTimeConfirm = (date: Date) => {
    const timeString = format(date, 'HH:mm');
    
    if (currentTimeField.includes('.')) {
      // Handle nested fields like preferredMealTimes.breakfast
      const [parent, child] = currentTimeField.split('.');
      setRoutineData({
        ...routineData,
        [parent]: {
          ...routineData[parent as keyof DailyRoutineData],
          [child]: timeString
        }
      });
    } else {
      // Handle top-level fields
      setRoutineData({
        ...routineData,
        [currentTimeField]: timeString
      });
    }
    
    hideTimePicker();
  };

  const handleToggleChange = (field: string, value: boolean) => {
    setRoutineData({
      ...routineData,
      [field]: value
    });
  };

  const handleTextChange = (field: string, value: string) => {
    setRoutineData({
      ...routineData,
      [field]: value
    });
  };

  const renderTimeInput = (label: string, field: string, value: string) => {
    return (
      <View style={styles.inputGroup}>
        <Text style={[styles.inputLabel, { color: colors.text }]}>{label}</Text>
        <TouchableOpacity
          style={[styles.timeInput, { backgroundColor: colors.surface, borderColor: colors.border }]}
          onPress={() => showTimePicker(field)}
        >
          <Text style={[styles.timeText, { color: colors.text }]}>{value}</Text>
          <Ionicons name="time-outline" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderCurrentStep = () => {
    const step = steps[currentStep];
    
    switch (step.id) {
      case 'sleep':
        return (
          <View>
            {renderTimeInput('Wake up time (weekdays)', 'wakeUpTime', routineData.wakeUpTime)}
            {renderTimeInput('Bedtime (weekdays)', 'bedTime', routineData.bedTime)}
            
            <View style={styles.toggleContainer}>
              <Text style={[styles.toggleLabel, { color: colors.text }]}>Different weekend schedule?</Text>
              <Switch
                value={routineData.differentWeekendSchedule}
                onValueChange={(value) => handleToggleChange('differentWeekendSchedule', value)}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={Platform.OS === 'ios' ? undefined : colors.background}
              />
            </View>
            
            {routineData.differentWeekendSchedule && (
              <>
                {renderTimeInput('Wake up time (weekends)', 'weekendWakeUpTime', routineData.weekendWakeUpTime || '')}
                {renderTimeInput('Bedtime (weekends)', 'weekendBedTime', routineData.weekendBedTime || '')}
              </>
            )}
          </View>
        );
        
      case 'workout':
        return (
          <View>
            {renderTimeInput('Preferred workout time', 'preferredWorkoutTime', routineData.preferredWorkoutTime)}
          </View>
        );
        
      case 'meals':
        return (
          <View>
            {renderTimeInput('Breakfast time', 'preferredMealTimes.breakfast', routineData.preferredMealTimes.breakfast)}
            {renderTimeInput('Lunch time', 'preferredMealTimes.lunch', routineData.preferredMealTimes.lunch)}
            {renderTimeInput('Dinner time', 'preferredMealTimes.dinner', routineData.preferredMealTimes.dinner)}
            
            <View style={styles.toggleContainer}>
              <Text style={[styles.toggleLabel, { color: colors.text }]}>Do you eat snacks between meals?</Text>
              <Switch
                value={routineData.snacks}
                onValueChange={(value) => handleToggleChange('snacks', value)}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={Platform.OS === 'ios' ? undefined : colors.background}
              />
            </View>
          </View>
        );
        
      case 'work':
        return (
          <View>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Work schedule</Text>
              <TextInput
                style={[styles.textInput, { backgroundColor: colors.surface, borderColor: colors.border, color: colors.text }]}
                value={routineData.workSchedule}
                onChangeText={(text) => handleTextChange('workSchedule', text)}
                placeholder="e.g., Standard 9-5, Night shift, Flexible..."
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.toggleContainer}>
              <Text style={[styles.toggleLabel, { color: colors.text }]}>Would you like water reminders?</Text>
              <Switch
                value={routineData.waterReminders}
                onValueChange={(value) => handleToggleChange('waterReminders', value)}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={Platform.OS === 'ios' ? undefined : colors.background}
              />
            </View>
          </View>
        );
        
      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={colors.primary} />
      </TouchableOpacity>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <Text style={[styles.title, { color: colors.text }]}>{steps[currentStep].title}</Text>
        <Text style={[styles.description, { color: colors.textSecondary }]}>{steps[currentStep].description}</Text>
        
        {renderCurrentStep()}
      </ScrollView>
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.nextButton, { backgroundColor: colors.primary }]}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentStep === steps.length - 1 ? 'Complete' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <DateTimePickerModal
        isVisible={timePickerVisible}
        mode="time"
        onConfirm={handleTimeConfirm}
        onCancel={hideTimePicker}
        is24Hour={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  backButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 10,
  },
  scrollView: {
    flex: 1,
    marginTop: 60,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  title: {
    ...typography.h1,
    marginBottom: 10,
  },
  description: {
    ...typography.body,
    marginBottom: 30,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    ...typography.subtitle,
    marginBottom: 8,
  },
  timeInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  timeText: {
    ...typography.body,
  },
  textInput: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    ...typography.body,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  toggleLabel: {
    ...typography.body,
    flex: 1,
    marginRight: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  nextButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  nextButtonText: {
    ...typography.button,
    color: 'white',
  },
});
