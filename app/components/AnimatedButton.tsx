import React, { useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
  TextStyle
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  Easing,
  interpolateColor
} from 'react-native-reanimated';

interface AnimatedButtonProps {
  title: string;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  backgroundColor?: string;
  textColor?: string;
  disabledBackgroundColor?: string;
  disabledTextColor?: string;
  isLoading?: boolean;
  disabled?: boolean;
  loadingColor?: string;
  animationDuration?: number;
}

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  title,
  onPress,
  style,
  textStyle,
  backgroundColor = '#ADD8E6',
  textColor = '#FFFFFF',
  disabledBackgroundColor = 'rgba(173, 216, 230, 0.5)',
  disabledTextColor = 'rgba(255, 255, 255, 0.7)',
  isLoading = false,
  disabled = false,
  loadingColor = '#FFFFFF',
  animationDuration = 150
}) => {
  const scale = useSharedValue(1);
  const bgColor = useSharedValue(0);

  useEffect(() => {
    bgColor.value = withTiming(disabled ? 1 : 0, {
      duration: 300,
      easing: Easing.inOut(Easing.ease)
    });
  }, [disabled, bgColor]);

  const handlePressIn = () => {
    scale.value = withTiming(0.95, {
      duration: animationDuration,
      easing: Easing.inOut(Easing.quad)
    });
  };

  const handlePressOut = () => {
    scale.value = withTiming(1, {
      duration: animationDuration,
      easing: Easing.inOut(Easing.quad)
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      backgroundColor: interpolateColor(
        bgColor.value,
        [0, 1],
        [backgroundColor, disabledBackgroundColor]
      )
    };
  });

  const textAnimatedStyle = useAnimatedStyle(() => {
    return {
      color: interpolateColor(
        bgColor.value,
        [0, 1],
        [textColor, disabledTextColor]
      )
    };
  });

  return (
    <AnimatedTouchable
      style={[styles.button, animatedStyle, style]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || isLoading}
      activeOpacity={0.8}
    >
      {isLoading ? (
        <ActivityIndicator color={loadingColor} size="small" />
      ) : (
        <Animated.Text style={[styles.text, textAnimatedStyle, textStyle]}>
          {title}
        </Animated.Text>
      )}
    </AnimatedTouchable>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 120,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  }
});

export default AnimatedButton;
