import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';
import { spacing } from '../theme/spacing';
import { typography } from '../theme/typography';

interface OfflineIndicatorProps {
  showWhenOnline?: boolean;
  position?: 'top' | 'bottom';
  style?: any;
}

/**
 * Network status indicator with smooth animations
 */
export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  showWhenOnline = false,
  position = 'top',
  style,
}) => {
  const { colors } = useTheme();
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const [showIndicator, setShowIndicator] = useState(false);
  
  const translateY = useSharedValue(-100);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      
      // Show indicator when offline or when explicitly requested for online state
      const shouldShow = !state.isConnected || (showWhenOnline && state.isConnected);
      setShowIndicator(shouldShow);
    });

    return () => unsubscribe();
  }, [showWhenOnline]);

  useEffect(() => {
    if (showIndicator) {
      // Animate in
      translateY.value = withSpring(0, {
        damping: 15,
        stiffness: 150,
      });
      opacity.value = withTiming(1, { duration: 300 });
    } else {
      // Animate out
      translateY.value = withTiming(position === 'top' ? -100 : 100, { duration: 300 });
      opacity.value = withTiming(0, { duration: 300 });
    }
  }, [showIndicator, position]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  const getIndicatorConfig = () => {
    if (isConnected === false) {
      return {
        backgroundColor: colors.error || '#FF6B6B',
        icon: 'cloud-offline-outline' as const,
        text: 'No Internet Connection',
        textColor: '#FFFFFF',
      };
    } else if (isConnected === true && showWhenOnline) {
      return {
        backgroundColor: colors.success || '#4ECDC4',
        icon: 'cloud-done-outline' as const,
        text: `Connected via ${connectionType}`,
        textColor: '#FFFFFF',
      };
    }
    
    return null;
  };

  const config = getIndicatorConfig();
  
  if (!config) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: config.backgroundColor,
          [position]: 0,
        },
        animatedStyle,
        style,
      ]}
    >
      <View style={styles.content}>
        <Ionicons 
          name={config.icon} 
          size={16} 
          color={config.textColor}
          style={styles.icon}
        />
        <Text style={[styles.text, { color: config.textColor }]}>
          {config.text}
        </Text>
      </View>
    </Animated.View>
  );
};

/**
 * Hook for network status
 */
export const useNetworkStatus = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const [isInternetReachable, setIsInternetReachable] = useState<boolean | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);
    });

    return () => unsubscribe();
  }, []);

  return {
    isConnected,
    connectionType,
    isInternetReachable,
    isOnline: isConnected === true && isInternetReachable !== false,
  };
};

/**
 * Component that shows children only when online
 */
interface OnlineOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const OnlineOnly: React.FC<OnlineOnlyProps> = ({ children, fallback }) => {
  const { isOnline } = useNetworkStatus();

  if (isOnline === false) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
};

/**
 * Component that shows children only when offline
 */
interface OfflineOnlyProps {
  children: React.ReactNode;
}

export const OfflineOnly: React.FC<OfflineOnlyProps> = ({ children }) => {
  const { isOnline } = useNetworkStatus();

  if (isOnline !== false) {
    return null;
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: Platform.OS === 'ios' ? 44 : 0, // Account for status bar on iOS
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.padding.element,
    paddingHorizontal: spacing.padding.container,
  },
  icon: {
    marginRight: spacing.margin.tight,
  },
  text: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily.medium,
  },
});

export default OfflineIndicator;
