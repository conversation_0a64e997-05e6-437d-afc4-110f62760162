import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Easing,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { typography } from '../theme/typography';
import AnimatedToggle from './AnimatedToggle';

interface AnimatedSettingsItemProps {
  title: string;
  description?: string;
  icon: keyof typeof Ionicons.glyphMap;
  type: 'toggle' | 'button' | 'navigation';
  value?: boolean;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  iconColor?: string;
  backgroundColor?: string;
  textColor?: string;
  descriptionColor?: string;
  disabled?: boolean;
}

const AnimatedSettingsItem: React.FC<AnimatedSettingsItemProps> = ({
  title,
  description,
  icon,
  type,
  value = false,
  onPress,
  onToggle,
  iconColor = '#007AFF',
  backgroundColor = '#FFFFFF',
  textColor = '#000000',
  descriptionColor = '#8E8E93',
  disabled = false,
}) => {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Handle press animation
  const handlePressIn = () => {
    if (disabled || (type === 'toggle' && !onPress)) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || (type === 'toggle' && !onPress)) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.inOut(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Handle press action
  const handlePress = () => {
    if (disabled) return;
    if (type === 'toggle' && onToggle) {
      onToggle(!value);
    } else if (onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled}
    >
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor,
            transform: [{ scale: scaleAnim }],
            opacity: disabled ? 0.6 : opacityAnim,
          },
        ]}
      >
        <View style={styles.iconContainer}>
          <View style={[styles.iconBackground, { backgroundColor: iconColor + '20' }]}>
            <Ionicons name={icon} size={22} color={iconColor} />
          </View>
        </View>
        
        <View style={styles.contentContainer}>
          <Text style={[styles.title, { color: textColor }]}>{title}</Text>
          {description && (
            <Text style={[styles.description, { color: descriptionColor }]}>
              {description}
            </Text>
          )}
        </View>
        
        <View style={styles.actionContainer}>
          {type === 'toggle' && onToggle && (
            <AnimatedToggle
              value={value}
              onValueChange={onToggle}
              activeColor={iconColor}
              disabled={disabled}
            />
          )}
          
          {type === 'navigation' && (
            <Ionicons name="chevron-forward" size={20} color={descriptionColor} />
          )}
          
          {type === 'button' && (
            <View style={[styles.buttonIndicator, { backgroundColor: iconColor + '20' }]}>
              <Text style={[styles.buttonText, { color: iconColor }]}>
                {value ? 'ON' : 'View'}
              </Text>
            </View>
          )}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  iconContainer: {
    marginRight: 16,
  },
  iconBackground: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 2,
  },
  description: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
  },
  actionContainer: {
    marginLeft: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 12,
    fontFamily: typography.fontFamily.semibold,
  },
});

export default AnimatedSettingsItem;
