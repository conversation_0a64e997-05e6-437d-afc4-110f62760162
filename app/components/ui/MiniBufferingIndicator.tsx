import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

interface MiniBufferingIndicatorProps {
  size?: 'tiny' | 'small' | 'medium';
  color?: string;
  dotCount?: number;
}

const MiniBufferingIndicator: React.FC<MiniBufferingIndicatorProps> = ({
  size = 'small',
  color,
  dotCount = 3
}) => {
  const { colors } = useTheme();
  const dotColor = color || colors.primary;

  // Array of animation values for each dot
  const dotAnimations = useRef(
    Array(dotCount)
      .fill(0)
      .map(() => new Animated.Value(0))
  ).current;

  // Calculate the dot size based on the size prop
  const getDotSize = () => {
    switch(size) {
      case 'tiny': return 4;
      case 'small': return 5;
      case 'medium': return 7;
      default: return 5;
    }
  };

  const dotSize = getDotSize();
  const dotSpacing = dotSize * 1.5;

  useEffect(() => {
    // Create animations for each dot with staggered timing
    const animations = dotAnimations.map((anim, index) => {
      // Reset animation before starting
      anim.setValue(0);

      // Create sequence of scaling up and down
      return Animated.sequence([
        // Delay based on position - stagger the dots
        Animated.delay(index * 160),
        // Animation loop
        Animated.loop(
          Animated.sequence([
            // Scale up with spring physics for more natural motion
            Animated.spring(anim, {
              toValue: 1,
              friction: 7, // Lower friction for more bounce
              tension: 40, // Lower tension for smoother animation
              useNativeDriver: true,
            }),
            // Hold at full size briefly
            Animated.timing(anim, {
              toValue: 1,
              duration: 100,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
            // Scale down smoothly
            Animated.timing(anim, {
              toValue: 0,
              duration: 250,
              easing: Easing.out(Easing.cubic), // Cubic easing for smoother exit
              useNativeDriver: true,
            }),
            // Pause before next cycle
            Animated.timing(anim, {
              toValue: 0,
              duration: 300, // Longer pause for better rhythm
              easing: Easing.linear,
              useNativeDriver: true,
            }),
          ])
        )
      ]);
    });

    // Start all animations
    Animated.parallel(animations).start();

    // Clean up animations on component unmount
    return () => {
      animations.forEach(anim => anim.stop());
    };
  }, [dotAnimations, dotCount]);

  return (
    <View style={styles.container}>
      {dotAnimations.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              width: dotSize,
              height: dotSize,
              borderRadius: dotSize / 2,
              backgroundColor: dotColor,
              marginHorizontal: dotSpacing / 2,
              opacity: anim.interpolate({
                inputRange: [0, 0.5, 1],
                outputRange: [0.2, 0.6, 1],
              }),
              transform: [
                {
                  scale: anim.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0.5, 0.85, 1],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 20, // Smaller fixed height
    paddingHorizontal: 2, // Add some padding
  },
  dot: {
    borderRadius: 50,
  },
});

export default MiniBufferingIndicator;
