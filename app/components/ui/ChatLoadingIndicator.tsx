import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing, Text } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';
import { typography } from '../../theme/typography';

interface ChatLoadingIndicatorProps {
  message?: string;
}

const ChatLoadingIndicator: React.FC<ChatLoadingIndicatorProps> = ({
  message = "Lotus is thinking..."
}) => {
  const { colors } = useTheme();
  const dotCount = 3;
  
  // Array of animation values for each dot
  const dotAnimations = useRef(
    Array(dotCount)
      .fill(0)
      .map(() => new Animated.Value(0))
  ).current;

  // Dot size for chat is smaller
  const dotSize = 5;
  const dotSpacing = dotSize * 1.5;

  useEffect(() => {
    // Create animations for each dot with staggered timing
    const animations = dotAnimations.map((anim, index) => {
      // Reset animation before starting
      anim.setValue(0);
      
      // Create sequence of scaling up and down
      return Animated.sequence([
        // Delay based on position
        Animated.delay(index * 120),
        // Animation loop
        Animated.loop(
          Animated.sequence([
            // Scale up
            Animated.timing(anim, {
              toValue: 1,
              duration: 400,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
            // Hold at full size
            Animated.timing(anim, {
              toValue: 1,
              duration: 100,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
            // Scale down
            Animated.timing(anim, {
              toValue: 0,
              duration: 400,
              easing: Easing.in(Easing.ease),
              useNativeDriver: true,
            }),
            // Pause before next cycle
            Animated.timing(anim, {
              toValue: 0,
              duration: 300,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
          ])
        )
      ]);
    });

    // Start all animations
    Animated.parallel(animations).start();

    // Clean up animations on component unmount
    return () => {
      animations.forEach(anim => anim.stop());
    };
  }, [dotAnimations, dotCount]);

  return (
    <View style={styles.container}>
      <Text style={[styles.message, { color: colors.textSecondary }]}>
        {message}
      </Text>
      <View style={styles.dotsContainer}>
        {dotAnimations.map((anim, index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                borderRadius: dotSize / 2,
                backgroundColor: colors.primary,
                marginHorizontal: dotSpacing / 2,
                opacity: anim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.3, 1],
                }),
                transform: [
                  {
                    scale: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.7, 1],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    maxWidth: '85%',
    alignSelf: 'flex-start',
  },
  message: {
    fontSize: 14,
    fontFamily: typography.fontFamily.regular,
    marginRight: 8,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 20,
  },
  dot: {
    borderRadius: 50,
  },
});

export default ChatLoadingIndicator;
