const fs = require('fs');
const path = require('path');

const assetsDir = path.join(__dirname, '../assets');

// Create assets directory if it doesn't exist
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir);
}

// Create a simple SVG for each required asset
const createPlaceholderSVG = (filename) => {
  const svg = `
    <svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
      <rect width="1024" height="1024" fill="#ADD8E6"/>
      <text x="512" y="512" font-family="Arial" font-size="48" fill="#121212" text-anchor="middle" dominant-baseline="middle">
        Lotus
      </text>
    </svg>
  `;
  fs.writeFileSync(path.join(assetsDir, filename), svg);
};

// Generate required assets
createPlaceholderSVG('icon.png');
createPlaceholderSVG('splash.png');
createPlaceholderSVG('adaptive-icon.png');
createPlaceholderSVG('favicon.png');

console.log('Placeholder assets generated successfully!');