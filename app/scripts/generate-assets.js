const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const assetsDir = path.join(__dirname, '../assets');

// Create assets directory if it doesn't exist
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir);
}

// Create a simple placeholder image
async function createPlaceholderImage(filename, size = 1024) {
  const svgBuffer = Buffer.from(`
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" fill="#ADD8E6"/>
      <text x="${size/2}" y="${size/2}" font-family="Arial" font-size="${size/20}" fill="#121212" text-anchor="middle" dominant-baseline="middle">
        Lotus
      </text>
    </svg>
  `);

  await sharp(svgBuffer)
    .png()
    .toFile(path.join(assetsDir, filename));
}

async function generateAssets() {
  try {
    await createPlaceholderImage('icon.png');
    await createPlaceholderImage('splash.png', 2048);
    await createPlaceholderImage('adaptive-icon.png');
    await createPlaceholderImage('favicon.png', 32);
    console.log('Placeholder assets generated successfully!');
  } catch (error) {
    console.error('Error generating assets:', error);
  }
}

generateAssets();