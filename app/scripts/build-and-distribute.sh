#!/bin/bash

# Lotus App Build and Distribution Script
# This script helps you build and distribute beta versions of your app

set -e

echo "🚀 Lotus App Build & Distribution"
echo "================================="

# Function to show available options
show_menu() {
    echo ""
    echo "Choose an option:"
    echo "1) Build development client (for iOS testing with Expo Go alternative)"
    echo "2) Build preview version (for iOS internal testing)"
    echo "3) Build production version (for iOS App Store)"
    echo "4) Create internal distribution link"
    echo "5) Exit"
    echo ""
}

# Function to build development client
build_development() {
    echo "📱 Building development client..."
    echo "This creates a custom development client with your native dependencies."
    echo "Building for iOS..."
    eas build --profile development --platform ios
}

# Function to build preview version
build_preview() {
    echo "🔍 Building preview version..."
    echo "This creates a standalone app for internal testing."
    echo "Building preview for iOS..."
    eas build --profile preview --platform ios
}

# Function to build production version
build_production() {
    echo "🏭 Building production version..."
    echo "This creates a production-ready build."
    
    read -p "Are you sure you want to build for production? (y/N) " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "Building production for iOS..."
        eas build --profile production --platform ios
    else
        echo "Production build cancelled."
    fi
}

# Function to create internal distribution
create_distribution() {
    echo "📤 Creating internal distribution..."
    echo "This will show you how to share your builds with testers."
    
    echo ""
    echo "To distribute your builds:"
    echo "1. Go to https://expo.dev/accounts/[your-username]/projects/lotus/builds"
    echo "2. Find your latest build"
    echo "3. Click 'Install' to get a shareable link"
    echo "4. Share this link with your beta testers"
    echo ""
    echo "Testers can:"
    echo "- Install via TestFlight on iOS (if you have Apple Developer account)"
    echo "- Install on iOS Simulator"
    echo "- Use Expo Go for development builds"
    echo ""
    
    read -p "Open Expo dashboard in browser? (y/N) " open_browser
    if [[ $open_browser =~ ^[Yy]$ ]]; then
        open "https://expo.dev/accounts/$(whoami)/projects/lotus/builds" 2>/dev/null || echo "Please manually open: https://expo.dev/accounts/[your-username]/projects/lotus/builds"
    fi
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            build_development
            ;;
        2)
            build_preview
            ;;
        3)
            build_production
            ;;
        4)
            create_distribution
            ;;
        5)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option. Please choose 1-5."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done 