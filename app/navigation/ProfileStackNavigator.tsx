import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '../theme/ThemeProvider';
import { useProfile } from '../context/ProfileContext';
import ProfileScreen from '../screens/ProfileScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import PrivacySettingsScreen from '../screens/PrivacySettingsScreen';
import HelpSupportScreen from '../screens/HelpSupportScreen';
import { typography } from '../theme/typography';

const Stack = createNativeStackNavigator();

// Create a profile placeholder that exactly matches the profile screen layout
// This ensures no frame shifts when the real screen renders
const ProfilePlaceholder = () => {
  const { colors } = useTheme();
  const { profile } = useProfile();

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: colors.background }
      ]}
    >
      {/* Fixed header */}
      <View
        style={[
          styles.header,
          {
            backgroundColor: colors.background,
            borderBottomColor: colors.border
          }
        ]}
      >
        <Text
          style={[
            styles.headerText,
            { color: colors.text }
          ]}
        >
          Profile
        </Text>
      </View>

      {/* Placeholder content with identical structure as the real profile screen */}
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        <View style={styles.profileSection}>
          {/* Avatar placeholder */}
          <View
            style={[
              styles.avatarPlaceholder,
              { backgroundColor: colors.primaryLight }
            ]}
          />

          {/* Name placeholder */}
          <Text
            style={[
              styles.namePlaceholder,
              { color: colors.text }
            ]}
          >
            {profile?.name || 'User'}
          </Text>

          {/* Profile info container placeholder */}
          <View
            style={[
              styles.infoContainer,
              { backgroundColor: colors.card }
            ]}
          >
            {/* Profile detail items placeholder */}
            {[
              { icon: 'calendar-outline', text: profile?.birthday || 'Not specified' },
              { icon: 'resize-outline', text: profile?.height ? `${profile.height} in` : 'Not specified' },
              { icon: 'barbell-outline', text: profile?.weight ? `${profile.weight} lbs` : 'Not specified' },
              { icon: 'fitness-outline', text: profile?.fitnessGoal || 'Not specified' }
            ].map((item, i) => (
              <View
                key={i}
                style={styles.detailRow}
              >
                <View
                  style={[
                    styles.iconPlaceholder,
                    { backgroundColor: colors.primary }
                  ]}
                />
                <View
                  style={[
                    styles.textPlaceholder,
                    { backgroundColor: colors.border + '40' }
                  ]}
                />
              </View>
            ))}
          </View>

          {/* Edit profile button placeholder */}
          <View
            style={[
              styles.buttonPlaceholder,
              { backgroundColor: colors.primary }
            ]}
          />
        </View>
      </View>
    </View>
  );
};

export default function ProfileStackNavigator() {
  const { colors } = useTheme();
  const { profile } = useProfile();

  // State for controlling screen loading
  const [isReady, setIsReady] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  // Load profile data asynchronously while showing placeholder
  useEffect(() => {
    // Wait a bit to ensure profile data is loaded from context
    const timer = setTimeout(() => {
      setIsReady(true);

      // Fade in animation when ready
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true
      }).start();
    }, 300); // Slightly longer delay to ensure profile data is loaded

    return () => clearTimeout(timer);
  }, []);

  // Memoize the Navigator to prevent unnecessary rerenders
  const navigator = useMemo(() => (
    <Stack.Navigator
      initialRouteName="ProfileMain"
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        // Disable all animations between screens
        animation: 'none',
        // Use transparent presentation mode
        presentation: 'transparentModal',
        // Prevent any animation when replacing screens
        animationTypeForReplace: 'pop',
      }}
    >
      <Stack.Screen
        name="ProfileMain"
        component={ProfileScreen}
        // Ensure ProfileScreen is not lazily loaded
        options={{
          lazy: false
        }}
      />
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
      />
      <Stack.Screen
        name="PrivacySettings"
        component={PrivacySettingsScreen}
      />
      <Stack.Screen
        name="HelpSupport"
        component={HelpSupportScreen}
      />
    </Stack.Navigator>
  ), [colors.background]);

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: colors.background }
      ]}
    >
      {/* Solid background to prevent any flash */}
      <View
        style={[
          styles.backgroundLayer,
          { backgroundColor: colors.background }
        ]}
      />

      {/* Always render the placeholder until ready */}
      {!isReady && <ProfilePlaceholder />}

      {/* Fade in the real navigator when ready */}
      <Animated.View
        style={[
          styles.navigatorContainer,
          {
            opacity: fadeAnim,
            // Hide completely until ready to prevent any frame of unformatted content
            display: isReady ? 'flex' : 'none'
          }
        ]}
      >
        {navigator}
      </Animated.View>
    </View>
  );
}

// Extract styles to improve performance and readability
const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative'
  },
  backgroundLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0
  },
  navigatorContainer: {
    flex: 1,
    zIndex: 1
  },
  header: {
    height: 50,
    borderBottomWidth: 1,
    width: '100%',
    justifyContent: 'center',
    paddingHorizontal: 16
  },
  headerText: {
    fontSize: 20,
    fontFamily: typography.fontFamily.bold
  },
  profileSection: {
    alignItems: 'center',
    paddingTop: 20
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15
  },
  namePlaceholder: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    marginBottom: 16
  },
  infoContainer: {
    width: '85%',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    gap: 10
  },
  iconPlaceholder: {
    width: 18,
    height: 18,
    borderRadius: 9
  },
  textPlaceholder: {
    width: 200,
    height: 16,
    borderRadius: 8
  },
  buttonPlaceholder: {
    width: 120,
    height: 36,
    borderRadius: 18,
    marginTop: 15
  }
});