import React from 'react';
import { View, Platform, TouchableOpacity, Text } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTheme } from '../theme/ThemeProvider';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  Easing
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';

// Import screens
import LotusScreen from '../screens/LotusScreen';
import StrengthScreen from '../screens/StrengthScreen';
import DietScreen from '../screens/DietScreen';
import DigestScreen from '../screens/DigestScreen';
import ProfileStackNavigator from './ProfileStackNavigator';

export type MainTabParamList = {
  Lotus: { conversationId?: string } | undefined;
  Strength: undefined;
  Diet: undefined;
  Digest: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

// Enhanced Tab Bar Icon Component
interface TabBarIconProps {
  focused: boolean;
  color: string;
  size: number;
  iconName: keyof typeof Ionicons.glyphMap;
  route: string;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ focused, color, size, iconName, route }) => {
  const scale = useSharedValue(focused ? 1 : 0.8);
  const opacity = useSharedValue(focused ? 1 : 0.7);
  const glowOpacity = useSharedValue(0);
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    scale.value = withSpring(focused ? 1.1 : 0.9, {
      damping: 15,
      stiffness: 200,
      mass: 0.8
    });
    
    opacity.value = withTiming(focused ? 1 : 0.6, {
      duration: 200,
      easing: Easing.out(Easing.cubic)
    });

    translateY.value = withSpring(focused ? -2 : 0, {
      damping: 12,
      stiffness: 150
    });

    if (focused) {
      glowOpacity.value = withTiming(0.3, { duration: 300 });
      // Add haptic feedback when tab becomes focused
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      glowOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [focused]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value }
    ],
    opacity: opacity.value,
  }));

  const glowStyle = useAnimatedStyle(() => ({
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 20,
    backgroundColor: color,
    opacity: glowOpacity.value,
    zIndex: -1,
  }));

  return (
    <View style={{ 
      position: 'relative', 
      alignItems: 'center', 
      justifyContent: 'center',
      width: 40,
      height: 40
    }}>
      <Animated.View style={glowStyle} />
      <Animated.View style={animatedStyle}>
        <Ionicons
          name={iconName}
          size={size}
          color={color}
        />
      </Animated.View>
    </View>
  );
};

// Custom Tab Bar Component
interface CustomTabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

const CustomTabBar: React.FC<CustomTabBarProps> = ({ state, descriptors, navigation }) => {
  const { colors, isDark } = useTheme();

  const getIconName = (routeName: string, focused: boolean): keyof typeof Ionicons.glyphMap => {
    switch (routeName) {
      case 'Lotus':
        return focused ? 'chatbubbles' : 'chatbubbles-outline';
      case 'Strength':
        return focused ? 'barbell' : 'barbell-outline';
      case 'Diet':
        return focused ? 'restaurant' : 'restaurant-outline';
      case 'Digest':
        return focused ? 'calendar' : 'calendar-outline';
      case 'Profile':
        return focused ? 'person' : 'person-outline';
      default:
        return 'chatbubbles-outline';
    }
  };

  return (
    <View style={{
      flexDirection: 'row',
      backgroundColor: colors.background,
      borderTopColor: colors.border,
      borderTopWidth: 1,
      paddingBottom: Platform.OS === 'ios' ? 20 : 10,
      paddingTop: 12,
      paddingHorizontal: 20,
      elevation: 4,
      shadowOpacity: isDark ? 0.2 : 0.08,
      shadowOffset: { height: -4, width: 0 },
      shadowRadius: 4,
      shadowColor: colors.text,
    }}>
      {state.routes.map((route: any, index: number) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TouchableOpacity
            key={route.key}
            style={{ flex: 1, alignItems: 'center' }}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onPress();
            }}
            onLongPress={onLongPress}
            activeOpacity={0.7}
          >
            <TabBarIcon
              focused={isFocused}
              color={isFocused ? colors.primary : colors.textSecondary}
              size={26}
              iconName={getIconName(route.name, isFocused)}
              route={route.name}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default function EnhancedTabNavigator() {
  const { colors } = useTheme();

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background
    }}>
      <Tab.Navigator
        tabBar={(props) => <CustomTabBar {...props} />}
        screenOptions={{
          headerShown: false,
          lazy: false,
          detachInactiveScreens: false,
          freezeOnBlur: false,
          animationEnabled: true,
        }}
      >
        <Tab.Screen
          name="Lotus"
          component={LotusScreen}
          options={{
            title: 'Lotus',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Strength"
          component={StrengthScreen}
          options={{
            title: 'Strength',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Diet"
          component={DietScreen}
          options={{
            title: 'Diet',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Digest"
          component={DigestScreen}
          options={{
            title: 'Digest',
            unmountOnBlur: false,
          }}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileStackNavigator}
          options={{
            title: 'Profile',
            unmountOnBlur: false,
            lazy: false,
          }}
        />
      </Tab.Navigator>
    </View>
  );
}
