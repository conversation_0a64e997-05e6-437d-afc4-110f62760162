import { Easing } from 'react-native-reanimated';
import { TransitionSpec } from '@react-navigation/native-stack';

// Smooth fade transition
export const fadeTransition = {
  gestureEnabled: true,
  transitionSpec: {
    open: {
      animation: 'timing',
      config: {
        duration: 300,
        easing: Easing.out(Easing.cubic),
      },
    } as TransitionSpec,
    close: {
      animation: 'timing',
      config: {
        duration: 300,
        easing: Easing.in(Easing.cubic),
      },
    } as TransitionSpec,
  },
  cardStyleInterpolator: ({ current }: { current: { progress: number } }) => {
    return {
      cardStyle: {
        opacity: current.progress,
      },
    };
  },
};

// Smooth slide transition
export const slideTransition = {
  gestureEnabled: true,
  transitionSpec: {
    open: {
      animation: 'timing',
      config: {
        duration: 350,
        easing: Easing.out(Easing.cubic),
      },
    } as TransitionSpec,
    close: {
      animation: 'timing',
      config: {
        duration: 350,
        easing: Easing.in(Easing.cubic),
      },
    } as TransitionSpec,
  },
  cardStyleInterpolator: ({ current, layouts }: { 
    current: { progress: number }, 
    layouts: { screen: { width: number } } 
  }) => {
    return {
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    };
  },
};

// Smooth scale transition
export const scaleTransition = {
  gestureEnabled: true,
  transitionSpec: {
    open: {
      animation: 'timing',
      config: {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      },
    } as TransitionSpec,
    close: {
      animation: 'timing',
      config: {
        duration: 400,
        easing: Easing.in(Easing.cubic),
      },
    } as TransitionSpec,
  },
  cardStyleInterpolator: ({ current }: { current: { progress: number } }) => {
    return {
      cardStyle: {
        opacity: current.progress,
        transform: [
          {
            scale: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [0.9, 1],
            }),
          },
        ],
      },
    };
  },
};
