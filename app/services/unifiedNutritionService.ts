/**
 * Unified Nutrition Service - Single source of truth for all nutrition calculations
 * 
 * This service consolidates all nutrition-related calculations to ensure consistency
 * across the entire application. All other services should use this for calculations.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest, getRequest } from './apiRequestManager';

// Standardized constants - these are the single source of truth
export const NUTRITION_CONSTANTS = {
  // Activity level multipliers for TDEE calculation
  ACTIVITY_MULTIPLIERS: {
    sedentary: 1.2,
    lightlyActive: 1.375,
    moderatelyActive: 1.55,
    veryActive: 1.725,
    extraActive: 1.9
  },
  
  // Calorie content per gram of macronutrients
  CALORIES_PER_GRAM: {
    protein: 4,
    carbs: 4,
    fat: 9,
    alcohol: 7
  },
  
  // Protein requirements per pound of body weight
  PROTEIN_MULTIPLIERS: {
    maintenance: 0.8,
    weightLoss: 1.0,
    muscleGain: 1.2,
    athletic: 1.4
  },
  
  // Calorie adjustment percentages for goals
  GOAL_ADJUSTMENTS: {
    maintain: 0,
    lose: -0.20, // 20% deficit (max 500 calories)
    gain: 0.15   // 15% surplus (max 500 calories)
  },
  
  // Weight conversion factors
  CONVERSIONS: {
    lbsToKg: 0.453592,
    inchesToCm: 2.54,
    kgToCaloriesPerDay: 7700 // For weight change calculations
  },
  
  // Reasonable limits for calculations
  LIMITS: {
    minCalories: 1200,
    maxCalories: 6000,
    minBMR: 800,
    maxBMR: 4000,
    maxDeficit: 500,
    maxSurplus: 500
  }
} as const;

export type ActivityLevel = keyof typeof NUTRITION_CONSTANTS.ACTIVITY_MULTIPLIERS;
export type NutritionGoal = 'maintain' | 'lose' | 'gain';
export type Gender = 'male' | 'female' | 'other';

export interface NutritionProfile {
  weight: number; // pounds
  height?: number; // inches
  age?: number;
  gender?: Gender;
  activityLevel: ActivityLevel;
  goal: NutritionGoal;
  bodyFatPercentage?: number;
  dietaryRestrictions?: string[];
  lastUpdated: string;
}

export interface NutritionTargets {
  calories: number;
  protein: number; // grams
  carbs: number;   // grams
  fat: number;     // grams
  fiber?: number;  // grams
  sugar?: number;  // grams
  sodium?: number; // mg
}

export interface BMRCalculationResult {
  bmr: number;
  method: 'mifflin-st-jeor' | 'harris-benedict' | 'weight-based-estimate';
  confidence: 'high' | 'medium' | 'low';
  inputs: {
    weight: number;
    height?: number;
    age?: number;
    gender?: Gender;
  };
}

export interface TDEECalculationResult {
  tdee: number;
  bmr: number;
  activityMultiplier: number;
  method: 'calculated' | 'dynamic' | 'estimated';
  confidence: 'high' | 'medium' | 'low';
}

export interface MacroCalculationResult {
  protein: number;
  carbs: number;
  fat: number;
  proteinCalories: number;
  carbCalories: number;
  fatCalories: number;
  totalCalories: number;
  ratios: {
    protein: number;
    carbs: number;
    fat: number;
  };
}

const CACHE_KEYS = {
  PROFILE: '@unified_nutrition_profile',
  TARGETS: '@unified_nutrition_targets',
  BMR_CACHE: '@unified_bmr_cache',
  TDEE_CACHE: '@unified_tdee_cache'
};

const CACHE_TTL = {
  PROFILE: 24 * 60 * 60 * 1000, // 24 hours
  TARGETS: 60 * 60 * 1000,      // 1 hour
  BMR: 24 * 60 * 60 * 1000,     // 24 hours
  TDEE: 60 * 60 * 1000          // 1 hour
};

class UnifiedNutritionService {
  private profileCache: { profile: NutritionProfile; timestamp: number } | null = null;
  private targetsCache: { targets: NutritionTargets; timestamp: number } | null = null;

  /**
   * Calculate BMR using the most appropriate method based on available data
   */
  calculateBMR(profile: NutritionProfile): BMRCalculationResult {
    const { weight, height, age, gender } = profile;
    const weightKg = weight * NUTRITION_CONSTANTS.CONVERSIONS.lbsToKg;

    // Method 1: Mifflin-St Jeor (most accurate when all data available)
    if (height && age && gender && gender !== 'other') {
      const heightCm = height * NUTRITION_CONSTANTS.CONVERSIONS.inchesToCm;
      let bmr: number;

      if (gender === 'male') {
        bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) + 5;
      } else {
        bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) - 161;
      }

      return {
        bmr: Math.max(NUTRITION_CONSTANTS.LIMITS.minBMR, Math.min(NUTRITION_CONSTANTS.LIMITS.maxBMR, bmr)),
        method: 'mifflin-st-jeor',
        confidence: 'high',
        inputs: { weight, height, age, gender }
      };
    }

    // Method 2: Harris-Benedict (if we have height and age but not gender)
    if (height && age) {
      const heightCm = height * NUTRITION_CONSTANTS.CONVERSIONS.inchesToCm;
      // Use average of male and female formulas
      const maleBMR = 88.362 + (13.397 * weightKg) + (4.799 * heightCm) - (5.677 * age);
      const femaleBMR = 447.593 + (9.247 * weightKg) + (3.098 * heightCm) - (4.330 * age);
      const bmr = (maleBMR + femaleBMR) / 2;

      return {
        bmr: Math.max(NUTRITION_CONSTANTS.LIMITS.minBMR, Math.min(NUTRITION_CONSTANTS.LIMITS.maxBMR, bmr)),
        method: 'harris-benedict',
        confidence: 'medium',
        inputs: { weight, height, age, gender }
      };
    }

    // Method 3: Weight-based estimate (least accurate but always available)
    const bmr = weightKg * 24; // Basic metabolic rate estimate

    return {
      bmr: Math.max(NUTRITION_CONSTANTS.LIMITS.minBMR, Math.min(NUTRITION_CONSTANTS.LIMITS.maxBMR, bmr)),
      method: 'weight-based-estimate',
      confidence: 'low',
      inputs: { weight, height, age, gender }
    };
  }

  /**
   * Calculate TDEE using BMR and activity level
   */
  calculateTDEE(bmrResult: BMRCalculationResult, activityLevel: ActivityLevel): TDEECalculationResult {
    const activityMultiplier = NUTRITION_CONSTANTS.ACTIVITY_MULTIPLIERS[activityLevel];
    const tdee = bmrResult.bmr * activityMultiplier;

    return {
      tdee: Math.max(NUTRITION_CONSTANTS.LIMITS.minCalories, Math.min(NUTRITION_CONSTANTS.LIMITS.maxCalories, tdee)),
      bmr: bmrResult.bmr,
      activityMultiplier,
      method: 'calculated',
      confidence: bmrResult.confidence
    };
  }

  /**
   * Calculate target calories based on TDEE and goal
   */
  calculateTargetCalories(tdee: number, goal: NutritionGoal): number {
    const adjustment = NUTRITION_CONSTANTS.GOAL_ADJUSTMENTS[goal];
    let targetCalories: number;

    if (goal === 'lose') {
      const deficit = Math.min(NUTRITION_CONSTANTS.LIMITS.maxDeficit, Math.abs(tdee * adjustment));
      targetCalories = tdee - deficit;
    } else if (goal === 'gain') {
      const surplus = Math.min(NUTRITION_CONSTANTS.LIMITS.maxSurplus, tdee * adjustment);
      targetCalories = tdee + surplus;
    } else {
      targetCalories = tdee;
    }

    return Math.max(NUTRITION_CONSTANTS.LIMITS.minCalories, Math.min(NUTRITION_CONSTANTS.LIMITS.maxCalories, Math.round(targetCalories)));
  }

  /**
   * Calculate macro targets based on calories and profile
   */
  calculateMacroTargets(calories: number, profile: NutritionProfile): MacroCalculationResult {
    // Determine protein multiplier based on goal
    let proteinMultiplier: number;
    switch (profile.goal) {
      case 'lose':
        proteinMultiplier = NUTRITION_CONSTANTS.PROTEIN_MULTIPLIERS.weightLoss;
        break;
      case 'gain':
        proteinMultiplier = NUTRITION_CONSTANTS.PROTEIN_MULTIPLIERS.muscleGain;
        break;
      default:
        proteinMultiplier = NUTRITION_CONSTANTS.PROTEIN_MULTIPLIERS.maintenance;
    }

    // Calculate protein in grams
    const proteinGrams = Math.round(profile.weight * proteinMultiplier);
    const proteinCalories = proteinGrams * NUTRITION_CONSTANTS.CALORIES_PER_GRAM.protein;

    // Calculate remaining calories for carbs and fat
    const remainingCalories = calories - proteinCalories;

    // Determine carb/fat ratio based on goal
    let carbRatio: number;
    switch (profile.goal) {
      case 'lose':
        carbRatio = 0.50; // Lower carbs for fat loss
        break;
      case 'gain':
        carbRatio = 0.65; // Higher carbs for muscle gain
        break;
      default:
        carbRatio = 0.60; // Balanced for maintenance
    }

    // Calculate carbs and fat
    const carbCalories = remainingCalories * carbRatio;
    const fatCalories = remainingCalories * (1 - carbRatio);

    const carbGrams = Math.round(carbCalories / NUTRITION_CONSTANTS.CALORIES_PER_GRAM.carbs);
    const fatGrams = Math.round(fatCalories / NUTRITION_CONSTANTS.CALORIES_PER_GRAM.fat);

    // Calculate actual ratios
    const totalCaloriesCheck = proteinCalories + carbCalories + fatCalories;
    const ratios = {
      protein: proteinCalories / totalCaloriesCheck,
      carbs: carbCalories / totalCaloriesCheck,
      fat: fatCalories / totalCaloriesCheck
    };

    return {
      protein: proteinGrams,
      carbs: carbGrams,
      fat: fatGrams,
      proteinCalories,
      carbCalories,
      fatCalories,
      totalCalories: totalCaloriesCheck,
      ratios
    };
  }

  /**
   * Get complete nutrition targets for a profile
   */
  async calculateNutritionTargets(profile: NutritionProfile): Promise<{
    targets: NutritionTargets;
    calculations: {
      bmr: BMRCalculationResult;
      tdee: TDEECalculationResult;
      macros: MacroCalculationResult;
    };
  }> {
    // Calculate BMR
    const bmrResult = this.calculateBMR(profile);
    
    // Calculate TDEE
    const tdeeResult = this.calculateTDEE(bmrResult, profile.activityLevel);
    
    // Calculate target calories
    const targetCalories = this.calculateTargetCalories(tdeeResult.tdee, profile.goal);
    
    // Calculate macros
    const macroResult = this.calculateMacroTargets(targetCalories, profile);

    // Calculate additional targets
    const fiberTarget = Math.round(targetCalories / 1000 * 14); // 14g per 1000 calories
    const sugarTarget = Math.round(targetCalories * 0.10 / 4); // 10% of calories from sugar
    const sodiumTarget = 2300; // Standard recommendation in mg

    const targets: NutritionTargets = {
      calories: targetCalories,
      protein: macroResult.protein,
      carbs: macroResult.carbs,
      fat: macroResult.fat,
      fiber: fiberTarget,
      sugar: sugarTarget,
      sodium: sodiumTarget
    };

    return {
      targets,
      calculations: {
        bmr: bmrResult,
        tdee: tdeeResult,
        macros: macroResult
      }
    };
  }

  /**
   * Validate nutrition profile data
   */
  validateProfile(profile: Partial<NutritionProfile>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!profile.weight || profile.weight <= 0 || profile.weight > 1000) {
      errors.push('Weight must be between 1 and 1000 pounds');
    }

    if (profile.height && (profile.height <= 0 || profile.height > 120)) {
      errors.push('Height must be between 1 and 120 inches');
    }

    if (profile.age && (profile.age <= 0 || profile.age > 120)) {
      errors.push('Age must be between 1 and 120 years');
    }

    if (profile.activityLevel && !Object.keys(NUTRITION_CONSTANTS.ACTIVITY_MULTIPLIERS).includes(profile.activityLevel)) {
      errors.push('Invalid activity level');
    }

    if (profile.goal && !['maintain', 'lose', 'gain'].includes(profile.goal)) {
      errors.push('Goal must be maintain, lose, or gain');
    }

    if (profile.bodyFatPercentage && (profile.bodyFatPercentage < 3 || profile.bodyFatPercentage > 50)) {
      errors.push('Body fat percentage must be between 3% and 50%');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get cached profile or load from storage
   */
  async getProfile(): Promise<NutritionProfile | null> {
    const now = Date.now();
    
    // Check cache first
    if (this.profileCache && (now - this.profileCache.timestamp) < CACHE_TTL.PROFILE) {
      return this.profileCache.profile;
    }

    try {
      const stored = await AsyncStorage.getItem(CACHE_KEYS.PROFILE);
      if (stored) {
        const profile = JSON.parse(stored) as NutritionProfile;
        this.profileCache = { profile, timestamp: now };
        return profile;
      }
    } catch (error) {
      console.error('[UnifiedNutrition] Error loading profile:', error);
    }

    return null;
  }

  /**
   * Save profile to storage and cache
   */
  async saveProfile(profile: NutritionProfile): Promise<void> {
    const validation = this.validateProfile(profile);
    if (!validation.isValid) {
      throw new Error(`Invalid profile: ${validation.errors.join(', ')}`);
    }

    const profileWithTimestamp = {
      ...profile,
      lastUpdated: new Date().toISOString()
    };

    try {
      await AsyncStorage.setItem(CACHE_KEYS.PROFILE, JSON.stringify(profileWithTimestamp));
      this.profileCache = { profile: profileWithTimestamp, timestamp: Date.now() };
      
      // Clear targets cache when profile changes
      this.targetsCache = null;
      await AsyncStorage.removeItem(CACHE_KEYS.TARGETS);
      
      console.log('[UnifiedNutrition] Profile saved successfully');
    } catch (error) {
      console.error('[UnifiedNutrition] Error saving profile:', error);
      throw error;
    }
  }

  /**
   * Get nutrition targets with caching
   */
  async getTargets(forceRefresh = false): Promise<NutritionTargets | null> {
    const now = Date.now();
    
    // Check cache first
    if (!forceRefresh && this.targetsCache && (now - this.targetsCache.timestamp) < CACHE_TTL.TARGETS) {
      return this.targetsCache.targets;
    }

    const profile = await this.getProfile();
    if (!profile) {
      return null;
    }

    try {
      const result = await this.calculateNutritionTargets(profile);
      this.targetsCache = { targets: result.targets, timestamp: now };
      
      // Cache to storage
      await AsyncStorage.setItem(CACHE_KEYS.TARGETS, JSON.stringify({
        targets: result.targets,
        timestamp: now
      }));

      return result.targets;
    } catch (error) {
      console.error('[UnifiedNutrition] Error calculating targets:', error);
      return null;
    }
  }

  /**
   * Clear all caches
   */
  async clearCache(): Promise<void> {
    this.profileCache = null;
    this.targetsCache = null;
    
    await Promise.all([
      AsyncStorage.removeItem(CACHE_KEYS.PROFILE),
      AsyncStorage.removeItem(CACHE_KEYS.TARGETS),
      AsyncStorage.removeItem(CACHE_KEYS.BMR_CACHE),
      AsyncStorage.removeItem(CACHE_KEYS.TDEE_CACHE)
    ]);
  }
}

// Export singleton instance
export const unifiedNutritionService = new UnifiedNutritionService();

// Convenience functions
export const calculateBMR = (profile: NutritionProfile) => 
  unifiedNutritionService.calculateBMR(profile);

export const calculateTDEE = (bmrResult: BMRCalculationResult, activityLevel: ActivityLevel) =>
  unifiedNutritionService.calculateTDEE(bmrResult, activityLevel);

export const calculateNutritionTargets = (profile: NutritionProfile) =>
  unifiedNutritionService.calculateNutritionTargets(profile);

export const getNutritionProfile = () =>
  unifiedNutritionService.getProfile();

export const saveNutritionProfile = (profile: NutritionProfile) =>
  unifiedNutritionService.saveProfile(profile);

export const getNutritionTargets = (forceRefresh = false) =>
  unifiedNutritionService.getTargets(forceRefresh);

export const validateNutritionProfile = (profile: Partial<NutritionProfile>) =>
  unifiedNutritionService.validateProfile(profile);

export const clearNutritionCache = () =>
  unifiedNutritionService.clearCache();

// Migration helper to update existing services
export const migrateToUnifiedService = async () => {
  console.log('[UnifiedNutrition] Starting migration to unified service...');
  // This would contain logic to migrate existing data to the new unified format
  // Implementation would depend on existing data structures
};
