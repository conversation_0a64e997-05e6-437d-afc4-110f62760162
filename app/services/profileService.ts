import { getProfileFromStorage } from './profile';

/**
 * Get the user profile
 * 
 * @returns The user profile or null if not found
 */
export async function getUserProfile() {
  try {
    return await getProfileFromStorage();
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

/**
 * Check if the user has a profile
 * 
 * @returns True if the user has a profile, false otherwise
 */
export async function hasUserProfile(): Promise<boolean> {
  try {
    const profile = await getProfileFromStorage();
    return !!profile && !!profile.name;
  } catch (error) {
    console.error('Error checking if user has profile:', error);
    return false;
  }
}

export default {
  getUserProfile,
  hasUserProfile
};
