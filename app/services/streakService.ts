/**
 * Streak Service - Tracks user progress streaks and habits
 * 
 * Features:
 * - Multiple streak types (workout, nutrition, weight logging, etc.)
 * - Automatic streak calculation and maintenance
 * - Streak recovery (grace periods)
 * - Historical streak data
 * - Streak achievements and milestones
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest, getRequest } from './apiRequestManager';

export enum StreakType {
  WORKOUT = 'workout',
  NUTRITION_LOGGING = 'nutrition_logging',
  WEIGHT_LOGGING = 'weight_logging',
  DAILY_CHECK_IN = 'daily_check_in',
  MEAL_PLANNING = 'meal_planning',
  HYDRATION = 'hydration',
  SLEEP_LOGGING = 'sleep_logging',
  STEP_GOAL = 'step_goal'
}

export interface StreakData {
  type: StreakType;
  currentStreak: number;
  longestStreak: number;
  lastActivityDate: string;
  streakStartDate: string;
  totalActiveDays: number;
  gracePeriodUsed: boolean;
  gracePeriodExpiry?: string;
  milestones: StreakMilestone[];
  weeklyGoal: number;
  monthlyGoal: number;
}

export interface StreakMilestone {
  days: number;
  achievedDate: string;
  title: string;
  description: string;
  reward?: string;
}

export interface StreakActivity {
  type: StreakType;
  date: string;
  value?: number; // For quantifiable activities
  metadata?: Record<string, any>;
}

export interface StreakSummary {
  totalActiveStreaks: number;
  longestCurrentStreak: StreakData;
  recentAchievements: StreakMilestone[];
  weeklyProgress: {
    type: StreakType;
    completed: number;
    target: number;
    percentage: number;
  }[];
}

const STREAK_STORAGE_KEY = '@streak_data';
const STREAK_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

class StreakService {
  private streakCache: Map<StreakType, StreakData> = new Map();
  private lastCacheUpdate: number = 0;

  /**
   * Initialize default streak data for a user
   */
  async initializeUserStreaks(): Promise<void> {
    const existingData = await this.getAllStreaks();
    
    // Initialize default streaks if they don't exist
    const defaultStreaks: StreakType[] = [
      StreakType.WORKOUT,
      StreakType.NUTRITION_LOGGING,
      StreakType.WEIGHT_LOGGING,
      StreakType.DAILY_CHECK_IN
    ];

    for (const type of defaultStreaks) {
      if (!existingData.has(type)) {
        const defaultStreak: StreakData = {
          type,
          currentStreak: 0,
          longestStreak: 0,
          lastActivityDate: '',
          streakStartDate: '',
          totalActiveDays: 0,
          gracePeriodUsed: false,
          milestones: [],
          weeklyGoal: this.getDefaultWeeklyGoal(type),
          monthlyGoal: this.getDefaultMonthlyGoal(type)
        };
        
        await this.updateStreak(type, defaultStreak);
      }
    }
  }

  /**
   * Record a streak activity
   */
  async recordActivity(activity: StreakActivity): Promise<StreakData> {
    const currentStreak = await this.getStreak(activity.type);
    const today = new Date().toISOString().split('T')[0];
    const activityDate = activity.date.split('T')[0];
    
    // Don't record future activities
    if (activityDate > today) {
      throw new Error('Cannot record future activities');
    }

    // Check if activity already recorded for this date
    if (currentStreak.lastActivityDate.split('T')[0] === activityDate) {
      console.log(`[Streak] Activity already recorded for ${activity.type} on ${activityDate}`);
      return currentStreak;
    }

    const updatedStreak = this.calculateStreakUpdate(currentStreak, activityDate);
    
    // Check for new milestones
    const newMilestones = this.checkForMilestones(updatedStreak);
    updatedStreak.milestones.push(...newMilestones);

    // Update local storage and sync to server
    await this.updateStreak(activity.type, updatedStreak);
    
    // Sync to server in background
    this.syncStreakToServer(activity.type, updatedStreak).catch(error => {
      console.error('[Streak] Error syncing to server:', error);
    });

    console.log(`[Streak] Recorded ${activity.type} activity. Current streak: ${updatedStreak.currentStreak}`);
    
    return updatedStreak;
  }

  /**
   * Get streak data for a specific type
   */
  async getStreak(type: StreakType): Promise<StreakData> {
    const now = Date.now();
    
    // Check cache first
    if (this.streakCache.has(type) && (now - this.lastCacheUpdate) < STREAK_CACHE_TTL) {
      return this.streakCache.get(type)!;
    }

    try {
      // Try to get from local storage first
      const stored = await AsyncStorage.getItem(`${STREAK_STORAGE_KEY}_${type}`);
      if (stored) {
        const streakData = JSON.parse(stored) as StreakData;
        this.streakCache.set(type, streakData);
        return streakData;
      }
    } catch (error) {
      console.error('[Streak] Error loading from storage:', error);
    }

    // Return default if not found
    return this.getDefaultStreak(type);
  }

  /**
   * Get all streak data
   */
  async getAllStreaks(): Promise<Map<StreakType, StreakData>> {
    const streaks = new Map<StreakType, StreakData>();
    
    for (const type of Object.values(StreakType)) {
      const streak = await this.getStreak(type);
      streaks.set(type, streak);
    }
    
    return streaks;
  }

  /**
   * Get streak summary for dashboard
   */
  async getStreakSummary(): Promise<StreakSummary> {
    const allStreaks = await this.getAllStreaks();
    const streakArray = Array.from(allStreaks.values());
    
    // Find longest current streak
    const longestCurrentStreak = streakArray.reduce((longest, current) => 
      current.currentStreak > longest.currentStreak ? current : longest
    );

    // Get recent achievements (last 7 days)
    const recentAchievements: StreakMilestone[] = [];
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    
    streakArray.forEach(streak => {
      streak.milestones.forEach(milestone => {
        if (milestone.achievedDate >= sevenDaysAgo) {
          recentAchievements.push(milestone);
        }
      });
    });

    // Calculate weekly progress
    const weeklyProgress = streakArray.map(streak => {
      const weekStart = this.getWeekStart();
      const activeDaysThisWeek = this.getActiveDaysInWeek(streak, weekStart);
      
      return {
        type: streak.type,
        completed: activeDaysThisWeek,
        target: streak.weeklyGoal,
        percentage: Math.min(100, (activeDaysThisWeek / streak.weeklyGoal) * 100)
      };
    });

    return {
      totalActiveStreaks: streakArray.filter(s => s.currentStreak > 0).length,
      longestCurrentStreak,
      recentAchievements: recentAchievements.sort((a, b) => 
        new Date(b.achievedDate).getTime() - new Date(a.achievedDate).getTime()
      ),
      weeklyProgress
    };
  }

  /**
   * Calculate streak update based on new activity
   */
  private calculateStreakUpdate(currentStreak: StreakData, activityDate: string): StreakData {
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const lastActivity = currentStreak.lastActivityDate.split('T')[0];
    
    let newStreak = { ...currentStreak };
    newStreak.lastActivityDate = activityDate;
    newStreak.totalActiveDays += 1;

    // If this is the first activity
    if (!lastActivity) {
      newStreak.currentStreak = 1;
      newStreak.streakStartDate = activityDate;
      newStreak.longestStreak = Math.max(1, newStreak.longestStreak);
      return newStreak;
    }

    // Calculate days between last activity and this activity
    const daysBetween = this.getDaysBetween(lastActivity, activityDate);

    if (daysBetween === 1) {
      // Consecutive day - extend streak
      newStreak.currentStreak += 1;
      newStreak.longestStreak = Math.max(newStreak.currentStreak, newStreak.longestStreak);
    } else if (daysBetween === 0) {
      // Same day - no change to streak
      return currentStreak;
    } else if (daysBetween === 2 && !newStreak.gracePeriodUsed) {
      // One day gap - use grace period if available
      newStreak.currentStreak += 1;
      newStreak.gracePeriodUsed = true;
      newStreak.gracePeriodExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
      newStreak.longestStreak = Math.max(newStreak.currentStreak, newStreak.longestStreak);
    } else {
      // Streak broken - reset
      newStreak.currentStreak = 1;
      newStreak.streakStartDate = activityDate;
      newStreak.gracePeriodUsed = false;
      newStreak.gracePeriodExpiry = undefined;
    }

    return newStreak;
  }

  /**
   * Check for new milestones
   */
  private checkForMilestones(streak: StreakData): StreakMilestone[] {
    const milestones: StreakMilestone[] = [];
    const currentStreak = streak.currentStreak;
    
    // Define milestone thresholds
    const thresholds = [3, 7, 14, 30, 50, 75, 100, 150, 200, 365];
    
    for (const threshold of thresholds) {
      if (currentStreak >= threshold) {
        // Check if this milestone hasn't been achieved yet
        const alreadyAchieved = streak.milestones.some(m => m.days === threshold);
        if (!alreadyAchieved) {
          milestones.push({
            days: threshold,
            achievedDate: new Date().toISOString(),
            title: this.getMilestoneTitle(streak.type, threshold),
            description: this.getMilestoneDescription(streak.type, threshold),
            reward: this.getMilestoneReward(threshold)
          });
        }
      }
    }
    
    return milestones;
  }

  /**
   * Helper methods
   */
  private getDefaultStreak(type: StreakType): StreakData {
    return {
      type,
      currentStreak: 0,
      longestStreak: 0,
      lastActivityDate: '',
      streakStartDate: '',
      totalActiveDays: 0,
      gracePeriodUsed: false,
      milestones: [],
      weeklyGoal: this.getDefaultWeeklyGoal(type),
      monthlyGoal: this.getDefaultMonthlyGoal(type)
    };
  }

  private getDefaultWeeklyGoal(type: StreakType): number {
    switch (type) {
      case StreakType.WORKOUT: return 4;
      case StreakType.NUTRITION_LOGGING: return 7;
      case StreakType.WEIGHT_LOGGING: return 2;
      case StreakType.DAILY_CHECK_IN: return 7;
      case StreakType.MEAL_PLANNING: return 5;
      case StreakType.HYDRATION: return 7;
      case StreakType.SLEEP_LOGGING: return 7;
      case StreakType.STEP_GOAL: return 5;
      default: return 3;
    }
  }

  private getDefaultMonthlyGoal(type: StreakType): number {
    return this.getDefaultWeeklyGoal(type) * 4;
  }

  private getDaysBetween(date1: string, date2: string): number {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private getWeekStart(): string {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    const weekStart = new Date(now.setDate(diff));
    return weekStart.toISOString().split('T')[0];
  }

  private getActiveDaysInWeek(streak: StreakData, weekStart: string): number {
    // This would need to be implemented with actual activity data
    // For now, return a simple calculation based on current streak
    const today = new Date().toISOString().split('T')[0];
    const lastActivity = streak.lastActivityDate.split('T')[0];
    
    if (lastActivity >= weekStart && lastActivity <= today) {
      return Math.min(streak.currentStreak, 7);
    }
    
    return 0;
  }

  private getMilestoneTitle(type: StreakType, days: number): string {
    const typeNames = {
      [StreakType.WORKOUT]: 'Workout',
      [StreakType.NUTRITION_LOGGING]: 'Nutrition',
      [StreakType.WEIGHT_LOGGING]: 'Weight Tracking',
      [StreakType.DAILY_CHECK_IN]: 'Daily Check-in',
      [StreakType.MEAL_PLANNING]: 'Meal Planning',
      [StreakType.HYDRATION]: 'Hydration',
      [StreakType.SLEEP_LOGGING]: 'Sleep',
      [StreakType.STEP_GOAL]: 'Steps'
    };
    
    return `${days}-Day ${typeNames[type]} Streak!`;
  }

  private getMilestoneDescription(type: StreakType, days: number): string {
    return `Congratulations! You've maintained your ${type.replace('_', ' ')} habit for ${days} consecutive days.`;
  }

  private getMilestoneReward(days: number): string | undefined {
    if (days >= 365) return 'Year Champion Badge';
    if (days >= 100) return 'Century Club Badge';
    if (days >= 30) return 'Monthly Master Badge';
    if (days >= 7) return 'Weekly Warrior Badge';
    return undefined;
  }

  private async updateStreak(type: StreakType, streak: StreakData): Promise<void> {
    try {
      await AsyncStorage.setItem(`${STREAK_STORAGE_KEY}_${type}`, JSON.stringify(streak));
      this.streakCache.set(type, streak);
      this.lastCacheUpdate = Date.now();
    } catch (error) {
      console.error('[Streak] Error saving to storage:', error);
    }
  }

  private async syncStreakToServer(type: StreakType, streak: StreakData): Promise<void> {
    try {
      await postRequest('/streaks', {
        type,
        data: streak
      }, {
        retries: 2,
        retryDelay: 1000,
        logTag: '[Streak]'
      });
    } catch (error) {
      console.error('[Streak] Error syncing to server:', error);
    }
  }
}

// Export singleton instance
export const streakService = new StreakService();

// Convenience functions
export const recordStreakActivity = (activity: StreakActivity) => 
  streakService.recordActivity(activity);

export const getStreakData = (type: StreakType) => 
  streakService.getStreak(type);

export const getStreakSummary = () => 
  streakService.getStreakSummary();

export const initializeUserStreaks = () => 
  streakService.initializeUserStreaks();
