/**
 * Habit Tracking Service - Advanced habit formation and tracking system
 * 
 * Features:
 * - Custom habit creation and management
 * - Habit completion tracking with flexible scheduling
 * - Habit analytics and insights
 * - Habit stacking and dependency management
 * - Behavioral pattern analysis
 * - Habit difficulty progression
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest, getRequest } from './apiRequestManager';

export enum HabitFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  WEEKDAYS = 'weekdays',
  WEEKENDS = 'weekends',
  CUSTOM = 'custom'
}

export enum HabitCategory {
  FITNESS = 'fitness',
  NUTRITION = 'nutrition',
  WELLNESS = 'wellness',
  MINDFULNESS = 'mindfulness',
  PRODUCTIVITY = 'productivity',
  CUSTOM = 'custom'
}

export enum HabitDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

export interface Habit {
  id: string;
  name: string;
  description: string;
  category: HabitCategory;
  frequency: HabitFrequency;
  customSchedule?: number[]; // Days of week (0-6) for custom frequency
  targetValue?: number; // For quantifiable habits (e.g., 8 glasses of water)
  unit?: string; // Unit for target value (e.g., 'glasses', 'minutes')
  difficulty: HabitDifficulty;
  isActive: boolean;
  createdAt: string;
  startDate: string;
  endDate?: string; // For time-limited habits
  reminderTime?: string; // Time for notifications
  stackedWith?: string; // ID of habit this is stacked with
  color: string;
  icon: string;
  motivationalQuote?: string;
  rewards: HabitReward[];
}

export interface HabitCompletion {
  id: string;
  habitId: string;
  date: string;
  completed: boolean;
  value?: number; // Actual value achieved
  notes?: string;
  mood?: number; // 1-5 scale
  difficulty?: number; // How difficult it felt (1-5)
  timestamp: string;
}

export interface HabitReward {
  id: string;
  name: string;
  description: string;
  requiredCompletions: number;
  isUnlocked: boolean;
  unlockedAt?: string;
}

export interface HabitAnalytics {
  habitId: string;
  totalCompletions: number;
  currentStreak: number;
  longestStreak: number;
  completionRate: number; // Percentage
  averageValue?: number;
  weeklyTrend: number; // -1 to 1
  monthlyTrend: number;
  bestDay: string; // Day of week with highest completion rate
  averageMood: number;
  averageDifficulty: number;
  consistencyScore: number; // 0-100
}

export interface HabitSummary {
  totalHabits: number;
  activeHabits: number;
  completedToday: number;
  currentStreaks: number;
  overallCompletionRate: number;
  topPerformingHabit: Habit | null;
  strugglingHabit: Habit | null;
  weeklyProgress: {
    date: string;
    completions: number;
    target: number;
  }[];
}

const HABITS_STORAGE_KEY = '@habits';
const COMPLETIONS_STORAGE_KEY = '@habit_completions';
const ANALYTICS_STORAGE_KEY = '@habit_analytics';

class HabitTrackingService {
  private habits: Map<string, Habit> = new Map();
  private completions: Map<string, HabitCompletion[]> = new Map();
  private analytics: Map<string, HabitAnalytics> = new Map();

  /**
   * Create a new habit
   */
  async createHabit(habitData: Omit<Habit, 'id' | 'createdAt' | 'rewards'>): Promise<Habit> {
    const habit: Habit = {
      ...habitData,
      id: `habit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      rewards: this.generateDefaultRewards()
    };

    this.habits.set(habit.id, habit);
    await this.saveHabits();
    
    // Initialize analytics
    await this.initializeHabitAnalytics(habit.id);

    console.log(`[HabitTracking] Created habit: ${habit.name}`);
    return habit;
  }

  /**
   * Record habit completion
   */
  async recordCompletion(
    habitId: string, 
    completed: boolean, 
    value?: number, 
    notes?: string,
    mood?: number,
    difficulty?: number
  ): Promise<HabitCompletion> {
    const today = new Date().toISOString().split('T')[0];
    
    // Check if already recorded today
    const existingCompletion = await this.getCompletionForDate(habitId, today);
    
    const completion: HabitCompletion = {
      id: existingCompletion?.id || `completion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      habitId,
      date: today,
      completed,
      value,
      notes,
      mood,
      difficulty,
      timestamp: new Date().toISOString()
    };

    // Update completions
    const habitCompletions = this.completions.get(habitId) || [];
    const existingIndex = habitCompletions.findIndex(c => c.date === today);
    
    if (existingIndex >= 0) {
      habitCompletions[existingIndex] = completion;
    } else {
      habitCompletions.push(completion);
    }
    
    this.completions.set(habitId, habitCompletions);
    await this.saveCompletions();

    // Update analytics
    await this.updateHabitAnalytics(habitId);

    // Check for rewards
    await this.checkForRewards(habitId);

    console.log(`[HabitTracking] Recorded completion for habit ${habitId}: ${completed}`);
    return completion;
  }

  /**
   * Get all active habits
   */
  async getActiveHabits(): Promise<Habit[]> {
    await this.loadHabits();
    return Array.from(this.habits.values()).filter(h => h.isActive);
  }

  /**
   * Get habit by ID
   */
  async getHabit(habitId: string): Promise<Habit | null> {
    await this.loadHabits();
    return this.habits.get(habitId) || null;
  }

  /**
   * Get habit analytics
   */
  async getHabitAnalytics(habitId: string): Promise<HabitAnalytics | null> {
    await this.loadAnalytics();
    return this.analytics.get(habitId) || null;
  }

  /**
   * Get habits summary for dashboard
   */
  async getHabitsSummary(): Promise<HabitSummary> {
    const habits = await this.getActiveHabits();
    const today = new Date().toISOString().split('T')[0];
    
    let completedToday = 0;
    let currentStreaks = 0;
    let totalCompletionRate = 0;
    let topPerformingHabit: Habit | null = null;
    let strugglingHabit: Habit | null = null;
    let maxCompletionRate = 0;
    let minCompletionRate = 100;

    // Calculate metrics for each habit
    for (const habit of habits) {
      const analytics = await this.getHabitAnalytics(habit.id);
      if (!analytics) continue;

      // Check if completed today
      const todayCompletion = await this.getCompletionForDate(habit.id, today);
      if (todayCompletion?.completed) {
        completedToday++;
      }

      // Count current streaks
      if (analytics.currentStreak > 0) {
        currentStreaks++;
      }

      // Track completion rates
      totalCompletionRate += analytics.completionRate;
      
      if (analytics.completionRate > maxCompletionRate) {
        maxCompletionRate = analytics.completionRate;
        topPerformingHabit = habit;
      }
      
      if (analytics.completionRate < minCompletionRate) {
        minCompletionRate = analytics.completionRate;
        strugglingHabit = habit;
      }
    }

    // Generate weekly progress
    const weeklyProgress = await this.getWeeklyProgress();

    return {
      totalHabits: habits.length,
      activeHabits: habits.length,
      completedToday,
      currentStreaks,
      overallCompletionRate: habits.length > 0 ? totalCompletionRate / habits.length : 0,
      topPerformingHabit,
      strugglingHabit: minCompletionRate < 70 ? strugglingHabit : null,
      weeklyProgress
    };
  }

  /**
   * Get habit recommendations based on user patterns
   */
  async getHabitRecommendations(): Promise<Habit[]> {
    const userHabits = await this.getActiveHabits();
    const recommendations: Partial<Habit>[] = [];

    // Analyze user's current habits to suggest complementary ones
    const categories = userHabits.map(h => h.category);
    
    // Fitness recommendations
    if (!categories.includes(HabitCategory.FITNESS)) {
      recommendations.push({
        name: 'Daily Walk',
        description: 'Take a 10-minute walk every day',
        category: HabitCategory.FITNESS,
        frequency: HabitFrequency.DAILY,
        targetValue: 10,
        unit: 'minutes',
        difficulty: HabitDifficulty.EASY,
        color: '#34C759',
        icon: 'walk'
      });
    }

    // Wellness recommendations
    if (!categories.includes(HabitCategory.WELLNESS)) {
      recommendations.push({
        name: 'Drink Water',
        description: 'Drink 8 glasses of water daily',
        category: HabitCategory.WELLNESS,
        frequency: HabitFrequency.DAILY,
        targetValue: 8,
        unit: 'glasses',
        difficulty: HabitDifficulty.EASY,
        color: '#007AFF',
        icon: 'water'
      });
    }

    // Convert to full habits (would be stored as templates)
    return recommendations.map(rec => ({
      ...rec,
      id: '',
      isActive: false,
      createdAt: '',
      startDate: '',
      rewards: []
    })) as Habit[];
  }

  /**
   * Private helper methods
   */
  private async loadHabits(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(HABITS_STORAGE_KEY);
      if (stored) {
        const habitsArray = JSON.parse(stored) as Habit[];
        this.habits.clear();
        habitsArray.forEach(habit => this.habits.set(habit.id, habit));
      }
    } catch (error) {
      console.error('[HabitTracking] Error loading habits:', error);
    }
  }

  private async saveHabits(): Promise<void> {
    try {
      const habitsArray = Array.from(this.habits.values());
      await AsyncStorage.setItem(HABITS_STORAGE_KEY, JSON.stringify(habitsArray));
    } catch (error) {
      console.error('[HabitTracking] Error saving habits:', error);
    }
  }

  private async loadCompletions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(COMPLETIONS_STORAGE_KEY);
      if (stored) {
        const completionsData = JSON.parse(stored);
        this.completions.clear();
        Object.entries(completionsData).forEach(([habitId, completions]) => {
          this.completions.set(habitId, completions as HabitCompletion[]);
        });
      }
    } catch (error) {
      console.error('[HabitTracking] Error loading completions:', error);
    }
  }

  private async saveCompletions(): Promise<void> {
    try {
      const completionsData: Record<string, HabitCompletion[]> = {};
      this.completions.forEach((completions, habitId) => {
        completionsData[habitId] = completions;
      });
      await AsyncStorage.setItem(COMPLETIONS_STORAGE_KEY, JSON.stringify(completionsData));
    } catch (error) {
      console.error('[HabitTracking] Error saving completions:', error);
    }
  }

  private async loadAnalytics(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(ANALYTICS_STORAGE_KEY);
      if (stored) {
        const analyticsData = JSON.parse(stored);
        this.analytics.clear();
        Object.entries(analyticsData).forEach(([habitId, analytics]) => {
          this.analytics.set(habitId, analytics as HabitAnalytics);
        });
      }
    } catch (error) {
      console.error('[HabitTracking] Error loading analytics:', error);
    }
  }

  private async saveAnalytics(): Promise<void> {
    try {
      const analyticsData: Record<string, HabitAnalytics> = {};
      this.analytics.forEach((analytics, habitId) => {
        analyticsData[habitId] = analytics;
      });
      await AsyncStorage.setItem(ANALYTICS_STORAGE_KEY, JSON.stringify(analyticsData));
    } catch (error) {
      console.error('[HabitTracking] Error saving analytics:', error);
    }
  }

  private async getCompletionForDate(habitId: string, date: string): Promise<HabitCompletion | null> {
    await this.loadCompletions();
    const completions = this.completions.get(habitId) || [];
    return completions.find(c => c.date === date) || null;
  }

  private async initializeHabitAnalytics(habitId: string): Promise<void> {
    const analytics: HabitAnalytics = {
      habitId,
      totalCompletions: 0,
      currentStreak: 0,
      longestStreak: 0,
      completionRate: 0,
      weeklyTrend: 0,
      monthlyTrend: 0,
      bestDay: 'Monday',
      averageMood: 0,
      averageDifficulty: 0,
      consistencyScore: 0
    };

    this.analytics.set(habitId, analytics);
    await this.saveAnalytics();
  }

  private async updateHabitAnalytics(habitId: string): Promise<void> {
    await this.loadCompletions();
    const completions = this.completions.get(habitId) || [];
    
    if (completions.length === 0) return;

    // Calculate analytics
    const completedCompletions = completions.filter(c => c.completed);
    const totalCompletions = completedCompletions.length;
    const completionRate = (totalCompletions / completions.length) * 100;

    // Calculate current streak
    let currentStreak = 0;
    const sortedCompletions = completions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    for (const completion of sortedCompletions) {
      if (completion.completed) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Calculate longest streak
    let longestStreak = 0;
    let tempStreak = 0;
    
    for (const completion of sortedCompletions.reverse()) {
      if (completion.completed) {
        tempStreak++;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        tempStreak = 0;
      }
    }

    // Calculate averages
    const moodValues = completedCompletions.map(c => c.mood).filter(m => m !== undefined) as number[];
    const difficultyValues = completedCompletions.map(c => c.difficulty).filter(d => d !== undefined) as number[];
    
    const averageMood = moodValues.length > 0 ? moodValues.reduce((a, b) => a + b, 0) / moodValues.length : 0;
    const averageDifficulty = difficultyValues.length > 0 ? difficultyValues.reduce((a, b) => a + b, 0) / difficultyValues.length : 0;

    const analytics: HabitAnalytics = {
      habitId,
      totalCompletions,
      currentStreak,
      longestStreak,
      completionRate,
      weeklyTrend: 0, // Would calculate from historical data
      monthlyTrend: 0, // Would calculate from historical data
      bestDay: 'Monday', // Would calculate from day-of-week analysis
      averageMood,
      averageDifficulty,
      consistencyScore: completionRate // Simplified consistency score
    };

    this.analytics.set(habitId, analytics);
    await this.saveAnalytics();
  }

  private generateDefaultRewards(): HabitReward[] {
    return [
      {
        id: 'reward_3_days',
        name: '3-Day Starter',
        description: 'Complete your habit for 3 consecutive days',
        requiredCompletions: 3,
        isUnlocked: false
      },
      {
        id: 'reward_7_days',
        name: 'Week Warrior',
        description: 'Complete your habit for 7 consecutive days',
        requiredCompletions: 7,
        isUnlocked: false
      },
      {
        id: 'reward_30_days',
        name: 'Monthly Master',
        description: 'Complete your habit for 30 consecutive days',
        requiredCompletions: 30,
        isUnlocked: false
      }
    ];
  }

  private async checkForRewards(habitId: string): Promise<void> {
    const habit = await this.getHabit(habitId);
    const analytics = await this.getHabitAnalytics(habitId);
    
    if (!habit || !analytics) return;

    let updated = false;
    for (const reward of habit.rewards) {
      if (!reward.isUnlocked && analytics.currentStreak >= reward.requiredCompletions) {
        reward.isUnlocked = true;
        reward.unlockedAt = new Date().toISOString();
        updated = true;
        
        console.log(`[HabitTracking] Reward unlocked: ${reward.name} for habit ${habit.name}`);
      }
    }

    if (updated) {
      this.habits.set(habitId, habit);
      await this.saveHabits();
    }
  }

  private async getWeeklyProgress(): Promise<{ date: string; completions: number; target: number; }[]> {
    const weeklyProgress = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split('T')[0];
      
      // Count completions for this date across all habits
      const habits = await this.getActiveHabits();
      let completions = 0;
      
      for (const habit of habits) {
        const completion = await this.getCompletionForDate(habit.id, dateString);
        if (completion?.completed) {
          completions++;
        }
      }
      
      weeklyProgress.push({
        date: dateString,
        completions,
        target: habits.length
      });
    }
    
    return weeklyProgress;
  }
}

// Export singleton instance
export const habitTrackingService = new HabitTrackingService();

// Convenience functions
export const createHabit = (habitData: Omit<Habit, 'id' | 'createdAt' | 'rewards'>) =>
  habitTrackingService.createHabit(habitData);

export const recordHabitCompletion = (habitId: string, completed: boolean, value?: number, notes?: string, mood?: number, difficulty?: number) =>
  habitTrackingService.recordCompletion(habitId, completed, value, notes, mood, difficulty);

export const getActiveHabits = () =>
  habitTrackingService.getActiveHabits();

export const getHabitsSummary = () =>
  habitTrackingService.getHabitsSummary();

export const getHabitRecommendations = () =>
  habitTrackingService.getHabitRecommendations();

export const getHabitAnalytics = (habitId: string) =>
  habitTrackingService.getHabitAnalytics(habitId);
