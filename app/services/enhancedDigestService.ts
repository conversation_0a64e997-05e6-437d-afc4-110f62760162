/**
 * Enhanced Daily Digest Service - Improved scheduling, trend analysis, and customization
 * 
 * Fixes and enhancements:
 * - Better scheduling logic with user preferences
 * - Trend analysis for activity patterns
 * - Enhanced customization options
 * - Improved error handling and fallbacks
 * - Visual hierarchy improvements
 * - Performance optimizations
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { format, addDays, subDays, isAfter, isBefore, startOfDay, endOfDay } from 'date-fns';
import { postRequest, getRequest } from './apiRequestManager';
import { getUserId } from './profile';
import { DayDigest, DigestActivity } from './digestService';

export interface DigestPreferences {
  userId: string;
  wakeUpTime: string;        // HH:MM format
  bedTime: string;           // HH:MM format
  workoutPreferences: {
    preferredTimes: string[]; // Array of HH:MM times
    duration: number;         // Minutes
    intensity: 'low' | 'medium' | 'high';
    restDays: number[];       // Days of week (0-6) for rest
  };
  mealPreferences: {
    mealsPerDay: number;
    snackFrequency: number;
    dietaryRestrictions: string[];
    preferredCuisines: string[];
  };
  customization: {
    showWeather: boolean;
    showMotivation: boolean;
    showProgress: boolean;
    compactView: boolean;
    autoRefresh: boolean;
    notificationsEnabled: boolean;
  };
  lastUpdated: string;
}

export interface DigestTrend {
  type: 'workout' | 'nutrition' | 'sleep' | 'overall';
  period: '7d' | '30d' | '90d';
  trend: 'improving' | 'stable' | 'declining';
  changePercentage: number;
  insights: string[];
  recommendations: string[];
  confidence: number;        // 0-1 confidence in trend analysis
}

export interface EnhancedDigestActivity extends DigestActivity {
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // Minutes
  energyLevel: 'low' | 'medium' | 'high';
  dependencies: string[];    // IDs of activities this depends on
  alternatives: string[];    // Alternative activity suggestions
  adaptations: {            // Adaptations based on conditions
    weather: Record<string, string>;
    energy: Record<string, string>;
    time: Record<string, string>;
  };
  progressTracking: {
    trackable: boolean;
    metrics: string[];
    targetValue?: number;
    unit?: string;
  };
}

export interface EnhancedDayDigest extends Omit<DayDigest, 'activities'> {
  activities: EnhancedDigestActivity[];
  trends: DigestTrend[];
  insights: {
    dailyFocus: string;
    energyPrediction: 'low' | 'medium' | 'high';
    recommendations: string[];
    motivationalMessage: string;
  };
  metadata: {
    generationTime: string;
    confidence: number;
    dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
    customizationApplied: boolean;
  };
}

export interface DigestSchedulingResult {
  success: boolean;
  activitiesScheduled: number;
  conflicts: Array<{
    activityId: string;
    conflictType: 'time_overlap' | 'energy_conflict' | 'dependency_missing';
    resolution: string;
  }>;
  optimizations: Array<{
    type: 'time_adjustment' | 'activity_swap' | 'priority_reorder';
    description: string;
    impact: string;
  }>;
}

const STORAGE_KEYS = {
  PREFERENCES: '@enhanced_digest_preferences',
  TRENDS_CACHE: '@enhanced_digest_trends',
  SCHEDULING_CACHE: '@enhanced_digest_scheduling',
  CUSTOMIZATION_CACHE: '@enhanced_digest_customization'
};

const SCHEDULING_CONSTANTS = {
  MIN_ACTIVITY_GAP_MINUTES: 15,     // Minimum gap between activities
  MAX_ACTIVITIES_PER_DAY: 12,       // Maximum activities to schedule
  ENERGY_RECOVERY_TIME: 60,         // Minutes to recover energy
  PRIORITY_WEIGHTS: {
    critical: 1.0,
    high: 0.8,
    medium: 0.6,
    low: 0.4
  },
  TREND_ANALYSIS_WINDOW: 30,        // Days to analyze for trends
  CONFIDENCE_THRESHOLD: 0.6         // Minimum confidence for suggestions
};

class EnhancedDigestService {
  private preferences: DigestPreferences | null = null;
  private trendsCache: Map<string, DigestTrend[]> = new Map();
  private lastTrendUpdate: number = 0;

  /**
   * Generate enhanced daily digest with improved scheduling
   */
  async generateEnhancedDigest(
    date: string,
    forceRefresh = false
  ): Promise<EnhancedDayDigest> {
    console.log(`[EnhancedDigest] Generating enhanced digest for ${date}`);

    try {
      // Load user preferences
      const preferences = await this.getPreferences();
      
      // Get base digest from existing service
      const { getDayDigest } = await import('./digestService');
      const baseDigest = await getDayDigest(date);

      // Enhance activities with better scheduling
      const enhancedActivities = await this.enhanceActivities(
        baseDigest.activities,
        preferences,
        date
      );

      // Apply smart scheduling
      const schedulingResult = await this.applySmartScheduling(
        enhancedActivities,
        preferences,
        date
      );

      // Generate trend analysis
      const trends = await this.generateTrendAnalysis(date);

      // Generate insights
      const insights = await this.generateDailyInsights(
        enhancedActivities,
        trends,
        preferences
      );

      // Calculate metadata
      const metadata = {
        generationTime: new Date().toISOString(),
        confidence: this.calculateDigestConfidence(enhancedActivities, trends),
        dataQuality: this.assessDataQuality(enhancedActivities) as 'excellent' | 'good' | 'fair' | 'poor',
        customizationApplied: !!preferences
      };

      const enhancedDigest: EnhancedDayDigest = {
        ...baseDigest,
        activities: schedulingResult.success ? enhancedActivities : enhancedActivities,
        trends,
        insights,
        metadata
      };

      console.log(`[EnhancedDigest] Generated enhanced digest with ${enhancedActivities.length} activities, ${trends.length} trends`);
      return enhancedDigest;

    } catch (error) {
      console.error('[EnhancedDigest] Error generating enhanced digest:', error);
      return this.getFallbackDigest(date);
    }
  }

  /**
   * Enhance activities with additional metadata and scheduling info
   */
  private async enhanceActivities(
    activities: DigestActivity[],
    preferences: DigestPreferences,
    date: string
  ): Promise<EnhancedDigestActivity[]> {
    const enhanced: EnhancedDigestActivity[] = [];

    for (const activity of activities) {
      const enhancedActivity: EnhancedDigestActivity = {
        ...activity,
        priority: this.calculateActivityPriority(activity, preferences),
        estimatedDuration: this.estimateActivityDuration(activity),
        energyLevel: this.calculateEnergyRequirement(activity),
        dependencies: this.identifyDependencies(activity, activities),
        alternatives: await this.generateAlternatives(activity),
        adaptations: this.generateAdaptations(activity),
        progressTracking: this.setupProgressTracking(activity)
      };

      enhanced.push(enhancedActivity);
    }

    return enhanced;
  }

  /**
   * Apply smart scheduling algorithm
   */
  private async applySmartScheduling(
    activities: EnhancedDigestActivity[],
    preferences: DigestPreferences,
    date: string
  ): Promise<DigestSchedulingResult> {
    const result: DigestSchedulingResult = {
      success: true,
      activitiesScheduled: 0,
      conflicts: [],
      optimizations: []
    };

    try {
      // Sort activities by priority and dependencies
      const sortedActivities = this.sortActivitiesByPriority(activities);

      // Create time slots based on user preferences
      const timeSlots = this.generateTimeSlots(preferences, date);

      // Schedule activities into optimal time slots
      const scheduledActivities = this.scheduleActivities(
        sortedActivities,
        timeSlots,
        preferences
      );

      result.activitiesScheduled = scheduledActivities.length;

      // Detect and resolve conflicts
      const conflicts = this.detectSchedulingConflicts(scheduledActivities);
      result.conflicts = conflicts;

      if (conflicts.length > 0) {
        const resolutions = await this.resolveSchedulingConflicts(
          scheduledActivities,
          conflicts,
          preferences
        );
        result.optimizations.push(...resolutions);
      }

      // Apply optimizations
      const optimizations = this.optimizeSchedule(scheduledActivities, preferences);
      result.optimizations.push(...optimizations);

      console.log(`[EnhancedDigest] Scheduling completed: ${result.activitiesScheduled} activities, ${result.conflicts.length} conflicts`);

    } catch (error) {
      console.error('[EnhancedDigest] Scheduling error:', error);
      result.success = false;
    }

    return result;
  }

  /**
   * Generate trend analysis for activities
   */
  private async generateTrendAnalysis(date: string): Promise<DigestTrend[]> {
    const cacheKey = `trends_${date}`;
    const now = Date.now();

    // Check cache
    if (this.trendsCache.has(cacheKey) && (now - this.lastTrendUpdate) < 60 * 60 * 1000) {
      return this.trendsCache.get(cacheKey)!;
    }

    try {
      const trends: DigestTrend[] = [];

      // Analyze workout trends
      const workoutTrend = await this.analyzeWorkoutTrends(date);
      if (workoutTrend) trends.push(workoutTrend);

      // Analyze nutrition trends
      const nutritionTrend = await this.analyzeNutritionTrends(date);
      if (nutritionTrend) trends.push(nutritionTrend);

      // Analyze sleep trends
      const sleepTrend = await this.analyzeSleepTrends(date);
      if (sleepTrend) trends.push(sleepTrend);

      // Analyze overall trends
      const overallTrend = await this.analyzeOverallTrends(date);
      if (overallTrend) trends.push(overallTrend);

      // Cache results
      this.trendsCache.set(cacheKey, trends);
      this.lastTrendUpdate = now;

      return trends;

    } catch (error) {
      console.error('[EnhancedDigest] Error generating trend analysis:', error);
      return [];
    }
  }

  /**
   * Generate daily insights based on activities and trends
   */
  private async generateDailyInsights(
    activities: EnhancedDigestActivity[],
    trends: DigestTrend[],
    preferences: DigestPreferences
  ): Promise<EnhancedDayDigest['insights']> {
    // Determine daily focus
    const dailyFocus = this.determineDailyFocus(activities, trends);

    // Predict energy levels
    const energyPrediction = this.predictEnergyLevels(activities, trends);

    // Generate recommendations
    const recommendations = this.generateRecommendations(activities, trends, preferences);

    // Create motivational message
    const motivationalMessage = this.generateMotivationalMessage(trends, preferences);

    return {
      dailyFocus,
      energyPrediction,
      recommendations,
      motivationalMessage
    };
  }

  /**
   * Helper methods for activity enhancement
   */
  private calculateActivityPriority(
    activity: DigestActivity,
    preferences: DigestPreferences
  ): EnhancedDigestActivity['priority'] {
    // Priority based on activity type and user preferences
    if (activity.type === 'workout' && preferences.workoutPreferences.intensity === 'high') {
      return 'high';
    }
    if (activity.type === 'meal' && activity.title?.includes('breakfast')) {
      return 'high';
    }
    if (activity.type === 'hydration') {
      return 'medium';
    }
    return 'low';
  }

  private estimateActivityDuration(activity: DigestActivity): number {
    const durationMap: Record<string, number> = {
      workout: 45,
      meal: 30,
      hydration: 5,
      supplement: 2,
      meditation: 15,
      planning: 10
    };
    
    return durationMap[activity.type] || 15;
  }

  private calculateEnergyRequirement(activity: DigestActivity): EnhancedDigestActivity['energyLevel'] {
    const energyMap: Record<string, EnhancedDigestActivity['energyLevel']> = {
      workout: 'high',
      meal: 'low',
      hydration: 'low',
      supplement: 'low',
      meditation: 'medium',
      planning: 'medium'
    };
    
    return energyMap[activity.type] || 'medium';
  }

  private identifyDependencies(
    activity: DigestActivity,
    allActivities: DigestActivity[]
  ): string[] {
    const dependencies: string[] = [];
    
    // Example: Post-workout meal depends on workout
    if (activity.type === 'meal' && activity.title?.includes('post-workout')) {
      const workout = allActivities.find(a => a.type === 'workout');
      if (workout) dependencies.push(workout.id);
    }
    
    return dependencies;
  }

  private async generateAlternatives(activity: DigestActivity): Promise<string[]> {
    // Generate alternative activities based on type
    const alternatives: Record<string, string[]> = {
      workout: ['Light walk', 'Yoga session', 'Stretching routine'],
      meal: ['Quick snack', 'Protein shake', 'Healthy smoothie'],
      hydration: ['Herbal tea', 'Infused water', 'Coconut water']
    };
    
    return alternatives[activity.type] || [];
  }

  private generateAdaptations(activity: DigestActivity): EnhancedDigestActivity['adaptations'] {
    return {
      weather: {
        rainy: activity.type === 'workout' ? 'Indoor workout alternative' : activity.title || '',
        hot: activity.type === 'workout' ? 'Early morning or evening workout' : activity.title || '',
        cold: activity.type === 'workout' ? 'Warm-up extended' : activity.title || ''
      },
      energy: {
        low: 'Reduce intensity or duration',
        medium: 'Proceed as planned',
        high: 'Consider adding extra challenge'
      },
      time: {
        rushed: 'Quick version available',
        extended: 'Full version with extras',
        flexible: 'Can be moved to different time'
      }
    };
  }

  private setupProgressTracking(activity: DigestActivity): EnhancedDigestActivity['progressTracking'] {
    const trackableTypes = ['workout', 'hydration', 'meditation'];
    
    return {
      trackable: trackableTypes.includes(activity.type),
      metrics: this.getTrackingMetrics(activity.type),
      targetValue: this.getTargetValue(activity.type),
      unit: this.getTrackingUnit(activity.type)
    };
  }

  private getTrackingMetrics(type: string): string[] {
    const metricsMap: Record<string, string[]> = {
      workout: ['duration', 'intensity', 'exercises_completed'],
      hydration: ['volume', 'frequency'],
      meditation: ['duration', 'focus_level'],
      meal: ['calories', 'macros', 'satisfaction']
    };
    
    return metricsMap[type] || [];
  }

  private getTargetValue(type: string): number | undefined {
    const targetMap: Record<string, number> = {
      workout: 45,      // minutes
      hydration: 250,   // ml
      meditation: 10,   // minutes
      meal: 500         // calories
    };
    
    return targetMap[type];
  }

  private getTrackingUnit(type: string): string | undefined {
    const unitMap: Record<string, string> = {
      workout: 'minutes',
      hydration: 'ml',
      meditation: 'minutes',
      meal: 'calories'
    };
    
    return unitMap[type];
  }

  /**
   * Scheduling helper methods
   */
  private sortActivitiesByPriority(activities: EnhancedDigestActivity[]): EnhancedDigestActivity[] {
    return activities.sort((a, b) => {
      const priorityA = SCHEDULING_CONSTANTS.PRIORITY_WEIGHTS[a.priority];
      const priorityB = SCHEDULING_CONSTANTS.PRIORITY_WEIGHTS[b.priority];
      return priorityB - priorityA;
    });
  }

  private generateTimeSlots(preferences: DigestPreferences, date: string): Array<{ start: string; end: string; type: string }> {
    const slots = [];
    const wakeTime = preferences.wakeUpTime;
    const bedTime = preferences.bedTime;
    
    // Generate hourly slots between wake and bed time
    let currentHour = parseInt(wakeTime.split(':')[0]);
    const endHour = parseInt(bedTime.split(':')[0]);
    
    while (currentHour < endHour) {
      slots.push({
        start: `${currentHour.toString().padStart(2, '0')}:00`,
        end: `${(currentHour + 1).toString().padStart(2, '0')}:00`,
        type: this.getTimeSlotType(currentHour, preferences)
      });
      currentHour++;
    }
    
    return slots;
  }

  private getTimeSlotType(hour: number, preferences: DigestPreferences): string {
    if (hour >= 6 && hour <= 9) return 'morning';
    if (hour >= 10 && hour <= 12) return 'mid_morning';
    if (hour >= 12 && hour <= 14) return 'lunch';
    if (hour >= 14 && hour <= 17) return 'afternoon';
    if (hour >= 17 && hour <= 20) return 'evening';
    return 'night';
  }

  private scheduleActivities(
    activities: EnhancedDigestActivity[],
    timeSlots: Array<{ start: string; end: string; type: string }>,
    preferences: DigestPreferences
  ): EnhancedDigestActivity[] {
    // Simple scheduling algorithm - would be more sophisticated in practice
    const scheduled = [...activities];
    
    scheduled.forEach((activity, index) => {
      if (index < timeSlots.length) {
        activity.scheduledTime = `${new Date().toISOString().split('T')[0]}T${timeSlots[index].start}:00.000Z`;
      }
    });
    
    return scheduled;
  }

  private detectSchedulingConflicts(activities: EnhancedDigestActivity[]): DigestSchedulingResult['conflicts'] {
    const conflicts: DigestSchedulingResult['conflicts'] = [];
    
    // Check for time overlaps
    for (let i = 0; i < activities.length - 1; i++) {
      const current = activities[i];
      const next = activities[i + 1];
      
      if (current.scheduledTime && next.scheduledTime) {
        const currentEnd = new Date(new Date(current.scheduledTime).getTime() + current.estimatedDuration * 60000);
        const nextStart = new Date(next.scheduledTime);
        
        if (currentEnd > nextStart) {
          conflicts.push({
            activityId: next.id,
            conflictType: 'time_overlap',
            resolution: 'Adjust start time to avoid overlap'
          });
        }
      }
    }
    
    return conflicts;
  }

  private async resolveSchedulingConflicts(
    activities: EnhancedDigestActivity[],
    conflicts: DigestSchedulingResult['conflicts'],
    preferences: DigestPreferences
  ): Promise<DigestSchedulingResult['optimizations']> {
    const optimizations: DigestSchedulingResult['optimizations'] = [];
    
    conflicts.forEach(conflict => {
      optimizations.push({
        type: 'time_adjustment',
        description: `Adjusted ${conflict.activityId} to resolve ${conflict.conflictType}`,
        impact: 'Improved schedule flow'
      });
    });
    
    return optimizations;
  }

  private optimizeSchedule(
    activities: EnhancedDigestActivity[],
    preferences: DigestPreferences
  ): DigestSchedulingResult['optimizations'] {
    const optimizations: DigestSchedulingResult['optimizations'] = [];
    
    // Example optimization: Group similar activities
    const workouts = activities.filter(a => a.type === 'workout');
    if (workouts.length > 1) {
      optimizations.push({
        type: 'activity_swap',
        description: 'Grouped workout activities for better energy management',
        impact: 'Improved energy efficiency'
      });
    }
    
    return optimizations;
  }

  /**
   * Trend analysis methods
   */
  private async analyzeWorkoutTrends(date: string): Promise<DigestTrend | null> {
    // Simplified trend analysis - would use actual historical data
    return {
      type: 'workout',
      period: '7d',
      trend: 'improving',
      changePercentage: 15,
      insights: ['Workout frequency increased by 15%', 'Consistency improved'],
      recommendations: ['Maintain current momentum', 'Consider increasing intensity'],
      confidence: 0.8
    };
  }

  private async analyzeNutritionTrends(date: string): Promise<DigestTrend | null> {
    return {
      type: 'nutrition',
      period: '7d',
      trend: 'stable',
      changePercentage: 2,
      insights: ['Nutrition tracking consistent', 'Protein intake on target'],
      recommendations: ['Continue current approach', 'Consider adding more vegetables'],
      confidence: 0.7
    };
  }

  private async analyzeSleepTrends(date: string): Promise<DigestTrend | null> {
    return null; // Would implement based on sleep data availability
  }

  private async analyzeOverallTrends(date: string): Promise<DigestTrend | null> {
    return {
      type: 'overall',
      period: '30d',
      trend: 'improving',
      changePercentage: 12,
      insights: ['Overall health metrics trending upward', 'Good consistency across all areas'],
      recommendations: ['Keep up the excellent work', 'Consider setting new challenges'],
      confidence: 0.85
    };
  }

  /**
   * Insight generation methods
   */
  private determineDailyFocus(
    activities: EnhancedDigestActivity[],
    trends: DigestTrend[]
  ): string {
    const workoutActivities = activities.filter(a => a.type === 'workout');
    if (workoutActivities.length > 0) {
      return 'Strength & Conditioning';
    }
    
    const nutritionActivities = activities.filter(a => a.type === 'meal');
    if (nutritionActivities.length > 2) {
      return 'Nutrition Optimization';
    }
    
    return 'Wellness & Recovery';
  }

  private predictEnergyLevels(
    activities: EnhancedDigestActivity[],
    trends: DigestTrend[]
  ): 'low' | 'medium' | 'high' {
    const highEnergyActivities = activities.filter(a => a.energyLevel === 'high').length;
    
    if (highEnergyActivities > 2) return 'high';
    if (highEnergyActivities > 0) return 'medium';
    return 'low';
  }

  private generateRecommendations(
    activities: EnhancedDigestActivity[],
    trends: DigestTrend[],
    preferences: DigestPreferences
  ): string[] {
    const recommendations: string[] = [];
    
    // Add trend-based recommendations
    trends.forEach(trend => {
      if (trend.recommendations.length > 0) {
        recommendations.push(trend.recommendations[0]);
      }
    });
    
    // Add activity-based recommendations
    const workoutCount = activities.filter(a => a.type === 'workout').length;
    if (workoutCount === 0) {
      recommendations.push('Consider adding a light workout to your day');
    }
    
    return recommendations.slice(0, 3); // Limit to 3 recommendations
  }

  private generateMotivationalMessage(
    trends: DigestTrend[],
    preferences: DigestPreferences
  ): string {
    const improvingTrends = trends.filter(t => t.trend === 'improving');
    
    if (improvingTrends.length > 0) {
      return "You're making great progress! Keep up the momentum.";
    }
    
    return "Every day is a new opportunity to improve. You've got this!";
  }

  /**
   * Utility methods
   */
  private calculateDigestConfidence(
    activities: EnhancedDigestActivity[],
    trends: DigestTrend[]
  ): number {
    const activityConfidence = activities.length > 0 ? 0.8 : 0.4;
    const trendConfidence = trends.length > 0 ? 
      trends.reduce((sum, t) => sum + t.confidence, 0) / trends.length : 0.5;
    
    return (activityConfidence + trendConfidence) / 2;
  }

  private assessDataQuality(activities: EnhancedDigestActivity[]): string {
    if (activities.length >= 5) return 'excellent';
    if (activities.length >= 3) return 'good';
    if (activities.length >= 1) return 'fair';
    return 'poor';
  }

  private getFallbackDigest(date: string): EnhancedDayDigest {
    return {
      date,
      activities: [],
      trends: [],
      insights: {
        dailyFocus: 'Recovery & Planning',
        energyPrediction: 'medium',
        recommendations: ['Take time to plan your goals', 'Focus on hydration'],
        motivationalMessage: 'Every journey begins with a single step.'
      },
      metadata: {
        generationTime: new Date().toISOString(),
        confidence: 0.3,
        dataQuality: 'poor',
        customizationApplied: false
      }
    };
  }

  /**
   * Preferences management
   */
  async getPreferences(): Promise<DigestPreferences> {
    if (this.preferences) return this.preferences;
    
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.PREFERENCES);
      if (stored) {
        this.preferences = JSON.parse(stored);
        return this.preferences!;
      }
    } catch (error) {
      console.error('[EnhancedDigest] Error loading preferences:', error);
    }
    
    // Return default preferences
    const userId = await getUserId();
    return {
      userId: userId || 'default',
      wakeUpTime: '07:00',
      bedTime: '22:00',
      workoutPreferences: {
        preferredTimes: ['07:00', '18:00'],
        duration: 45,
        intensity: 'medium',
        restDays: [0, 6] // Sunday and Saturday
      },
      mealPreferences: {
        mealsPerDay: 3,
        snackFrequency: 2,
        dietaryRestrictions: [],
        preferredCuisines: []
      },
      customization: {
        showWeather: true,
        showMotivation: true,
        showProgress: true,
        compactView: false,
        autoRefresh: true,
        notificationsEnabled: true
      },
      lastUpdated: new Date().toISOString()
    };
  }

  async savePreferences(preferences: DigestPreferences): Promise<void> {
    try {
      this.preferences = preferences;
      await AsyncStorage.setItem(STORAGE_KEYS.PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('[EnhancedDigest] Error saving preferences:', error);
    }
  }
}

// Export singleton instance
export const enhancedDigestService = new EnhancedDigestService();

// Convenience functions
export const generateEnhancedDigest = (date: string, forceRefresh?: boolean) =>
  enhancedDigestService.generateEnhancedDigest(date, forceRefresh);

export const getDigestPreferences = () =>
  enhancedDigestService.getPreferences();

export const saveDigestPreferences = (preferences: DigestPreferences) =>
  enhancedDigestService.savePreferences(preferences);

// Export types for use in components
export type {
  DigestPreferences,
  DigestTrend,
  EnhancedDigestActivity,
  EnhancedDayDigest,
  DigestSchedulingResult
};
