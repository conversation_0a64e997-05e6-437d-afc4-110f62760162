import { api, makeRequestWithRetry } from './apiClient';
import { getAuthHeader } from './auth';
import { getUserId } from './profile';
import { addContextEntry, ContextType } from './contextService';

// Types for the structured analysis response
interface AnalysisResult {
  preferences: PreferenceItem[];
  dietaryRestrictions: DietaryRestrictionItem[];
  injuries: InjuryItem[];
  lifeUpdates: LifeUpdateItem[];
  goals: GoalItem[];
  weight?: number;
}

interface PreferenceItem {
  value: string;
  confidence: number; // 0-1 score indicating confidence in extraction
  sentiment: 'positive' | 'negative' | 'neutral'; // whether they like or dislike something
  category?: string; // optional category of the preference
  reasoning?: string;
}

interface DietaryRestrictionItem {
  value: string;
  confidence: number;
  reason?: string; // e.g., "allergy", "diet", "preference", "medical"
}

interface InjuryItem {
  value: string;
  confidence: number;
  bodyPart?: string; // e.g., "knee", "shoulder", "back"
  severity?: string; // e.g., "mild", "moderate", "severe"
}

interface LifeUpdateItem {
  value: string;
  confidence: number;
  timeframe?: string; // e.g., "past", "present", "future"
}

interface GoalItem {
  value: string;
  confidence: number;
  timeframe?: string; // e.g., "short-term", "long-term"
}

// Function to send a message for contextual analysis using LLM
export async function analyzeMessage(message: string, conversationId?: string, messageId?: string): Promise<AnalysisResult | null> {
  try {
    console.log('[MessageAnalysis] Analyzing message for context extraction');

    // Get user ID
    const userId = await getUserId();
    if (!userId) {
      console.warn('[MessageAnalysis] No user ID available, context will not be stored');
    }

    // Get authentication headers
    const headers = await getAuthHeader();
    if (!headers.Authorization) {
      console.warn('[MessageAnalysis] No auth token available, using anonymous analysis');
    }

    // Create analysis request
    const analysisRequest = {
      message,
      requestType: 'context_analysis',
      userId: userId || 'anonymous',
      messageId: messageId || `msg_${Date.now()}_${Math.floor(Math.random() * 10000)}`,
      conversationId: conversationId || 'unknown',
    };

    // Use a longer timeout for analysis requests
    const response = await makeRequestWithRetry({
      url: '/message-analysis',
      method: 'POST',
      data: analysisRequest,
      headers,
      timeout: 10000 // 10 second timeout
    }, {
      maxRetries: 2,
      retryDelay: 1000,
      shouldRetry: (error) => {
        return error.message.includes('Network Error') || 
               error.code === 'ECONNABORTED' ||
               !error.response;
      }
    });

    const analysisResult = response?.data?.analysis;
    
    if (!analysisResult) {
      console.warn('[MessageAnalysis] No analysis result returned');
      return null;
    }

    console.log('[MessageAnalysis] Successfully analyzed message, extracting context');
    
    // Store each type of context
    await storeAnalysisResults(analysisResult, message, messageId, conversationId);
    
    return analysisResult;
  } catch (error) {
    console.error('[MessageAnalysis] Error analyzing message:', error);
    throw new Error('Failed to analyze message for context');
  }
}

// Helper function to store analysis results in context
async function storeAnalysisResults(
  analysis: AnalysisResult,
  originalMessage: string,
  messageId?: string,
  conversationId?: string
): Promise<void> {
  const uniqueId = messageId || `msg_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
  const convId = conversationId || 'unknown';
  const invalidPreferences = ['dinner', 'lunch', 'breakfast', 'food', 'a meal', 'a workout'];

  try {
    // 1. First, store the raw message content
    await addContextEntry({
      type: ContextType.MESSAGE_CONTENT,
      value: originalMessage,
      source: 'user_input',
      messageId: uniqueId,
      conversationId: convId,
      metadata: { role: 'user' }
    });

    // 2. Store preferences with enhanced synchronization
    if (analysis.preferences && analysis.preferences.length > 0) {
      console.log(`[MessageAnalysis] Storing ${analysis.preferences.length} preferences`);
      
      for (const pref of analysis.preferences) {
        if (pref.confidence >= 0.5 && !invalidPreferences.includes(pref.value.toLowerCase())) {
          console.log(`[MessageAnalysis] Processing preference: "${pref.value}" [${pref.sentiment}] confidence:${pref.confidence}`);
          
          const preferenceMetadata = {
            confidence: pref.confidence,
            sentiment: pref.sentiment,
            category: pref.sentiment === 'negative' ? 'dislikes' : (pref.category || 'general'),
            reasoning: pref.reasoning,
            extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : ''),
            conversationId: convId,
            messageId: uniqueId,
            timestamp: new Date().toISOString()
          };
          
          let preferenceValue: string;
          
          if (pref.sentiment === 'negative') {
            // Handle negative preferences by adding them to a specific sub-category or with a specific flag
            preferenceValue = `dislikes ${pref.value}`;
            console.log(`[MessageAnalysis] Storing negative preference: "${preferenceValue}"`);
          } else {
            preferenceValue = pref.value;
            console.log(`[MessageAnalysis] Storing positive preference: "${preferenceValue}"`);
          }
          
          // Check if this preference conflicts with existing preferences and should overwrite them
          const shouldOverwrite = await checkForConflictingPreferences(pref.value, pref.sentiment);
          
          // Store the preference with enhanced error handling
          try {
            const success = await addContextEntry({
              type: ContextType.PREFERENCE,
              value: preferenceValue,
              source: 'user_input',
              messageId: uniqueId,
              conversationId: convId,
              metadata: preferenceMetadata
            });
            
            if (success) {
              console.log(`[MessageAnalysis] ✅ Successfully stored preference: "${preferenceValue}"`);
              
              // Force immediate sync to server with overwrite flag if needed
              try {
                const { saveContextData } = require('./contextService');
                await saveContextData({
                  contextType: ContextType.PREFERENCE,
                  value: preferenceValue,
                  source: 'user_input',
                  timestamp: new Date().toISOString(),
                  metadata: preferenceMetadata
                }, shouldOverwrite);
                console.log(`[MessageAnalysis] ✅ Force-synced preference to server: "${preferenceValue}" (overwrite: ${shouldOverwrite})`);
              } catch (syncError) {
                console.warn(`[MessageAnalysis] ⚠️ Could not force-sync preference to server: ${syncError}`);
                // Continue - local storage should still work
              }
              
            } else {
              console.error(`[MessageAnalysis] ❌ Failed to store preference: "${preferenceValue}"`);
            }
          } catch (prefError) {
            console.error(`[MessageAnalysis] ❌ Error storing preference "${preferenceValue}":`, prefError);
          }
        } else {
          console.log(`[MessageAnalysis] Skipping preference "${pref.value}": confidence ${pref.confidence} (min 0.5) or invalid preference`);
        }
      }
      
      // After storing all preferences, force a cache clear to ensure fresh retrieval
      try {
        const { contextEngine } = require('./contextEngineV2');
        contextEngine.clearCache();
        console.log(`[MessageAnalysis] ✅ Cleared context cache to ensure fresh preference retrieval`);
      } catch (cacheError) {
        console.warn(`[MessageAnalysis] Could not clear context cache:`, cacheError);
      }
    }

    // 3. Store dietary restrictions
    if (analysis.dietaryRestrictions && analysis.dietaryRestrictions.length > 0) {
      console.log(`[MessageAnalysis] Storing ${analysis.dietaryRestrictions.length} dietary restrictions`);
      for (const restriction of analysis.dietaryRestrictions) {
        if (restriction.confidence >= 0.7) {
          await addContextEntry({
            type: ContextType.DIETARY_RESTRICTION,
            value: restriction.value,
            source: 'user_input',
            messageId: uniqueId,
            conversationId: convId,
            metadata: {
              confidence: restriction.confidence,
              reason: restriction.reason || 'preference',
              extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : '')
            }
          });
        }
      }
    }

    // 4. Store injuries
    if (analysis.injuries && analysis.injuries.length > 0) {
      console.log(`[MessageAnalysis] Storing ${analysis.injuries.length} injuries`);
      for (const injury of analysis.injuries) {
        if (injury.confidence >= 0.7) {
          await addContextEntry({
            type: ContextType.INJURY,
            value: injury.value,
            source: 'user_input',
            messageId: uniqueId,
            conversationId: convId,
            metadata: {
              confidence: injury.confidence,
              bodyPart: injury.bodyPart,
              severity: injury.severity,
              extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : '')
            }
          });
        }
      }
    }

    // 5. Store life updates
    if (analysis.lifeUpdates && analysis.lifeUpdates.length > 0) {
      console.log(`[MessageAnalysis] Storing ${analysis.lifeUpdates.length} life updates`);
      for (const update of analysis.lifeUpdates) {
        if (update.confidence >= 0.7) {
          await addContextEntry({
            type: ContextType.LIFE_UPDATE,
            value: update.value,
            source: 'user_input',
            messageId: uniqueId,
            conversationId: convId,
            metadata: {
              confidence: update.confidence,
              timeframe: update.timeframe || 'present',
              extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : '')
            }
          });
        }
      }
    }

    // 6. Store goals
    if (analysis.goals && analysis.goals.length > 0) {
      console.log(`[MessageAnalysis] Storing ${analysis.goals.length} goals`);
      for (const goal of analysis.goals) {
        if (goal.confidence >= 0.7) {
          await addContextEntry({
            type: ContextType.GOAL,
            value: goal.value,
            source: 'user_input',
            messageId: uniqueId,
            conversationId: convId,
            metadata: {
              confidence: goal.confidence,
              timeframe: goal.timeframe || 'unknown',
              extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : '')
            }
          });
        }
      }
    }

    // 7. Store weight if detected
    if (analysis.weight) {
      console.log(`[MessageAnalysis] Storing weight: ${analysis.weight}`);
      await addContextEntry({
        type: ContextType.WEIGHT_HISTORY,
        value: analysis.weight,
        source: 'user_input',
        messageId: uniqueId,
        conversationId: convId,
        metadata: {
          extractedFrom: originalMessage.substring(0, 100) + (originalMessage.length > 100 ? '...' : '')
        }
      });
    }

    console.log('[MessageAnalysis] Successfully stored all analysis results');
  } catch (error) {
    console.error('[MessageAnalysis] Error storing analysis results:', error);
  }
}

/**
 * Check if a new preference conflicts with existing preferences and should overwrite them
 * For example: "I love dumbbells" should overwrite "I don't like dumbbells"
 */
async function checkForConflictingPreferences(newPreferenceValue: string, newSentiment: string): Promise<boolean> {
  try {
    const { getContextFromLocalStorage, ContextType } = require('./contextService');
    const existingPreferences = await getContextFromLocalStorage(ContextType.PREFERENCE);
    
    // Extract the core item from the new preference (e.g., "dumbbells" from "dislikes dumbbells")
    const coreItem = newPreferenceValue.toLowerCase()
      .replace(/^(dislikes?|likes?|loves?|hates?|enjoys?)\s+/, '')
      .trim();
    
    console.log(`[MessageAnalysis] Checking for conflicts with "${coreItem}" (sentiment: ${newSentiment})`);
    
    // Look for existing preferences about the same item with different sentiment
    const conflictingPrefs = existingPreferences.filter((pref: any) => {
      const existingValue = pref.value.toLowerCase();
      const existingSentiment = pref.metadata?.sentiment || 'neutral';
      
      // Check if this preference is about the same item
      const isAboutSameItem = existingValue.includes(coreItem) || 
                             existingValue.replace(/^(dislikes?|likes?|loves?|hates?|enjoys?)\s+/, '').trim() === coreItem;
      
      // Check if sentiment is different
      const hasDifferentSentiment = existingSentiment !== newSentiment;
      
      if (isAboutSameItem && hasDifferentSentiment) {
        console.log(`[MessageAnalysis] Found conflicting preference: "${pref.value}" [${existingSentiment}] vs new "${newPreferenceValue}" [${newSentiment}]`);
        return true;
      }
      
      return false;
    });
    
    const shouldOverwrite = conflictingPrefs.length > 0;
    console.log(`[MessageAnalysis] Should overwrite existing preferences: ${shouldOverwrite} (found ${conflictingPrefs.length} conflicts)`);
    
    return shouldOverwrite;
  } catch (error) {
    console.error('[MessageAnalysis] Error checking for conflicting preferences:', error);
    return false; // Default to not overwriting if there's an error
  }
}

// Export for testing purposes
export const _test = {
  storeAnalysisResults,
  checkForConflictingPreferences
};
