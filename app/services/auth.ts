import * as SecureStore from 'expo-secure-store';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { api } from './apiClient';

const API_URL = process.env.EXPO_PUBLIC_API_URL;

// Flag to prevent multiple refresh attempts at the same time
let isRefreshing = false;
// Pending requests queue
let failedQueue: any[] = [];

export interface AuthTokens {
  accessToken: string;
  idToken: string;
  refreshToken: string;
}

export interface AuthResponse {
  message: string;
  tokens?: AuthTokens;
}

export async function signIn(email: string, password: string): Promise<{ tokens: AuthTokens }> {
  try {
    console.log('Starting sign in process for:', email);
    
    // Validate API URL is configured
    if (!API_URL) {
      console.error('API URL is not configured');
      throw new Error('API configuration error. Please check your environment settings.');
    }
    
    console.log('API URL configured:', API_URL);
    
    // Make the API call to authenticate
    const response = await api.post('/auth', {
      action: 'signIn',
      email,
      password,
    });

    console.log('Sign in API response received');

    // Validate the response has tokens
    if (!response.data?.tokens) {
      console.error('Sign in response missing tokens object');
      throw new Error('Invalid response from server');
    }

    const tokens = response.data.tokens;

    // Validate all required tokens are present
    if (!tokens.accessToken || !tokens.idToken || !tokens.refreshToken) {
      console.error('Sign in response missing required tokens:', {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
        hasRefreshToken: !!tokens.refreshToken
      });
      throw new Error('Incomplete tokens received from server');
    }

    console.log('Valid tokens received, storing...');
    
    // Store the tokens securely with validation
    await storeTokens(tokens);

    // Verify tokens were stored correctly
    const storedTokens = await getStoredTokens();
    if (!storedTokens || !storedTokens.refreshToken) {
      console.error('Token storage verification failed - refresh token not found after storage');
      throw new Error('Failed to store authentication tokens');
    }

    console.log('Sign in completed successfully, tokens stored and verified');
    return { tokens };
  } catch (error: any) {
    console.error('Error signing in:', error?.response?.data || error);
    
    // Provide more specific error messages based on error type
    let errorMessage = 'Error signing in';
    
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      errorMessage = 'Network error. Please check your internet connection and try again.';
    } else if (error.response?.status === 401) {
      errorMessage = 'Invalid email or password. Please check your credentials and try again.';
    } else if (error.response?.status === 404) {
      errorMessage = 'Authentication service not found. Please try again later.';
    } else if (error.response?.status >= 500) {
      errorMessage = 'Server error. Please try again later.';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    throw new Error(errorMessage);
  }
}

export async function signUp(email: string, password: string): Promise<void> {
  try {
    // Call the API to register the user
    await api.post('/auth', {
      action: 'signUp',
      email,
      password,
    });
  } catch (error: any) {
    console.error('Error signing up:', error?.response?.data || error);
    throw new Error(error?.response?.data?.message || 'Error signing up');
  }
}

export async function confirmSignUp(email: string, code: string): Promise<void> {
  try {
    // Call the API to confirm signup
    await api.post('/auth', {
      action: 'confirmSignUp',
      email,
      code,
    });
  } catch (error: any) {
    console.error('Error confirming sign up:', error?.response?.data || error);
    throw new Error(error?.response?.data?.message || 'Error confirming sign up');
  }
}

/**
 * Sign out the user by clearing all stored data
 */
export const signOut = async (): Promise<void> => {
  try {
    // Set a flag to prevent new API calls during sign out
    (window as any).__AUTH_SIGNING_OUT = true;

    // Cancel any pending Axios requests to prevent 401/500 errors during signout
    if (axios.defaults.cancelToken) {
      console.log('Cancelling pending requests during sign out');
      (axios.defaults as any).isCancel = true;
    }

    // Clear all user data (tokens, storage, etc.)
    await clearAllUserData();

    // Create a small delay to let pending operations complete
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('Sign out completed successfully');
  } catch (error) {
    console.error('Error during sign out:', error);
    // Handle error silently - always complete sign out
  } finally {
    // Clear the signing out flag
    (window as any).__AUTH_SIGNING_OUT = false;
  }
};

export async function getStoredTokens(): Promise<AuthTokens | null> {
  try {
    console.log('Attempting to get tokens from SecureStore');

    // Helper function to validate token completeness
    const validateTokens = (tokens: any): tokens is AuthTokens => {
      return tokens && 
             typeof tokens.accessToken === 'string' && tokens.accessToken.length > 0 &&
             typeof tokens.idToken === 'string' && tokens.idToken.length > 0 &&
             typeof tokens.refreshToken === 'string' && tokens.refreshToken.length > 0;
    };

    // Always check split storage first as it's our preferred method
    console.log('Checking split storage...');
    try {
      const [accessToken, idToken, refreshToken] = await Promise.all([
        SecureStore.getItemAsync('auth_token_access'),
        SecureStore.getItemAsync('auth_token_id'),
        SecureStore.getItemAsync('auth_token_refresh')
      ]);

      // If we have all three tokens in split storage, validate and use them
      if (accessToken && idToken && refreshToken) {
        const tokens = { accessToken, idToken, refreshToken };
        if (validateTokens(tokens)) {
          console.log('Retrieved and validated split tokens from SecureStore');
          return tokens;
        } else {
          console.error('Split tokens failed validation - some tokens are empty or invalid');
        }
      } else {
        console.log('Split tokens not found or incomplete:', {
          hasAccessToken: !!accessToken,
          hasIdToken: !!idToken,
          hasRefreshToken: !!refreshToken
        });
      }
    } catch (splitError) {
      console.error('Error retrieving split tokens:', splitError);
    }

    console.log('Split tokens not available, checking legacy format...');

    // Check legacy format as fallback
    try {
      const tokensJson = await SecureStore.getItemAsync('auth_tokens');
      if (tokensJson) {
        try {
          console.log('Retrieved legacy tokens from SecureStore');
          const tokens = JSON.parse(tokensJson);

          // Validate token structure
          if (validateTokens(tokens)) {
            console.log('Legacy tokens validated successfully, migrating to split storage...');
            // If we got valid tokens from legacy storage, migrate them to split storage
            try {
              await storeTokens(tokens);
              console.log('Legacy tokens migrated to split storage successfully');
            } catch (migrationError) {
              console.warn('Failed to migrate legacy tokens (non-critical):', migrationError);
            }
            return tokens;
          } else {
            console.error('Retrieved legacy tokens object failed validation');
          }
        } catch (parseError) {
          console.error('Error parsing legacy tokens:', parseError);
        }
      }
    } catch (legacyError) {
      console.error('Error retrieving legacy tokens:', legacyError);
    }

    console.log('SecureStore tokens not available, checking fallback storage...');

    // As last resort, check AsyncStorage fallback
    try {
      const fallbackTokensJson = await AsyncStorage.getItem('auth_tokens_fallback');
      if (fallbackTokensJson) {
        try {
          console.log('Retrieved tokens from fallback storage');
          const tokens = JSON.parse(fallbackTokensJson);

          // Validate fallback tokens
          if (validateTokens(tokens)) {
            console.log('Fallback tokens validated successfully, migrating to SecureStore...');
            // Migrate to split storage for future use
            try {
              await storeTokens(tokens);
              console.log('Fallback tokens migrated to SecureStore successfully');
            } catch (migrationError) {
              console.warn('Failed to migrate fallback tokens (non-critical):', migrationError);
            }
            return tokens;
          } else {
            console.error('Fallback tokens object failed validation');
          }
        } catch (parseError) {
          console.error('Error parsing fallback tokens:', parseError);
        }
      }
    } catch (fallbackError) {
      console.error('Error retrieving fallback tokens:', fallbackError);
    }

    console.log('No valid tokens found in any storage location');
    return null;
  } catch (error) {
    console.error('Error getting stored tokens:', error);

    // Try fallback as absolute last resort with simplified approach
    try {
      console.log('Attempting emergency fallback token retrieval...');
      const fallbackTokensJson = await AsyncStorage.getItem('auth_tokens_fallback');
      if (fallbackTokensJson) {
        const tokens = JSON.parse(fallbackTokensJson);
        if (tokens && tokens.accessToken && tokens.idToken && tokens.refreshToken) {
          console.log('Emergency fallback tokens retrieved successfully');
          return tokens;
        }
      }
    } catch (emergencyError) {
      console.error('Emergency fallback also failed:', emergencyError);
    }

    return null;
  }
}

export async function resendConfirmationCode(email: string): Promise<AuthResponse> {
  try {
    const response = await api.post('/auth', {
      action: 'resendConfirmationCode',
      email,
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to resend confirmation code');
  }
}

export async function deleteAccount(): Promise<void> {
  try {
    // Get authentication tokens
    const tokens = await getStoredTokens();

    if (!tokens) {
      throw new Error('Not authenticated');
    }

    // Extract email from token if possible
    let email = '';

    try {
      // Try to decode the JWT token to get the email/username
      // Most JWT tokens include user info in the payload
      if (tokens.idToken) {
        // Parse the JWT token payload (second part between dots)
        const tokenParts = tokens.idToken.split('.');
        if (tokenParts.length >= 2) {
          const payload = JSON.parse(atob(tokenParts[1]));
          // JWT could use different fields for the email
          email = payload.email || payload.username || payload.sub || '';
          console.log('Found email in ID token:', email);
        }
      }
    } catch (e) {
      console.error('Error decoding JWT token:', e);
    }

    // If token parsing failed, try to get email from profile
    if (!email) {
      // Get the email from AsyncStorage profile
      const userProfileString = await AsyncStorage.getItem('@user_profile');
      const userProfile = userProfileString ? JSON.parse(userProfileString) : null;
      email = userProfile?.email || userProfile?.userId || '';
      console.log('Found email in profile storage:', email ? email : 'Not found');
    }

    // If we still don't have an email, try one more approach
    if (!email) {
      // Try to get user info from the API
      try {
        const userResponse = await api.get('/profile', {
          headers: {
            Authorization: `Bearer ${tokens.accessToken}`
          }
        });

        if (userResponse.data && (userResponse.data.email || userResponse.data.userId)) {
          email = userResponse.data.email || userResponse.data.userId;
          console.log('Retrieved email from profile API:', email);
        }
      } catch (apiError) {
        console.error('Could not retrieve user info from API:', apiError);
      }
    }

    // Final check if we have an email or userId
    if (!email) {
      throw new Error('Could not determine user email or ID for account deletion');
    }

    console.log('Deleting account with identifier:', email);

    // Make API call to delete account on server
    try {
      const response = await api.post('/auth', {
        action: 'deleteAccount',
        email: email,
        username: email, // Add username as fallback
        userId: email,   // Add userId as fallback
        // Server should at least accept one of these fields
        password: ''
      }, {
        headers: {
          Authorization: `Bearer ${tokens.accessToken}`
        }
      });

      console.log('Delete account response:', response.data);

      // Clear all local data
      await clearAllUserData();
    } catch (error: any) {
      console.error('Error in deleteAccount API call:', error);
      console.error('Response data:', error.response?.data);
      console.error('Status code:', error.response?.status);
      throw error;
    }
  } catch (error: any) {
    console.error('Error deleting account:', error);
    throw new Error(error.response?.data?.message || 'Failed to delete account');
  }
}

// Add a helper function to clear all user data
async function clearAllUserData(): Promise<void> {
  try {
    // Cancel any pending requests to prevent further API calls with expired tokens
    // This helps prevent the flood of 401/500 errors when signing out due to token expiration
    if (axios.defaults.cancelToken) {
      (axios.defaults as any).isCancel = true;
    }

    // Reset the refresh token state
    isRefreshing = false;
    failedQueue = [];

    // Clear all token storage locations
    console.log('Clearing all token storage...');

    // Clear split tokens
    await Promise.all([
      SecureStore.deleteItemAsync('auth_tokens_format'),
      SecureStore.deleteItemAsync('auth_token_access'),
      SecureStore.deleteItemAsync('auth_token_id'),
      SecureStore.deleteItemAsync('auth_token_refresh'),
      // Clear legacy token storage
      SecureStore.deleteItemAsync('auth_tokens'),
      // Clear fallback storage
      AsyncStorage.removeItem('auth_tokens_fallback')
    ]);

    // Clear profile data
    await AsyncStorage.removeItem('@user_profile');

    // Clear all logs and conversation data
    await AsyncStorage.removeItem('@logs');
    await AsyncStorage.removeItem('@conversations');
    await AsyncStorage.removeItem('@messages');
    await AsyncStorage.removeItem('@saved_workouts');
    await AsyncStorage.removeItem('@saved_meals');

    // Clear sync timestamps to ensure fresh data on next login
    await AsyncStorage.removeItem('LAST_FULL_SYNC_TIME');

    // Clear any other user-specific data
    // Add any additional storage keys that need to be cleared

    console.log('All user data cleared during sign out');
  } catch (error) {
    console.error('Error clearing user data:', error);
    throw error;
  }
}

export async function storeTokens(tokens: AuthTokens): Promise<void> {
  try {
    console.log('Storing tokens to SecureStore...', {
      accessTokenLength: tokens.accessToken ? tokens.accessToken.length : 0,
      idTokenLength: tokens.idToken ? tokens.idToken.length : 0,
      refreshTokenLength: tokens.refreshToken ? tokens.refreshToken.length : 0
    });

    // Validate tokens before storing
    if (!tokens.accessToken || !tokens.idToken || !tokens.refreshToken) {
      console.error('Invalid tokens object missing required fields:', {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
        hasRefreshToken: !!tokens.refreshToken
      });
      throw new Error('Cannot store incomplete tokens');
    }

    // Clear any existing tokens first to avoid conflicts
    console.log('Clearing existing tokens before storing new ones...');
    await Promise.allSettled([
      SecureStore.deleteItemAsync('auth_token_access'),
      SecureStore.deleteItemAsync('auth_token_id'),
      SecureStore.deleteItemAsync('auth_token_refresh'),
      SecureStore.deleteItemAsync('auth_tokens'),
      AsyncStorage.removeItem('auth_tokens_fallback')
    ]);

    // Set format flag first to indicate split storage
    await SecureStore.setItemAsync('auth_tokens_format', 'split');

    // Store tokens with individual error handling
    console.log('Storing individual tokens...');
    const storagePromises = [
      SecureStore.setItemAsync('auth_token_access', tokens.accessToken),
      SecureStore.setItemAsync('auth_token_id', tokens.idToken),
      SecureStore.setItemAsync('auth_token_refresh', tokens.refreshToken)
    ];

    const storageResults = await Promise.allSettled(storagePromises);
    
    // Check if any storage operations failed
    const failedOperations = storageResults.filter(result => result.status === 'rejected');
    if (failedOperations.length > 0) {
      console.error('Some token storage operations failed:', failedOperations);
      throw new Error('Failed to store some tokens in SecureStore');
    }

    console.log('Tokens stored in split format successfully');

    // Verify storage worked by reading back with timeout
    console.log('Verifying token storage...');
    const verificationPromise = Promise.all([
      SecureStore.getItemAsync('auth_token_access'),
      SecureStore.getItemAsync('auth_token_id'),
      SecureStore.getItemAsync('auth_token_refresh')
    ]);

    // Add timeout to verification
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Token verification timeout')), 5000)
    );

    const [accessToken, idToken, refreshToken] = await Promise.race([
      verificationPromise,
      timeoutPromise
    ]) as [string | null, string | null, string | null];

    // Check if tokens were properly saved
    if (!accessToken || !idToken || !refreshToken) {
      console.error('Failed to verify token storage - some tokens not found after save:', {
        hasAccessToken: !!accessToken,
        hasIdToken: !!idToken,
        hasRefreshToken: !!refreshToken
      });
      throw new Error('Token storage verification failed');
    }

    console.log('Token storage verified successfully');

    // Store fallback copy for redundancy
    try {
      await AsyncStorage.setItem('auth_tokens_fallback', JSON.stringify(tokens));
      console.log('Fallback tokens stored successfully');
    } catch (fallbackError) {
      console.warn('Failed to store fallback tokens (non-critical):', fallbackError);
    }

  } catch (error) {
    console.error('Error storing tokens:', error);

    // Fallback to AsyncStorage if SecureStore fails completely
    try {
      console.log('Attempting fallback storage with AsyncStorage due to SecureStore failure');
      await AsyncStorage.setItem('auth_tokens_fallback', JSON.stringify(tokens));
      console.log('Tokens saved to fallback storage successfully');
      
      // Still throw the original error since SecureStore should be the primary storage
      throw error;
    } catch (fallbackError) {
      console.error('Fallback storage also failed:', fallbackError);
      throw new Error('Complete token storage failure - both SecureStore and AsyncStorage failed');
    }
  }
}

// Process the queued requests after a token refresh
function processQueue(error: any, token: string | null = null) {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
}

// Refresh token implementation
// Get auth header for API requests
export async function getAuthHeader(): Promise<{ Authorization?: string }> {
  try {
    const tokens = await getStoredTokens();
    if (tokens?.idToken) {
      return { Authorization: `Bearer ${tokens.idToken}` };
    }
    return {};
  } catch (error) {
    console.error('Error getting auth header:', error);
    return {};
  }
}

export async function refreshToken(): Promise<AuthTokens | null> {
  try {
    // If we're already signing out, don't attempt a refresh
    if ((window as any).__AUTH_SIGNING_OUT) {
      console.log('Skipping token refresh because app is signing out');
      return null;
    }

    if (isRefreshing) {
      // Return a promise that resolves when the refresh is done
      console.log('Token refresh already in progress, waiting for result');
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      });
    }

    isRefreshing = true;
    console.log('Starting token refresh process');

    // Get the current tokens with detailed logging
    console.log('Retrieving stored tokens for refresh...');
    const tokens = await getStoredTokens();
    
    if (!tokens) {
      console.error('No tokens found in storage for refresh');
      isRefreshing = false;
      const error = new Error('No tokens available for refresh');
      processQueue(error);
      return null;
    }

    if (!tokens.refreshToken) {
      console.error('Refresh token is missing from stored tokens:', {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
        hasRefreshToken: !!tokens.refreshToken,
        refreshTokenLength: tokens.refreshToken ? tokens.refreshToken.length : 0
      });
      isRefreshing = false;
      const error = new Error('No refresh token available');
      processQueue(error);
      return null;
    }

    console.log('Refresh token found, length:', tokens.refreshToken.length);

    // Create a dedicated axios instance for refresh to avoid interceptor loops
    const refreshAxios = axios.create({
      baseURL: API_URL,
      timeout: 15000
    });

    try {
      console.log('Sending refresh token request to server');
      const response = await refreshAxios.post('/auth', {
        action: 'refreshToken',
        refreshToken: tokens.refreshToken
      });

      console.log('Refresh token response received, status:', response.status);

      if (response.data && response.data.tokens) {
        const newTokens = response.data.tokens;

        // Validate the tokens before storing
        if (!newTokens.accessToken || !newTokens.idToken || !newTokens.refreshToken) {
          console.error('Invalid tokens received from server during refresh:', {
            hasAccessToken: !!newTokens.accessToken,
            hasIdToken: !!newTokens.idToken,
            hasRefreshToken: !!newTokens.refreshToken
          });
          throw new Error('Invalid tokens received from server');
        }

        console.log('Valid new tokens received, storing tokens...');
        await storeTokens(newTokens);

        console.log('Token refresh successful');
        isRefreshing = false;
        processQueue(null, newTokens.idToken);

        return newTokens;
      } else {
        console.error('Token refresh response missing tokens object:', {
          hasData: !!response.data,
          hasTokens: !!(response.data && response.data.tokens),
          responseData: response.data
        });
        throw new Error('Token refresh response invalid: missing tokens');
      }
    } catch (requestError: any) {
      // Log detailed error info for debugging
      console.error('Token refresh request failed:', {
        status: requestError.response?.status,
        statusText: requestError.response?.statusText,
        message: requestError.message,
        data: requestError.response?.data,
        url: requestError.config?.url
      });

      isRefreshing = false;
      const error = new Error(`Failed to refresh token: ${requestError.message}`);
      processQueue(error);
      throw requestError; // Re-throw to allow signout flow to continue
    }
  } catch (error) {
    console.error('Error in token refresh process:', error);
    isRefreshing = false;
    processQueue(error);
    return null;
  } finally {
    // Ensure we always reset the flag
    isRefreshing = false;
  }
}