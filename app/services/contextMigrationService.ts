/**
 * Context Migration Service
 * 
 * This service handles the migration from the old context system (V2) to the new
 * comprehensive context system (V3). It ensures data integrity and proper mapping
 * of existing user data to the new structure.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  contextEngineV3, 
  ContextType as NewContextType, 
  ContextEntry,
  ContextPriority,
  updateUserPreferences,
  updateUserProfile,
  updateDietaryRestrictions,
  updateInjuries
} from './contextEngineV3';
import { 
  ContextType as OldContextType, 
  ContextData as OldContextData,
  getContextFromLocalStorage 
} from './contextService';
import { getUserId } from './profile';

// Migration mapping from old context types to new ones
const CONTEXT_TYPE_MAPPING: Record<string, NewContextType> = {
  [OldContextType.DIETARY_RESTRICTION]: NewContextType.DIETARY_RESTRICTIONS,
  [OldContextType.INJURY]: NewContextType.INJURIES,
  [OldContextType.LIFE_UPDATE]: NewContextType.USER_PROFILE,
  [OldContextType.PREFERENCE]: NewContextType.PREFERENCES,
  [OldContextType.GOAL]: NewContextType.GOALS,
  [OldContextType.WORKOUT_HISTORY]: NewContextType.WORKOUT_HISTORY,
  [OldContextType.MEAL_HISTORY]: NewContextType.MEAL_HISTORY,
  [OldContextType.WEIGHT_HISTORY]: NewContextType.WEIGHT_HISTORY,
  [OldContextType.CHAT_SUMMARY]: NewContextType.CHAT_SUMMARIES,
  [OldContextType.MESSAGE_CONTENT]: NewContextType.CHAT_HISTORY,
  [OldContextType.CUSTOM]: NewContextType.CUSTOM
};

interface MigrationResult {
  success: boolean;
  migratedEntries: number;
  errors: string[];
  warnings: string[];
  skippedEntries: number;
}

class ContextMigrationService {
  private static instance: ContextMigrationService;
  private readonly MIGRATION_KEY = '@lotus_context_migration_v3';
  private readonly OLD_CONTEXT_BACKUP_KEY = '@lotus_context_v2_backup';

  private constructor() {}

  static getInstance(): ContextMigrationService {
    if (!ContextMigrationService.instance) {
      ContextMigrationService.instance = new ContextMigrationService();
    }
    return ContextMigrationService.instance;
  }

  /**
   * Check if migration is needed
   */
  async isMigrationNeeded(): Promise<boolean> {
    try {
      // Check if migration has already been completed
      const migrationStatus = await AsyncStorage.getItem(this.MIGRATION_KEY);
      if (migrationStatus) {
        const status = JSON.parse(migrationStatus);
        return !status.completed;
      }

      // Check if there's old context data to migrate
      const oldContextData = await getContextFromLocalStorage();
      return oldContextData.length > 0;
    } catch (error) {
      console.error('[ContextMigration] Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Perform the migration from old context system to new
   */
  async performMigration(): Promise<MigrationResult> {
    console.log('[ContextMigration] Starting context migration...');
    
    const result: MigrationResult = {
      success: false,
      migratedEntries: 0,
      errors: [],
      warnings: [],
      skippedEntries: 0
    };

    try {
      // Step 1: Get old context data
      const oldContextData = await getContextFromLocalStorage();
      console.log(`[ContextMigration] Found ${oldContextData.length} old context entries`);

      if (oldContextData.length === 0) {
        result.success = true;
        await this.markMigrationComplete(result);
        return result;
      }

      // Step 2: Backup old data
      await this.backupOldData(oldContextData);

      // Step 3: Group and process old data
      const groupedData = this.groupOldDataByType(oldContextData);

      // Step 4: Migrate each group with proper conflict resolution
      for (const [oldType, entries] of groupedData.entries()) {
        try {
          const migrated = await this.migrateContextGroup(oldType, entries);
          result.migratedEntries += migrated;
        } catch (error) {
          console.error(`[ContextMigration] Error migrating ${oldType}:`, error);
          result.errors.push(`Failed to migrate ${oldType}: ${error.message}`);
          result.skippedEntries += entries.length;
        }
      }

      // Step 5: Validate migration
      const validationResult = await this.validateMigration(oldContextData);
      result.warnings.push(...validationResult.warnings);
      result.errors.push(...validationResult.errors);

      result.success = result.errors.length === 0;

      // Step 6: Mark migration as complete
      await this.markMigrationComplete(result);

      console.log(`[ContextMigration] Migration completed: ${result.migratedEntries} entries migrated, ${result.skippedEntries} skipped`);

    } catch (error) {
      console.error('[ContextMigration] Migration failed:', error);
      result.errors.push(`Migration failed: ${error.message}`);
      result.success = false;
    }

    return result;
  }

  /**
   * Rollback migration if needed
   */
  async rollbackMigration(): Promise<boolean> {
    try {
      console.log('[ContextMigration] Rolling back migration...');

      // Restore old context data from backup
      const backupData = await AsyncStorage.getItem(this.OLD_CONTEXT_BACKUP_KEY);
      if (backupData) {
        const oldContextData = JSON.parse(backupData);
        await AsyncStorage.setItem('@user_context', JSON.stringify(oldContextData));
      }

      // Clear new context data
      await AsyncStorage.removeItem('@lotus_context_v3');

      // Reset migration status
      await AsyncStorage.removeItem(this.MIGRATION_KEY);

      console.log('[ContextMigration] Migration rollback completed');
      return true;
    } catch (error) {
      console.error('[ContextMigration] Rollback failed:', error);
      return false;
    }
  }

  // Private helper methods

  private async backupOldData(oldData: OldContextData[]): Promise<void> {
    await AsyncStorage.setItem(this.OLD_CONTEXT_BACKUP_KEY, JSON.stringify(oldData));
  }

  private groupOldDataByType(oldData: OldContextData[]): Map<string, OldContextData[]> {
    const grouped = new Map<string, OldContextData[]>();
    
    oldData.forEach(entry => {
      if (!grouped.has(entry.contextType)) {
        grouped.set(entry.contextType, []);
      }
      grouped.get(entry.contextType)!.push(entry);
    });

    return grouped;
  }

  private async migrateContextGroup(oldType: string, entries: OldContextData[]): Promise<number> {
    const newType = CONTEXT_TYPE_MAPPING[oldType];
    if (!newType) {
      console.warn(`[ContextMigration] No mapping found for old type: ${oldType}`);
      return 0;
    }

    let migratedCount = 0;

    // Handle different types with specific migration logic
    switch (newType) {
      case NewContextType.PREFERENCES:
        migratedCount = await this.migratePreferences(entries);
        break;
      
      case NewContextType.DIETARY_RESTRICTIONS:
        migratedCount = await this.migrateDietaryRestrictions(entries);
        break;
      
      case NewContextType.INJURIES:
        migratedCount = await this.migrateInjuries(entries);
        break;
      
      case NewContextType.USER_PROFILE:
        migratedCount = await this.migrateUserProfile(entries);
        break;
      
      default:
        migratedCount = await this.migrateGenericEntries(newType, entries);
        break;
    }

    return migratedCount;
  }

  private async migratePreferences(entries: OldContextData[]): Promise<number> {
    // Consolidate all preferences and overwrite old ones
    const allPreferences = entries.map(entry => entry.value).join('; ');
    
    const success = await updateUserPreferences(allPreferences, {
      overwrite: true,
      source: 'migration_v2_to_v3',
      category: 'general_preferences'
    });

    return success ? 1 : 0;
  }

  private async migrateDietaryRestrictions(entries: OldContextData[]): Promise<number> {
    const restrictions = entries.map(entry => entry.value);
    
    const success = await updateDietaryRestrictions(restrictions, {
      overwrite: true,
      source: 'migration_v2_to_v3'
    });

    return success ? restrictions.length : 0;
  }

  private async migrateInjuries(entries: OldContextData[]): Promise<number> {
    const injuries = entries.map(entry => entry.value);
    
    const success = await updateInjuries(injuries, {
      overwrite: true,
      source: 'migration_v2_to_v3'
    });

    return success ? injuries.length : 0;
  }

  private async migrateUserProfile(entries: OldContextData[]): Promise<number> {
    // Merge all life updates into profile
    const profileData = entries.reduce((acc, entry) => {
      return { ...acc, [entry.timestamp]: entry.value };
    }, {});
    
    const success = await updateUserProfile(profileData, {
      merge: true,
      source: 'migration_v2_to_v3'
    });

    return success ? entries.length : 0;
  }

  private async migrateGenericEntries(newType: NewContextType, entries: OldContextData[]): Promise<number> {
    let migratedCount = 0;

    for (const entry of entries) {
      try {
        const success = await contextEngineV3.updateContext(newType, entry.value, {
          source: 'migration_v2_to_v3',
          category: entry.metadata?.category || 'migrated'
        });

        if (success) {
          migratedCount++;
        }
      } catch (error) {
        console.error(`[ContextMigration] Error migrating entry:`, error);
      }
    }

    return migratedCount;
  }

  private async validateMigration(originalData: OldContextData[]): Promise<{ warnings: string[], errors: string[] }> {
    const warnings: string[] = [];
    const errors: string[] = [];

    try {
      // Get migrated data
      const migratedResult = await contextEngineV3.getUserContextForLLM(true);
      
      // Check if critical data was preserved
      const originalCriticalTypes = [
        OldContextType.DIETARY_RESTRICTION,
        OldContextType.INJURY
      ];
      
      const hasCriticalData = originalData.some(entry => 
        originalCriticalTypes.includes(entry.contextType as OldContextType)
      );
      
      if (hasCriticalData && !migratedResult.criticalDataIncluded) {
        errors.push('Critical data (dietary restrictions/injuries) may not have been properly migrated');
      }

      // Check data completeness
      if (migratedResult.totalEntries === 0 && originalData.length > 0) {
        errors.push('No data was migrated despite having original data');
      }

      if (migratedResult.totalEntries < originalData.length * 0.5) {
        warnings.push('Less than 50% of original data was migrated');
      }

    } catch (error) {
      errors.push(`Validation failed: ${error.message}`);
    }

    return { warnings, errors };
  }

  private async markMigrationComplete(result: MigrationResult): Promise<void> {
    const migrationStatus = {
      completed: true,
      completedAt: new Date().toISOString(),
      result
    };

    await AsyncStorage.setItem(this.MIGRATION_KEY, JSON.stringify(migrationStatus));
  }
}

// Export singleton instance and convenience methods
export const contextMigrationService = ContextMigrationService.getInstance();

export async function checkAndPerformMigration(): Promise<MigrationResult | null> {
  const migrationNeeded = await contextMigrationService.isMigrationNeeded();
  
  if (migrationNeeded) {
    console.log('[ContextMigration] Migration needed, starting...');
    return await contextMigrationService.performMigration();
  }
  
  return null;
}

export default contextMigrationService;
