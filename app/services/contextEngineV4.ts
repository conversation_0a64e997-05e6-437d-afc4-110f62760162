/**
 * Context Engine V4 - Advanced AI-Powered Context Management
 * 
 * Enhanced features:
 * - Smart prioritization with machine learning
 * - Predictive suggestions based on user patterns
 * - Adaptive learning algorithms
 * - Advanced confidence scoring
 * - Real-time context optimization
 * - Behavioral pattern recognition
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserId } from './profile';
import { postRequest, getRequest } from './apiRequestManager';
import { ContextEntry, ContextType, ContextPriority, ContextQuery } from './contextEngineV3';

// Enhanced interfaces for V4
export interface SmartContextEntry extends ContextEntry {
  relevanceScore: number;      // 0-1 relevance to current context
  usageFrequency: number;      // How often this data is accessed
  lastAccessed: string;        // When this was last used
  predictiveWeight: number;    // ML-calculated importance
  behavioralTags: string[];    // AI-identified behavioral patterns
  confidenceScore: number;     // 0-1 confidence in data accuracy
  decayFactor: number;         // How quickly this data becomes stale
  contextualRelevance: {       // Relevance in different contexts
    workout: number;
    nutrition: number;
    recovery: number;
    general: number;
  };
}

export interface PredictiveSuggestion {
  id: string;
  type: 'context_update' | 'preference_change' | 'goal_adjustment' | 'habit_formation';
  title: string;
  description: string;
  confidence: number;          // 0-1 confidence in suggestion
  impact: 'low' | 'medium' | 'high';
  reasoning: string[];
  suggestedAction: {
    contextType: ContextType;
    value: any;
    metadata?: Record<string, any>;
  };
  basedOnPatterns: string[];   // What patterns led to this suggestion
  expectedOutcome: string;
  timeframe: string;
  priority: number;            // 1-10 priority score
}

export interface LearningPattern {
  id: string;
  patternType: 'temporal' | 'behavioral' | 'preference' | 'performance';
  description: string;
  confidence: number;
  frequency: number;           // How often this pattern occurs
  strength: number;            // How strong the pattern is (0-1)
  lastObserved: string;
  examples: Array<{
    timestamp: string;
    context: string;
    outcome: string;
  }>;
  predictiveValue: number;     // How useful for predictions (0-1)
}

export interface ContextOptimizationResult {
  optimizedEntries: SmartContextEntry[];
  removedEntries: string[];    // IDs of entries removed for optimization
  suggestions: PredictiveSuggestion[];
  patterns: LearningPattern[];
  performanceMetrics: {
    relevanceScore: number;
    compressionRatio: number;
    responseTime: number;
        contextQuality: number;
  };
  insights: string[];
}

export interface UserBehaviorProfile {
  userId: string;
  activityPatterns: {
    workoutTimes: number[];     // Preferred hours for workouts
    mealTimes: number[];        // Typical meal times
    sleepPattern: {
      bedtime: number;
      wakeTime: number;
      consistency: number;      // 0-1 how consistent sleep schedule is
    };
    appUsagePattern: {
      peakHours: number[];
      sessionDuration: number;
      featureUsage: Record<string, number>;
    };
  };
  preferenceStability: {
    nutrition: number;          // How stable nutrition preferences are
    workout: number;
    goals: number;
    schedule: number;
  };
  responsePatterns: {
    suggestionAcceptanceRate: number;
    feedbackFrequency: number;
    engagementLevel: number;
  };
  learningVelocity: number;     // How quickly user adapts to suggestions
  lastUpdated: string;
}

const STORAGE_KEYS = {
  SMART_CONTEXT: '@context_v4_smart_entries',
  BEHAVIOR_PROFILE: '@context_v4_behavior_profile',
  LEARNING_PATTERNS: '@context_v4_learning_patterns',
  SUGGESTIONS: '@context_v4_suggestions',
  OPTIMIZATION_CACHE: '@context_v4_optimization_cache'
};

const ML_CONSTANTS = {
  RELEVANCE_DECAY_RATE: 0.95,     // Daily decay for relevance
  MIN_CONFIDENCE_THRESHOLD: 0.3,  // Minimum confidence to include
  MAX_CONTEXT_ENTRIES: 50,        // Maximum entries in optimized context
  PATTERN_MIN_OCCURRENCES: 3,     // Minimum occurrences to identify pattern
  SUGGESTION_COOLDOWN_HOURS: 24,  // Hours between similar suggestions
  LEARNING_RATE: 0.1,             // How quickly to adapt to new patterns
  CONFIDENCE_BOOST_FACTOR: 1.2,   // Boost for frequently accessed data
  TEMPORAL_WEIGHT: 0.7,           // Weight for temporal patterns
  BEHAVIORAL_WEIGHT: 0.8,         // Weight for behavioral patterns
  PERFORMANCE_WEIGHT: 0.9         // Weight for performance patterns
};

class ContextEngineV4 {
  private smartEntries: Map<string, SmartContextEntry> = new Map();
  private behaviorProfile: UserBehaviorProfile | null = null;
  private learningPatterns: Map<string, LearningPattern> = new Map();
  private suggestions: Map<string, PredictiveSuggestion> = new Map();
  private optimizationCache: ContextOptimizationResult | null = null;
  private lastOptimization: number = 0;

  /**
   * Get optimized context with AI-powered prioritization
   */
  async getOptimizedContext(
    contextType: 'workout' | 'nutrition' | 'recovery' | 'general' = 'general',
    maxEntries: number = ML_CONSTANTS.MAX_CONTEXT_ENTRIES
  ): Promise<ContextOptimizationResult> {
    console.log(`[ContextV4] Getting optimized context for: ${contextType}`);

    const now = Date.now();
    const cacheValid = this.optimizationCache && 
      (now - this.lastOptimization) < 5 * 60 * 1000; // 5 minute cache

    if (cacheValid && this.optimizationCache) {
      return this.optimizationCache;
    }

    try {
      // Load all data
      await this.loadSmartEntries();
      await this.loadBehaviorProfile();
      await this.loadLearningPatterns();

      // Update relevance scores based on current context
      await this.updateRelevanceScores(contextType);

      // Apply machine learning optimizations
      const optimizedEntries = await this.applyMLOptimization(contextType, maxEntries);

      // Generate predictive suggestions
      const suggestions = await this.generatePredictiveSuggestions();

      // Identify new patterns
      const patterns = await this.identifyNewPatterns();

      // Calculate performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics(optimizedEntries);

      // Generate insights
      const insights = this.generateInsights(optimizedEntries, patterns);

      const result: ContextOptimizationResult = {
        optimizedEntries,
        removedEntries: this.getRemovedEntryIds(optimizedEntries),
        suggestions,
        patterns: Array.from(this.learningPatterns.values()),
        performanceMetrics,
        insights
      };

      // Cache result
      this.optimizationCache = result;
      this.lastOptimization = now;
      await this.saveOptimizationCache(result);

      console.log(`[ContextV4] Optimized context: ${optimizedEntries.length} entries, ${suggestions.length} suggestions`);
      return result;

    } catch (error) {
      console.error('[ContextV4] Error optimizing context:', error);
      return this.getFallbackOptimization();
    }
  }

  /**
   * Update relevance scores using machine learning
   */
  private async updateRelevanceScores(contextType: string): Promise<void> {
    const now = Date.now();
    const behaviorProfile = await this.getBehaviorProfile();

    for (const [id, entry] of this.smartEntries) {
      // Base relevance from contextual relevance mapping
      let relevance = entry.contextualRelevance[contextType as keyof typeof entry.contextualRelevance] || 0.5;

      // Apply temporal decay
      const daysSinceUpdate = (now - new Date(entry.lastUpdated).getTime()) / (1000 * 60 * 60 * 24);
      const decayFactor = Math.pow(ML_CONSTANTS.RELEVANCE_DECAY_RATE, daysSinceUpdate * entry.decayFactor);
      relevance *= decayFactor;

      // Boost based on usage frequency
      const usageBoost = Math.min(1.5, 1 + (entry.usageFrequency * ML_CONSTANTS.CONFIDENCE_BOOST_FACTOR));
      relevance *= usageBoost;

      // Apply behavioral pattern matching
      if (behaviorProfile) {
        const behavioralRelevance = this.calculateBehavioralRelevance(entry, behaviorProfile, contextType);
        relevance = (relevance + behavioralRelevance) / 2;
      }

      // Apply confidence weighting
      relevance *= entry.confidenceScore;

      // Normalize to 0-1 range
      entry.relevanceScore = Math.max(0, Math.min(1, relevance));
    }
  }

  /**
   * Apply machine learning optimization to select best entries
   */
  private async applyMLOptimization(
    contextType: string,
    maxEntries: number
  ): Promise<SmartContextEntry[]> {
    const allEntries = Array.from(this.smartEntries.values());

    // Filter out low-confidence entries
    const validEntries = allEntries.filter(entry => 
      entry.confidenceScore >= ML_CONSTANTS.MIN_CONFIDENCE_THRESHOLD &&
      entry.isActive
    );

    // Calculate composite scores
    const scoredEntries = validEntries.map(entry => ({
      ...entry,
      compositeScore: this.calculateCompositeScore(entry, contextType)
    }));

    // Sort by composite score
    scoredEntries.sort((a, b) => b.compositeScore - a.compositeScore);

    // Apply diversity selection to avoid redundancy
    const selectedEntries = this.applyDiversitySelection(scoredEntries, maxEntries);

    return selectedEntries;
  }

  /**
   * Calculate composite score for ML optimization
   */
  private calculateCompositeScore(entry: SmartContextEntry, contextType: string): number {
    const weights = {
      relevance: 0.3,
      confidence: 0.25,
      frequency: 0.2,
      predictive: 0.15,
      recency: 0.1
    };

    // Relevance score
    const relevanceScore = entry.relevanceScore;

    // Confidence score
    const confidenceScore = entry.confidenceScore;

    // Usage frequency score (normalized)
    const frequencyScore = Math.min(1, entry.usageFrequency / 10);

    // Predictive weight
    const predictiveScore = entry.predictiveWeight;

    // Recency score (how recently accessed)
    const daysSinceAccess = (Date.now() - new Date(entry.lastAccessed).getTime()) / (1000 * 60 * 60 * 24);
    const recencyScore = Math.exp(-daysSinceAccess / 7); // Exponential decay over 7 days

    return (
      relevanceScore * weights.relevance +
      confidenceScore * weights.confidence +
      frequencyScore * weights.frequency +
      predictiveScore * weights.predictive +
      recencyScore * weights.recency
    );
  }

  /**
   * Apply diversity selection to avoid redundant information
   */
  private applyDiversitySelection(
    entries: (SmartContextEntry & { compositeScore: number })[],
    maxEntries: number
  ): SmartContextEntry[] {
    const selected: SmartContextEntry[] = [];
    const typeCount: Record<string, number> = {};

    for (const entry of entries) {
      if (selected.length >= maxEntries) break;

      // Limit entries per type to ensure diversity
      const maxPerType = Math.max(2, Math.floor(maxEntries / Object.keys(ContextType).length));
      const currentTypeCount = typeCount[entry.type] || 0;

      if (currentTypeCount < maxPerType) {
        selected.push(entry);
        typeCount[entry.type] = currentTypeCount + 1;
      }
    }

    // Fill remaining slots with highest scoring entries
    for (const entry of entries) {
      if (selected.length >= maxEntries) break;
      if (!selected.find(s => s.id === entry.id)) {
        selected.push(entry);
      }
    }

    return selected;
  }

  /**
   * Generate predictive suggestions based on patterns
   */
  private async generatePredictiveSuggestions(): Promise<PredictiveSuggestion[]> {
    const suggestions: PredictiveSuggestion[] = [];
    const behaviorProfile = await this.getBehaviorProfile();
    const patterns = Array.from(this.learningPatterns.values());

    if (!behaviorProfile) return suggestions;

    // Analyze patterns for suggestions
    for (const pattern of patterns) {
      if (pattern.confidence > 0.7 && pattern.predictiveValue > 0.6) {
        const suggestion = await this.createSuggestionFromPattern(pattern, behaviorProfile);
        if (suggestion) {
          suggestions.push(suggestion);
        }
      }
    }

    // Sort by priority and confidence
    suggestions.sort((a, b) => {
      const scoreA = a.priority * a.confidence;
      const scoreB = b.priority * b.confidence;
      return scoreB - scoreA;
    });

    return suggestions.slice(0, 5); // Return top 5 suggestions
  }

  /**
   * Identify new behavioral patterns
   */
  private async identifyNewPatterns(): Promise<LearningPattern[]> {
    const newPatterns: LearningPattern[] = [];
    const entries = Array.from(this.smartEntries.values());

    // Temporal pattern analysis
    const temporalPatterns = this.analyzeTemporalPatterns(entries);
    newPatterns.push(...temporalPatterns);

    // Behavioral pattern analysis
    const behavioralPatterns = this.analyzeBehavioralPatterns(entries);
    newPatterns.push(...behavioralPatterns);

    // Performance pattern analysis
    const performancePatterns = this.analyzePerformancePatterns(entries);
    newPatterns.push(...performancePatterns);

    // Update learning patterns map
    newPatterns.forEach(pattern => {
      this.learningPatterns.set(pattern.id, pattern);
    });

    await this.saveLearningPatterns();

    return newPatterns;
  }

  /**
   * Calculate behavioral relevance based on user profile
   */
  private calculateBehavioralRelevance(
    entry: SmartContextEntry,
    profile: UserBehaviorProfile,
    contextType: string
  ): number {
    let relevance = 0.5; // Base relevance

    // Time-based relevance
    const currentHour = new Date().getHours();
    if (contextType === 'workout' && profile.activityPatterns.workoutTimes.includes(currentHour)) {
      relevance += 0.3;
    }
    if (contextType === 'nutrition' && profile.activityPatterns.mealTimes.includes(currentHour)) {
      relevance += 0.3;
    }

    // Preference stability influence
    const stabilityMap = {
      workout: profile.preferenceStability.workout,
      nutrition: profile.preferenceStability.nutrition,
      recovery: profile.preferenceStability.schedule,
      general: (profile.preferenceStability.goals + profile.preferenceStability.schedule) / 2
    };

    const stability = stabilityMap[contextType as keyof typeof stabilityMap] || 0.5;
    relevance *= (0.5 + stability * 0.5); // Boost stable preferences

    // Engagement level influence
    relevance *= (0.7 + profile.responsePatterns.engagementLevel * 0.3);

    return Math.max(0, Math.min(1, relevance));
  }

  /**
   * Analyze temporal patterns in user data
   */
  private analyzeTemporalPatterns(entries: SmartContextEntry[]): LearningPattern[] {
    const patterns: LearningPattern[] = [];
    
    // Group entries by hour of day
    const hourlyActivity: Record<number, SmartContextEntry[]> = {};
    
    entries.forEach(entry => {
      const hour = new Date(entry.timestamp).getHours();
      if (!hourlyActivity[hour]) hourlyActivity[hour] = [];
      hourlyActivity[hour].push(entry);
    });

    // Find peak activity hours
    const activityCounts = Object.entries(hourlyActivity).map(([hour, entries]) => ({
      hour: parseInt(hour),
      count: entries.length,
      types: [...new Set(entries.map(e => e.type))]
    }));

    // Identify patterns with sufficient data
    const significantHours = activityCounts.filter(h => h.count >= ML_CONSTANTS.PATTERN_MIN_OCCURRENCES);
    
    if (significantHours.length > 0) {
      const peakHour = significantHours.reduce((max, current) => 
        current.count > max.count ? current : max
      );

      patterns.push({
        id: `temporal_peak_${peakHour.hour}`,
        patternType: 'temporal',
        description: `Peak activity at ${peakHour.hour}:00 with ${peakHour.count} activities`,
        confidence: Math.min(0.9, peakHour.count / 10),
        frequency: peakHour.count,
        strength: peakHour.count / Math.max(...activityCounts.map(h => h.count)),
        lastObserved: new Date().toISOString(),
        examples: hourlyActivity[peakHour.hour].slice(0, 3).map(entry => ({
          timestamp: entry.timestamp,
          context: entry.type,
          outcome: 'activity_logged'
        })),
        predictiveValue: ML_CONSTANTS.TEMPORAL_WEIGHT
      });
    }

    return patterns;
  }

  /**
   * Analyze behavioral patterns
   */
  private analyzeBehavioralPatterns(entries: SmartContextEntry[]): LearningPattern[] {
    const patterns: LearningPattern[] = [];
    
    // Analyze tag co-occurrence
    const tagPairs: Record<string, number> = {};
    
    entries.forEach(entry => {
      const tags = entry.behavioralTags;
      for (let i = 0; i < tags.length; i++) {
        for (let j = i + 1; j < tags.length; j++) {
          const pair = [tags[i], tags[j]].sort().join('|');
          tagPairs[pair] = (tagPairs[pair] || 0) + 1;
        }
      }
    });

    // Find significant tag associations
    Object.entries(tagPairs).forEach(([pair, count]) => {
      if (count >= ML_CONSTANTS.PATTERN_MIN_OCCURRENCES) {
        const [tag1, tag2] = pair.split('|');
        patterns.push({
          id: `behavioral_${pair.replace('|', '_')}`,
          patternType: 'behavioral',
          description: `Strong association between ${tag1} and ${tag2}`,
          confidence: Math.min(0.9, count / 10),
          frequency: count,
          strength: count / entries.length,
          lastObserved: new Date().toISOString(),
          examples: entries
            .filter(e => e.behavioralTags.includes(tag1) && e.behavioralTags.includes(tag2))
            .slice(0, 3)
            .map(entry => ({
              timestamp: entry.timestamp,
              context: `${tag1} + ${tag2}`,
              outcome: 'pattern_observed'
            })),
          predictiveValue: ML_CONSTANTS.BEHAVIORAL_WEIGHT
        });
      }
    });

    return patterns;
  }

  /**
   * Analyze performance patterns
   */
  private analyzePerformancePatterns(entries: SmartContextEntry[]): LearningPattern[] {
    const patterns: LearningPattern[] = [];
    
    // Analyze confidence score trends
    const confidenceEntries = entries
      .filter(e => e.confidenceScore > 0)
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    if (confidenceEntries.length >= ML_CONSTANTS.PATTERN_MIN_OCCURRENCES) {
      const recentConfidence = confidenceEntries.slice(-5).reduce((sum, e) => sum + e.confidenceScore, 0) / 5;
      const olderConfidence = confidenceEntries.slice(0, 5).reduce((sum, e) => sum + e.confidenceScore, 0) / 5;
      
      if (Math.abs(recentConfidence - olderConfidence) > 0.1) {
        const trend = recentConfidence > olderConfidence ? 'improving' : 'declining';
        
        patterns.push({
          id: `performance_confidence_${trend}`,
          patternType: 'performance',
          description: `Confidence scores are ${trend} (${recentConfidence.toFixed(2)} vs ${olderConfidence.toFixed(2)})`,
          confidence: Math.abs(recentConfidence - olderConfidence),
          frequency: confidenceEntries.length,
          strength: Math.abs(recentConfidence - olderConfidence),
          lastObserved: new Date().toISOString(),
          examples: confidenceEntries.slice(-3).map(entry => ({
            timestamp: entry.timestamp,
            context: `confidence_${entry.confidenceScore.toFixed(2)}`,
            outcome: trend
          })),
          predictiveValue: ML_CONSTANTS.PERFORMANCE_WEIGHT
        });
      }
    }

    return patterns;
  }

  /**
   * Create suggestion from identified pattern
   */
  private async createSuggestionFromPattern(
    pattern: LearningPattern,
    profile: UserBehaviorProfile
  ): Promise<PredictiveSuggestion | null> {
    // This is a simplified example - in practice, this would be much more sophisticated
    if (pattern.patternType === 'temporal' && pattern.description.includes('Peak activity')) {
      const hour = parseInt(pattern.description.match(/(\d+):00/)?.[1] || '0');
      
      return {
        id: `suggestion_${pattern.id}_${Date.now()}`,
        type: 'preference_change',
        title: 'Optimize Your Schedule',
        description: `You're most active around ${hour}:00. Consider scheduling important activities during this time.`,
        confidence: pattern.confidence,
        impact: 'medium',
        reasoning: [
          `Peak activity detected at ${hour}:00`,
          `${pattern.frequency} activities logged during this time`,
          `Confidence: ${(pattern.confidence * 100).toFixed(0)}%`
        ],
        suggestedAction: {
          contextType: ContextType.SCHEDULE,
          value: { preferredActivityTime: hour },
          metadata: { source: 'ml_pattern_analysis' }
        },
        basedOnPatterns: [pattern.id],
        expectedOutcome: 'Improved activity consistency and performance',
        timeframe: '1-2 weeks',
        priority: Math.round(pattern.confidence * 10)
      };
    }

    return null;
  }

  /**
   * Calculate performance metrics for optimization result
   */
  private calculatePerformanceMetrics(entries: SmartContextEntry[]): ContextOptimizationResult['performanceMetrics'] {
    const totalEntries = this.smartEntries.size;
    const avgRelevance = entries.reduce((sum, e) => sum + e.relevanceScore, 0) / entries.length;
    const avgConfidence = entries.reduce((sum, e) => sum + e.confidenceScore, 0) / entries.length;
    
    return {
      relevanceScore: avgRelevance,
      compressionRatio: entries.length / totalEntries,
      responseTime: Date.now() - this.lastOptimization,
      contextQuality: (avgRelevance + avgConfidence) / 2
    };
  }

  /**
   * Generate insights from optimization
   */
  private generateInsights(entries: SmartContextEntry[], patterns: LearningPattern[]): string[] {
    const insights: string[] = [];
    
    // Relevance insights
    const highRelevanceEntries = entries.filter(e => e.relevanceScore > 0.8);
    if (highRelevanceEntries.length > 0) {
      insights.push(`${highRelevanceEntries.length} highly relevant context entries identified`);
    }

    // Pattern insights
    const strongPatterns = patterns.filter(p => p.strength > 0.7);
    if (strongPatterns.length > 0) {
      insights.push(`${strongPatterns.length} strong behavioral patterns detected`);
    }

    // Confidence insights
    const avgConfidence = entries.reduce((sum, e) => sum + e.confidenceScore, 0) / entries.length;
    if (avgConfidence > 0.8) {
      insights.push('High confidence in context data quality');
    } else if (avgConfidence < 0.5) {
      insights.push('Context data quality could be improved');
    }

    return insights;
  }

  /**
   * Helper methods for data management
   */
  private async getBehaviorProfile(): Promise<UserBehaviorProfile | null> {
    if (this.behaviorProfile) return this.behaviorProfile;
    
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.BEHAVIOR_PROFILE);
      if (stored) {
        this.behaviorProfile = JSON.parse(stored);
        return this.behaviorProfile;
      }
    } catch (error) {
      console.error('[ContextV4] Error loading behavior profile:', error);
    }
    
    return null;
  }

  private getRemovedEntryIds(optimizedEntries: SmartContextEntry[]): string[] {
    const optimizedIds = new Set(optimizedEntries.map(e => e.id));
    return Array.from(this.smartEntries.keys()).filter(id => !optimizedIds.has(id));
  }

  private getFallbackOptimization(): ContextOptimizationResult {
    return {
      optimizedEntries: [],
      removedEntries: [],
      suggestions: [],
      patterns: [],
      performanceMetrics: {
        relevanceScore: 0,
        compressionRatio: 0,
        responseTime: 0,
        contextQuality: 0
      },
      insights: ['Fallback optimization due to error']
    };
  }

  private async loadSmartEntries(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.SMART_CONTEXT);
      if (stored) {
        const entries = JSON.parse(stored) as SmartContextEntry[];
        this.smartEntries.clear();
        entries.forEach(entry => this.smartEntries.set(entry.id, entry));
      }
    } catch (error) {
      console.error('[ContextV4] Error loading smart entries:', error);
    }
  }

  private async loadLearningPatterns(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.LEARNING_PATTERNS);
      if (stored) {
        const patterns = JSON.parse(stored) as LearningPattern[];
        this.learningPatterns.clear();
        patterns.forEach(pattern => this.learningPatterns.set(pattern.id, pattern));
      }
    } catch (error) {
      console.error('[ContextV4] Error loading learning patterns:', error);
    }
  }

  private async saveLearningPatterns(): Promise<void> {
    try {
      const patterns = Array.from(this.learningPatterns.values());
      await AsyncStorage.setItem(STORAGE_KEYS.LEARNING_PATTERNS, JSON.stringify(patterns));
    } catch (error) {
      console.error('[ContextV4] Error saving learning patterns:', error);
    }
  }

  private async saveOptimizationCache(result: ContextOptimizationResult): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.OPTIMIZATION_CACHE, JSON.stringify(result));
    } catch (error) {
      console.error('[ContextV4] Error saving optimization cache:', error);
    }
  }
}

// Export singleton instance
export const contextEngineV4 = new ContextEngineV4();

// Convenience functions
export const getOptimizedContext = (
  contextType?: 'workout' | 'nutrition' | 'recovery' | 'general',
  maxEntries?: number
) => contextEngineV4.getOptimizedContext(contextType, maxEntries);

export const generatePredictiveSuggestions = () =>
  contextEngineV4.getOptimizedContext().then(result => result.suggestions);

export const getLearningPatterns = () =>
  contextEngineV4.getOptimizedContext().then(result => result.patterns);

/**
 * Migration service to upgrade from ContextEngineV3 to V4
 */
export class ContextMigrationService {
  async migrateFromV3(): Promise<{ success: boolean; migratedCount: number; errors: string[] }> {
    console.log('[ContextV4] Starting migration from V3...');

    try {
      // Import V3 engine
      const { contextEngineV3 } = await import('./contextEngineV3');

      // Get all V3 entries
      const v3Entries = await contextEngineV3.getContextEntries({ activeOnly: true });

      const migratedEntries: SmartContextEntry[] = [];
      const errors: string[] = [];

      for (const v3Entry of v3Entries) {
        try {
          const smartEntry = this.convertV3ToV4(v3Entry);
          migratedEntries.push(smartEntry);
        } catch (error) {
          errors.push(`Failed to migrate entry ${v3Entry.id}: ${error}`);
        }
      }

      // Save migrated entries
      await AsyncStorage.setItem(STORAGE_KEYS.SMART_CONTEXT, JSON.stringify(migratedEntries));

      console.log(`[ContextV4] Migration completed: ${migratedEntries.length} entries migrated`);

      return {
        success: true,
        migratedCount: migratedEntries.length,
        errors
      };

    } catch (error) {
      console.error('[ContextV4] Migration failed:', error);
      return {
        success: false,
        migratedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  private convertV3ToV4(v3Entry: ContextEntry): SmartContextEntry {
    // Convert V3 entry to V4 with enhanced properties
    return {
      ...v3Entry,
      relevanceScore: 0.5, // Default relevance
      usageFrequency: 1,   // Default usage
      lastAccessed: v3Entry.timestamp,
      predictiveWeight: 0.5,
      behavioralTags: this.extractBehavioralTags(v3Entry),
      confidenceScore: v3Entry.metadata?.confidence || 0.7,
      decayFactor: this.calculateDecayFactor(v3Entry.type),
      contextualRelevance: this.calculateContextualRelevance(v3Entry)
    };
  }

  private extractBehavioralTags(entry: ContextEntry): string[] {
    const tags: string[] = [];

    // Extract tags based on entry type and content
    if (entry.type.includes('workout')) tags.push('fitness', 'exercise');
    if (entry.type.includes('meal') || entry.type.includes('nutrition')) tags.push('nutrition', 'food');
    if (entry.type.includes('weight')) tags.push('tracking', 'progress');
    if (entry.type.includes('goal')) tags.push('motivation', 'planning');

    // Add metadata tags if available
    if (entry.metadata?.tags) {
      tags.push(...entry.metadata.tags);
    }

    return [...new Set(tags)]; // Remove duplicates
  }

  private calculateDecayFactor(type: ContextType): number {
    // Different types decay at different rates
    const decayMap = {
      [ContextType.MEDICAL_CONDITIONS]: 0.1,    // Very slow decay
      [ContextType.DIETARY_RESTRICTIONS]: 0.2,  // Slow decay
      [ContextType.GOALS]: 0.3,                 // Medium decay
      [ContextType.PREFERENCES]: 0.4,           // Medium decay
      [ContextType.WORKOUT_HISTORY]: 0.8,       // Fast decay
      [ContextType.MEAL_HISTORY]: 0.9,          // Very fast decay
    };

    return decayMap[type] || 0.5; // Default decay
  }

  private calculateContextualRelevance(entry: ContextEntry): SmartContextEntry['contextualRelevance'] {
    const base = { workout: 0.3, nutrition: 0.3, recovery: 0.3, general: 0.5 };

    // Adjust based on entry type
    switch (entry.type) {
      case ContextType.WORKOUT_PREFERENCES:
      case ContextType.WORKOUT_HISTORY:
        return { ...base, workout: 0.9 };

      case ContextType.NUTRITION_PREFERENCES:
      case ContextType.MEAL_HISTORY:
      case ContextType.DIETARY_RESTRICTIONS:
        return { ...base, nutrition: 0.9 };

      case ContextType.MEDICAL_CONDITIONS:
      case ContextType.INJURIES:
        return { ...base, recovery: 0.9 };

      default:
        return base;
    }
  }
}

export const contextMigrationService = new ContextMigrationService();
