import AsyncStorage from '@react-native-async-storage/async-storage';
import { getContextFromLocalStorage, ContextType } from './contextService';
import { getWeightEntries, WeightEntry } from './weightTracking';
import { getLogs, MealData, Log } from './conversationService';
import axios from 'axios';
import { getUserId } from './chatService';
import api from '../utils/api';
import { makeRequestWithRetry } from './apiClient';
import { postRequest, getRequest } from './apiRequestManager';

// Nutrition target calculation constants
const ACTIVITY_MULTIPLIERS = {
  sedentary: 1.2,
  lightlyActive: 1.375,
  moderatelyActive: 1.55,
  veryActive: 1.725,
  extraActive: 1.9
};

// Macro distribution defaults (percentages)
const DEFAULT_MACRO_SPLIT = {
  protein: 0.3, // 30%
  carbs: 0.4,   // 40%
  fat: 0.3      // 30%
};

// Nutrition targets interface
export interface NutritionTargets {
  calories: number;
  protein: number; // in grams
  carbs: number;   // in grams
  fat: number;     // in grams
}

// User profile for nutrition calculations
export interface NutritionProfile {
  weight: number; // in pounds
  height?: number; // in inches
  age?: number;
  gender?: 'male' | 'female' | 'other';
  activityLevel: keyof typeof ACTIVITY_MULTIPLIERS;
  goal: 'maintain' | 'lose' | 'gain';
  dietaryPreferences: string[];
}

// Weather data interface
export interface WeatherData {
  temperature: number | null; // in Celsius
  temperatureF: number | null; // in Fahrenheit
  condition: string;
  humidity: number | null;
  location: string;
  timestamp: string;
  // Additional fields from WeatherAPI.com
  feelsLike?: number | null;
  feelsLikeF?: number | null;
  windSpeed?: number | null; // in km/h
  windSpeedMph?: number | null; // in mph
  windDirection?: string | null;
  iconUrl?: string | null;
  isDay?: boolean;
  unavailable?: boolean; // Flag to indicate weather data is unavailable
}

// Meal suggestion interface
export interface MealSuggestion {
  title: string;
  description: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  imageUrl?: string;
  tags: string[];
  timeOfDay: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  weatherAppropriate: boolean;
  ingredients?: string[];
  instructions?: string[];
  prepTime?: string;
  cookTime?: string;
  servings?: number;
  cuisine?: string;
}

// Storage keys
const NUTRITION_PROFILE_KEY = '@nutrition_profile';
const WEATHER_DATA_KEY = '@weather_data';

// Cache for nutrition profile - exported so it can be directly modified by other services
export let nutritionProfileCache: {
  profile: NutritionProfile;
  timestamp: number;
} | null = null;

// Get the user's nutrition profile from storage or create default
export async function getNutritionProfile(forceRefresh: boolean = false): Promise<NutritionProfile> {
  try {
    // Check if we have cached profile and it's less than 5 minutes old
    const now = Date.now();
    if (!forceRefresh && nutritionProfileCache && (now - nutritionProfileCache.timestamp < 5 * 60 * 1000)) {
      console.log('Using cached nutrition profile');
      return nutritionProfileCache.profile;
    }

    console.log('Getting fresh nutrition profile');

    // Try to get from storage
    const profileJson = await AsyncStorage.getItem(NUTRITION_PROFILE_KEY);
    if (profileJson) {
      const profile = JSON.parse(profileJson);

      // Update with latest weight if available
      try {
        const { getWeightEntries } = require('./weightTracking');
        const weightEntries = await getWeightEntries();
        if (weightEntries.length > 0) {
          // Sort by date (newest first)
          const sortedEntries = [...weightEntries].sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          );
          const latestWeight = sortedEntries[0].value;
          console.log(`Updating nutrition profile with latest weight: ${latestWeight} lbs`);
          profile.weight = latestWeight;

          // Save the updated profile
          await AsyncStorage.setItem(NUTRITION_PROFILE_KEY, JSON.stringify(profile));
        }
      } catch (weightError) {
        console.error('Error updating nutrition profile with latest weight:', weightError);
      }

      // Cache the profile
      nutritionProfileCache = {
        profile,
        timestamp: now
      };

      return profile;
    }

    // If not in storage, create a default profile based on user data
    const newProfile = await createDefaultNutritionProfile();

    // Cache the new profile
    nutritionProfileCache = {
      profile: newProfile,
      timestamp: now
    };

    return newProfile;
  } catch (error) {
    console.error('Error getting nutrition profile:', error);
    // Return a very basic default profile
    return {
      weight: 150,
      activityLevel: 'moderatelyActive',
      goal: 'maintain',
      dietaryPreferences: []
    };
  }
}

// Save the nutrition profile
export async function saveNutritionProfile(profile: NutritionProfile): Promise<void> {
  try {
    console.log('Saving nutrition profile with weight:', profile.weight);

    // Save to local storage
    await AsyncStorage.setItem(NUTRITION_PROFILE_KEY, JSON.stringify(profile));

    // Update the cache with the new profile
    nutritionProfileCache = {
      profile,
      timestamp: Date.now()
    };

    // Clear nutrition targets cache to force recalculation
    nutritionTargetsCache = null;

    // Also save to user profile in database via the log service
    // This ensures the data is consistent with the profile page
    try {
      const { saveLog } = require('./conversationService');

      // Save weight to database
      if (profile.weight) {
        const timestamp = new Date().toISOString();
        const weightId = `weight_${Date.now()}`;

        await saveLog({
          id: weightId,
          type: 'weight',
          description: `Weight: ${profile.weight} lbs`,
          timestamp: timestamp,
          contextDate: timestamp,
          metrics: {
            weight: {
              value: Number(profile.weight), // Ensure it's a number
              date: timestamp,
            }
          }
        });
      }

      // Don't save profile_update as it's not a valid log type
      // Instead, update the user profile directly
      try {
        const { getProfileFromStorage, saveProfileToStorage } = require('./profile');
        const userProfile = await getProfileFromStorage();

        if (userProfile) {
          // Update profile with nutrition data
          if (profile.height) userProfile.height = profile.height.toString();
          if (profile.weight) userProfile.weight = profile.weight.toString();

          // Save updated profile
          await saveProfileToStorage(userProfile);
          console.log('Updated user profile with nutrition data');
        }
      } catch (profileError) {
        console.error('Error updating user profile:', profileError);
      }
    } catch (dbError) {
      console.error('Error saving nutrition profile to database:', dbError);
      // Continue even if database save fails - at least local storage worked
    }
  } catch (error) {
    console.error('Error saving nutrition profile:', error);
    throw error;
  }
}

// Create a default nutrition profile based on user data
async function createDefaultNutritionProfile(): Promise<NutritionProfile> {
  try {
    // Get the user's weight from weight entries
    let latestWeight = 150; // Default weight if no entries

    // First try to get from weight entries
    const weightEntries = await getWeightEntries();
    if (weightEntries.length > 0) {
      latestWeight = weightEntries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0].value;
      console.log(`Using latest weight from weight tracker for default profile: ${latestWeight} lbs`);
    } else {
      // If no weight entries, try to get from user profile
      try {
        const { getProfileFromStorage } = require('./profile');
        const userProfile = await getProfileFromStorage();

        // If user profile has weight, use it
        if (userProfile?.weight) {
          const userWeight = parseFloat(userProfile.weight);
          if (!isNaN(userWeight) && userWeight > 0) {
            console.log(`Using weight from user profile for default profile: ${userWeight} lbs`);
            latestWeight = userWeight;
          }
        }
      } catch (profileError) {
        console.error('Error getting user profile for weight:', profileError);
      }
    }

    // Get dietary preferences from context
    const dietaryContext = await getContextFromLocalStorage(ContextType.DIETARY_RESTRICTION);
    const dietaryPreferences = dietaryContext.map(item => item.value as string);

    // Extract goal from context if available
    const goalContext = await getContextFromLocalStorage(ContextType.GOAL);
    let goal: 'maintain' | 'lose' | 'gain' = 'maintain';

    // Simple goal detection from context
    for (const item of goalContext) {
      const goalText = item.value as string;
      if (typeof goalText === 'string') {
        const lowerGoal = goalText.toLowerCase();
        if (lowerGoal.includes('lose weight') || lowerGoal.includes('cut') || lowerGoal.includes('slim down')) {
          goal = 'lose';
          break;
        } else if (lowerGoal.includes('gain weight') || lowerGoal.includes('bulk') || lowerGoal.includes('build muscle')) {
          goal = 'gain';
          break;
        }
      }
    }

    // Create the profile
    const profile: NutritionProfile = {
      weight: latestWeight,
      activityLevel: 'moderatelyActive', // Default to moderately active
      goal,
      dietaryPreferences
    };

    // Save the profile for future use
    await saveNutritionProfile(profile);
    return profile;
  } catch (error) {
    console.error('Error creating default nutrition profile:', error);
    return {
      weight: 150,
      activityLevel: 'moderatelyActive',
      goal: 'maintain',
      dietaryPreferences: []
    };
  }
}

// Calculate BMR using Mifflin-St Jeor Equation
function calculateBMR(weight: number, height?: number, age?: number, gender?: string): number {
  // Convert weight from pounds to kg
  const weightKg = weight * 0.453592;

  console.log('BMR calculation inputs:', {
    weightLbs: weight,
    weightKg,
    heightInches: height,
    heightCm: height ? height * 2.54 : 'missing',
    age,
    gender
  });

  // If we have complete data, use the full equation
  if (height && age && gender) {
    const heightCm = height * 2.54; // Convert inches to cm
    let bmr: number;

    if (gender === 'male') {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) + 5;
      console.log('Using male BMR formula:', {
        weightTerm: 10 * weightKg,
        heightTerm: 6.25 * heightCm,
        ageTerm: 5 * age,
        constant: 5,
        result: bmr
      });
    } else {
      bmr = (10 * weightKg) + (6.25 * heightCm) - (5 * age) - 161;
      console.log('Using female BMR formula:', {
        weightTerm: 10 * weightKg,
        heightTerm: 6.25 * heightCm,
        ageTerm: 5 * age,
        constant: -161,
        result: bmr
      });
    }

    return bmr;
  }

  // Simplified equation based only on weight if other data is missing
  const estimatedBmr = weightKg * 24 * 1.2; // Basic estimate: 24 calories per kg of body weight at rest
  console.log('Using simplified BMR formula (missing data):', {
    formula: 'weightKg * 24 * 1.2',
    result: estimatedBmr
  });

  return estimatedBmr;
}

// Calculate TDEE (Total Daily Energy Expenditure)
function calculateTDEE(bmr: number, activityLevel: keyof typeof ACTIVITY_MULTIPLIERS): number {
  return bmr * ACTIVITY_MULTIPLIERS[activityLevel];
}

// Calculate target calories based on goal
function calculateTargetCalories(tdee: number, goal: 'maintain' | 'lose' | 'gain'): number {
  switch (goal) {
    case 'lose':
      // For weight loss, create a moderate deficit of 500 calories or 20%, whichever is less extreme
      const deficit = Math.min(500, tdee * 0.2);
      return Math.round(tdee - deficit);

    case 'gain':
      // For muscle gain, create a moderate surplus of 300-500 calories or 15%, whichever is less extreme
      const surplus = Math.min(500, tdee * 0.15);
      return Math.round(tdee + surplus);

    case 'maintain':
    default:
      return Math.round(tdee);
  }
}

// Calculate macro targets based on calories and body weight
function calculateMacroTargets(calories: number, profile: NutritionProfile): {protein: number, carbs: number, fat: number} {
  // Calculate protein based on body weight (0.8g per pound is the recommended amount)
  // For weight loss, use 1g per pound; for muscle gain, use 1.2g per pound
  let proteinMultiplier = 0.8; // Default for maintenance

  if (profile.goal === 'lose') {
    proteinMultiplier = 1.0; // Higher protein for weight loss
  } else if (profile.goal === 'gain') {
    proteinMultiplier = 1.2; // Even higher protein for muscle gain
  }

  // Calculate protein in grams based on weight
  const proteinGrams = Math.round(profile.weight * proteinMultiplier);

  // Calculate protein calories
  const proteinCalories = proteinGrams * 4; // 4 calories per gram of protein

  // Calculate remaining calories for carbs and fat
  const remainingCalories = calories - proteinCalories;

  // Determine carb and fat ratio based on goal
  let carbRatio = 0.65; // Default for maintenance

  if (profile.goal === 'lose') {
    carbRatio = 0.55; // Lower carbs for weight loss
  } else if (profile.goal === 'gain') {
    carbRatio = 0.7; // Higher carbs for muscle gain
  }

  // Calculate carbs and fat
  const carbCalories = remainingCalories * carbRatio;
  const fatCalories = remainingCalories * (1 - carbRatio);

  const carbs = Math.round(carbCalories / 4); // 4 calories per gram
  const fat = Math.round(fatCalories / 9);    // 9 calories per gram

  console.log('Macro calculation details:', {
    weight: profile.weight,
    proteinMultiplier,
    proteinGrams,
    proteinCalories,
    remainingCalories,
    carbRatio,
    carbCalories,
    fatCalories,
    carbs,
    fat
  });

  return { protein: proteinGrams, carbs, fat };
}

// Cache for nutrition targets - exported so it can be directly modified by other services
export let nutritionTargetsCache: {
  targets: NutritionTargets;
  timestamp: number;
} | null = null;

// Get personalized nutrition targets
export async function getNutritionTargets(forceRefresh: boolean = false): Promise<NutritionTargets> {
  try {
    // Check if we have cached targets and they're less than 5 minutes old
    const now = Date.now();
    if (!forceRefresh && nutritionTargetsCache && (now - nutritionTargetsCache.timestamp < 5 * 60 * 1000)) {
      console.log('Using cached nutrition targets');
      return nutritionTargetsCache.targets;
    }

    console.log('Calculating fresh nutrition targets');

    // Get the latest profile data
    const profile = await getNutritionProfile();

    // Get the latest weight from weight entries if available
    try {
      const { getWeightEntries } = require('./weightTracking');
      const weightEntries = await getWeightEntries();
      if (weightEntries.length > 0) {
        // Sort by date (newest first)
        const sortedEntries = [...weightEntries].sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        const latestWeight = sortedEntries[0].value;
        console.log(`Using latest weight from weight tracker: ${latestWeight} lbs`);
        profile.weight = latestWeight;
      } else {
        // If no weight entries, try to get from user profile
        const { getProfileFromStorage } = require('./profile');
        const userProfile = await getProfileFromStorage();

        // If user profile has weight, use it
        if (userProfile?.weight) {
          const userWeight = parseFloat(userProfile.weight);
          if (!isNaN(userWeight) && userWeight > 0) {
            console.log(`Using weight from user profile: ${userWeight} lbs`);
            profile.weight = userWeight;
          }
        }
      }
    } catch (error) {
      console.error('Error getting latest weight:', error);
      // Continue with nutrition profile weight
    }

    console.log('Nutrition profile for calculation:', {
      weight: profile.weight,
      height: profile.height,
      age: profile.age,
      gender: profile.gender,
      activityLevel: profile.activityLevel,
      goal: profile.goal
    });

    // Calculate BMR
    const bmr = calculateBMR(profile.weight, profile.height, profile.age, profile.gender);
    console.log('Calculated BMR:', bmr);

    // Calculate TDEE
    const tdee = calculateTDEE(bmr, profile.activityLevel);
    console.log('Calculated TDEE:', tdee);

    // Calculate target calories
    const calories = calculateTargetCalories(tdee, profile.goal);
    console.log('Calculated target calories:', calories);

    // Calculate macro targets
    const macros = calculateMacroTargets(calories, profile);
    console.log('Calculated macro targets:', macros);

    // Create targets object
    const targets = {
      calories,
      ...macros
    };

    // Cache the results
    nutritionTargetsCache = {
      targets,
      timestamp: now
    };

    return targets;
  } catch (error) {
    console.error('Error calculating nutrition targets:', error);
    // Return default targets
    return {
      calories: 2000,
      protein: 150,
      carbs: 200,
      fat: 65
    };
  }
}

// Get today's consumed nutrition from meal logs
export async function getTodayNutrition(): Promise<{calories: number, protein: number, carbs: number, fat: number}> {
  try {
    // Get all logs
    const logs = await getLogs();

    // Filter for meal logs from today
    const today = new Date().toISOString().split('T')[0];
    const todayMeals = logs.filter(log => {
      return log.type === 'meal' &&
        new Date(log.timestamp).toISOString().split('T')[0] === today;
    });

    // Calculate total nutrition
    let calories = 0;
    let protein = 0;
    let carbs = 0;
    let fat = 0;

    todayMeals.forEach(meal => {
      // Check if meal has nutrition data
      if (meal.metrics?.meal?.nutrition) {
        calories += meal.metrics.meal.nutrition.calories || 0;
        protein += meal.metrics.meal.nutrition.protein || 0;
        carbs += meal.metrics.meal.nutrition.carbs || 0;
        fat += meal.metrics.meal.nutrition.fat || 0;
      } else if (meal.metrics?.meal) {
        // Try to extract from the meal object directly
        const mealData = meal.metrics.meal as MealData;
        calories += mealData.calories || 0;
        protein += mealData.protein || 0;
        carbs += mealData.carbs || 0;
        fat += mealData.fat || 0;
      }
    });

    return { calories, protein, carbs, fat };
  } catch (error) {
    console.error('Error getting today nutrition:', error);
    return { calories: 0, protein: 0, carbs: 0, fat: 0 };
  }
}

// Get current weather data
export async function getCurrentWeather(forceRefresh: boolean = false): Promise<WeatherData | null> {
  try {
    // Check if we should use cached data
    if (!forceRefresh) {
      // First check if we have recent weather data in storage (less than 30 minutes old)
      const weatherJson = await AsyncStorage.getItem(WEATHER_DATA_KEY);
      if (weatherJson) {
        const storedWeather = JSON.parse(weatherJson) as WeatherData;
        const storedTime = new Date(storedWeather.timestamp).getTime();
        const currentTime = new Date().getTime();

        // If weather data is less than 30 minutes old, use it
        const thirtyMinutesInMs = 30 * 60 * 1000;
        if (currentTime - storedTime < thirtyMinutesInMs) {
          console.log('Using cached weather data:', storedWeather.location);
          return storedWeather;
        }
      }
    } else {
      console.log('Force refreshing weather data');
    }

    // Otherwise, fetch new weather data
    return await fetchWeatherData();
  } catch (error) {
    console.error('Error getting weather data:', error);
    return null;
  }
}

// Fetch weather data from API
async function fetchWeatherData(): Promise<WeatherData | null> {
  try {
    // Import location service
    const { getLocationWithFreshnessCheck, checkLocationPermission, requestLocationPermission } = require('./locationService');
    // Import direct weather API service
    const { fetchWeatherByCoordinates, fetchWeatherByCity: directFetchWeatherByCity, getUnavailableWeatherData } = require('./weatherApiService');

    // Check if we have location permission
    let hasPermission = await checkLocationPermission();

    // If no permission, try to request it
    if (!hasPermission) {
      console.log('No location permission, requesting...');
      hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        console.log('Location permission denied, using default location');
        return directFetchWeatherByCity('New York');
      }
    }

    // Get location - force refresh if we're explicitly refreshing weather
    const locationData = await getLocationWithFreshnessCheck(true);
    if (!locationData) {
      console.log('Could not get location, using default location');
      return directFetchWeatherByCity('New York');
    }

    console.log(`Got location: ${locationData.latitude}, ${locationData.longitude}`);

    // Use the direct weather API service to fetch weather data
    const weatherData = await fetchWeatherByCoordinates(
      locationData.latitude,
      locationData.longitude
    );

    if (weatherData) {
      return weatherData;
    }

    // If we couldn't get weather data by coordinates, try with a default city
    console.log('Could not get weather data by coordinates, trying with default city');
    return directFetchWeatherByCity('New York');
  } catch (error) {
    console.error('Error fetching weather data:', error);

    // Import the getUnavailableWeatherData function
    const { getUnavailableWeatherData } = require('./weatherApiService');

    // Return a special "unavailable" weather object
    const unavailableWeather = getUnavailableWeatherData();

    // Save to storage so we don't keep trying to fetch if there's a persistent issue
    await AsyncStorage.setItem(WEATHER_DATA_KEY, JSON.stringify(unavailableWeather));

    return unavailableWeather;
  }
}

// Helper function to fetch weather by city name
async function fetchWeatherByCity(city: string): Promise<WeatherData | null> {
  try {
    // Import direct weather API service
    const { fetchWeatherByCity: directFetchWeatherByCity, getUnavailableWeatherData } = require('./weatherApiService');

    // Use the direct weather API service to fetch weather data
    return directFetchWeatherByCity(city);
  } catch (error) {
    console.error(`Error fetching weather for city ${city}:`, error);

    // Import the getUnavailableWeatherData function
    const { getUnavailableWeatherData } = require('./weatherApiService');

    // Return a special "unavailable" weather object
    const unavailableWeather = getUnavailableWeatherData();

    // Save to storage so we don't keep trying to fetch if there's a persistent issue
    await AsyncStorage.setItem(WEATHER_DATA_KEY, JSON.stringify(unavailableWeather));

    return unavailableWeather;
  }
}

// Get time of day (morning, afternoon, evening)
export function getTimeOfDay(): 'breakfast' | 'lunch' | 'dinner' | 'snack' {
  const hour = new Date().getHours();

  if (hour >= 5 && hour < 10) {
    return 'breakfast';
  } else if (hour >= 10 && hour < 14) {
    return 'lunch';
  } else if (hour >= 17 && hour < 21) {
    return 'dinner';
  } else {
    return 'snack';
  }
}

// Cache for meal suggestions
let mealSuggestionsCache: {
  suggestions: MealSuggestion[];
  timestamp: number;
  timeOfDay: string;
  cuisinePreferences?: string[];
  cacheId?: string; // Unique identifier for the cache to help with invalidation
} | null = null;

// Generate meal suggestions based on remaining macros, time of day, weather, and cuisine preferences
// Always uses the LLM (Groq) API for meal suggestions - never uses mock data
export async function getMealSuggestions(forceRefresh: boolean = false, cuisinePreferences: string[] = []): Promise<MealSuggestion[]> {
  console.log('🍳 getMealSuggestions called with:', { forceRefresh, cuisinePreferences });
  try {
    console.log('Loading meal suggestions (forceRefresh:', forceRefresh, ', cuisines:', cuisinePreferences.join(', '), ')');

    // Get time of day
    const timeOfDay = getTimeOfDay();

    // Generate a unique cache ID based on time of day and cuisine preferences
    const uniqueCacheId = `${timeOfDay}_${cuisinePreferences.sort().join('_')}_${Date.now() % 86400000}`; // Include time of day in ms

    // Check if we have cached suggestions that match our criteria and are less than 10 minutes old
    // Only use cache if not forcing refresh
    const now = Date.now();

    // Sort cuisine preferences for consistent comparison
    const sortedCuisinePrefs = [...cuisinePreferences].sort();

    // Check if cuisine preferences match the cache
    const cuisinePrefsMatch = mealSuggestionsCache &&
      JSON.stringify(mealSuggestionsCache.cuisinePreferences?.sort() || []) === JSON.stringify(sortedCuisinePrefs);

    console.log('🔍 Cache check:', {
      forceRefresh,
      timeOfDayMatch: mealSuggestionsCache?.timeOfDay === timeOfDay,
      cuisinePrefsMatch,
      cacheAge: mealSuggestionsCache ? (now - mealSuggestionsCache.timestamp) / 1000 : 'no cache',
      cacheFresh: mealSuggestionsCache ? (now - mealSuggestionsCache.timestamp < 10 * 60 * 1000) : false
    });

    if (!forceRefresh &&
        mealSuggestionsCache &&
        mealSuggestionsCache.timeOfDay === timeOfDay &&
        cuisinePrefsMatch &&
        (now - mealSuggestionsCache.timestamp < 10 * 60 * 1000)) { // 10 minutes cache time
      console.log('✅ Using cached meal suggestions for', timeOfDay, 'with cuisines:', cuisinePreferences.join(', '));
      return mealSuggestionsCache.suggestions;
    }

    // Force refresh if user has selected cuisine preferences and they don't match the cache
    if (cuisinePreferences.length > 0 &&
        mealSuggestionsCache &&
        !cuisinePrefsMatch) {
      console.log('🔄 Cuisine preferences changed, forcing refresh of meal suggestions');
      console.log('   Cache cuisines:', mealSuggestionsCache.cuisinePreferences || []);
      console.log('   New cuisines:', cuisinePreferences);
      forceRefresh = true; // Force refresh when cuisine preferences change
    }

    // Get nutrition targets and consumed nutrition
    const targets = await getNutritionTargets();
    console.log('Using cached nutrition targets');
    const consumed = await getTodayNutrition();

    // Calculate remaining macros
    const remaining = {
      calories: Math.max(0, targets.calories - consumed.calories),
      protein: Math.max(0, targets.protein - consumed.protein),
      carbs: Math.max(0, targets.carbs - consumed.carbs),
      fat: Math.max(0, targets.fat - consumed.fat)
    };

    // Get weather data
    const weather = await getCurrentWeather();
    console.log('Using cached weather data:', weather?.location);

    // Get dietary preferences
    const profile = await getNutritionProfile();

    // Always use the LLM (Groq) API for meal suggestions
    let suggestions: MealSuggestion[] = [];

    try {
      console.log('Using Groq API for meal suggestions with cuisines:', cuisinePreferences.join(', '));

      // Combine user's dietary preferences with cuisine preferences
      const allPreferences = [...profile.dietaryPreferences];

      // Add cuisine preferences if specified
      if (cuisinePreferences.length > 0) {
        cuisinePreferences.forEach(cuisine => {
          if (!allPreferences.includes(cuisine)) {
            allPreferences.push(cuisine);
          }
        });
      }

      // Make multiple attempts if needed
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          attempts++;
          console.log(`Attempt ${attempts}/${maxAttempts} to get meal suggestions from Groq API`);

          // Add a small delay between retries
          if (attempts > 1) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
          }

          suggestions = await getLLMMealSuggestions(remaining, timeOfDay, weather, allPreferences, cuisinePreferences);

          // If we got suggestions, break out of the retry loop
          if (suggestions && suggestions.length > 0) {
            console.log(`Successfully got ${suggestions.length} meal suggestions from Groq API on attempt ${attempts}`);
            break;
          }
        } catch (attemptError) {
          console.error(`Error on attempt ${attempts}/${maxAttempts}:`, attemptError);

          // If this is the last attempt, rethrow the error
          if (attempts >= maxAttempts) {
            throw attemptError;
          }
        }
      }

      // If we still don't have suggestions after all attempts, throw an error
      if (!suggestions || suggestions.length === 0) {
        throw new Error('Failed to get meal suggestions from Groq API after multiple attempts');
      }
    } catch (error) {
      console.error('All attempts to get meal suggestions from Groq API failed:', error);

      // Instead of falling back to mock data, throw an error to the UI
      // This will show an error message to the user
      throw new Error('Unable to generate meal suggestions. Please try again later.');
    }

    // Cache the suggestions with the unique cache ID and cuisine preferences
    mealSuggestionsCache = {
      suggestions,
      timestamp: now,
      timeOfDay,
      cuisinePreferences,
      cacheId: uniqueCacheId
    };

    console.log('Loaded', suggestions.length, 'meal suggestions');
    return suggestions;
  } catch (error) {
    console.error('Error generating meal suggestions:', error);

    // Instead of falling back to mock data, propagate the error to the UI
    // This will show an error message to the user and allow them to retry
    throw new Error('Unable to generate meal suggestions. Please try again later.');
  }
}

// Generate meal suggestions based on parameters
function generateMealSuggestions(
  remaining: {calories: number, protein: number, carbs: number, fat: number},
  timeOfDay: 'breakfast' | 'lunch' | 'dinner' | 'snack',
  weather: WeatherData | null,
  dietaryPreferences: string[]
): MealSuggestion[] {
  // Create meal suggestions based on time of day and weather
  const suggestions: MealSuggestion[] = [];

  // Determine if it's hot or cold using Fahrenheit
  const isHot = weather && weather.temperatureF > 77; // Above 77°F (25°C) is considered hot
  const isCold = weather && weather.temperatureF < 50; // Below 50°F (10°C) is considered cold

  // Adjust meal suggestions based on time of day
  if (timeOfDay === 'breakfast') {
    // Breakfast suggestions
    if (isHot) {
      suggestions.push({
        title: 'Refreshing Smoothie Bowl',
        description: 'Blend frozen berries, banana, and Greek yogurt. Top with granola and fresh fruit.',
        calories: 350,
        protein: 20,
        carbs: 45,
        fat: 10,
        tags: ['breakfast', 'refreshing', 'quick'],
        timeOfDay: 'breakfast',
        weatherAppropriate: true
      });
    } else {
      suggestions.push({
        title: 'Protein Oatmeal',
        description: 'Oats cooked with milk, protein powder, cinnamon, and topped with banana and almond butter.',
        calories: 420,
        protein: 25,
        carbs: 50,
        fat: 15,
        tags: ['breakfast', 'warming', 'high-protein'],
        timeOfDay: 'breakfast',
        weatherAppropriate: true
      });
    }

    // Add a standard option regardless of weather
    suggestions.push({
      title: 'Veggie Egg White Omelet',
      description: 'Egg whites with spinach, bell peppers, onions, and a sprinkle of feta cheese.',
      calories: 280,
      protein: 30,
      carbs: 10,
      fat: 12,
      tags: ['breakfast', 'high-protein', 'low-carb'],
      timeOfDay: 'breakfast',
      weatherAppropriate: true
    });
  } else if (timeOfDay === 'lunch') {
    // Lunch suggestions
    if (isHot) {
      suggestions.push({
        title: 'Mediterranean Salad',
        description: 'Mixed greens, cucumber, cherry tomatoes, olives, feta cheese, and grilled chicken with olive oil and lemon dressing.',
        calories: 450,
        protein: 35,
        carbs: 20,
        fat: 25,
        tags: ['lunch', 'refreshing', 'high-protein'],
        timeOfDay: 'lunch',
        weatherAppropriate: true
      });
    } else if (isCold) {
      suggestions.push({
        title: 'Hearty Vegetable Soup',
        description: 'Warm soup with mixed vegetables, beans, and quinoa for protein and fiber.',
        calories: 380,
        protein: 18,
        carbs: 45,
        fat: 12,
        tags: ['lunch', 'warming', 'comfort'],
        timeOfDay: 'lunch',
        weatherAppropriate: true
      });
    } else {
      suggestions.push({
        title: 'Turkey & Avocado Wrap',
        description: 'Whole grain wrap with sliced turkey, avocado, lettuce, tomato, and mustard.',
        calories: 420,
        protein: 30,
        carbs: 35,
        fat: 18,
        tags: ['lunch', 'balanced', 'portable'],
        timeOfDay: 'lunch',
        weatherAppropriate: true
      });
    }
  } else if (timeOfDay === 'dinner') {
    // Dinner suggestions
    if (isHot) {
      suggestions.push({
        title: 'Grilled Fish Tacos',
        description: 'Grilled white fish with cabbage slaw, avocado, and lime in corn tortillas.',
        calories: 480,
        protein: 35,
        carbs: 40,
        fat: 20,
        tags: ['dinner', 'light', 'high-protein'],
        timeOfDay: 'dinner',
        weatherAppropriate: true
      });
    } else if (isCold) {
      suggestions.push({
        title: 'Slow Cooker Chicken Stew',
        description: 'Hearty stew with chicken, root vegetables, and herbs in a rich broth.',
        calories: 520,
        protein: 40,
        carbs: 35,
        fat: 22,
        tags: ['dinner', 'warming', 'comfort'],
        timeOfDay: 'dinner',
        weatherAppropriate: true
      });
    } else {
      suggestions.push({
        title: 'Baked Salmon with Roasted Vegetables',
        description: 'Herb-crusted salmon fillet with roasted Brussels sprouts and sweet potatoes.',
        calories: 490,
        protein: 38,
        carbs: 30,
        fat: 25,
        tags: ['dinner', 'balanced', 'high-protein'],
        timeOfDay: 'dinner',
        weatherAppropriate: true
      });
    }
  } else {
    // Snack suggestions
    if (isHot) {
      suggestions.push({
        title: 'Frozen Greek Yogurt with Berries',
        description: 'Greek yogurt topped with frozen mixed berries and a drizzle of honey.',
        calories: 180,
        protein: 15,
        carbs: 20,
        fat: 5,
        tags: ['snack', 'refreshing', 'quick'],
        timeOfDay: 'snack',
        weatherAppropriate: true
      });
    } else {
      suggestions.push({
        title: 'Apple with Almond Butter',
        description: 'Sliced apple with a tablespoon of almond butter for dipping.',
        calories: 200,
        protein: 5,
        carbs: 25,
        fat: 10,
        tags: ['snack', 'balanced', 'quick'],
        timeOfDay: 'snack',
        weatherAppropriate: true
      });
    }
  }

  // Filter out suggestions that don't match dietary preferences
  const filteredSuggestions = suggestions.filter(suggestion => {
    // If no dietary preferences, include all suggestions
    if (dietaryPreferences.length === 0) {
      return true;
    }

    // Check if any dietary preference excludes this meal
    for (const preference of dietaryPreferences) {
      const lowerPref = preference.toLowerCase();

      // Check for common dietary restrictions
      if (lowerPref.includes('vegan') &&
          (suggestion.description.toLowerCase().includes('meat') ||
           suggestion.description.toLowerCase().includes('chicken') ||
           suggestion.description.toLowerCase().includes('fish') ||
           suggestion.description.toLowerCase().includes('egg') ||
           suggestion.description.toLowerCase().includes('dairy') ||
           suggestion.description.toLowerCase().includes('yogurt') ||
           suggestion.description.toLowerCase().includes('cheese'))) {
        return false;
      }

      if (lowerPref.includes('vegetarian') &&
          (suggestion.description.toLowerCase().includes('meat') ||
           suggestion.description.toLowerCase().includes('chicken') ||
           suggestion.description.toLowerCase().includes('fish'))) {
        return false;
      }

      // Check for specific allergies
      if (lowerPref.includes('gluten') &&
          (suggestion.description.toLowerCase().includes('bread') ||
           suggestion.description.toLowerCase().includes('pasta') ||
           suggestion.description.toLowerCase().includes('wheat') ||
           suggestion.description.toLowerCase().includes('oats'))) {
        return false;
      }

      if (lowerPref.includes('dairy') &&
          (suggestion.description.toLowerCase().includes('milk') ||
           suggestion.description.toLowerCase().includes('cheese') ||
           suggestion.description.toLowerCase().includes('yogurt'))) {
        return false;
      }

      if (lowerPref.includes('nut') &&
          (suggestion.description.toLowerCase().includes('nut') ||
           suggestion.description.toLowerCase().includes('almond') ||
           suggestion.description.toLowerCase().includes('peanut'))) {
        return false;
      }
    }

    return true;
  });

  // If no suggestions match dietary preferences, return a generic suggestion
  if (filteredSuggestions.length === 0) {
    return [{
      title: 'Custom Meal',
      description: 'Based on your dietary preferences, we recommend consulting with Lotus for a personalized meal suggestion.',
      calories: Math.min(remaining.calories, 500),
      protein: Math.min(remaining.protein, 30),
      carbs: Math.min(remaining.carbs, 40),
      fat: Math.min(remaining.fat, 20),
      tags: ['custom'],
      timeOfDay,
      weatherAppropriate: true
    }];
  }

  return filteredSuggestions;
}

// API request manager is imported at the top of the file

// Generate meal suggestions using the LLM
async function getLLMMealSuggestions(
  remaining: {calories: number, protein: number, carbs: number, fat: number},
  timeOfDay: 'breakfast' | 'lunch' | 'dinner' | 'snack',
  weather: WeatherData | null,
  dietaryPreferences: string[],
  cuisinePreferences: string[] = []
): Promise<MealSuggestion[]> {
  try {
    console.log(`Generating meal suggestions for ${timeOfDay} with LLM`);

    // Get the user ID
    const userId = await getUserId();
    if (!userId) {
      console.warn('User ID not available, using fallback suggestions');
      return generateFallbackSuggestions(timeOfDay, weather, remaining);
    }

    // Create a system prompt for meal suggestions
    const systemPrompt = `You are a nutrition expert assistant that creates personalized meal suggestions.
    When given user information, respond ONLY with a JSON array containing 3 meal suggestions.

    The JSON MUST have this EXACT format with NO DEVIATIONS:
    [
      {
        "title": "Meal name",
        "description": "Brief description of the meal",
        "calories": 500,
        "protein": 30,
        "carbs": 40,
        "fat": 20,
        "tags": ["breakfast", "high-protein", "quick", "chicken", "mediterranean", "healthy"],
        "timeOfDay": "breakfast",
        "weatherAppropriate": true,
        "ingredients": [
          "1 cup ingredient one",
          "2 tbsp ingredient two",
          "1/4 tsp ingredient three"
        ],
        "instructions": [
          "Step 1: Do this first",
          "Step 2: Then do this",
          "Step 3: Finally, do this"
        ],
        "prepTime": "10 minutes",
        "cookTime": "15 minutes",
        "servings": 2,
        "cuisine": "Mediterranean"
      }
    ]

    CRITICAL REQUIREMENTS:
    1. ONLY return the JSON array, no other text, explanation, or formatting
    2. Keep the JSON structure EXACTLY as shown above
    3. All numeric values MUST be numbers (not strings)
    4. Generate EXACTLY 3 meal suggestions
    5. Start your response with [ and end with ]
    6. Use proper JSON formatting with double quotes only
    7. No trailing commas in the JSON
    8. Make sure the meals are appropriate for the time of day (${timeOfDay})
    9. Consider the current weather: ${weather ? `${Math.round(weather.temperatureF || 70)}°F, ${weather.condition || 'Clear'}` : 'Unknown'}
    10. Respect dietary preferences: ${dietaryPreferences.length > 0 ? dietaryPreferences.join(', ') : 'None specified'}
    11. Target these remaining macros: ${remaining.calories} calories, ${remaining.protein}g protein, ${remaining.carbs}g carbs, ${remaining.fat}g fat
    12. Make suggestions HIGHLY creative, varied, and realistic - BE CREATIVE and DIVERSE in your suggestions
    13. Include DETAILED ingredients with measurements and CLEAR step-by-step instructions
    14. Keep descriptions under 100 words
    15. IMPORTANT: Each time you're called, generate COMPLETELY DIFFERENT meal suggestions than before
    16. Use a wide variety of ingredients, cuisines, and cooking methods
    ${cuisinePreferences.length > 0 ? `17. IMPORTANT: Focus specifically on these cuisines: ${cuisinePreferences.join(', ')}` : '17. Include at least one internationally-inspired dish in your suggestions'}
    18. Provide realistic prep and cook times
    19. Make sure each meal has at least 5 ingredients and 3 instruction steps
    20. IMPORTANT: Include COMPREHENSIVE tags for each meal (at least 6 tags per meal) that include:
       - Meal type (breakfast, lunch, dinner, snack, dessert)
       - Main protein source (chicken, beef, fish, tofu, beans, etc.)
       - Cuisine type (italian, mexican, asian, etc.)
       - Diet type if applicable (keto, paleo, vegetarian, vegan, gluten-free, etc.)
       - Cooking method (baked, grilled, fried, etc.)
       - Characteristics (quick, healthy, comfort, spicy, etc.)
    21. Timestamp: ${Date.now()}_${Math.random().toString(36).substring(2, 10)} - use this to ensure variety
    22. CRITICAL: Your response must be valid JSON that can be parsed by JSON.parse()`;

    // Prepare the messages for the API
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: `Generate 3 COMPLETELY DIFFERENT and CREATIVE meal suggestions for ${timeOfDay} that are appropriate for ${weather ? `${Math.round(weather.temperatureF || 70)}°F, ${weather.condition || 'Clear'} weather` : 'the current weather'}. ${dietaryPreferences.length > 0 ? `Consider these dietary preferences: ${dietaryPreferences.join(', ')}.` : ''} Target these remaining macros: ${remaining.calories} calories, ${remaining.protein}g protein, ${remaining.carbs}g carbs, ${remaining.fat}g fat.

IMPORTANT: Be extremely creative and diverse in your suggestions. ${cuisinePreferences.length > 0 ? `Focus specifically on these cuisines: ${cuisinePreferences.join(', ')}.` : 'Each meal should be from a completely different cuisine.'} Include detailed ingredients with measurements and clear step-by-step instructions.

Make sure to include:
1. A dish inspired by ${['Asian', 'Mediterranean', 'Latin American', 'African', 'European', 'Middle Eastern'][Math.floor(Math.random() * 6)]} cuisine
2. A dish that is ${['quick and easy', 'gourmet', 'comfort food', 'healthy', 'fusion', 'seasonal'][Math.floor(Math.random() * 6)]}
3. A dish that uses ${['plant-based proteins', 'lean meats', 'seafood', 'whole grains', 'superfoods', 'fermented foods'][Math.floor(Math.random() * 6)]}

Randomization factors:
- Current time: ${new Date().toISOString()}
- Random seed: ${Math.random().toString(36).substring(2, 10)}
- Unique identifier: ${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
      }
    ];

    // Generate a temporary conversation ID for this meal request with added randomness
    const randomSeed = Math.random().toString(36).substring(2, 10);
    const tempConversationId = `meal_suggestions_${Date.now()}_${randomSeed}`;

    // Create fallback suggestions in advance
    const fallbackSuggestions = generateFallbackSuggestions(timeOfDay, weather, remaining);

    // Make the API call using the new request manager with better error handling
    console.log('[getLLMMealSuggestions] Making API request with improved handling');

    // Use postRequest with fallback data and caching
    const response = await postRequest(
      '/chat',
      {
        messages,
        userId,
        conversationId: tempConversationId,
        temperature: 0.9, // Higher temperature for more variety
        requestType: 'meal_suggestions',
        responseFormat: 'json', // Request JSON format explicitly
        maxTokens: 2000 // Ensure enough tokens for complete response
      },
      {
        // If the request fails, use fallback suggestions
        fallbackData: { content: JSON.stringify(fallbackSuggestions) },
        // Use a longer cache TTL for meal suggestions
        cacheTTL: 15 * 60 * 1000, // 15 minutes
        // Add a delay to prevent rate limiting
        delayMs: 500,
        // Use more retries with longer delays
        retries: 3,
        retryDelay: 2000,
        // Use a custom log tag
        logTag: '[Meal Suggestions]',
        // Don't cache errors
        useCache: true
      }
    );

    // Check if we got a response or if we're using fallback data
    if (response) {
      console.log('Received response from chat API, extracting meal suggestions');

      // Check if backend already parsed meal suggestions
      if ((response as any).isMealSuggestionJson && (response as any).detectedMeal) {
        console.log('Backend already parsed meal suggestions, using detectedMeal');
        const mealSuggestions = Array.isArray((response as any).detectedMeal) ? (response as any).detectedMeal : [(response as any).detectedMeal];

        // Validate and format the suggestions
        const validatedSuggestions = mealSuggestions.map(suggestion => ({
          title: suggestion.title || 'Untitled Meal',
          description: suggestion.description || 'No description provided',
          calories: typeof suggestion.calories === 'number' ? suggestion.calories :
                   (suggestion.nutritionInfo?.calories || 0),
          protein: typeof suggestion.protein === 'number' ? suggestion.protein :
                  (suggestion.nutritionInfo?.protein || 0),
          carbs: typeof suggestion.carbs === 'number' ? suggestion.carbs :
                (suggestion.nutritionInfo?.carbs || 0),
          fat: typeof suggestion.fat === 'number' ? suggestion.fat :
              (suggestion.nutritionInfo?.fat || 0),
          tags: Array.isArray(suggestion.tags) ? suggestion.tags : [],
          timeOfDay: suggestion.timeOfDay || timeOfDay,
          weatherAppropriate: suggestion.weatherAppropriate !== false,
          ingredients: Array.isArray(suggestion.ingredients) ? suggestion.ingredients : [],
          instructions: Array.isArray(suggestion.instructions) ? suggestion.instructions : [],
          prepTime: suggestion.prepTime || '15 minutes',
          cookTime: suggestion.cookTime || '20 minutes',
          servings: typeof suggestion.servings === 'number' ? suggestion.servings : 2,
          cuisine: suggestion.cuisine || 'Mixed'
        }));

        console.log(`Successfully processed ${validatedSuggestions.length} meal suggestions from backend`);
        return validatedSuggestions;
      }

      // Fallback to parsing content if backend didn't parse it
      if (!response.content) {
        throw new Error('No content in response');
      }

      const content = response.content;

      try {
        // If the response is already our fallback suggestions (pre-formatted JSON), return it directly
        if (Array.isArray(content) && content.length > 0 && 'title' in content[0]) {
          console.log('Using pre-formatted fallback suggestions');
          return content as MealSuggestion[];
        }

        // Log the first part of the content for debugging
        console.log('Response content preview:',
          typeof content === 'string'
            ? content.substring(0, 100).replace(/\n/g, ' ')
            : JSON.stringify(content).substring(0, 100)
        );

        // First, try to clean up the response to handle common issues
        let cleanedContent = typeof content === 'string' ? content : JSON.stringify(content);
        
        // Log the raw content for debugging
        console.log('Raw content (first 200 chars):', cleanedContent.substring(0, 200));
        
        // Remove any markdown code block markers
        cleanedContent = cleanedContent.replace(/```json/g, '').replace(/```/g, '');
        
        // Remove any text before the first '[' and after the last ']'
        const firstBracketIndex = cleanedContent.indexOf('[');
        const lastBracketIndex = cleanedContent.lastIndexOf(']');
        
        if (firstBracketIndex !== -1 && lastBracketIndex !== -1 && lastBracketIndex > firstBracketIndex) {
          cleanedContent = cleanedContent.substring(firstBracketIndex, lastBracketIndex + 1);
          console.log('Extracted JSON array from content');
        }
        
        // Enhanced JSON cleaning for better parsing
        cleanedContent = cleanedContent
          // Remove any text before the first '[' and after the last ']'
          .replace(/^[^[]*/, '')
          .replace(/[^\]]*$/, '')
          // Handle escaped forward slashes
          .replace(/\\\/|\/\//g, '/')
          // Normalize whitespace and newlines
          .replace(/\s*\n\s*/g, ' ')
          .replace(/\s+/g, ' ')
          // Fix trailing commas
          .replace(/,(\s*[\]}])/g, '$1')
          // Fix missing quotes around property names (more precise)
          .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)(\s*:)/g, '$1"$2"$3')
          // Fix single quotes to double quotes
          .replace(/'/g, '"')
          // Fix unescaped quotes in strings
          .replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1$2$3":')
          // Clean up spaces around JSON punctuation
          .replace(/\s*([{}[\],:])\s*/g, '$1')
          // Add back necessary spaces after colons and commas
          .replace(/([,:])([^\s])/g, '$1 $2')
          // Fix any remaining formatting issues
          .replace(/\s*\n\s*/g, '')
          .trim();
        
        console.log('Cleaned content (first 200 chars):', cleanedContent.substring(0, 200));

        // Try multiple parsing approaches with enhanced error handling
        const parseAttempts = [
          // Attempt 1: Direct parsing
          () => JSON.parse(cleanedContent),

          // Attempt 2: Extract array content first
          () => {
            const arrayMatch = cleanedContent.match(/\[[\s\S]*\]/);
            if (arrayMatch) {
              return JSON.parse(arrayMatch[0]);
            }
            throw new Error('No array found');
          },

          // Attempt 3: Add brackets if missing
          () => {
            let bracketedContent = cleanedContent;
            if (!bracketedContent.startsWith('[')) bracketedContent = '[' + bracketedContent;
            if (!bracketedContent.endsWith(']')) bracketedContent = bracketedContent + ']';
            return JSON.parse(bracketedContent);
          },

          // Attempt 4: Fix common JSON issues
          () => {
            let fixedContent = cleanedContent
              // Fix unquoted property names more carefully
              .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)(\s*):/g, '$1"$2"$3:')
              // Fix trailing commas
              .replace(/,(\s*[}\]])/g, '$1')
              // Fix single quotes to double quotes (but not in contractions)
              .replace(/(?<!\\)'/g, '"')
              // Fix escaped quotes in values
              .replace(/\\"/g, '\\"')
              // Fix newlines in strings
              .replace(/"\s*\n\s*"/g, '" "')
              // Remove any remaining control characters
              .replace(/[\x00-\x1F\x7F]/g, '');
            return JSON.parse(fixedContent);
          },

          // Attempt 5: Manual object extraction
          () => {
            const objects = [];
            const objectRegex = /\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g;
            let match;
            while ((match = objectRegex.exec(cleanedContent)) !== null) {
              try {
                const obj = JSON.parse(match[0]);
                objects.push(obj);
              } catch (e) {
                // Skip invalid objects
              }
            }
            if (objects.length > 0) {
              return objects;
            }
            throw new Error('No valid objects found');
          }
        ];

        for (let i = 0; i < parseAttempts.length; i++) {
          try {
            const parsedSuggestions = parseAttempts[i]();

            // Validate the structure
            if (Array.isArray(parsedSuggestions) && parsedSuggestions.length > 0) {
              console.log(`Successfully parsed ${parsedSuggestions.length} meal suggestions (attempt ${i + 1})`);

              // Ensure all required fields are present
              const validatedSuggestions = parsedSuggestions.map(suggestion => ({
                title: suggestion.title || 'Untitled Meal',
                description: suggestion.description || 'No description provided',
                calories: typeof suggestion.calories === 'number' ? suggestion.calories : 0,
                protein: typeof suggestion.protein === 'number' ? suggestion.protein : 0,
                carbs: typeof suggestion.carbs === 'number' ? suggestion.carbs : 0,
                fat: typeof suggestion.fat === 'number' ? suggestion.fat : 0,
                tags: Array.isArray(suggestion.tags) ? suggestion.tags : [],
                timeOfDay: suggestion.timeOfDay || timeOfDay,
                weatherAppropriate: suggestion.weatherAppropriate !== false,
                ingredients: Array.isArray(suggestion.ingredients) ? suggestion.ingredients : [],
                instructions: Array.isArray(suggestion.instructions) ? suggestion.instructions : [],
                prepTime: suggestion.prepTime || '15 minutes',
                cookTime: suggestion.cookTime || '20 minutes',
                servings: typeof suggestion.servings === 'number' ? suggestion.servings : 2,
                cuisine: suggestion.cuisine || 'Mixed'
              }));

              return validatedSuggestions;
            }
          } catch (parseError) {
            console.log(`Parse attempt ${i + 1} failed:`, parseError instanceof Error ? parseError.message : 'Unknown error');
            continue;
          }
        }

        // If direct parsing fails, try regex extraction
        const jsonRegex = /\[\s*\{[\s\S]*?\}\s*\]/g;
        const jsonMatches = cleanedContent.match(jsonRegex);
        
        // If regex extraction fails, try a more aggressive approach
        if (!jsonMatches) {
          console.log('Standard regex extraction failed, trying more aggressive approach');
          
          // Try to manually construct a valid JSON array
          try {
            // Find all objects in the content
            const objectRegex = /\{\s*"[^"]+"\s*:/g;
            const objectMatches = cleanedContent.match(objectRegex);
            
            if (objectMatches && objectMatches.length > 0) {
              console.log(`Found ${objectMatches.length} potential JSON objects`);
              
              // Extract each object and try to parse it
              const objects = [];
              let startIndex = 0;
              
              for (const match of objectMatches) {
                const objStartIndex = cleanedContent.indexOf(match, startIndex);
                if (objStartIndex !== -1) {
                  // Find the matching closing brace
                  let openBraces = 1;
                  let objEndIndex = objStartIndex + match.length;
                  
                  while (openBraces > 0 && objEndIndex < cleanedContent.length) {
                    if (cleanedContent[objEndIndex] === '{') openBraces++;
                    if (cleanedContent[objEndIndex] === '}') openBraces--;
                    objEndIndex++;
                  }
                  
                  if (openBraces === 0) {
                    const objJson = cleanedContent.substring(objStartIndex, objEndIndex);
                    try {
                      const obj = JSON.parse(objJson);
                      objects.push(obj);
                      startIndex = objEndIndex;
                    } catch (objParseError) {
                      console.log(`Failed to parse object: ${objJson.substring(0, 50)}...`);
                    }
                  }
                }
              }
              
              if (objects.length > 0) {
                console.log(`Successfully extracted ${objects.length} objects manually`);
                return objects.map(obj => ({
                  title: obj.title || 'Untitled Meal',
                  description: obj.description || 'No description provided',
                  calories: typeof obj.calories === 'number' ? obj.calories : 0,
                  protein: typeof obj.protein === 'number' ? obj.protein : 0,
                  carbs: typeof obj.carbs === 'number' ? obj.carbs : 0,
                  fat: typeof obj.fat === 'number' ? obj.fat : 0,
                  tags: Array.isArray(obj.tags) ? obj.tags : [],
                  timeOfDay: obj.timeOfDay || timeOfDay,
                  weatherAppropriate: obj.weatherAppropriate !== false,
                  ingredients: Array.isArray(obj.ingredients) ? obj.ingredients : [],
                  instructions: Array.isArray(obj.instructions) ? obj.instructions : [],
                  prepTime: obj.prepTime || '15 minutes',
                  cookTime: obj.cookTime || '20 minutes',
                  servings: typeof obj.servings === 'number' ? obj.servings : 2,
                  cuisine: obj.cuisine || 'Mixed'
                }));
              }
            }
          } catch (manualExtractionError) {
            console.error('Manual extraction failed:', manualExtractionError);
          }
        }

        if (jsonMatches && jsonMatches.length > 0) {
          console.log('Found JSON array using regex');

          // Try to parse the first match
          try {
            const parsedSuggestions = JSON.parse(jsonMatches[0]);

            // Validate the structure
            if (Array.isArray(parsedSuggestions) && parsedSuggestions.length > 0) {
              console.log(`Successfully extracted ${parsedSuggestions.length} meal suggestions via regex`);

              // Ensure all required fields are present
              const validatedSuggestions = parsedSuggestions.map(suggestion => ({
                title: suggestion.title || 'Untitled Meal',
                description: suggestion.description || 'No description provided',
                calories: typeof suggestion.calories === 'number' ? suggestion.calories : 0,
                protein: typeof suggestion.protein === 'number' ? suggestion.protein : 0,
                carbs: typeof suggestion.carbs === 'number' ? suggestion.carbs : 0,
                fat: typeof suggestion.fat === 'number' ? suggestion.fat : 0,
                tags: Array.isArray(suggestion.tags) ? suggestion.tags : [],
                timeOfDay: suggestion.timeOfDay || timeOfDay,
                weatherAppropriate: suggestion.weatherAppropriate !== false,
                ingredients: Array.isArray(suggestion.ingredients) ? suggestion.ingredients : [],
                instructions: Array.isArray(suggestion.instructions) ? suggestion.instructions : [],
                prepTime: suggestion.prepTime || '15 minutes',
                cookTime: suggestion.cookTime || '20 minutes',
                servings: typeof suggestion.servings === 'number' ? suggestion.servings : 2,
                cuisine: suggestion.cuisine || 'Mixed'
              }));

              return validatedSuggestions;
            }
          } catch (regexParseError) {
            console.log('Regex extraction parsing failed:', regexParseError instanceof Error ? regexParseError.message : 'Unknown error');
          }
        }

        // If all parsing attempts fail, throw an error
        // Final attempt: Try to extract individual meal objects manually
        console.log('All standard parsing attempts failed, trying manual extraction...');
        try {
          const manuallyExtracted = extractMealObjectsManually(content);
          if (manuallyExtracted.length > 0) {
            console.log(`Successfully extracted ${manuallyExtracted.length} meals manually`);
            return manuallyExtracted;
          }
        } catch (manualError) {
          console.log('Manual extraction also failed:', manualError);
        }

        console.log('All parsing attempts failed, unable to parse Groq API response');
        throw new Error('Failed to parse meal suggestions from Groq API');
      } catch (parseError) {
        console.error('Error parsing meal suggestions:', parseError);
        throw new Error('Failed to parse meal suggestions from Groq API');
      }
    }

    // If we couldn't get a valid response, throw an error
    throw new Error('Failed to get valid meal suggestions from Groq API');
  } catch (error) {
    console.error('Error getting meal suggestions from Groq API:', error);
    // Propagate the error instead of falling back to mock data
    throw new Error('Failed to generate meal suggestions. Please try again later.');
  }
}

// Helper function to manually extract meal objects from malformed JSON
const extractMealObjectsManually = (content: string): MealSuggestion[] => {
  const meals: MealSuggestion[] = [];

  try {
    // Look for title patterns
    const titleMatches = content.match(/"title":\s*"([^"]+)"/g);
    const descriptionMatches = content.match(/"description":\s*"([^"]+)"/g);
    const calorieMatches = content.match(/"calories":\s*(\d+)/g);
    const proteinMatches = content.match(/"protein":\s*(\d+)/g);
    const carbMatches = content.match(/"carbs?":\s*(\d+)/g);
    const fatMatches = content.match(/"fat":\s*(\d+)/g);

    if (titleMatches && titleMatches.length > 0) {
      for (let i = 0; i < titleMatches.length; i++) {
        const title = titleMatches[i].match(/"title":\s*"([^"]+)"/)?.[1] || 'Untitled Meal';
        const description = descriptionMatches?.[i]?.match(/"description":\s*"([^"]+)"/)?.[1] || 'No description';
        const calories = parseInt(calorieMatches?.[i]?.match(/"calories":\s*(\d+)/)?.[1] || '0');
        const protein = parseInt(proteinMatches?.[i]?.match(/"protein":\s*(\d+)/)?.[1] || '0');
        const carbs = parseInt(carbMatches?.[i]?.match(/"carbs?":\s*(\d+)/)?.[1] || '0');
        const fat = parseInt(fatMatches?.[i]?.match(/"fat":\s*(\d+)/)?.[1] || '0');

        meals.push({
          title,
          description,
          calories,
          protein,
          carbs,
          fat,
          tags: [],
          timeOfDay: 'snack',
          weatherAppropriate: true,
          ingredients: [],
          instructions: [],
          prepTime: '15 minutes',
          cookTime: '20 minutes',
          servings: 2,
          cuisine: 'Mixed'
        });
      }
    }
  } catch (error) {
    console.error('Manual extraction failed:', error);
  }

  return meals;
};

// Generate fallback meal suggestions when LLM parsing fails
function generateFallbackSuggestions(
  timeOfDay: 'breakfast' | 'lunch' | 'dinner' | 'snack',
  weather: WeatherData | null,
  remaining: {calories: number, protein: number, carbs: number, fat: number}
): MealSuggestion[] {
  console.log(`Generating fallback suggestions for ${timeOfDay}`);

  // Create appropriate suggestions based on time of day
  const suggestions: MealSuggestion[] = [];

  // Determine if it's hot or cold (for future use in meal selection)
  const isHot = weather && weather.temperatureF !== null && weather.temperatureF > 77;
  const isCold = weather && weather.temperatureF !== null && weather.temperatureF < 50;

  // Weather-appropriate meal adjustments could use isHot/isCold variables

  if (timeOfDay === 'breakfast') {
    suggestions.push({
      title: "Protein-Packed Breakfast Bowl",
      description: "A nutritious bowl with eggs, vegetables, and whole grains to start your day right.",
      calories: Math.min(remaining.calories * 0.3, 450),
      protein: Math.min(remaining.protein * 0.3, 30),
      carbs: Math.min(remaining.carbs * 0.3, 40),
      fat: Math.min(remaining.fat * 0.3, 15),
      tags: ["breakfast", "high-protein", "healthy", "eggs", "quick", "balanced"],
      timeOfDay: "breakfast",
      weatherAppropriate: true,
      ingredients: [
        "2 large eggs",
        "1/2 cup cooked quinoa",
        "1/4 avocado, sliced",
        "1/2 cup spinach, wilted",
        "1 tbsp olive oil",
        "Salt and pepper to taste"
      ],
      instructions: [
        "Cook quinoa according to package instructions",
        "In a pan, scramble eggs with olive oil",
        "Assemble bowl with quinoa as base, topped with eggs, spinach, and avocado",
        "Season with salt and pepper"
      ],
      prepTime: "5 minutes",
      cookTime: "10 minutes",
      servings: 1,
      cuisine: "American"
    });

    suggestions.push({
      title: isHot ? "Refreshing Fruit Smoothie Bowl" : "Warming Oatmeal with Berries",
      description: isHot ?
        "A cooling smoothie bowl topped with fresh fruits and granola." :
        "Hearty oatmeal with warm berries and a touch of cinnamon.",
      calories: Math.min(remaining.calories * 0.25, 350),
      protein: Math.min(remaining.protein * 0.2, 15),
      carbs: Math.min(remaining.carbs * 0.4, 50),
      fat: Math.min(remaining.fat * 0.2, 10),
      tags: isHot ?
        ["breakfast", "refreshing", "fruit", "cold", "quick", "vegetarian"] :
        ["breakfast", "warming", "comfort", "hot", "whole-grain", "vegetarian"],
      timeOfDay: "breakfast",
      weatherAppropriate: true,
      ingredients: isHot ?
        [
          "1 frozen banana",
          "1 cup mixed berries",
          "1/2 cup Greek yogurt",
          "1/4 cup almond milk",
          "1 tbsp honey",
          "2 tbsp granola for topping"
        ] :
        [
          "1 cup rolled oats",
          "1 1/2 cups milk of choice",
          "1 cup mixed berries",
          "1 tbsp maple syrup",
          "1/2 tsp cinnamon",
          "1 tbsp chopped nuts"
        ],
      instructions: isHot ?
        [
          "Blend frozen banana, berries, yogurt, and almond milk until smooth",
          "Pour into a bowl",
          "Top with granola and additional fresh fruit",
          "Drizzle with honey"
        ] :
        [
          "Combine oats and milk in a pot over medium heat",
          "Cook for 5 minutes, stirring occasionally",
          "Add berries and cook for another 2 minutes",
          "Serve topped with maple syrup, cinnamon, and nuts"
        ],
      prepTime: "5 minutes",
      cookTime: isHot ? "0 minutes" : "8 minutes",
      servings: 1,
      cuisine: "International"
    });
  }
  else if (timeOfDay === 'lunch') {
    suggestions.push({
      title: "Balanced Protein Salad",
      description: "A satisfying salad with lean protein, fresh vegetables, and a light dressing.",
      calories: Math.min(remaining.calories * 0.35, 500),
      protein: Math.min(remaining.protein * 0.4, 35),
      carbs: Math.min(remaining.carbs * 0.25, 30),
      fat: Math.min(remaining.fat * 0.3, 20),
      tags: ["lunch", "salad", "high-protein", "healthy", "balanced", "quick"],
      timeOfDay: "lunch",
      weatherAppropriate: true,
      ingredients: [
        "4 oz grilled chicken breast",
        "2 cups mixed greens",
        "1/4 cup cherry tomatoes, halved",
        "1/4 cucumber, sliced",
        "1/4 avocado, diced",
        "2 tbsp balsamic vinaigrette",
        "1 tbsp sunflower seeds"
      ],
      instructions: [
        "Season and grill chicken until fully cooked",
        "Chop chicken into bite-sized pieces",
        "Combine all vegetables in a large bowl",
        "Top with chicken and sunflower seeds",
        "Drizzle with dressing and toss gently"
      ],
      prepTime: "10 minutes",
      cookTime: "15 minutes",
      servings: 1,
      cuisine: "Mediterranean"
    });

    suggestions.push({
      title: isHot ? "Chilled Gazpacho with Grilled Shrimp" : "Hearty Vegetable Soup with Beans",
      description: isHot ?
        "A refreshing cold soup perfect for hot days, topped with protein-rich shrimp." :
        "A warming vegetable soup with beans for protein and fiber.",
      calories: Math.min(remaining.calories * 0.3, 400),
      protein: Math.min(remaining.protein * 0.35, 25),
      carbs: Math.min(remaining.carbs * 0.3, 35),
      fat: Math.min(remaining.fat * 0.25, 15),
      tags: isHot ?
        ["lunch", "cold", "soup", "seafood", "refreshing", "spanish"] :
        ["lunch", "hot", "soup", "vegetarian", "warming", "comfort"],
      timeOfDay: "lunch",
      weatherAppropriate: true,
      ingredients: isHot ?
        [
          "2 large tomatoes",
          "1/2 cucumber",
          "1/2 red bell pepper",
          "1 garlic clove",
          "2 tbsp olive oil",
          "1 tbsp red wine vinegar",
          "4 oz shrimp, peeled and deveined",
          "Salt and pepper to taste"
        ] :
        [
          "1 cup mixed vegetables (carrots, celery, onions)",
          "1/2 cup white beans, cooked",
          "2 cups vegetable broth",
          "1 tbsp olive oil",
          "1 tsp dried herbs",
          "1 small potato, diced",
          "Salt and pepper to taste"
        ],
      instructions: isHot ?
        [
          "Blend tomatoes, cucumber, bell pepper, garlic, olive oil, and vinegar until smooth",
          "Season with salt and pepper and chill for at least 30 minutes",
          "Season shrimp and grill for 2 minutes per side",
          "Serve gazpacho cold topped with grilled shrimp"
        ] :
        [
          "Sauté vegetables in olive oil for 5 minutes",
          "Add broth, beans, potato, and herbs",
          "Simmer for 15-20 minutes until vegetables are tender",
          "Season with salt and pepper to taste"
        ],
      prepTime: "15 minutes",
      cookTime: isHot ? "5 minutes" : "25 minutes",
      servings: 1,
      cuisine: isHot ? "Spanish" : "Italian"
    });
  }
  else if (timeOfDay === 'dinner') {
    suggestions.push({
      title: "Balanced Protein Bowl",
      description: "A complete meal with lean protein, complex carbs, and healthy fats.",
      calories: Math.min(remaining.calories * 0.45, 600),
      protein: Math.min(remaining.protein * 0.5, 40),
      carbs: Math.min(remaining.carbs * 0.4, 50),
      fat: Math.min(remaining.fat * 0.4, 20),
      tags: ["dinner", "bowl", "high-protein", "balanced", "healthy", "complete"],
      timeOfDay: "dinner",
      weatherAppropriate: true,
      ingredients: [
        "5 oz salmon fillet",
        "3/4 cup brown rice, cooked",
        "1 cup roasted vegetables (broccoli, bell peppers, onions)",
        "1/2 avocado, sliced",
        "1 tbsp olive oil",
        "1 tsp lemon juice",
        "1 tsp herbs and spices"
      ],
      instructions: [
        "Season salmon with herbs and spices",
        "Cook salmon in olive oil for 4-5 minutes per side",
        "Toss vegetables in olive oil and roast at 400°F for 20 minutes",
        "Assemble bowl with rice, vegetables, salmon, and avocado",
        "Drizzle with lemon juice"
      ],
      prepTime: "10 minutes",
      cookTime: "25 minutes",
      servings: 1,
      cuisine: "Fusion"
    });

    suggestions.push({
      title: isHot ? "Light Summer Pasta Primavera" : "Comforting Beef and Vegetable Stew",
      description: isHot ?
        "A light pasta dish with seasonal vegetables and a touch of lemon." :
        "A hearty beef stew with root vegetables, perfect for cold weather.",
      calories: Math.min(remaining.calories * 0.4, 550),
      protein: Math.min(remaining.protein * 0.45, 35),
      carbs: Math.min(remaining.carbs * 0.45, 60),
      fat: Math.min(remaining.fat * 0.35, 18),
      tags: isHot ?
        ["dinner", "pasta", "light", "vegetarian", "summer", "italian"] :
        ["dinner", "stew", "comfort", "beef", "winter", "hearty"],
      timeOfDay: "dinner",
      weatherAppropriate: true,
      ingredients: isHot ?
        [
          "2 oz whole wheat pasta",
          "1 cup mixed summer vegetables (zucchini, cherry tomatoes, bell peppers)",
          "1 tbsp olive oil",
          "1 clove garlic, minced",
          "1 tbsp grated parmesan cheese",
          "1 tsp lemon zest",
          "Fresh basil leaves",
          "Salt and pepper to taste"
        ] :
        [
          "4 oz lean beef, cubed",
          "1 cup root vegetables (carrots, potatoes, turnips)",
          "1/2 onion, diced",
          "1 cup beef broth",
          "1 tbsp tomato paste",
          "1 bay leaf",
          "1 tsp herbs",
          "Salt and pepper to taste"
        ],
      instructions: isHot ?
        [
          "Cook pasta according to package instructions",
          "Sauté garlic in olive oil for 30 seconds",
          "Add vegetables and cook for 3-4 minutes until tender-crisp",
          "Toss with cooked pasta, parmesan, and lemon zest",
          "Garnish with fresh basil"
        ] :
        [
          "Brown beef cubes in a pot",
          "Add onions and cook until softened",
          "Add vegetables, broth, tomato paste, and seasonings",
          "Simmer covered for 45 minutes until meat is tender",
          "Remove bay leaf before serving"
        ],
      prepTime: "10 minutes",
      cookTime: isHot ? "15 minutes" : "50 minutes",
      servings: 1,
      cuisine: isHot ? "Italian" : "French"
    });
  }
  else { // snack
    suggestions.push({
      title: "Protein-Rich Snack Plate",
      description: "A balanced snack plate with protein, healthy fats, and complex carbs.",
      calories: Math.min(remaining.calories * 0.2, 250),
      protein: Math.min(remaining.protein * 0.15, 15),
      carbs: Math.min(remaining.carbs * 0.15, 20),
      fat: Math.min(remaining.fat * 0.2, 12),
      tags: ["snack", "protein", "balanced", "quick", "no-cook", "healthy"],
      timeOfDay: "snack",
      weatherAppropriate: true,
      ingredients: [
        "2 oz turkey or chicken slices",
        "1 oz cheese cubes",
        "10 whole grain crackers",
        "1/4 cup hummus",
        "1/2 cup sliced vegetables (cucumber, bell peppers, carrots)"
      ],
      instructions: [
        "Arrange all ingredients on a plate",
        "Serve immediately or store in a container for on-the-go"
      ],
      prepTime: "5 minutes",
      cookTime: "0 minutes",
      servings: 1,
      cuisine: "American"
    });

    suggestions.push({
      title: isHot ? "Frozen Yogurt Bark with Berries" : "Warm Spiced Apple with Nut Butter",
      description: isHot ?
        "A refreshing frozen treat with yogurt and fresh berries." :
        "A warm, comforting apple topped with protein-rich nut butter.",
      calories: Math.min(remaining.calories * 0.15, 200),
      protein: Math.min(remaining.protein * 0.1, 10),
      carbs: Math.min(remaining.carbs * 0.2, 25),
      fat: Math.min(remaining.fat * 0.15, 8),
      tags: isHot ?
        ["snack", "cold", "yogurt", "fruit", "refreshing", "sweet"] :
        ["snack", "warm", "fruit", "comfort", "sweet", "quick"],
      timeOfDay: "snack",
      weatherAppropriate: true,
      ingredients: isHot ?
        [
          "1 cup Greek yogurt",
          "1 tbsp honey",
          "1/2 cup mixed berries",
          "1 tbsp chopped nuts",
          "1/4 tsp vanilla extract"
        ] :
        [
          "1 medium apple",
          "1 tbsp almond or peanut butter",
          "1/4 tsp cinnamon",
          "1 tsp honey",
          "1 tbsp chopped nuts"
        ],
      instructions: isHot ?
        [
          "Mix yogurt with honey and vanilla",
          "Spread on a parchment-lined baking sheet",
          "Top with berries and nuts",
          "Freeze for at least 2 hours",
          "Break into pieces and enjoy"
        ] :
        [
          "Core the apple and slice into thick rings",
          "Microwave apple slices for 1-2 minutes until warm and slightly soft",
          "Spread nut butter on warm apple slices",
          "Sprinkle with cinnamon and nuts",
          "Drizzle with honey"
        ],
      prepTime: isHot ? "5 minutes" : "3 minutes",
      cookTime: isHot ? "2 hours (freezing)" : "2 minutes",
      servings: 1,
      cuisine: "American"
    });
  }

  // Add a third suggestion that's always appropriate
  suggestions.push({
    title: "Balanced Nutrition Bowl",
    description: "A customizable bowl with balanced macros to fit your daily needs.",
    calories: Math.min(remaining.calories * 0.35, 500),
    protein: Math.min(remaining.protein * 0.35, 30),
    carbs: Math.min(remaining.carbs * 0.35, 45),
    fat: Math.min(remaining.fat * 0.35, 18),
    tags: ["balanced", "customizable", "healthy", "bowl", "complete", "versatile"],
    timeOfDay: timeOfDay,
    weatherAppropriate: true,
    ingredients: [
      "4 oz protein of choice (chicken, tofu, beans, etc.)",
      "3/4 cup complex carbs (rice, quinoa, sweet potato)",
      "1 cup mixed vegetables",
      "1 tbsp healthy fat (olive oil, avocado, nuts)",
      "Herbs and spices to taste",
      "Optional sauce or dressing"
    ],
    instructions: [
      "Prepare protein according to preference",
      "Cook complex carbs according to instructions",
      "Steam or roast vegetables",
      "Assemble all components in a bowl",
      "Top with healthy fat and seasonings"
    ],
    prepTime: "10 minutes",
    cookTime: "20 minutes",
    servings: 1,
    cuisine: "Fusion"
  });

  return suggestions;
}

// Update nutrition profile with new weight
export const updateNutritionProfileWeight = async (weight: number): Promise<void> => {
  try {
    console.log(`Updating nutrition profile with new weight: ${weight} lbs`);

    // Get current profile
    const profile = await getNutritionProfile();

    // Update weight
    profile.weight = weight;

    // Save updated profile
    await AsyncStorage.setItem(NUTRITION_PROFILE_KEY, JSON.stringify(profile));

    console.log('Successfully updated nutrition profile with new weight');
  } catch (error) {
    console.error('Error updating nutrition profile weight:', error);
    throw error;
  }
};
