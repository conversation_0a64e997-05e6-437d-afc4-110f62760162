import { format } from 'date-fns';
import { postRequest } from './apiRequestManager';
import { getUserId } from './profile';
import { getContextData } from './contextService';
import { getCurrentWeather } from './nutritionService';
import { getDayDigest, DigestActivity, DigestActivityType } from './digestService';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Interface for a recommendation
 */
export interface Recommendation {
  id: string;
  title: string;
  description: string;
  scheduledTime: string;
  metadata?: Record<string, any>;
}

/**
 * Clear cached recommendations to force refresh from daily digest
 */
export async function clearRecommendationCache(): Promise<void> {
  try {
    await AsyncStorage.removeItem('cached_next_meal');
    await AsyncStorage.removeItem('cached_next_workout');
    console.log('[Recommendation Service] Cleared recommendation cache');
  } catch (error) {
    console.error('[Recommendation Service] Error clearing recommendation cache:', error);
  }
}

/**
 * Get the next recommended workout for today from the daily digest
 * @returns The next recommended workout or null if none found
 */
export async function getNextWorkout(): Promise<Recommendation | null> {
  try {
    console.log('[Recommendation Service] Getting next workout from daily digest');
    
    const today = new Date();
    const digest = await getDayDigest(today);
    
    if (!digest || !digest.activities || digest.activities.length === 0) {
      console.log('[Recommendation Service] No digest activities found, falling back to Groq');
      return await getGroqWorkoutRecommendation();
    }

    // Find workout activities from the digest
    const workoutActivities = digest.activities.filter(
      activity => activity.type === DigestActivityType.WORKOUT && !activity.completed
    );

    if (workoutActivities.length === 0) {
      console.log('[Recommendation Service] No workout activities in digest, falling back to Groq');
      return await getGroqWorkoutRecommendation();
    }

    // Get the next scheduled workout (first uncompleted one that's upcoming or current)
    const now = new Date();
    const upcomingWorkouts = workoutActivities.filter(activity => {
      const activityTime = new Date(activity.scheduledTime);
      // Include activities that are within the next 2 hours or haven't passed yet
      const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
      return activityTime >= now || activityTime >= twoHoursFromNow;
    });

    // If no upcoming workouts, get the next scheduled one regardless of time
    const workoutsToConsider = upcomingWorkouts.length > 0 ? upcomingWorkouts : workoutActivities;
    const nextWorkout = workoutsToConsider
      .sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime())[0];

    return {
      id: nextWorkout.id,
      title: nextWorkout.title,
      description: nextWorkout.description,
      scheduledTime: nextWorkout.scheduledTime,
      metadata: nextWorkout.metadata
    };

  } catch (error) {
    console.error('[Recommendation Service] Error getting next workout from digest:', error);
    return await getGroqWorkoutRecommendation();
  }
}

/**
 * Get the next recommended meal for today from the daily digest
 * @returns The next recommended meal or null if none found
 */
export async function getNextMeal(): Promise<Recommendation | null> {
  try {
    console.log('[Recommendation Service] Getting next meal from daily digest');
    
    const today = new Date();
    const digest = await getDayDigest(today);
    
    if (!digest || !digest.activities || digest.activities.length === 0) {
      console.log('[Recommendation Service] No digest activities found, falling back to Groq');
      return await getGroqMealRecommendation();
    }

    // Find meal activities from the digest
    const mealActivities = digest.activities.filter(
      activity => activity.type === DigestActivityType.MEAL && !activity.completed
    );

    if (mealActivities.length === 0) {
      console.log('[Recommendation Service] No meal activities in digest, falling back to Groq');
      return await getGroqMealRecommendation();
    }

    // Get the next scheduled meal (first uncompleted one that's upcoming or current)
    const now = new Date();
    const upcomingMeals = mealActivities.filter(activity => {
      const activityTime = new Date(activity.scheduledTime);
      // Include activities that are within the next 2 hours or haven't passed yet
      const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
      return activityTime >= now || activityTime >= twoHoursFromNow;
    });

    // If no upcoming meals, get the next scheduled one regardless of time
    const mealsToConsider = upcomingMeals.length > 0 ? upcomingMeals : mealActivities;
    const nextMeal = mealsToConsider
      .sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime())[0];

    return {
      id: nextMeal.id,
      title: nextMeal.title,
      description: nextMeal.description,
      scheduledTime: nextMeal.scheduledTime,
      metadata: nextMeal.metadata
    };

  } catch (error) {
    console.error('[Recommendation Service] Error getting next meal from digest:', error);
    return await getGroqMealRecommendation();
  }
}

/**
 * Get a workout recommendation from Groq API
 * @returns A workout recommendation
 */
async function getGroqWorkoutRecommendation(): Promise<Recommendation | null> {
  try {
    console.log('[Recommendation Service] Getting workout recommendation from Groq');

    // Get user ID
    const userId = await getUserId();
    if (!userId) {
      console.error('[Recommendation Service] No user ID found');
      return null;
    }

    // Get context data for better recommendations
    const contextData = await getContextData();
    const weather = await getCurrentWeather();

    // Generate a temporary conversation ID for this request
    const tempConversationId = `workout_recommendation_${Date.now()}`;

    // Create system message
    const systemMessage = {
      role: 'system',
      content: `You are a fitness expert assistant. Generate a personalized workout recommendation for the user based on their context data, preferences, and current weather. Respond with ONLY a JSON object in this exact format:
      {
        "title": "Workout title",
        "description": "Brief description of the workout",
        "scheduledTime": "ISO time string for when this should be done",
        "metadata": {
          "workoutDetails": {
            "duration": 45,
            "exercises": [
              {
                "name": "Exercise name",
                "sets": 3,
                "reps": "8-10",
                "weight": "moderate",
                "notes": "Form tips"
              }
            ]
          }
        }
      }`
    };

    // Create user message with context
    const userMessage = {
      role: 'user',
      content: `Generate a workout recommendation for me for today. Current weather: ${weather ? JSON.stringify(weather) : 'Unknown'}`
    };

    // Make the API call
    const response = await postRequest(
      '/chat',
      {
        messages: [systemMessage, userMessage],
        userId,
        conversationId: tempConversationId,
        temperature: 0.7,
        requestType: 'workout_recommendation'
      },
      {
        retries: 3,
        retryDelay: 2000,
        logTag: '[Workout Recommendation]',
        useCache: true,
        cacheTTL: 30 * 60 * 1000 // 30 minutes
      }
    );

    if (!response || !response.content) {
      console.error('[Recommendation Service] No content in Groq response');
      return null;
    }

    // Parse the JSON response
    try {
      const content = response.content;
      // Extract JSON from the response (it might be wrapped in markdown code blocks)
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/```\n([\s\S]*?)\n```/) || content.match(/\{[\s\S]*\}/);

      const jsonString = jsonMatch ? jsonMatch[1] || jsonMatch[0] : content;
      const recommendation = JSON.parse(jsonString);

      return {
        id: `groq_workout_${Date.now()}`,
        title: recommendation.title,
        description: recommendation.description,
        scheduledTime: recommendation.scheduledTime || new Date().toISOString(),
        metadata: recommendation.metadata
      };
    } catch (parseError) {
      console.error('[Recommendation Service] Error parsing Groq response:', parseError);
      return null;
    }
  } catch (error) {
    console.error('[Recommendation Service] Error getting Groq workout recommendation:', error);
    return null;
  }
}

/**
 * Get a meal recommendation from Groq API
 * @returns A meal recommendation
 */
async function getGroqMealRecommendation(): Promise<Recommendation | null> {
  try {
    console.log('[Recommendation Service] Getting meal recommendation from Groq');

    // Get user ID
    const userId = await getUserId();
    if (!userId) {
      console.error('[Recommendation Service] No user ID found');
      return null;
    }

    // Get context data for better recommendations
    const contextData = await getContextData();
    const weather = await getCurrentWeather();

    // Generate a temporary conversation ID for this request
    const tempConversationId = `meal_recommendation_${Date.now()}`;

    // Create system message
    const systemMessage = {
      role: 'system',
      content: `You are a nutrition expert assistant. Generate a personalized meal recommendation for the user based on their context data, preferences, and current weather. Respond with ONLY a JSON object in this exact format:
      {
        "title": "Meal title",
        "description": "Brief description of the meal",
        "scheduledTime": "ISO time string for when this should be eaten",
        "metadata": {
          "mealDetails": {
            "nutrition": {
              "calories": 450,
              "protein": 35,
              "carbs": 30,
              "fat": 22
            },
            "ingredients": [
              "Ingredient 1",
              "Ingredient 2"
            ],
            "instructions": [
              "Step 1",
              "Step 2"
            ],
            "tags": ["tag1", "tag2"]
          }
        }
      }`
    };

    // Create user message with context
    const userMessage = {
      role: 'user',
      content: `Generate a meal recommendation for me for today. Current weather: ${weather ? JSON.stringify(weather) : 'Unknown'}`
    };

    // Make the API call
    const response = await postRequest(
      '/chat',
      {
        messages: [systemMessage, userMessage],
        userId,
        conversationId: tempConversationId,
        temperature: 0.7,
        requestType: 'meal_recommendation'
      },
      {
        retries: 3,
        retryDelay: 2000,
        logTag: '[Meal Recommendation]',
        useCache: true,
        cacheTTL: 30 * 60 * 1000 // 30 minutes
      }
    );

    if (!response || !response.content) {
      console.error('[Recommendation Service] No content in Groq response');
      return null;
    }

    // Parse the JSON response
    try {
      const content = response.content;
      // Extract JSON from the response (it might be wrapped in markdown code blocks)
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/```\n([\s\S]*?)\n```/) || content.match(/\{[\s\S]*\}/);

      const jsonString = jsonMatch ? jsonMatch[1] || jsonMatch[0] : content;
      const recommendation = JSON.parse(jsonString);

      return {
        id: `groq_meal_${Date.now()}`,
        title: recommendation.title,
        description: recommendation.description,
        scheduledTime: recommendation.scheduledTime || new Date().toISOString(),
        metadata: recommendation.metadata
      };
    } catch (parseError) {
      console.error('[Recommendation Service] Error parsing Groq response:', parseError);
      return null;
    }
  } catch (error) {
    console.error('[Recommendation Service] Error getting Groq meal recommendation:', error);
    return null;
  }
}
