import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthTokens } from './auth';

// Get API URL with fallback
const API_URL = process.env.EXPO_PUBLIC_API_URL || 'MISSING_API_URL';

// Validate and log API URL initialization
console.log('[API Client] Initializing with URL:', API_URL);
if (!API_URL || API_URL === 'MISSING_API_URL') {
  console.error('[API Client] WARNING: API_URL is not properly configured!');
  console.error('[API Client] Make sure EXPO_PUBLIC_API_URL is set in your environment variables');

  // Try to load from storage as a fallback
  (async () => {
    try {
      const storedApiUrl = await AsyncStorage.getItem('api_url_override');
      if (storedApiUrl) {
        console.log('[API Client] Using API URL from storage:', storedApiUrl);
        // We can't update API_URL const, but we will set it on axios instance below
      } else {
        console.error('[API Client] No fallback API URL found in storage.');
      }
    } catch (error) {
      console.error('[API Client] Error fetching API URL from storage:', error);
    }
  })();
}

// Export a function to get the current API URL
export const getApiUrl = async (): Promise<string> => {
  // Always return the CDK output URL to ensure consistency
  return 'https://2fl5484vo7.execute-api.us-east-1.amazonaws.com/prod/';
};

// Export a function to set a custom API URL (for testing/debugging)
// This function is now disabled to prevent URL overrides
export const setApiUrlOverride = async (url: string): Promise<void> => {
  console.log('[API Client] API URL override is disabled. Using CDK output URL.');
  // Do nothing - always use the CDK output URL
  return;
};

// Utility function to check API URL and connectivity
export const checkApiUrl = async (): Promise<{
  url: string;
  host: string;
  port: string | null;
  protocol: string;
  status: 'checking' | 'ok' | 'error';
  error?: string;
}> => {
  try {
    const currentUrl = await getApiUrl();
    console.log(`[API URL Debug] Current API URL: ${currentUrl}`);

    // If no URL, return error
    if (!currentUrl || currentUrl === 'MISSING_API_URL') {
      console.error('[API URL Debug] No valid API URL configured');
      return {
        url: currentUrl || 'MISSING_API_URL',
        host: 'unknown',
        port: null,
        protocol: 'unknown',
        status: 'error',
        error: 'No valid API URL configured'
      };
    }

    // Parse the URL to get components
    let parsedUrl: URL;
    try {
      parsedUrl = new URL(currentUrl);
      console.log(`[API URL Debug] Parsed URL components:`, {
        protocol: parsedUrl.protocol,
        host: parsedUrl.host,
        hostname: parsedUrl.hostname,
        port: parsedUrl.port,
        pathname: parsedUrl.pathname
      });
    } catch (parseError) {
      console.error(`[API URL Debug] Failed to parse URL: ${currentUrl}`, parseError);
      return {
        url: currentUrl,
        host: 'invalid',
        port: null,
        protocol: 'invalid',
        status: 'error',
        error: `Invalid URL format: ${parseError}`
      };
    }

    // Check if the URL is at least theoretically valid
    if (!parsedUrl.host) {
      console.error(`[API URL Debug] URL has no host: ${currentUrl}`);
      return {
        url: currentUrl,
        host: 'missing',
        port: null,
        protocol: parsedUrl.protocol,
        status: 'error',
        error: 'URL has no host component'
      };
    }

    return {
      url: currentUrl,
      host: parsedUrl.hostname,
      port: parsedUrl.port || null,
      protocol: parsedUrl.protocol,
      status: 'checking'
    };
  } catch (error: any) {
    console.error(`[API URL Debug] Error checking API URL:`, error);
    return {
      url: 'error',
      host: 'error',
      port: null,
      protocol: 'error',
      status: 'error',
      error: `Error checking API URL: ${error.message}`
    };
  }
};

// Default API configuration
const DEFAULT_API_CONFIG = {
  timeout: 20000,
  retries: 2,
  logRequests: false
};

// Current API configuration
let currentApiConfig = { ...DEFAULT_API_CONFIG };

// Configure API client
export const configureApiClient = (config: {
  timeout?: number;
  retries?: number;
  logRequests?: boolean;
}) => {
  currentApiConfig = { ...currentApiConfig, ...config };

  // Update axios instance
  api.defaults.timeout = currentApiConfig.timeout;

  // Only log when explicitly requested
  if (currentApiConfig.logRequests) {
    console.log('[API Client] Configuration updated:', currentApiConfig);
  }
  return currentApiConfig;
};

// Reset API client configuration
export const resetApiClientConfig = () => {
  currentApiConfig = { ...DEFAULT_API_CONFIG };
  api.defaults.timeout = currentApiConfig.timeout;
  return currentApiConfig;
};

// Get current API configuration
export const getApiClientConfig = () => {
  return { ...currentApiConfig };
};

// Axios instance
export const api = axios.create({
  baseURL: API_URL,
  timeout: currentApiConfig.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Initialize with the correct API URL from CDK output
(async () => {
  // Force the API URL to be the one from CDK output
  const cdkApiUrl = 'https://2fl5484vo7.execute-api.us-east-1.amazonaws.com/prod/';
  
  // Clear any stored override that might be causing issues
  try {
    await AsyncStorage.removeItem('api_url_override');
    console.log('[API Client] Cleared API URL override');
  } catch (error) {
    console.error('[API Client] Error clearing API URL override:', error);
  }
  
  // Set the API URL to the CDK output
  console.log('[API Client] Setting baseURL to CDK output:', cdkApiUrl);
  api.defaults.baseURL = cdkApiUrl;
})();

// Add request interceptor for debugging with more detail
api.interceptors.request.use(request => {
  // Only log critical endpoints or when explicitly enabled
  if (currentApiConfig.logRequests && request.url?.includes('/chat')) {
    // More detailed logging for chat endpoint
    console.log('[API Debug] Chat request to:', `${request.baseURL || ''}${request.url || ''}`);
  }
  return request;
});

// Add response interceptor for debugging with more detail
api.interceptors.response.use(
  response => {
    // Only log critical endpoints or when explicitly enabled
    if (currentApiConfig.logRequests && response.config.url?.includes('/chat')) {
      console.log('[API Debug] Chat response received:', response.status);
    }
    return response;
  },
  error => {
    // Only log errors for critical endpoints or status codes
    if (error.config?.url?.includes('/chat') || 
        (error.response?.status && error.response.status >= 500)) {
      console.error('[API Error]:', error.config?.url, error.response?.status, error.message);
    }
    return Promise.reject(error);
  }
);

// Helper function to get tokens without circular imports
async function getTokensFromStorage(): Promise<AuthTokens | null> {
  try {
    // First check split storage (preferred method)
    const accessToken = await SecureStore.getItemAsync('auth_token_access');
    const idToken = await SecureStore.getItemAsync('auth_token_id');
    const refreshToken = await SecureStore.getItemAsync('auth_token_refresh');

    if (accessToken && idToken && refreshToken) {
      return { accessToken, idToken, refreshToken };
    }

    // Check legacy format as fallback
    const tokensJson = await SecureStore.getItemAsync('auth_tokens');
    if (tokensJson) {
      return JSON.parse(tokensJson);
    }

    // Final fallback to AsyncStorage
    const fallbackTokensJson = await AsyncStorage.getItem('auth_tokens_fallback');
    if (fallbackTokensJson) {
      return JSON.parse(fallbackTokensJson);
    }

    return null;
  } catch (error) {
    console.error('Error retrieving tokens in API client:', error);
    return null;
  }
}

// --- Request Interceptor ---
api.interceptors.request.use(
  async (config) => {
    // Do not attach token for auth endpoints themselves (e.g., /auth for login/refresh)
    if (config.url?.endsWith('/auth')) {
      return config;
    }

    const tokens = await getTokensFromStorage();

    // Use idToken for all API Gateway endpoints (including chat)
    if (tokens?.idToken) {
      const headerValue = `Bearer ${tokens.idToken}`;
      config.headers.Authorization = headerValue;
    }

    return config;
  },
  (error) => {
    console.error('[API Interceptor] Error setting up request:', error);
    return Promise.reject(error);
  }
);

// --- Response Interceptor ---
let isRefreshing = false; // Keep refresh state local to this module
let failedQueue: { resolve: (value: unknown) => void; reject: (reason?: any) => void; }[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Simple function to store tokens (avoids circular imports)
async function storeTokensDirectly(tokens: AuthTokens): Promise<void> {
  try {
    // Always use split storage for tokens
    await SecureStore.setItemAsync('auth_tokens_format', 'split');
    await SecureStore.setItemAsync('auth_token_access', tokens.accessToken);
    await SecureStore.setItemAsync('auth_token_id', tokens.idToken);
    await SecureStore.setItemAsync('auth_token_refresh', tokens.refreshToken);

    // Also store in fallback location
    await AsyncStorage.setItem('auth_tokens_fallback', JSON.stringify(tokens));
  } catch (error) {
    console.error('Error storing tokens in API client:', error);
  }
}

// Simple direct refresh token function (avoids circular imports)
async function refreshTokenDirectly(refreshTokenStr: string): Promise<AuthTokens | null> {
  try {
    console.log('[API Client] Starting direct token refresh, refresh token length:', refreshTokenStr.length);
    
    // Use direct axios instance to avoid interceptor loops
    const axiosInstance = axios.create({
      baseURL: API_URL,
      timeout: 15000
    });

    console.log('[API Client] Sending refresh request to:', `${API_URL}/auth`);
    const response = await axiosInstance.post('/auth', {
      action: 'refreshToken',
      refreshToken: refreshTokenStr
    });

    console.log('[API Client] Refresh response received, status:', response.status);

    if (response.data?.tokens) {
      const newTokens = response.data.tokens;
      
      // Validate tokens before storing
      if (!newTokens.accessToken || !newTokens.idToken || !newTokens.refreshToken) {
        console.error('[API Client] Invalid tokens received from refresh:', {
          hasAccessToken: !!newTokens.accessToken,
          hasIdToken: !!newTokens.idToken,
          hasRefreshToken: !!newTokens.refreshToken
        });
        return null;
      }
      
      console.log('[API Client] Valid tokens received, storing...');
      await storeTokensDirectly(newTokens);
      console.log('[API Client] Direct token refresh completed successfully');
      return newTokens;
    }

    console.error('[API Client] Refresh response missing tokens:', response.data);
    return null;
  } catch (error: any) {
    console.error('[API Client] Direct token refresh failed:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
    return null;
  }
}

api.interceptors.response.use(
  (response) => {
    // No logging for successful responses
    return response;
  },
  async (error) => {
    // Only log critical errors
    if (error.response?.status >= 500) {
      console.error(`[API] Server error:`, error.config?.url, error.response?.status);
    }

    const originalRequest = error.config;

    if (!originalRequest) {
      return Promise.reject(error);
    }

    const isTokenExpired =
      error.response?.status === 401 ||
      error.message?.includes('jwt expired') ||
      error.message?.includes('JwtExpiredError') ||
      error.response?.data?.message?.includes('Token expired');

    // Handle token refresh logic
    if (isTokenExpired && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise(function(resolve, reject) {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers['Authorization'] = 'Bearer ' + token;
          return axios(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Get the current refresh token directly
        console.log('[API Client] Retrieving tokens for refresh...');
        const tokens = await getTokensFromStorage();
        
        if (!tokens) {
          console.error('[API Client] No tokens found in storage for refresh');
          throw new Error('No tokens available for refresh');
        }
        
        if (!tokens.refreshToken) {
          console.error('[API Client] Refresh token missing from stored tokens:', {
            hasAccessToken: !!tokens.accessToken,
            hasIdToken: !!tokens.idToken,
            hasRefreshToken: !!tokens.refreshToken
          });
          throw new Error('No refresh token available');
        }

        console.log('[API Client] Refresh token found, attempting token refresh...');
        const newTokens = await refreshTokenDirectly(tokens.refreshToken);

        if (newTokens) {
          console.log('[API Client] Token refresh successful, updating request and continuing');
          isRefreshing = false;
          processQueue(null, newTokens.idToken);
          originalRequest.headers['Authorization'] = `Bearer ${newTokens.idToken}`;
          return axios(originalRequest); // Retry with new token using plain axios
        } else {
          console.error('[API Client] Token refresh failed - no new tokens returned');
          throw new Error('Token refresh failed - no new tokens returned');
        }
      } catch (refreshError: any) {
        console.error('[API Client] Token refresh failed, handling session expiration:', refreshError);
        processQueue(refreshError, null);
        isRefreshing = false;

        // Session expired, clear tokens directly
        console.log('[API Client] Clearing all stored tokens due to refresh failure...');
        await Promise.allSettled([
          SecureStore.deleteItemAsync('auth_tokens_format'),
          SecureStore.deleteItemAsync('auth_token_access'),
          SecureStore.deleteItemAsync('auth_token_id'),
          SecureStore.deleteItemAsync('auth_token_refresh'),
          SecureStore.deleteItemAsync('auth_tokens'),
          AsyncStorage.removeItem('auth_tokens_fallback')
        ]);

        Alert.alert(
          "Session Expired",
          "Your session has expired. Please sign in again.",
          [{ text: "OK" }]
        );
        return Promise.reject({ ...refreshError, __isAuthError: true }); // Reject original request
      }
    }

    // Handle cases where it's already an auth error propagated from refresh logic
    if (error.__isAuthError) {
      return Promise.reject(error);
    }

    // For other errors, just reject
    return Promise.reject(error);
  }
);

// Helper function to check network if needed by interceptors
export const isNetworkAvailable = async (): Promise<boolean> => {
  try {
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-store'
    });
    return response.ok || response.type === 'opaque'; // Opaque means success for no-cors HEAD
  } catch (error) {
    console.warn('Network check failed:', error);
    return false;
  }
};

// Utility function to make requests with automatic retries for network errors
export const makeRequestWithRetry = async <T = any>(
  config: AxiosRequestConfig,
  {
    maxRetries = 3,
    retryDelay = 1000,
    shouldRetry = (error: AxiosError) => error.message.includes('Network Error') || error.code === 'ECONNABORTED',
    useExponentialBackoff = true, // Use exponential backoff by default
    jitter = true, // Add randomness to avoid thundering herd problem
    logPrefix = '[API Retry]' // Prefix for log messages
  } = {}
): Promise<AxiosResponse<T>> => {
  let retries = 0;
  let lastError: AxiosError | null = null;

  // Special handling for chat endpoint
  const isGroqRequest = config.url === '/chat';
  const logTag = isGroqRequest ? '[GROQ API]' : logPrefix;

  // For Groq requests, add special headers and parameters
  if (isGroqRequest) {
    // Add additional headers for better Groq API compatibility
    config.headers = {
      ...config.headers,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'X-Client-Version': '1.0.0',
      'X-Client-Name': 'Lotus-App'
    };

    // Increase timeout for Groq requests
    if (!config.timeout || config.timeout < 60000) {
      config.timeout = 60000; // 60 seconds for Groq
      console.log(`${logTag} Setting extended timeout of 60s for Groq request`);
    }
  }

  while (retries < maxRetries) {
    try {
      console.log(`${logTag} Attempt ${retries + 1}/${maxRetries} for ${config.url}`);

      // For Groq requests, add detailed logging
      if (isGroqRequest) {
        console.log(`${logTag} Request details:`, {
          url: config.url,
          method: config.method,
          timeout: config.timeout,
          dataSize: config.data ? JSON.stringify(config.data).length : 0,
          timestamp: new Date().toISOString()
        });
      }

      const response = await api.request(config);

      // For Groq requests, log success details
      if (isGroqRequest) {
        console.log(`${logTag} Request succeeded:`, {
          status: response.status,
          dataSize: response.data ? JSON.stringify(response.data).length : 0,
          timestamp: new Date().toISOString()
        });
      }

      return response;
    } catch (error: any) {
      lastError = error;

      // Enhanced error logging for Groq
      if (isGroqRequest) {
        console.error(`${logTag} ====== CHAT ERROR DETAILS ======`);
        console.error(`${logTag} URL: ${config.url}`);
        console.error(`${logTag} Error Name: ${error.name}`);
        console.error(`${logTag} Error Message: ${error.message}`);
        console.error(`${logTag} Status: ${error.response?.status}`);
        console.error(`${logTag} Status Text: ${error.response?.statusText}`);
        console.error(`${logTag} Time: ${new Date().toISOString()}`);
        console.error(`${logTag} Response Data:`, error.response?.data);
        console.error(`${logTag} ==================================`);
      }

      if (!shouldRetry(error) || retries + 1 >= maxRetries) {
        console.log(`${logTag} Error not retriable or max retries reached: ${error.message}`);
        break;
      }

      retries++;

      // Calculate delay with exponential backoff if enabled
      let delay = useExponentialBackoff
        ? retryDelay * Math.pow(2, retries - 1) // Exponential: 1000, 2000, 4000, 8000, etc.
        : retryDelay * retries; // Linear: 1000, 2000, 3000, 4000, etc.

      // Add jitter (±25%) to avoid thundering herd problem if enabled
      if (jitter) {
        const jitterFactor = 0.75 + (Math.random() * 0.5); // Random between 0.75 and 1.25
        delay = Math.floor(delay * jitterFactor);
      }

      // For Groq requests, use longer delays
      if (isGroqRequest) {
        delay = Math.min(delay * 1.5, 15000); // Up to 15 seconds for Groq
      }

      // Log the error and retry plan
      console.log(`${logTag} Retrying in ${delay}ms after error: ${error.message}`);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  if (lastError) {
    // Use the appropriate log tag for the error
    console.error(`${logTag} All ${maxRetries} attempts failed for ${config.url}`);

    // For Groq requests, add additional error information
    if (isGroqRequest) {
      console.error(`${logTag} FINAL ERROR DETAILS:`, {
        name: lastError.name,
        message: lastError.message,
        code: lastError.code,
        status: lastError.response?.status,
        statusText: lastError.response?.statusText,
        data: lastError.response?.data,
        timestamp: new Date().toISOString()
      });

      // Add custom properties to the error for better handling
      (lastError as any).isGroqError = true;
      (lastError as any).retriesExhausted = true;
      (lastError as any).finalAttempt = retries;
    }

    throw lastError;
  }

  // This should never happen but TypeScript requires it
  throw new Error('Unexpected error in makeRequestWithRetry');
};