/**
 * Service Health Check System
 * 
 * This service monitors the health of critical app services and provides
 * fallback mechanisms when services are unavailable.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { api } from './apiClient';

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'down';
  lastChecked: string;
  responseTime?: number;
  error?: string;
}

export interface HealthCheckResult {
  overall: 'healthy' | 'degraded' | 'down';
  services: ServiceHealth[];
  timestamp: string;
}

const HEALTH_CHECK_CACHE_KEY = '@service_health_check';
const HEALTH_CHECK_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

class ServiceHealthChecker {
  private healthCache: HealthCheckResult | null = null;
  private lastCheck: number = 0;

  /**
   * Check the health of all critical services
   */
  async checkAllServices(forceRefresh = false): Promise<HealthCheckResult> {
    const now = Date.now();
    
    // Return cached result if still valid and not forcing refresh
    if (!forceRefresh && this.healthCache && (now - this.lastCheck) < HEALTH_CHECK_CACHE_TTL) {
      return this.healthCache;
    }

    console.log('[HealthCheck] Checking service health...');
    
    const services: ServiceHealth[] = [];
    const timestamp = new Date().toISOString();

    // Check API Gateway health
    services.push(await this.checkAPIGateway());
    
    // Check Chat/Groq service health
    services.push(await this.checkChatService());
    
    // Check Context service health
    services.push(await this.checkContextService());

    // Determine overall health
    const downServices = services.filter(s => s.status === 'down').length;
    const degradedServices = services.filter(s => s.status === 'degraded').length;
    
    let overall: 'healthy' | 'degraded' | 'down';
    if (downServices > 1) {
      overall = 'down';
    } else if (downServices > 0 || degradedServices > 1) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    const result: HealthCheckResult = {
      overall,
      services,
      timestamp
    };

    // Cache the result
    this.healthCache = result;
    this.lastCheck = now;
    
    // Store in AsyncStorage for persistence
    try {
      await AsyncStorage.setItem(HEALTH_CHECK_CACHE_KEY, JSON.stringify(result));
    } catch (error) {
      console.error('[HealthCheck] Error caching health check result:', error);
    }

    console.log('[HealthCheck] Health check completed:', {
      overall,
      servicesChecked: services.length,
      downServices,
      degradedServices
    });

    return result;
  }

  /**
   * Check API Gateway health
   */
  private async checkAPIGateway(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Make a simple request to check if the API is responding
      const response = await api.get('/health', { timeout: 5000 });
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'API Gateway',
        status: response.status === 200 ? 'healthy' : 'degraded',
        lastChecked: new Date().toISOString(),
        responseTime
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'API Gateway',
        status: 'down',
        lastChecked: new Date().toISOString(),
        responseTime,
        error: error.message || 'Unknown error'
      };
    }
  }

  /**
   * Check Chat/Groq service health
   */
  private async checkChatService(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Make a minimal chat request to check Groq service
      const response = await api.post('/chat', {
        message: 'health check',
        conversationId: 'health-check',
        isHealthCheck: true
      }, { timeout: 10000 });
      
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'Chat Service',
        status: response.status === 200 ? 'healthy' : 'degraded',
        lastChecked: new Date().toISOString(),
        responseTime
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      // Determine status based on error type
      let status: 'degraded' | 'down' = 'down';
      if (error.response?.status === 502 || error.response?.status === 503) {
        status = 'degraded'; // Server errors are often temporary
      }
      
      return {
        name: 'Chat Service',
        status,
        lastChecked: new Date().toISOString(),
        responseTime,
        error: error.message || 'Unknown error'
      };
    }
  }

  /**
   * Check Context service health
   */
  private async checkContextService(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      // Make a simple context request
      const response = await api.get('/context', { timeout: 5000 });
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'Context Service',
        status: response.status === 200 ? 'healthy' : 'degraded',
        lastChecked: new Date().toISOString(),
        responseTime
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'Context Service',
        status: 'down',
        lastChecked: new Date().toISOString(),
        responseTime,
        error: error.message || 'Unknown error'
      };
    }
  }

  /**
   * Get cached health check result
   */
  async getCachedHealth(): Promise<HealthCheckResult | null> {
    try {
      const cached = await AsyncStorage.getItem(HEALTH_CHECK_CACHE_KEY);
      if (cached) {
        const result = JSON.parse(cached) as HealthCheckResult;
        const age = Date.now() - new Date(result.timestamp).getTime();
        
        // Return cached result if less than TTL
        if (age < HEALTH_CHECK_CACHE_TTL) {
          return result;
        }
      }
    } catch (error) {
      console.error('[HealthCheck] Error getting cached health:', error);
    }
    
    return null;
  }

  /**
   * Check if a specific service is healthy
   */
  async isServiceHealthy(serviceName: string): Promise<boolean> {
    const health = await this.checkAllServices();
    const service = health.services.find(s => s.name === serviceName);
    return service?.status === 'healthy';
  }

  /**
   * Get service-specific fallback recommendations
   */
  getServiceFallbacks(serviceName: string): string[] {
    const fallbacks: Record<string, string[]> = {
      'Chat Service': [
        'Try refreshing the conversation',
        'Check your internet connection',
        'The AI service may be temporarily unavailable'
      ],
      'Context Service': [
        'Your preferences are stored locally',
        'Context sync will resume when service is available',
        'App functionality is not affected'
      ],
      'API Gateway': [
        'Check your internet connection',
        'The app may be in offline mode',
        'Some features may be limited'
      ]
    };

    return fallbacks[serviceName] || ['Service temporarily unavailable'];
  }
}

// Export singleton instance
export const serviceHealthChecker = new ServiceHealthChecker();

// Convenience functions
export const checkServiceHealth = (forceRefresh = false) => 
  serviceHealthChecker.checkAllServices(forceRefresh);

export const isServiceHealthy = (serviceName: string) => 
  serviceHealthChecker.isServiceHealthy(serviceName);

export const getServiceFallbacks = (serviceName: string) => 
  serviceHealthChecker.getServiceFallbacks(serviceName);
