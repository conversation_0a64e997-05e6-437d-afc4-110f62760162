/**
 * Workout Recommendation Service - AI-powered workout recommendations based on performance data
 * 
 * Features:
 * - Performance-based workout suggestions
 * - Progressive overload recommendations
 * - Recovery-aware scheduling
 * - Weakness identification and targeting
 * - Plateau detection and breakthrough strategies
 * - Personalized workout intensity adjustment
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest, getRequest } from './apiRequestManager';

export enum WorkoutType {
  STRENGTH = 'strength',
  CARDIO = 'cardio',
  HIIT = 'hiit',
  FLEXIBILITY = 'flexibility',
  RECOVERY = 'recovery',
  SPORT_SPECIFIC = 'sport_specific'
}

export enum IntensityLevel {
  LOW = 'low',
  MODERATE = 'moderate',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}

export enum MuscleGroup {
  CHEST = 'chest',
  BACK = 'back',
  SHOULDERS = 'shoulders',
  ARMS = 'arms',
  LEGS = 'legs',
  CORE = 'core',
  FULL_BODY = 'full_body'
}

export interface WorkoutRecommendation {
  id: string;
  name: string;
  description: string;
  type: WorkoutType;
  intensity: IntensityLevel;
  estimatedDuration: number; // minutes
  targetMuscleGroups: MuscleGroup[];
  exercises: RecommendedExercise[];
  reasoning: string;
  confidence: number; // 0-100
  adaptations: WorkoutAdaptation[];
  prerequisites?: string[];
  alternatives?: string[];
  expectedBenefits: string[];
  recoveryTime: number; // hours
  difficulty: number; // 1-10
}

export interface RecommendedExercise {
  name: string;
  sets: number;
  reps?: number;
  duration?: number; // seconds
  weight?: number;
  restTime: number; // seconds
  notes?: string;
  progressionTips?: string[];
  formCues?: string[];
  modifications?: ExerciseModification[];
}

export interface ExerciseModification {
  type: 'easier' | 'harder' | 'alternative';
  description: string;
  when: string; // When to use this modification
}

export interface WorkoutAdaptation {
  type: 'progressive_overload' | 'deload' | 'intensity_adjustment' | 'volume_adjustment';
  description: string;
  reason: string;
  implementation: string;
}

export interface PerformanceMetrics {
  workoutId: string;
  date: string;
  type: WorkoutType;
  duration: number;
  intensity: IntensityLevel;
  exercises: ExercisePerformance[];
  overallRating: number; // 1-10
  fatigue: number; // 1-10
  motivation: number; // 1-10
  notes?: string;
}

export interface ExercisePerformance {
  exerciseName: string;
  sets: SetPerformance[];
  personalRecord?: boolean;
  difficulty: number; // 1-10
  formQuality: number; // 1-10
}

export interface SetPerformance {
  reps: number;
  weight?: number;
  duration?: number;
  rpe?: number; // Rate of Perceived Exertion (1-10)
  completed: boolean;
}

export interface WorkoutAnalysis {
  strengthTrends: {
    muscleGroup: MuscleGroup;
    trend: 'improving' | 'plateauing' | 'declining';
    changePercentage: number;
  }[];
  weaknesses: {
    muscleGroup: MuscleGroup;
    severity: 'minor' | 'moderate' | 'major';
    recommendations: string[];
  }[];
  plateauRisk: {
    exercises: string[];
    riskLevel: 'low' | 'medium' | 'high';
    suggestions: string[];
  };
  recoveryNeeds: {
    currentLevel: number; // 1-10
    recommendedRestDays: number;
    signs: string[];
  };
  progressPrediction: {
    timeframe: string;
    expectedGains: Record<string, number>;
    confidence: number;
  };
}

const PERFORMANCE_STORAGE_KEY = '@workout_performance';
const RECOMMENDATIONS_CACHE_KEY = '@workout_recommendations';
const ANALYSIS_CACHE_KEY = '@workout_analysis';

class WorkoutRecommendationService {
  private performanceHistory: PerformanceMetrics[] = [];
  private cachedRecommendations: WorkoutRecommendation[] = [];
  private lastAnalysis: WorkoutAnalysis | null = null;

  /**
   * Get personalized workout recommendations
   */
  async getWorkoutRecommendations(
    preferences?: {
      type?: WorkoutType;
      duration?: number;
      intensity?: IntensityLevel;
      targetMuscleGroups?: MuscleGroup[];
      availableEquipment?: string[];
    }
  ): Promise<WorkoutRecommendation[]> {
    try {
      console.log('[WorkoutRec] Generating personalized workout recommendations...');

      // Load performance history
      await this.loadPerformanceHistory();

      // Analyze current performance state
      const analysis = await this.analyzePerformanceData();

      // Generate recommendations based on analysis and preferences
      const recommendations = await this.generateRecommendations(analysis, preferences);

      // Cache recommendations
      await this.cacheRecommendations(recommendations);

      console.log(`[WorkoutRec] Generated ${recommendations.length} recommendations`);
      return recommendations;

    } catch (error) {
      console.error('[WorkoutRec] Error generating recommendations:', error);
      return this.getCachedRecommendations();
    }
  }

  /**
   * Record workout performance
   */
  async recordWorkoutPerformance(performance: Omit<PerformanceMetrics, 'workoutId'>): Promise<void> {
    const workoutPerformance: PerformanceMetrics = {
      ...performance,
      workoutId: `workout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    this.performanceHistory.push(workoutPerformance);
    await this.savePerformanceHistory();

    // Trigger analysis update
    await this.analyzePerformanceData();

    console.log('[WorkoutRec] Recorded workout performance');
  }

  /**
   * Get workout analysis and insights
   */
  async getWorkoutAnalysis(): Promise<WorkoutAnalysis> {
    if (this.lastAnalysis) {
      return this.lastAnalysis;
    }

    return await this.analyzePerformanceData();
  }

  /**
   * Get next recommended workout based on recent performance
   */
  async getNextWorkout(): Promise<WorkoutRecommendation | null> {
    const recommendations = await this.getWorkoutRecommendations();
    
    if (recommendations.length === 0) {
      return null;
    }

    // Consider recovery time and last workout
    const lastWorkout = this.getLastWorkout();
    if (lastWorkout) {
      const hoursSinceLastWorkout = (Date.now() - new Date(lastWorkout.date).getTime()) / (1000 * 60 * 60);
      
      // Filter recommendations based on recovery time
      const recoveredRecommendations = recommendations.filter(rec => 
        hoursSinceLastWorkout >= rec.recoveryTime
      );

      if (recoveredRecommendations.length > 0) {
        return recoveredRecommendations[0];
      }
    }

    return recommendations[0];
  }

  /**
   * Analyze performance data to identify trends and patterns
   */
  private async analyzePerformanceData(): Promise<WorkoutAnalysis> {
    await this.loadPerformanceHistory();

    if (this.performanceHistory.length < 3) {
      // Not enough data for meaningful analysis
      return this.getDefaultAnalysis();
    }

    const recentWorkouts = this.performanceHistory.slice(-10); // Last 10 workouts
    
    // Analyze strength trends
    const strengthTrends = this.analyzeStrengthTrends(recentWorkouts);
    
    // Identify weaknesses
    const weaknesses = this.identifyWeaknesses(recentWorkouts);
    
    // Detect plateau risk
    const plateauRisk = this.detectPlateauRisk(recentWorkouts);
    
    // Assess recovery needs
    const recoveryNeeds = this.assessRecoveryNeeds(recentWorkouts);
    
    // Predict progress
    const progressPrediction = this.predictProgress(recentWorkouts);

    const analysis: WorkoutAnalysis = {
      strengthTrends,
      weaknesses,
      plateauRisk,
      recoveryNeeds,
      progressPrediction
    };

    this.lastAnalysis = analysis;
    await this.cacheAnalysis(analysis);

    return analysis;
  }

  /**
   * Generate workout recommendations based on analysis
   */
  private async generateRecommendations(
    analysis: WorkoutAnalysis,
    preferences?: any
  ): Promise<WorkoutRecommendation[]> {
    const recommendations: WorkoutRecommendation[] = [];

    // Recovery-focused workout if needed
    if (analysis.recoveryNeeds.currentLevel >= 7) {
      recommendations.push(this.generateRecoveryWorkout(analysis));
    }

    // Weakness-targeting workouts
    for (const weakness of analysis.weaknesses) {
      if (weakness.severity !== 'minor') {
        recommendations.push(this.generateWeaknessTargetingWorkout(weakness));
      }
    }

    // Plateau-breaking workouts
    if (analysis.plateauRisk.riskLevel !== 'low') {
      recommendations.push(this.generatePlateauBreakingWorkout(analysis.plateauRisk));
    }

    // Progressive overload workouts
    const progressiveWorkouts = this.generateProgressiveWorkouts(analysis.strengthTrends);
    recommendations.push(...progressiveWorkouts);

    // Apply preferences filter
    let filteredRecommendations = recommendations;
    if (preferences) {
      filteredRecommendations = this.applyPreferencesFilter(recommendations, preferences);
    }

    // Sort by confidence and relevance
    filteredRecommendations.sort((a, b) => b.confidence - a.confidence);

    return filteredRecommendations.slice(0, 5); // Return top 5 recommendations
  }

  /**
   * Helper methods for analysis
   */
  private analyzeStrengthTrends(workouts: PerformanceMetrics[]): WorkoutAnalysis['strengthTrends'] {
    const trends: WorkoutAnalysis['strengthTrends'] = [];
    
    // Group workouts by muscle group and analyze trends
    const muscleGroups = Object.values(MuscleGroup);
    
    for (const muscleGroup of muscleGroups) {
      const relevantWorkouts = workouts.filter(w => 
        w.exercises.some(e => this.getExerciseMuscleGroup(e.exerciseName) === muscleGroup)
      );

      if (relevantWorkouts.length >= 3) {
        const trend = this.calculateTrend(relevantWorkouts, muscleGroup);
        trends.push(trend);
      }
    }

    return trends;
  }

  private identifyWeaknesses(workouts: PerformanceMetrics[]): WorkoutAnalysis['weaknesses'] {
    const weaknesses: WorkoutAnalysis['weaknesses'] = [];
    
    // Analyze performance ratings by muscle group
    const muscleGroupPerformance: Record<MuscleGroup, number[]> = {} as any;
    
    workouts.forEach(workout => {
      workout.exercises.forEach(exercise => {
        const muscleGroup = this.getExerciseMuscleGroup(exercise.exerciseName);
        if (!muscleGroupPerformance[muscleGroup]) {
          muscleGroupPerformance[muscleGroup] = [];
        }
        muscleGroupPerformance[muscleGroup].push(exercise.difficulty);
      });
    });

    // Identify muscle groups with consistently high difficulty ratings
    Object.entries(muscleGroupPerformance).forEach(([muscleGroup, difficulties]) => {
      const avgDifficulty = difficulties.reduce((a, b) => a + b, 0) / difficulties.length;
      
      if (avgDifficulty >= 8) {
        weaknesses.push({
          muscleGroup: muscleGroup as MuscleGroup,
          severity: 'major',
          recommendations: [
            `Focus on ${muscleGroup} strengthening exercises`,
            'Consider reducing intensity temporarily',
            'Add more warm-up for this muscle group'
          ]
        });
      } else if (avgDifficulty >= 6.5) {
        weaknesses.push({
          muscleGroup: muscleGroup as MuscleGroup,
          severity: 'moderate',
          recommendations: [
            `Include more ${muscleGroup} exercises`,
            'Work on form and technique'
          ]
        });
      }
    });

    return weaknesses;
  }

  private detectPlateauRisk(workouts: PerformanceMetrics[]): WorkoutAnalysis['plateauRisk'] {
    // Analyze if performance has stagnated
    const recentRatings = workouts.slice(-5).map(w => w.overallRating);
    const variance = this.calculateVariance(recentRatings);
    
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    const suggestions: string[] = [];
    
    if (variance < 0.5) { // Low variance indicates stagnation
      riskLevel = 'high';
      suggestions.push(
        'Try new exercise variations',
        'Increase workout intensity',
        'Change your routine structure',
        'Add new training methods'
      );
    } else if (variance < 1.0) {
      riskLevel = 'medium';
      suggestions.push(
        'Consider varying your routine',
        'Try progressive overload'
      );
    }

    return {
      exercises: [], // Would identify specific exercises
      riskLevel,
      suggestions
    };
  }

  private assessRecoveryNeeds(workouts: PerformanceMetrics[]): WorkoutAnalysis['recoveryNeeds'] {
    const recentFatigue = workouts.slice(-3).map(w => w.fatigue);
    const avgFatigue = recentFatigue.reduce((a, b) => a + b, 0) / recentFatigue.length;
    
    const signs: string[] = [];
    let recommendedRestDays = 1;
    
    if (avgFatigue >= 8) {
      signs.push('High fatigue levels', 'Decreased motivation');
      recommendedRestDays = 2;
    } else if (avgFatigue >= 6) {
      signs.push('Moderate fatigue');
      recommendedRestDays = 1;
    }

    return {
      currentLevel: avgFatigue,
      recommendedRestDays,
      signs
    };
  }

  private predictProgress(workouts: PerformanceMetrics[]): WorkoutAnalysis['progressPrediction'] {
    // Simple progress prediction based on recent trends
    const recentRatings = workouts.slice(-5).map(w => w.overallRating);
    const trend = this.calculateLinearTrend(recentRatings);
    
    return {
      timeframe: '4 weeks',
      expectedGains: {
        strength: Math.max(0, trend * 4), // Extrapolate trend
        endurance: Math.max(0, trend * 3)
      },
      confidence: Math.min(100, Math.abs(trend) * 50 + 50)
    };
  }

  private generateRecoveryWorkout(analysis: WorkoutAnalysis): WorkoutRecommendation {
    return {
      id: `recovery_${Date.now()}`,
      name: 'Active Recovery Session',
      description: 'Low-intensity workout focused on recovery and mobility',
      type: WorkoutType.RECOVERY,
      intensity: IntensityLevel.LOW,
      estimatedDuration: 30,
      targetMuscleGroups: [MuscleGroup.FULL_BODY],
      exercises: [
        {
          name: 'Light Walking',
          sets: 1,
          duration: 600, // 10 minutes
          restTime: 0,
          notes: 'Keep heart rate low and comfortable'
        },
        {
          name: 'Dynamic Stretching',
          sets: 1,
          duration: 900, // 15 minutes
          restTime: 0,
          notes: 'Focus on major muscle groups'
        }
      ],
      reasoning: 'High fatigue levels detected. Recovery workout recommended.',
      confidence: 90,
      adaptations: [],
      expectedBenefits: ['Improved recovery', 'Reduced fatigue', 'Better mobility'],
      recoveryTime: 0,
      difficulty: 2
    };
  }

  private generateWeaknessTargetingWorkout(weakness: WorkoutAnalysis['weaknesses'][0]): WorkoutRecommendation {
    return {
      id: `weakness_${weakness.muscleGroup}_${Date.now()}`,
      name: `${weakness.muscleGroup.charAt(0).toUpperCase() + weakness.muscleGroup.slice(1)} Strengthening`,
      description: `Targeted workout to address ${weakness.muscleGroup} weakness`,
      type: WorkoutType.STRENGTH,
      intensity: IntensityLevel.MODERATE,
      estimatedDuration: 45,
      targetMuscleGroups: [weakness.muscleGroup],
      exercises: this.getExercisesForMuscleGroup(weakness.muscleGroup),
      reasoning: `${weakness.severity} weakness detected in ${weakness.muscleGroup}`,
      confidence: 85,
      adaptations: [
        {
          type: 'volume_adjustment',
          description: 'Increased volume for target muscle group',
          reason: 'Address identified weakness',
          implementation: 'Add 1-2 extra sets for target exercises'
        }
      ],
      expectedBenefits: [`Improved ${weakness.muscleGroup} strength`, 'Better muscle balance'],
      recoveryTime: 48,
      difficulty: 6
    };
  }

  private generatePlateauBreakingWorkout(plateauRisk: WorkoutAnalysis['plateauRisk']): WorkoutRecommendation {
    return {
      id: `plateau_breaker_${Date.now()}`,
      name: 'Plateau Breaker Workout',
      description: 'High-intensity workout designed to break through performance plateaus',
      type: WorkoutType.HIIT,
      intensity: IntensityLevel.HIGH,
      estimatedDuration: 35,
      targetMuscleGroups: [MuscleGroup.FULL_BODY],
      exercises: [
        {
          name: 'Burpees',
          sets: 4,
          reps: 10,
          restTime: 60,
          notes: 'Focus on explosive movement'
        },
        {
          name: 'Mountain Climbers',
          sets: 4,
          duration: 30,
          restTime: 30,
          notes: 'Maintain high intensity'
        }
      ],
      reasoning: 'Performance plateau detected. High-intensity variation recommended.',
      confidence: 80,
      adaptations: [
        {
          type: 'intensity_adjustment',
          description: 'Increased workout intensity',
          reason: 'Break through performance plateau',
          implementation: 'Use higher intensity intervals'
        }
      ],
      expectedBenefits: ['Breakthrough plateau', 'Improved cardiovascular fitness', 'Increased metabolic rate'],
      recoveryTime: 24,
      difficulty: 8
    };
  }

  private generateProgressiveWorkouts(trends: WorkoutAnalysis['strengthTrends']): WorkoutRecommendation[] {
    // Generate workouts that progressively overload improving muscle groups
    return trends
      .filter(trend => trend.trend === 'improving')
      .slice(0, 2) // Limit to 2 progressive workouts
      .map(trend => ({
        id: `progressive_${trend.muscleGroup}_${Date.now()}`,
        name: `Progressive ${trend.muscleGroup.charAt(0).toUpperCase() + trend.muscleGroup.slice(1)} Training`,
        description: `Progressive overload workout for ${trend.muscleGroup}`,
        type: WorkoutType.STRENGTH,
        intensity: IntensityLevel.MODERATE,
        estimatedDuration: 50,
        targetMuscleGroups: [trend.muscleGroup],
        exercises: this.getProgressiveExercisesForMuscleGroup(trend.muscleGroup),
        reasoning: `${trend.muscleGroup} showing improvement. Progressive overload recommended.`,
        confidence: 75,
        adaptations: [
          {
            type: 'progressive_overload',
            description: 'Gradually increase weight or reps',
            reason: 'Capitalize on improving strength trend',
            implementation: 'Increase weight by 2.5-5% or add 1-2 reps'
          }
        ],
        expectedBenefits: ['Continued strength gains', 'Progressive adaptation'],
        recoveryTime: 48,
        difficulty: 7
      }));
  }

  /**
   * Utility methods
   */
  private getExerciseMuscleGroup(exerciseName: string): MuscleGroup {
    // Simple mapping - in a real app, this would be more comprehensive
    const exerciseMap: Record<string, MuscleGroup> = {
      'push-up': MuscleGroup.CHEST,
      'bench press': MuscleGroup.CHEST,
      'pull-up': MuscleGroup.BACK,
      'squat': MuscleGroup.LEGS,
      'deadlift': MuscleGroup.BACK,
      'shoulder press': MuscleGroup.SHOULDERS,
      'bicep curl': MuscleGroup.ARMS,
      'plank': MuscleGroup.CORE
    };

    const lowerName = exerciseName.toLowerCase();
    for (const [exercise, muscleGroup] of Object.entries(exerciseMap)) {
      if (lowerName.includes(exercise)) {
        return muscleGroup;
      }
    }

    return MuscleGroup.FULL_BODY;
  }

  private getExercisesForMuscleGroup(muscleGroup: MuscleGroup): RecommendedExercise[] {
    // Return appropriate exercises for the muscle group
    const exerciseMap: Record<MuscleGroup, RecommendedExercise[]> = {
      [MuscleGroup.CHEST]: [
        { name: 'Push-ups', sets: 3, reps: 12, restTime: 60 },
        { name: 'Chest Press', sets: 3, reps: 10, restTime: 90 }
      ],
      [MuscleGroup.BACK]: [
        { name: 'Pull-ups', sets: 3, reps: 8, restTime: 90 },
        { name: 'Rows', sets: 3, reps: 12, restTime: 60 }
      ],
      [MuscleGroup.LEGS]: [
        { name: 'Squats', sets: 3, reps: 15, restTime: 90 },
        { name: 'Lunges', sets: 3, reps: 12, restTime: 60 }
      ],
      [MuscleGroup.SHOULDERS]: [
        { name: 'Shoulder Press', sets: 3, reps: 10, restTime: 60 },
        { name: 'Lateral Raises', sets: 3, reps: 15, restTime: 45 }
      ],
      [MuscleGroup.ARMS]: [
        { name: 'Bicep Curls', sets: 3, reps: 12, restTime: 45 },
        { name: 'Tricep Dips', sets: 3, reps: 10, restTime: 60 }
      ],
      [MuscleGroup.CORE]: [
        { name: 'Plank', sets: 3, duration: 60, restTime: 60 },
        { name: 'Crunches', sets: 3, reps: 20, restTime: 45 }
      ],
      [MuscleGroup.FULL_BODY]: [
        { name: 'Burpees', sets: 3, reps: 10, restTime: 90 },
        { name: 'Mountain Climbers', sets: 3, duration: 30, restTime: 60 }
      ]
    };

    return exerciseMap[muscleGroup] || exerciseMap[MuscleGroup.FULL_BODY];
  }

  private getProgressiveExercisesForMuscleGroup(muscleGroup: MuscleGroup): RecommendedExercise[] {
    const baseExercises = this.getExercisesForMuscleGroup(muscleGroup);
    
    // Add progressive overload adaptations
    return baseExercises.map(exercise => ({
      ...exercise,
      progressionTips: [
        'Increase weight by 2.5-5% when you can complete all sets',
        'Add 1-2 reps when weight increase is not possible',
        'Focus on controlled movement and full range of motion'
      ]
    }));
  }

  private calculateTrend(workouts: PerformanceMetrics[], muscleGroup: MuscleGroup): WorkoutAnalysis['strengthTrends'][0] {
    // Simplified trend calculation
    const ratings = workouts.map(w => w.overallRating);
    const trend = ratings[ratings.length - 1] > ratings[0] ? 'improving' : 
                 ratings[ratings.length - 1] < ratings[0] ? 'declining' : 'plateauing';
    
    const changePercentage = ((ratings[ratings.length - 1] - ratings[0]) / ratings[0]) * 100;

    return {
      muscleGroup,
      trend,
      changePercentage
    };
  }

  private calculateVariance(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
  }

  private calculateLinearTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  private applyPreferencesFilter(
    recommendations: WorkoutRecommendation[], 
    preferences: any
  ): WorkoutRecommendation[] {
    return recommendations.filter(rec => {
      if (preferences.type && rec.type !== preferences.type) return false;
      if (preferences.duration && Math.abs(rec.estimatedDuration - preferences.duration) > 15) return false;
      if (preferences.intensity && rec.intensity !== preferences.intensity) return false;
      return true;
    });
  }

  private getLastWorkout(): PerformanceMetrics | null {
    return this.performanceHistory.length > 0 ? 
      this.performanceHistory[this.performanceHistory.length - 1] : null;
  }

  private getDefaultAnalysis(): WorkoutAnalysis {
    return {
      strengthTrends: [],
      weaknesses: [],
      plateauRisk: { exercises: [], riskLevel: 'low', suggestions: [] },
      recoveryNeeds: { currentLevel: 3, recommendedRestDays: 1, signs: [] },
      progressPrediction: { timeframe: '4 weeks', expectedGains: {}, confidence: 50 }
    };
  }

  private async loadPerformanceHistory(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(PERFORMANCE_STORAGE_KEY);
      if (stored) {
        this.performanceHistory = JSON.parse(stored);
      }
    } catch (error) {
      console.error('[WorkoutRec] Error loading performance history:', error);
    }
  }

  private async savePerformanceHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem(PERFORMANCE_STORAGE_KEY, JSON.stringify(this.performanceHistory));
    } catch (error) {
      console.error('[WorkoutRec] Error saving performance history:', error);
    }
  }

  private async cacheRecommendations(recommendations: WorkoutRecommendation[]): Promise<void> {
    try {
      await AsyncStorage.setItem(RECOMMENDATIONS_CACHE_KEY, JSON.stringify({
        recommendations,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('[WorkoutRec] Error caching recommendations:', error);
    }
  }

  private async getCachedRecommendations(): Promise<WorkoutRecommendation[]> {
    try {
      const cached = await AsyncStorage.getItem(RECOMMENDATIONS_CACHE_KEY);
      if (cached) {
        const data = JSON.parse(cached);
        // Return cached recommendations if less than 1 hour old
        if (Date.now() - data.timestamp < 60 * 60 * 1000) {
          return data.recommendations;
        }
      }
    } catch (error) {
      console.error('[WorkoutRec] Error getting cached recommendations:', error);
    }
    return [];
  }

  private async cacheAnalysis(analysis: WorkoutAnalysis): Promise<void> {
    try {
      await AsyncStorage.setItem(ANALYSIS_CACHE_KEY, JSON.stringify({
        analysis,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('[WorkoutRec] Error caching analysis:', error);
    }
  }
}

// Export singleton instance
export const workoutRecommendationService = new WorkoutRecommendationService();

// Convenience functions
export const getWorkoutRecommendations = (preferences?: any) =>
  workoutRecommendationService.getWorkoutRecommendations(preferences);

export const recordWorkoutPerformance = (performance: Omit<PerformanceMetrics, 'workoutId'>) =>
  workoutRecommendationService.recordWorkoutPerformance(performance);

export const getWorkoutAnalysis = () =>
  workoutRecommendationService.getWorkoutAnalysis();

export const getNextWorkout = () =>
  workoutRecommendationService.getNextWorkout();
