// Legacy context service import removed - using contextEngineV3 instead
import { format, addDays, subDays, isSameDay, parseISO, isAfter, isBefore, isToday } from 'date-fns';
import { getUserId } from './profile';
import { generateUUID } from '../utils/uuid';
import { getApiUrl } from './apiClient';
import { getAuthHeader } from './auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStoredTokens } from './auth';
import { api } from './apiClient';

// Digest-specific auth header that uses ID tokens (reverting back)
async function getDigestAuthHeader(): Promise<{ Authorization?: string }> {
  try {
    const tokens = await getStoredTokens();
    if (tokens?.idToken) {
      return { Authorization: `Bearer ${tokens.idToken}` };
    }
    return {};
  } catch (error) {
    console.error('Error getting digest auth header:', error);
    return {};
  }
}

// Define the types of activities that can appear in the digest
export enum DigestActivityType {
  WORKOUT = 'workout',
  MEAL = 'meal',
  WATER = 'water',
  SLEEP = 'sleep',
  REMINDER = 'reminder',
  OTHER = 'other'
}

// Interface for a digest activity
export interface DigestActivity {
  id: string;
  type: DigestActivityType;
  title: string;
  description: string;
  scheduledTime: string; // ISO string
  completed: boolean;
  isUserOverride: boolean;
  originalActivity?: DigestActivity; // If this is an override, store the original
  metadata?: Record<string, any>;
}

// Interface for a full day's digest
export interface DayDigest {
  date: string; // ISO date string (YYYY-MM-DD)
  activities: DigestActivity[];
  noDataReason?: 'before-join' | 'no-data-collected' | 'api-error' | 'generated-locally'; // Reason why there's no data for this day
  error?: string; // Error message if there was an error generating the digest
}

// Context type for storing user overrides - will be defined when needed

/**
 * Get the digest for a specific date
 * @param date The date to get the digest for
 * @param isAppInitializing Optional flag to indicate this is being called during app initialization
 * @returns The day's digest with all activities
 */
export async function getDayDigest(date: Date, isAppInitializing: boolean = false): Promise<DayDigest> {
  const dateString = format(date, 'yyyy-MM-dd');

  // Use tighter timeouts during app initialization
  const timeoutDuration = isAppInitializing ? 4000 : 15000;

  try {
    const userId = await getUserId();

    if (!userId) {
      throw new Error('User ID not found');
    }

    // Get user join date to determine if we should show data for this date
    const userJoinDate = await getUserJoinDate();
    const isBeforeUserJoined = userJoinDate && isBefore(date, userJoinDate);

    // If the date is before the user joined, return an empty digest with reason
    if (isBeforeUserJoined) {
      return {
        date: dateString,
        activities: [],
        noDataReason: 'before-join'
      };
    }

    try {
      // Verify that we have a valid auth token before making the request
      const { getStoredTokens, refreshToken } = require('./auth');
      const tokens = await getStoredTokens();

      // If we have no tokens or expired access token, try to refresh first
      if (!tokens || !tokens.accessToken) {
        console.log('[Digest Service] No access token found, attempting to refresh');
        try {
          await refreshToken();
        } catch (refreshError) {
          console.error('[Digest Service] Failed to refresh tokens:', refreshError);
        }
      }

      // Fetch digest from the API
      let apiUrl = await getApiUrl();
      apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

      // Set up a timeout controller for the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

      try {
        // Get fresh auth headers for this request (digest-specific)
        const authHeader = await getDigestAuthHeader();

        const response = await fetch(`${apiUrl}/digest/${dateString}`, {
          method: 'GET',
          headers: {
            ...authHeader,
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if fetch completes

        if (!response.ok) {
          // Try to get more detailed error information
          let errorDetails = response.statusText;
          try {
            const errorData = await response.json();
            errorDetails = JSON.stringify(errorData);
          } catch (e) {
            // If we can't parse the error response, just use the status text
          }

          throw new Error(`Failed to fetch digest: ${response.status} - ${errorDetails}`);
        }

        const digestData = await response.json();

        // Transform the API response to match our client-side model
        return {
          date: dateString,
          activities: digestData.activities ? digestData.activities.map((activity: any) => ({
            ...activity,
            type: mapActivityType(activity.type),
          })) : [],
          noDataReason: isPastDate(date) && (!digestData.activities || digestData.activities.length === 0)
            ? 'no-data-collected'
            : undefined
        };
      } catch (fetchError) {
        // Handle the AbortController timeout or other fetch errors
        clearTimeout(timeoutId); // Ensure timeout is cleared
        console.error(`[Digest Service] Fetch error for ${dateString}:`, fetchError);

        if ((fetchError as Error).name === 'AbortError') {
          console.error(`[Digest Service] Request timed out for ${dateString}`);
          throw new Error(`Request timed out while fetching digest for ${dateString}`);
        }

        // Re-throw the error for the outer catch block to handle
        throw fetchError;
      }
    } catch (error: any) { // Added :any type assertion
      console.error('[Digest Service] API error in getDayDigest:', error);
      // Do not fall back to local generation.
      // Return an error state or an empty digest with an error reason.
      return {
        date: dateString,
        activities: [],
        noDataReason: 'api-error',
        error: (error as Error).message || 'Failed to fetch digest from API.'
      };
    }
    // The outer catch block is removed as the inner one now handles the API error directly
    // by returning an error-state digest.
  } catch (error: any) { // Added :any type assertion, This catch block now primarily handles errors like getUserId() failing
    console.error('[Digest Service] Critical error in getDayDigest (e.g., getUserId failed):', error);
    return {
      date: dateString,
      activities: [],
      noDataReason: 'api-error', // Or a more specific client-side error reason
      error: (error as Error).message || 'An unexpected error occurred.'
    };
  }
}

/**
 * Check if a date is in the past
 */
function isPastDate(date: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return isBefore(date, today);
}

/**
 * Generate basic activities for a date (used as fallback when API fails)
 */
export async function generateBasicActivities(date: Date): Promise<DigestActivity[]> {
  const activities: DigestActivity[] = [];
  const dateString = format(date, 'yyyy-MM-dd');
  const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

  // Try to get user preferences from context
  let userPreferences: any = {
    // Default values
    wakeUpTime: '07:00',
    bedTime: '22:30',
    differentWeekendSchedule: false,
    weekendWakeUpTime: '08:30',
    weekendBedTime: '23:30',
    preferredWorkoutTime: '17:30',
    preferredMealTimes: {
      breakfast: '08:00',
      lunch: '12:30',
      dinner: '19:00',
    },
    snacks: true,
    waterReminders: true
  };

  try {
    // Import here to avoid circular dependencies
    const { contextEngineV3, ContextType } = require('./contextEngineV3');

    // Get context data for personalization using V3 engine
    console.log('[generateBasicActivities] Retrieving context data...');
    const allContextData = await contextEngineV3.getContextEntries({ activeOnly: true });
    console.log(`[generateBasicActivities] All context data count: ${allContextData.length}`);

    const customContextData = await contextEngineV3.getContextEntries({
      types: [ContextType.CUSTOM],
      activeOnly: true
    });
    console.log(`[generateBasicActivities] Custom context data count: ${customContextData.length}`);

    const preferenceContextData = await contextEngineV3.getContextEntries({
      types: [ContextType.PREFERENCES],
      activeOnly: true
    });
    console.log(`[generateBasicActivities] Preference context data count: ${preferenceContextData.length}`);

    // Get custom context data specifically
    const contextData = customContextData; // Use the already fetched data
    console.log('[generateBasicActivities] Custom context sample:', contextData.length > 0 ? contextData.slice(0, 2) : 'none');
    
    // Log preference context sample
    if (preferenceContextData.length > 0) {
      console.log('[generateBasicActivities] Preference context sample:', preferenceContextData.slice(0, 2));
    }
    
    // Look specifically for daily_preferences in both types
    const dailyPrefsInCustom = contextData.filter((item: any) => 
      item.metadata?.category === 'daily_preferences'
    );
    const dailyPrefsInPreference = preferenceContextData.filter((item: any) => 
      item.metadata?.category === 'daily_preferences'
    );
    
    console.log('[generateBasicActivities] Daily preferences in CUSTOM:', dailyPrefsInCustom.length);
    console.log('[generateBasicActivities] Daily preferences in PREFERENCE:', dailyPrefsInPreference.length);
    
    if (dailyPrefsInCustom.length > 0) {
      console.log('[generateBasicActivities] Latest daily pref in CUSTOM:', dailyPrefsInCustom[0]);
    }
    if (dailyPrefsInPreference.length > 0) {
      console.log('[generateBasicActivities] Latest daily pref in PREFERENCE:', dailyPrefsInPreference[0]);
    }
    
    const routineData = contextData.find((item: any) =>
      item.metadata?.category === 'daily_routine'
    );

    if (routineData?.value) {
      console.log('Found user daily routine preferences');
      userPreferences = {
        ...userPreferences,
        ...routineData.value
      };
    }

    // Also check for individual preferences
    const mealPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'meal_times'
    );

    if (mealPrefs?.metadata) {
      userPreferences.preferredMealTimes = {
        breakfast: mealPrefs.metadata.breakfast || userPreferences.preferredMealTimes.breakfast,
        lunch: mealPrefs.metadata.lunch || userPreferences.preferredMealTimes.lunch,
        dinner: mealPrefs.metadata.dinner || userPreferences.preferredMealTimes.dinner
      };
      userPreferences.snacks = mealPrefs.metadata.snacks ?? userPreferences.snacks;
    }

    const workoutPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'workout_preference'
    );

    if (workoutPrefs?.metadata?.preferredTime) {
      userPreferences.preferredWorkoutTime = workoutPrefs.metadata.preferredTime;
    }

    const sleepPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'sleep_schedule'
    );

    if (sleepPrefs?.metadata) {
      userPreferences.wakeUpTime = sleepPrefs.metadata.wakeUpTime || userPreferences.wakeUpTime;
      userPreferences.bedTime = sleepPrefs.metadata.bedTime || userPreferences.bedTime;
    }

    const weekendSleepPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'weekend_sleep_schedule'
    );

    if (weekendSleepPrefs?.metadata) {
      userPreferences.differentWeekendSchedule = true;
      userPreferences.weekendWakeUpTime = weekendSleepPrefs.metadata.wakeUpTime || userPreferences.weekendWakeUpTime;
      userPreferences.weekendBedTime = weekendSleepPrefs.metadata.bedTime || userPreferences.weekendBedTime;
    }

    const reminderPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'reminders'
    );

    if (reminderPrefs?.metadata) {
      userPreferences.waterReminders = reminderPrefs.metadata.waterReminders ?? userPreferences.waterReminders;
    }

    // Check for daily preferences (from context modal) - check both CUSTOM and PREFERENCE types
    let dailyPrefs = contextData.find((item: any) =>
      item.metadata?.category === 'daily_preferences'
    );
    
    // If not found in CUSTOM, check PREFERENCE context data
    if (!dailyPrefs) {
      dailyPrefs = preferenceContextData.find((item: any) =>
        item.metadata?.category === 'daily_preferences'
      );
    }
    
    console.log('[generateBasicActivities] Daily preferences found:', dailyPrefs);

    if (dailyPrefs) {
      console.log('Found daily preferences from context modal:', dailyPrefs);
      
      // Check if there's explicit wake-up time in metadata
      if (dailyPrefs.metadata?.wakeUpTime) {
        userPreferences.wakeUpTime = dailyPrefs.metadata.wakeUpTime;
        console.log('Using wake-up time from metadata:', dailyPrefs.metadata.wakeUpTime);
      }
      
      // Also parse wake-up time from the text value if available
      if (dailyPrefs.value && typeof dailyPrefs.value === 'string') {
        const wakeUpTimeMatch = dailyPrefs.value.match(/(?:i|I)\s*(?:wake|get)\s*(?:up|at)\s*(?:at)?\s*(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
        
        if (wakeUpTimeMatch) {
          let hours = parseInt(wakeUpTimeMatch[1]);
          const minutes = wakeUpTimeMatch[2] ? parseInt(wakeUpTimeMatch[2]) : 0;
          const period = wakeUpTimeMatch[3]?.toLowerCase();

          // Convert to 24-hour format
          if (period === 'pm' && hours < 12) {
            hours += 12;
          } else if (period === 'am' && hours === 12) {
            hours = 0;
          }

          const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          userPreferences.wakeUpTime = formattedTime;
          console.log('Extracted wake-up time from text:', formattedTime);
          
          // Adjust meal times based on wake-up time
          const wakeUpHour = hours;
          const wakeUpMinutes = minutes;
          
          // Breakfast: 30-60 minutes after wake-up
          const breakfastHour = wakeUpHour + (wakeUpMinutes >= 30 ? 1 : 0);
          const breakfastMinutes = wakeUpMinutes >= 30 ? wakeUpMinutes - 30 : wakeUpMinutes + 30;
          userPreferences.preferredMealTimes.breakfast = `${breakfastHour.toString().padStart(2, '0')}:${breakfastMinutes.toString().padStart(2, '0')}`;
          
          // Lunch: 4-5 hours after breakfast
          const lunchHour = breakfastHour + 4;
          userPreferences.preferredMealTimes.lunch = `${lunchHour.toString().padStart(2, '0')}:${breakfastMinutes.toString().padStart(2, '0')}`;
          
          // Dinner: 6-7 hours after lunch
          const dinnerHour = lunchHour + 6;
          userPreferences.preferredMealTimes.dinner = `${dinnerHour.toString().padStart(2, '0')}:${breakfastMinutes.toString().padStart(2, '0')}`;
          
          console.log('Adjusted meal times based on wake-up time:', userPreferences.preferredMealTimes);
        }
      }
    }
  } catch (error) {
    console.error('Error getting user preferences:', error);
    // Continue with default values
  }

  // Generate workout based on day of week
  if (dayOfWeek !== 0) { // Skip Sunday
    // Different workout types based on day of week
    let workoutTitle = 'Daily Workout';
    let workoutDescription = 'Recommended workout for today';

    if (dayOfWeek === 1 || dayOfWeek === 4) { // Monday and Thursday
      workoutTitle = 'Upper Body Workout';
      workoutDescription = 'Focus on chest, back, shoulders, and arms';
    } else if (dayOfWeek === 2 || dayOfWeek === 5) { // Tuesday and Friday
      workoutTitle = 'Lower Body Workout';
      workoutDescription = 'Focus on legs, glutes, and core';
    } else if (dayOfWeek === 3) { // Wednesday
      workoutTitle = 'Cardio Session';
      workoutDescription = '30 minutes of zone 2 cardio';
    } else if (dayOfWeek === 6) { // Saturday
      workoutTitle = 'Active Recovery';
      workoutDescription = 'Light activity and mobility work';
    }

    // Use the user's preferred workout time
    const workoutTime = userPreferences.preferredWorkoutTime || '17:30';

    activities.push({
      id: generateUUID(),
      type: DigestActivityType.WORKOUT,
      title: workoutTitle,
      description: workoutDescription,
      scheduledTime: `${dateString}T${workoutTime}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        workoutDetails: {
          title: workoutTitle,
          description: workoutDescription,
          duration: 45,
          notes: 'Focus on proper form and controlled movements. Rest 60-90 seconds between sets.',
          exercises: [
            {
              name: 'Barbell Squat',
              sets: 4,
              reps: '8-10',
              weight: 'moderate',
              notes: 'Keep chest up, push through heels'
            },
            {
              name: 'Dumbbell Bench Press',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Full range of motion'
            },
            {
              name: 'Bent-Over Row',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Squeeze shoulder blades together'
            },
            {
              name: 'Romanian Deadlift',
              sets: 3,
              reps: '10-12',
              weight: 'moderate',
              notes: 'Hinge at hips, keep back flat'
            },
            {
              name: 'Overhead Press',
              sets: 3,
              reps: '8-10',
              weight: 'moderate',
              notes: 'Brace core throughout'
            },
            {
              name: 'Plank',
              sets: 3,
              reps: '30-60 seconds',
              notes: 'Keep body in straight line'
            }
          ]
        }
      }
    });
  }

  // Add meals with user's preferred times and detailed information
  const breakfastTime = userPreferences.preferredMealTimes.breakfast || '08:00';
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Protein-Packed Breakfast Bowl',
    description: 'Greek yogurt with fresh berries, honey, and granola for a balanced start to your day',
    scheduledTime: `${dateString}T${breakfastTime}`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        title: 'Protein-Packed Breakfast Bowl',
        description: 'A nutritious breakfast bowl featuring Greek yogurt, fresh berries, honey, and homemade granola. This balanced meal provides protein, healthy fats, and complex carbohydrates to fuel your morning.',
        nutrition: {
          calories: 420,
          protein: 24,
          carbs: 52,
          fat: 14
        },
        ingredients: [
          "1 cup Greek yogurt (plain, 2% fat)",
          "1/2 cup mixed berries (strawberries, blueberries, raspberries)",
          "1 tablespoon honey or maple syrup",
          "1/4 cup low-sugar granola",
          "1 tablespoon chia seeds",
          "1/2 tablespoon almond butter"
        ],
        instructions: [
          "Add Greek yogurt to a bowl",
          "Top with mixed berries",
          "Drizzle with honey or maple syrup",
          "Sprinkle granola and chia seeds on top",
          "Add a dollop of almond butter",
          "Enjoy immediately for best texture"
        ],
        tags: ["high-protein", "quick", "vegetarian", "breakfast"]
      }
    }
  });

  const lunchTime = userPreferences.preferredMealTimes.lunch || '12:30';
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Mediterranean Chicken Salad',
    description: 'Grilled chicken with mixed greens, cherry tomatoes, cucumber, olives, and feta cheese with olive oil dressing',
    scheduledTime: `${dateString}T${lunchTime}`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        title: 'Mediterranean Chicken Salad',
        description: 'A refreshing and protein-rich salad featuring grilled chicken, fresh vegetables, olives, and feta cheese, dressed with olive oil and lemon. This Mediterranean-inspired dish provides lean protein and healthy fats.',
        nutrition: {
          calories: 380,
          protein: 32,
          carbs: 14,
          fat: 22
        },
        ingredients: [
          "4 oz grilled chicken breast, sliced",
          "2 cups mixed greens",
          "1/2 cup cherry tomatoes, halved",
          "1/4 cucumber, sliced",
          "10 kalamata olives, pitted",
          "2 tablespoons feta cheese, crumbled",
          "1 tablespoon extra virgin olive oil",
          "1/2 tablespoon lemon juice",
          "1/4 teaspoon dried oregano",
          "Salt and pepper to taste"
        ],
        instructions: [
          "Grill chicken breast until fully cooked, then slice",
          "In a large bowl, combine mixed greens, cherry tomatoes, cucumber, and olives",
          "Add sliced chicken on top",
          "Sprinkle with crumbled feta cheese",
          "In a small bowl, whisk together olive oil, lemon juice, oregano, salt, and pepper",
          "Drizzle dressing over the salad just before serving"
        ],
        tags: ["high-protein", "low-carb", "mediterranean", "lunch"]
      }
    }
  });

  const dinnerTime = userPreferences.preferredMealTimes.dinner || '19:00';
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Baked Salmon with Roasted Vegetables',
    description: 'Herb-crusted salmon fillet with roasted sweet potatoes, broccoli, and bell peppers',
    scheduledTime: `${dateString}T${dinnerTime}`,
    completed: false,
    isUserOverride: false,
    metadata: {
      mealDetails: {
        title: 'Baked Salmon with Roasted Vegetables',
        description: 'A nutritious dinner featuring herb-crusted salmon and colorful roasted vegetables. This meal provides omega-3 fatty acids, quality protein, and a variety of vitamins and minerals from the vegetables.',
        nutrition: {
          calories: 450,
          protein: 35,
          carbs: 30,
          fat: 22
        },
        ingredients: [
          "5 oz salmon fillet",
          "1 tablespoon olive oil, divided",
          "1 teaspoon dried herbs (thyme, rosemary, parsley)",
          "1 small sweet potato, cubed",
          "1 cup broccoli florets",
          "1/2 bell pepper, sliced",
          "1/2 small red onion, sliced",
          "1 clove garlic, minced",
          "1/2 lemon",
          "Salt and pepper to taste"
        ],
        instructions: [
          "Preheat oven to 400°F (200°C)",
          "Toss sweet potato, broccoli, bell pepper, and onion with half the olive oil, garlic, salt, and pepper",
          "Spread vegetables on a baking sheet and roast for 10 minutes",
          "Meanwhile, pat salmon dry and season with salt, pepper, and dried herbs",
          "Push vegetables to one side of the baking sheet and add salmon",
          "Drizzle salmon with remaining olive oil",
          "Return to oven and bake for 12-15 minutes until salmon is cooked through",
          "Squeeze fresh lemon over the salmon before serving"
        ],
        tags: ["high-protein", "omega-3", "gluten-free", "dinner"]
      }
    }
  });

  // Add snacks if user prefers them
  if (userPreferences.snacks) {
    // Morning snack
    const morningSnackTime = averageTime(breakfastTime, lunchTime);
    activities.push({
      id: generateUUID(),
      type: DigestActivityType.MEAL,
      title: 'Apple with Almond Butter',
      description: 'Sliced apple with a tablespoon of almond butter for sustained energy',
      scheduledTime: `${dateString}T${morningSnackTime}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        mealDetails: {
          title: 'Apple with Almond Butter',
          description: 'A simple yet satisfying snack that combines the natural sweetness of apple with protein and healthy fats from almond butter. This combination provides sustained energy and helps maintain blood sugar levels between meals.',
          nutrition: {
            calories: 180,
            protein: 4,
            carbs: 22,
            fat: 10
          },
          ingredients: [
            "1 medium apple",
            "1 tablespoon almond butter",
            "Dash of cinnamon (optional)"
          ],
          instructions: [
            "Wash and slice the apple into wedges",
            "Spread or dip with almond butter",
            "Sprinkle with cinnamon if desired"
          ],
          tags: ["quick", "vegan", "gluten-free", "low-calorie", "snack"]
        }
      }
    });

    // Afternoon snack
    const afternoonSnackTime = averageTime(lunchTime, dinnerTime);
    activities.push({
      id: generateUUID(),
      type: DigestActivityType.MEAL,
      title: 'Greek Yogurt with Honey and Walnuts',
      description: 'Protein-rich Greek yogurt topped with honey and chopped walnuts',
      scheduledTime: `${dateString}T${afternoonSnackTime}`,
      completed: false,
      isUserOverride: false,
      metadata: {
        mealDetails: {
          title: 'Greek Yogurt with Honey and Walnuts',
          description: 'A protein-packed snack that satisfies sweet cravings while providing nutrition. The Greek yogurt offers protein, the honey provides natural sweetness, and the walnuts add healthy fats and a satisfying crunch.',
          nutrition: {
            calories: 200,
            protein: 15,
            carbs: 16,
            fat: 8
          },
          ingredients: [
            "3/4 cup Greek yogurt (plain, 2% fat)",
            "1 teaspoon honey",
            "1 tablespoon chopped walnuts",
            "Dash of cinnamon (optional)"
          ],
          instructions: [
            "Place Greek yogurt in a small bowl",
            "Drizzle with honey",
            "Top with chopped walnuts",
            "Sprinkle with cinnamon if desired"
          ],
          tags: ["high-protein", "quick", "vegetarian", "gluten-free", "snack"]
        }
      }
    });
  }

  // Add water reminders if user wants them
  if (userPreferences.waterReminders) {
    // Add multiple water reminders throughout the day
    const wakeTime = isWeekend && userPreferences.differentWeekendSchedule
      ? userPreferences.weekendWakeUpTime
      : userPreferences.wakeUpTime;

    const bedTime = isWeekend && userPreferences.differentWeekendSchedule
      ? userPreferences.weekendBedTime
      : userPreferences.bedTime;

    // Convert times to hours for calculation
    const wakeHour = parseInt(wakeTime.split(':')[0]);
    const bedHour = parseInt(bedTime.split(':')[0]);

    // Calculate active hours
    let activeHours = bedHour - wakeHour;
    if (activeHours <= 0) activeHours += 24; // Handle cases like 7am to 11pm

    // Add water reminders every ~3 hours during waking hours
    const reminderCount = Math.floor(activeHours / 3);

    for (let i = 0; i < reminderCount; i++) {
      const hourOffset = Math.round((i + 1) * (activeHours / (reminderCount + 1)));
      let reminderHour = wakeHour + hourOffset;
      if (reminderHour >= 24) reminderHour -= 24;

      const reminderTime = `${reminderHour.toString().padStart(2, '0')}:00`;

      activities.push({
        id: generateUUID(),
        type: DigestActivityType.WATER,
        title: 'Hydration Reminder',
        description: 'Drink 16oz of water',
        scheduledTime: `${dateString}T${reminderTime}:00`,
        completed: false,
        isUserOverride: false
      });
    }
  }

  // Add sleep reminder based on user's bedtime
  const sleepReminderTime = isWeekend && userPreferences.differentWeekendSchedule
    ? userPreferences.weekendBedTime
    : userPreferences.bedTime;

  activities.push({
    id: generateUUID(),
    type: DigestActivityType.SLEEP,
    title: 'Sleep Reminder',
    description: 'Prepare for bed to get 8 hours of sleep',
    scheduledTime: `${dateString}T${sleepReminderTime}:00`,
    completed: false,
    isUserOverride: false
  });

  // Sort activities by scheduled time
  return activities.sort((a, b) =>
    new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
  );
}

// Helper function to calculate the average time between two times
function averageTime(time1: string, time2: string): string {
  const [hours1, minutes1] = time1.split(':').map(Number);
  const [hours2, minutes2] = time2.split(':').map(Number);

  // Convert to minutes since midnight
  const totalMinutes1 = hours1 * 60 + minutes1;
  const totalMinutes2 = hours2 * 60 + minutes2;

  // Calculate average minutes
  let avgMinutes = Math.floor((totalMinutes1 + totalMinutes2) / 2);

  // Handle case where times cross midnight
  if (Math.abs(totalMinutes1 - totalMinutes2) > 12 * 60) {
    avgMinutes = (totalMinutes1 + totalMinutes2 + 24 * 60) / 2 % (24 * 60);
  }

  // Convert back to hours and minutes
  const avgHours = Math.floor(avgMinutes / 60);
  const avgMins = avgMinutes % 60;

  // Format as HH:MM
  return `${avgHours.toString().padStart(2, '0')}:${avgMins.toString().padStart(2, '0')}`;
}

/**
 * Map API activity type to client enum
 */
function mapActivityType(type: string): DigestActivityType {
  switch (type?.toLowerCase()) {
    case 'workout':
      return DigestActivityType.WORKOUT;
    case 'meal':
      return DigestActivityType.MEAL;
    case 'water':
      return DigestActivityType.WATER;
    case 'sleep':
      return DigestActivityType.SLEEP;
    case 'reminder':
      return DigestActivityType.REMINDER;
    default:
      return DigestActivityType.OTHER;
  }
}

/**
 * Get digest data for a range of dates
 * @param startDate The start date of the range
 * @param endDate The end date of the range
 * @param isAppInitializing Optional flag to indicate if this is being called during app initialization
 * @returns An array of day digests
 */
export async function getDigestRange(startDate: Date, endDate: Date, isAppInitializing: boolean = false): Promise<DayDigest[]> {
  try {
    const userId = await getUserId();

    if (!userId) {
      throw new Error('User ID not found');
    }

    const startDateString = format(startDate, 'yyyy-MM-dd');
    const endDateString = format(endDate, 'yyyy-MM-dd');

    try {
      // Fetch digest range from the API
      let apiUrl = await getApiUrl();
      apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

      console.log(`[Digest Service] Fetching digest range from: ${apiUrl}/digest?startDate=${startDateString}&endDate=${endDateString}`);

      // Set up a timeout controller for the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 20000); // 20-second timeout for range query

      try {
        const response = await fetch(`${apiUrl}/digest?startDate=${startDateString}&endDate=${endDateString}`, {
          method: 'GET',
          headers: {
            ...await getDigestAuthHeader(),
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if fetch completes

        console.log(`[Digest Service] Range response status: ${response.status}`);

        if (!response.ok) {
          // Try to get more detailed error information
          let errorDetails = response.statusText;
          try {
            const errorData = await response.json();
            errorDetails = JSON.stringify(errorData);
          } catch (e) {
            // If we can't parse the error response, just use the status text
          }

          throw new Error(`Failed to fetch digest range: ${response.status} - ${errorDetails}`);
        }

        const digestsData = await response.json();

        if (!Array.isArray(digestsData)) {
          throw new Error(`Invalid digest range response: expected array, got ${typeof digestsData}`);
        }

        console.log(`[Digest Service] Received ${digestsData.length} digests from API`);

        // Get user join date for proper filtering
        const userJoinDate = await getUserJoinDate();

        // Transform the API response to match our client-side model
        const digests: DayDigest[] = digestsData.map((digestData: any) => {
          const date = new Date(digestData.date);
          const isPast = isPastDate(date);
          const isBeforeJoin = userJoinDate && isBefore(date, userJoinDate);

          return {
            date: digestData.date,
            activities: digestData.activities ? digestData.activities.map((activity: any) => ({
              ...activity,
              type: mapActivityType(activity.type),
            })) : [],
            noDataReason: isBeforeJoin
              ? 'before-join'
              : (isPast && (!digestData.activities || digestData.activities.length === 0) ? 'no-data-collected' : undefined)
          };
        });

        // Check if we got all the dates we requested
        const expectedDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        if (digests.length < expectedDays) {
          console.log(`[Digest Service] Warning: Expected ${expectedDays} days but got ${digests.length}`);
          // Continue with what we have - the fallback will handle missing days
        }

        return digests;
      } catch (fetchError) {
        // Handle the AbortController timeout or other fetch errors
        clearTimeout(timeoutId); // Ensure timeout is cleared
        console.error(`[Digest Service] Fetch error for date range:`, fetchError);
  
        if ((fetchError as Error).name === 'AbortError') { // Added type assertion
          console.error('[Digest Service] Range request timed out');
          throw new Error('Request timed out while fetching digest range');
        }

        // Re-throw the error for the outer catch block to handle
        throw fetchError;
      }
    } catch (error) {
      console.error('[Digest Service] Error fetching digest range from API:', error);
      throw error; // Let the outer catch handle the fallback
    }
  } catch (error) {
    console.error('[Digest Service] Falling back to individual day fetches:', error);

    // Fallback to fetching individual days
    const digests: DayDigest[] = [];
    let currentDate = new Date(startDate);

    console.log(`[Digest Service] Fetching individual days from ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`);

    while (currentDate <= endDate) {
      try {
        // Pass the initialization flag to getDayDigest
        const digest = await getDayDigest(new Date(currentDate), isAppInitializing);
        digests.push(digest);
      } catch (dayError) {
        console.error(`[Digest Service] Error fetching digest for ${format(currentDate, 'yyyy-MM-dd')}:`, dayError);

        // During app initialization, generate activities faster without API saves
        if (isAppInitializing && !isPastDate(currentDate)) {
          console.log(`[Digest Service] Fast-tracking activities for ${format(currentDate, 'yyyy-MM-dd')} during initialization`);
          const activities = await generateBasicActivities(currentDate);
          digests.push({
            date: format(currentDate, 'yyyy-MM-dd'),
            activities,
            noDataReason: 'generated-locally' as 'generated-locally'
          });
        } else {
          // Add an empty digest as a last resort for past dates or non-init mode
          digests.push({
            date: format(currentDate, 'yyyy-MM-dd'),
            activities: isPastDate(currentDate) ? [] : await generateBasicActivities(currentDate),
            noDataReason: isPastDate(currentDate) ? 'no-data-collected' : 'generated-locally' as 'generated-locally'
          });
        }
      }

      currentDate = addDays(currentDate, 1);
    }

    return digests;
  }
}

/**
 * Save a user override for an activity
 * @param originalActivity The original activity being overridden
 * @param overrideActivity The new activity data
 * @returns True if the override was saved successfully
 */
export async function saveActivityOverride(
  originalActivity: DigestActivity,
  overrideActivity: Partial<DigestActivity>
): Promise<boolean> {
  try {
    const userId = await getUserId();
    if (!userId) {
      console.error('Cannot save override: No user ID');
      return false;
    }

    // Create the override activity
    const override: DigestActivity = {
      ...originalActivity,
      ...overrideActivity,
      id: generateUUID(),
      isUserOverride: true,
      originalActivity: originalActivity
    };

    // Extract the date from the scheduled time
    const digestDate = format(parseISO(override.scheduledTime), 'yyyy-MM-dd');

    // First, get the current digest for this date
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

    const response = await fetch(`${apiUrl}/digest/${digestDate}`, {
      method: 'GET',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch digest: ${response.statusText}`);
    }

    const digestData = await response.json();

    // Find and replace the activity in the digest
    const activities = digestData.activities.map((activity: any) => {
      if (activity.id === originalActivity.id) {
        // Convert client-side enum to string for API
        const apiOverride = {
          ...override,
          type: override.type.toString().toLowerCase()
        };

        // Remove circular reference before sending to API
        delete apiOverride.originalActivity;

        return apiOverride;
      }
      return activity;
    });

    // Update the digest with the new activities
    const updateResponse = await fetch(`${apiUrl}/digest/${digestDate}`, {
      method: 'PUT',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ activities }),
    });

    if (!updateResponse.ok) {
      throw new Error(`Failed to update digest: ${updateResponse.statusText}`);
    }

    // Also save to context for learning user preferences using V3 engine
    try {
      const { contextEngineV3, ContextType } = require('./contextEngineV3');
      await contextEngineV3.updateContext(
        ContextType.CUSTOM,
        override,
        {
          source: 'user_override',
          category: 'digest_override',
          digestDate,
          activityType: override.type,
          originalActivityId: originalActivity.id
        }
      );
    } catch (contextError) {
      console.error('[saveDigestOverride] Error saving to context:', contextError);
    }

    return true;
  } catch (error) {
    console.error('Error saving activity override:', error);
    return false;
  }
}

/**
 * Delete a user override for an activity
 * @param overrideId The ID of the override to delete
 * @param date The date of the digest containing the activity
 * @returns True if the override was deleted successfully
 */
export async function deleteActivityOverride(overrideId: string, date: Date): Promise<boolean> {
  try {
    const userId = await getUserId();
    if (!userId) {
      console.error('Cannot delete override: No user ID');
      return false;
    }

    const dateString = format(date, 'yyyy-MM-dd');

    // First, get the current digest for this date
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

    const response = await fetch(`${apiUrl}/digest/${dateString}`, {
      method: 'GET',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch digest: ${response.statusText}`);
    }

    const digestData = await response.json();

    // Find the activity to delete
    const activityToDelete = digestData.activities.find((activity: any) => activity.id === overrideId);

    if (!activityToDelete) {
      console.error(`Activity with ID ${overrideId} not found`);
      return false;
    }

    // If this is an override and has an original activity, restore the original
    let activities;
    if (activityToDelete.isUserOverride && activityToDelete.originalActivity) {
      // Replace with the original activity
      activities = digestData.activities.map((activity: any) => {
        if (activity.id === overrideId) {
          return activityToDelete.originalActivity;
        }
        return activity;
      });
    } else {
      // Just remove the activity
      activities = digestData.activities.filter((activity: any) => activity.id !== overrideId);
    }

    // Update the digest with the new activities
    const updateResponse = await fetch(`${apiUrl}/digest/${dateString}`, {
      method: 'PUT',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ activities }),
    });

    if (!updateResponse.ok) {
      throw new Error(`Failed to update digest: ${updateResponse.statusText}`);
    }

    return true;
  } catch (error) {
    console.error('Error deleting activity override:', error);
    return false;
  }
}

/**
 * Create a new digest on the API for the specified date
 */
export async function createDigestOnAPI(dateString: string, isRetry = false): Promise<DayDigest> {
  try {
    const userId = await getUserId();
    if (!userId) {
      console.error('[Digest Service] User ID not found');
      throw new Error('User ID not found');
    }

    console.log(`[Digest Service] Creating digest for date: ${dateString}, userId: ${userId}, isRetry: ${isRetry}`);

    // Get API URL and auth header
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
    console.log(`[Digest Service] Using API URL: ${apiUrl}`);
    
    const authHeader = await getDigestAuthHeader();
    console.log(`[Digest Service] Digest auth header present: ${!!authHeader}`);

    console.log(`[Digest Service] Calling regenerate API for ${dateString}`);

    // Set up with a longer timeout for this operation
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 seconds

    try {
      // Create a properly typed headers object
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // Add auth headers if available
      if (authHeader) {
        Object.assign(headers, authHeader);
      }
      
      // Add retry headers if needed
      if (isRetry) {
        headers['X-Retry-Attempt'] = 'true';
        headers['X-Auth-Protocol-Version'] = '2';
      }

      const requestBody = {
        forceRegenerate: true,
        bypassCache: isRetry,
        includeContext: true,
        refreshContext: true,
        // Add explicit context data to the request
        contextData: undefined as any
      };
      
      // Try to get and include context data directly in the request using V3 engine
      try {
        console.log(`[Digest Service] Attempting to include context data in API request...`);
        const { contextEngineV3, ContextType } = require('./contextEngineV3');

        const contextEntries = await contextEngineV3.getContextEntries({
          types: [ContextType.PREFERENCES, ContextType.CUSTOM],
          activeOnly: true
        });

        console.log(`[Digest Service] Found ${contextEntries.length} context entries`);

        // Look for daily preferences for today
        const dailyPrefs = contextEntries.find((item: any) =>
          item.category === 'daily_preferences' &&
          item.metadata?.date === dateString
        );
        
        if (dailyPrefs) {
          console.log(`[Digest Service] Found daily preferences for ${dateString}:`, dailyPrefs);
          requestBody.contextData = {
            dailyPreferences: dailyPrefs,
            allPreferences: contextEntries.filter((e: any) => e.type === 'preferences').slice(0, 5),
            allCustom: contextEntries.filter((e: any) => e.type === 'custom').slice(0, 5)
          };
        } else {
          console.log(`[Digest Service] No daily preferences found for ${dateString}`);
          requestBody.contextData = {
            allPreferences: contextEntries.filter((e: any) => e.type === 'preferences').slice(0, 5),
            allCustom: contextEntries.filter((e: any) => e.type === 'custom').slice(0, 5)
          };
        }
        
        console.log(`[Digest Service] Context data to send:`, JSON.stringify(requestBody.contextData, null, 2));
      } catch (contextError) {
        console.error(`[Digest Service] Error getting context data for API request:`, contextError);
      }
      
      console.log(`[Digest Service] Request headers:`, JSON.stringify(headers));
      console.log(`[Digest Service] Request body:`, JSON.stringify(requestBody));

      // Make an HTTP request to the regenerate endpoint with retries
      const response = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log(`[Digest Service] Response status: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        console.error(`[Digest Service] API error: ${response.status} ${response.statusText}`);
        
        // Try to get error details from response
        try {
          const errorData = await response.json();
          console.error(`[Digest Service] API error details:`, JSON.stringify(errorData));
        } catch (parseError) {
          console.error(`[Digest Service] Could not parse error response:`, parseError);
        }
        
        console.log(`[Digest Service] API failed, falling back to local generation due to ${response.status} error`);
        throw new Error(`API error: ${response.status}`);
      }

      let digest;
      try {
        const responseText = await response.text();
        console.log(`[Digest Service] Raw response text (first 500 chars):`, responseText.substring(0, 500));
        
        digest = JSON.parse(responseText);
        console.log(`[Digest Service] Response body type: ${typeof digest}`);
        console.log(`[Digest Service] Response has activities: ${!!(digest && digest.activities)}`);
        
        // Log the full response structure for debugging
        console.log(`[Digest Service] Full response structure:`, JSON.stringify(digest, null, 2));
        
        if (digest && digest.activities) {
          console.log(`[Digest Service] Activities array length: ${digest.activities.length}`);
          
          // Log the first few activities to inspect their structure
          const sampleSize = Math.min(digest.activities.length, 3);
          for (let i = 0; i < sampleSize; i++) {
            console.log(`[Digest Service] Sample activity ${i}:`, JSON.stringify(digest.activities[i], null, 2));
          }
          
          // Check if activities look like mock data
          const mockDataIndicators = ['Balanced', 'Generic', 'Sample', 'Mock'];
          const hasMockData = digest.activities.some((activity: any) => 
            mockDataIndicators.some(indicator => activity.title?.includes(indicator))
          );
          
          if (hasMockData) {
            console.warn(`[Digest Service] ⚠️ Response appears to contain mock data!`);
          } else {
            console.log(`[Digest Service] ✅ Response appears to contain real data`);
          }
        } else {
          console.warn(`[Digest Service] No activities found in response`);
        }
      } catch (parseError) {
        console.error(`[Digest Service] Failed to parse API response as JSON:`, parseError);
        throw new Error('Invalid API response format');
      }
      
              // Check if we got a valid digest with activities
        if (digest && digest.activities && digest.activities.length > 0) {
          console.log(`[Digest Service] Successfully regenerated digest with ${digest.activities.length} activities`);
          
          // Check if the response contains mock data indicators
          const mockDataIndicators = ['Balanced', 'Generic', 'Sample', 'Mock'];
          const hasMockData = digest.activities.some((activity: any) => 
            mockDataIndicators.some(indicator => activity.title?.includes(indicator))
          );
          
          if (hasMockData) {
            console.warn('[Digest Service] ⚠️ API returned mock data, falling back to local generation');
            return await generateLocalFallbackDigest(userId, dateString);
          }
          
          // Map the activities to ensure proper type conversion
          const typedActivities = digest.activities.map((activity: any) => {
            const mappedType = mapActivityType(activity.type);
            console.log(`[Digest Service] Mapped activity type: ${activity.type} -> ${mappedType}`);
            return {
              ...activity,
              type: mappedType
            };
          });
          
          console.log(`[Digest Service] Final activities count: ${typedActivities.length}`);
          
          // Return a properly formatted DayDigest
          return {
            date: dateString,
            activities: typedActivities
          };
        } else {
          console.warn('[Digest Service] Received empty activities from API, falling back to local generation');
          return await generateLocalFallbackDigest(userId, dateString);
        }
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('[Digest Service] Error calling API:', error);
      
      // If this was already a retry attempt or we can't reach the server, generate locally
      if (isRetry || (error instanceof Error && (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')))) {
        console.log('[Digest Service] Using local fallback after failed API call');
        return await generateLocalFallbackDigest(userId, dateString);
      }
      
      // If this is the first attempt and we encounter an error, retry once
      if (!isRetry) {
        console.log('[Digest Service] Retrying with special headers...');
        return await createDigestOnAPI(dateString, true);
      }
      
      throw error; // Let the caller handle retry logic
    }
  } catch (error) {
    console.error('[Digest Service] createDigestOnAPI error:', error);
    
    // Last resort fallback - generate digest locally
    const userId = await getUserId();
    return await generateLocalFallbackDigest(userId || 'unknown', dateString);
  }
}

/**
 * Generate a fallback digest locally when API fails
 */
async function generateLocalFallbackDigest(userId: string, dateString: string): Promise<DayDigest> {
  console.log(`[Digest Service] Generating local fallback digest for ${dateString}`);
  
  try {
    console.log(`[Digest Service] Starting basic activities generation for ${dateString}`);
    // Generate basic activities for the day - ensure we await the promise
    const activities = await generateBasicActivities(new Date(dateString));
    
    console.log(`[Digest Service] Generated ${activities.length} local fallback activities successfully`);
    console.log(`[Digest Service] Activity types: ${activities.map(a => a.type).join(', ')}`);
    
    // Check that we have some activities
    if (!activities || activities.length === 0) {
      console.error(`[Digest Service] Generated activities array is empty`);
      throw new Error('Generated activities array is empty');
    }
    
    const result = {
      date: dateString,
      activities: activities,
      noDataReason: 'generated-locally' as 'generated-locally'
    };
    
    console.log(`[Digest Service] Returning locally generated digest with ${activities.length} activities`);
    return result;
  } catch (error) {
    console.error('[Digest Service] Error generating local fallback:', error);
    
    // Ultra-minimal fallback with a few guaranteed activities
    console.log(`[Digest Service] Creating ultra-minimal digest with guaranteed activities`);
    
    // Try to get user wake-up time for minimal fallback
    let wakeUpTime = '08:00';
    try {
      console.log('[generateLocalFallbackDigest] Trying to get wake-up time for minimal fallback');
      const { contextEngineV3, ContextType } = require('./contextEngineV3');

      // Check both CUSTOM and PREFERENCE context types using V3 engine
      const contextData = await contextEngineV3.getContextEntries({
        types: [ContextType.CUSTOM, ContextType.PREFERENCES],
        activeOnly: true
      });

      console.log('[generateLocalFallbackDigest] Context data:', JSON.stringify(contextData, null, 2));

      let dailyPrefs = contextData.find((item: any) =>
        item.category === 'daily_preferences'
      );
      
      console.log('[generateLocalFallbackDigest] Daily preferences found:', dailyPrefs);
      
      if (dailyPrefs?.metadata?.wakeUpTime) {
        wakeUpTime = dailyPrefs.metadata.wakeUpTime;
        console.log('[generateLocalFallbackDigest] Using wake-up time from metadata:', wakeUpTime);
      } else if (dailyPrefs?.value && typeof dailyPrefs.value === 'string') {
        const wakeUpTimeMatch = dailyPrefs.value.match(/(?:i|I)\s*(?:wake|get)\s*(?:up|at)\s*(?:at)?\s*(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
        if (wakeUpTimeMatch) {
          let hours = parseInt(wakeUpTimeMatch[1]);
          const minutes = wakeUpTimeMatch[2] ? parseInt(wakeUpTimeMatch[2]) : 0;
          const period = wakeUpTimeMatch[3]?.toLowerCase();

          if (period === 'pm' && hours < 12) {
            hours += 12;
          } else if (period === 'am' && hours === 12) {
            hours = 0;
          }

          wakeUpTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          console.log('[generateLocalFallbackDigest] Extracted wake-up time from text:', wakeUpTime);
        }
      }
    } catch (error) {
      console.log('[generateLocalFallbackDigest] Could not get wake-up time for minimal fallback, using default:', error);
    }
    
    // Calculate times based on wake-up time
    const [wakeHour, wakeMinute] = wakeUpTime.split(':').map(Number);
    const breakfastTime = `${(wakeHour + 1).toString().padStart(2, '0')}:${wakeMinute.toString().padStart(2, '0')}`;
    const lunchTime = `${(wakeHour + 5).toString().padStart(2, '0')}:${wakeMinute.toString().padStart(2, '0')}`;
    const workoutTime = `${(wakeHour + 9).toString().padStart(2, '0')}:${wakeMinute.toString().padStart(2, '0')}`;
    const waterTime = `${(wakeHour + 6).toString().padStart(2, '0')}:${wakeMinute.toString().padStart(2, '0')}`;
    
    const minimalActivities = [
      {
        id: generateId(),
        type: DigestActivityType.REMINDER,
        title: 'Morning Routine',
        description: 'Start your day with intention and energy.',
        scheduledTime: `${dateString}T${breakfastTime}:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.MEAL,
        title: 'Healthy Lunch',
        description: 'Focus on protein and vegetables for sustained energy.',
        scheduledTime: `${dateString}T${lunchTime}:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.WORKOUT,
        title: 'Daily Exercise',
        description: 'Even 20 minutes of movement improves your health.',
        scheduledTime: `${dateString}T${workoutTime}:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.WATER,
        title: 'Stay Hydrated',
        description: 'Remember to drink water throughout the day.',
        scheduledTime: `${dateString}T${waterTime}:00`,
        completed: false,
        isUserOverride: false,
      }
    ];
    
    console.log(`[Digest Service] Created ${minimalActivities.length} minimal activities for emergency fallback`);
    
    return {
      date: dateString,
      activities: minimalActivities,
      noDataReason: 'generated-locally' as 'generated-locally'
    };
  }
}

/**
 * Generate a unique ID for new activities
 */
function generateId(): string {
  return 'act_' + Math.random().toString(36).substring(2, 11);
}

/**
 * Save a digest to the API
 * @param dateString The date string in YYYY-MM-DD format
 * @param activities The activities to save
 */
export async function saveDigestToAPI(dateString: string, activities: DigestActivity[]): Promise<void> {
  try {
    const userId = await getUserId();
    if (!userId) {
      throw new Error('User ID not found');
    }

    // Convert client-side activities to API format
    const apiActivities = activities.map(activity => ({
      ...activity,
      type: activity.type.toString().toLowerCase(),
      // Remove any circular references
      originalActivity: undefined
    }));

    // Try to get the existing digest first
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

    const getResponse = await fetch(`${apiUrl}/digest/${dateString}`, {
      method: 'GET',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
    });

    if (getResponse.ok) {
      // Digest exists, update it
      const updateResponse = await fetch(`${apiUrl}/digest/${dateString}`, {
        method: 'PUT',
        headers: {
          ...await getDigestAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ activities: apiActivities }),
      });

      if (!updateResponse.ok) {
        throw new Error(`Failed to update digest: ${updateResponse.statusText}`);
      }
    } else if (getResponse.status === 404) {
      // Digest doesn't exist, create it
      const createResponse = await fetch(`${apiUrl}/digest`, {
        method: 'POST',
        headers: {
          ...await getDigestAuthHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: dateString,
          activities: apiActivities
        }),
      });

      if (!createResponse.ok) {
        throw new Error(`Failed to create digest: ${createResponse.statusText}`);
      }
    } else {
      throw new Error(`Failed to check digest existence: ${getResponse.statusText}`);
    }
  } catch (error) {
    console.error('[Digest Service] Error saving digest to API:', error);
    throw error;
  }
}

/**
 * Get all user overrides for a specific date
 * @param date The date to get overrides for
 * @returns An array of override activities
 */
async function getDigestOverrides(date: Date): Promise<DigestActivity[]> {
  try {
    const userId = await getUserId();
    if (!userId) {
      console.error('Cannot get overrides: No user ID');
      return [];
    }

    const dateString = format(date, 'yyyy-MM-dd');

    // Get the digest for this date, which will include any overrides
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;

    const response = await fetch(`${apiUrl}/digest/${dateString}`, {
      method: 'GET',
      headers: {
        ...await getDigestAuthHeader(),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      // If the digest doesn't exist yet, there are no overrides
      if (response.status === 404) {
        return [];
      }
      throw new Error(`Failed to fetch digest: ${response.statusText}`);
    }

    const digestData = await response.json();

    // Filter to only include activities that are marked as user overrides
    return digestData.activities
      .filter((activity: any) => activity.isUserOverride)
      .map((activity: any) => ({
        ...activity,
        type: mapActivityType(activity.type),
      }));
  } catch (error) {
    console.error('Error getting digest overrides:', error);

    // Fallback to context-based overrides if API fails
    try {
      const dateString = format(date, 'yyyy-MM-dd');

      // Get all digest overrides from context using V3 engine
      const { contextEngineV3, ContextType } = require('./contextEngineV3');
      const allOverrides = await contextEngineV3.getContextEntries({
        types: [ContextType.CUSTOM],
        activeOnly: true
      });

      // Filter to only include overrides for the specified date
      return allOverrides
        .filter((context: any) => {
          const digestDate = context.metadata?.digestDate;
          return digestDate === dateString && context.category === 'digest_override';
        })
        .map((context: any) => context.value as DigestActivity);
    } catch (fallbackError) {
      console.error('Error getting fallback digest overrides:', fallbackError);
      return [];
    }
  }
}

/**
 * Generate personalized recommendations for a date based on user context
 * @param date The date to generate recommendations for
 * @returns An array of personalized recommended activities
 */
async function generatePersonalizedRecommendations(date: Date): Promise<DigestActivity[]> {
  try {
    // In a real implementation, this would:
    // 1. Analyze user context data (preferences, goals, history)
    // 2. Consider workout splits and patterns
    // 3. Account for dietary preferences and restrictions
    // 4. Adjust for weather and other external factors
    // 5. Incorporate any natural language instructions from chat

    // For now, we'll simulate personalized recommendations based on:
    // - Day of the week (for workout split)
    // - User preferences (simulated)
    // - Time of day patterns

    const activities: DigestActivity[] = [];
    const dateString = format(date, 'yyyy-MM-dd');
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const now = new Date();

    // Simulate fetching user preferences
    const userPreferences = await getUserPreferences();

    // Generate workout based on split pattern
    await addWorkoutBasedOnSplit(activities, date, dayOfWeek, userPreferences);

    // Add meals based on user preferences
    await addMealsBasedOnPreferences(activities, date, userPreferences);

    // Add water reminders
    activities.push({
      id: generateUUID(),
      type: DigestActivityType.WATER,
      title: 'Hydration Reminder',
      description: `Drink ${userPreferences.waterIntakeGoal} of water`,
      scheduledTime: `${dateString}T10:00`,
      completed: isSameDay(date, now) && now.getHours() > 10,
      isUserOverride: false
    });

    activities.push({
      id: generateUUID(),
      type: DigestActivityType.WATER,
      title: 'Afternoon Hydration',
      description: `Drink ${userPreferences.waterIntakeGoal} of water`,
      scheduledTime: `${dateString}T15:00`,
      completed: isSameDay(date, now) && now.getHours() > 15,
      isUserOverride: false
    });

    // Add sleep reminder based on user's preferred sleep time
    const sleepTime = userPreferences.preferredSleepTime || '22:00';
    activities.push({
      id: generateUUID(),
      type: DigestActivityType.SLEEP,
      title: 'Sleep Reminder',
      description: `Prepare for bed to get ${userPreferences.sleepGoal} hours of sleep`,
      scheduledTime: `${dateString}T${sleepTime}`,
      completed: isSameDay(date, now) && now.getHours() > parseInt(sleepTime.split(':')[0]),
      isUserOverride: false
    });

    // Sort activities by scheduled time
    return activities.sort((a, b) =>
      new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
    );
  } catch (error) {
    console.error('Error generating personalized recommendations:', error);
    return [];
  }
}

/**
 * Get user preferences from context
 * @returns User preferences object
 */
async function getUserPreferences(): Promise<{
  workoutSplit: string;
  workoutTime: string;
  dietaryRestrictions: string[];
  mealPreferences: string[];
  waterIntakeGoal: string;
  sleepGoal: string;
  preferredSleepTime: string;
}> {
  try {
    // In a real implementation, this would fetch actual user preferences from context
    // For now, we'll return simulated preferences

    // Get context data related to workouts and meals using V3 engine
    const { contextEngineV3, ContextType } = require('./contextEngineV3');
    const workoutContexts = await contextEngineV3.getContextEntries({
      types: [ContextType.CUSTOM],
      activeOnly: true
    });
    const mealContexts = workoutContexts; // Same data source

    // Extract workout split from context if available
    let workoutSplit = 'full-body';
    let workoutTime = '17:30';

    // Look for workout split information in context
    for (const context of workoutContexts) {
      const value = context.value?.toLowerCase() || '';

      if (value.includes('push pull legs') || value.includes('ppl')) {
        workoutSplit = 'ppl';
        break;
      } else if (value.includes('upper lower') || value.includes('upper/lower')) {
        workoutSplit = 'upper-lower';
        break;
      } else if (value.includes('body part') || value.includes('bro split')) {
        workoutSplit = 'body-part';
        break;
      }

      // Check for workout time preferences
      if (value.includes('morning workout') || value.includes('workout in the morning')) {
        workoutTime = '07:30';
      } else if (value.includes('evening workout') || value.includes('workout in the evening')) {
        workoutTime = '17:30';
      } else if (value.includes('afternoon workout') || value.includes('workout in the afternoon')) {
        workoutTime = '15:00';
      }
    }

    // Extract dietary restrictions and preferences
    const dietaryRestrictions: string[] = [];
    const mealPreferences: string[] = [];

    for (const context of mealContexts) {
      const value = context.value?.toLowerCase() || '';

      // Check for dietary restrictions
      if (value.includes('vegetarian')) {
        dietaryRestrictions.push('vegetarian');
      } else if (value.includes('vegan')) {
        dietaryRestrictions.push('vegan');
      } else if (value.includes('gluten-free') || value.includes('gluten free')) {
        dietaryRestrictions.push('gluten-free');
      } else if (value.includes('dairy-free') || value.includes('dairy free')) {
        dietaryRestrictions.push('dairy-free');
      }

      // Check for meal preferences
      if (value.includes('high protein')) {
        mealPreferences.push('high-protein');
      } else if (value.includes('low carb')) {
        mealPreferences.push('low-carb');
      } else if (value.includes('keto')) {
        mealPreferences.push('keto');
      } else if (value.includes('mediterranean')) {
        mealPreferences.push('mediterranean');
      }
    }

    return {
      workoutSplit,
      workoutTime,
      dietaryRestrictions,
      mealPreferences,
      waterIntakeGoal: '16oz',
      sleepGoal: '8',
      preferredSleepTime: '22:00'
    };
  } catch (error) {
    console.error('Error getting user preferences:', error);

    // Return default preferences
    return {
      workoutSplit: 'full-body',
      workoutTime: '17:30',
      dietaryRestrictions: [],
      mealPreferences: [],
      waterIntakeGoal: '16oz',
      sleepGoal: '8',
      preferredSleepTime: '22:00'
    };
  }
}

/**
 * Add workout to activities based on user's split pattern
 * @param activities Array to add workout to
 * @param date The date to generate workout for
 * @param dayOfWeek Day of the week (0-6)
 * @param userPreferences User preferences object
 */
async function addWorkoutBasedOnSplit(
  activities: DigestActivity[],
  date: Date,
  dayOfWeek: number,
  userPreferences: any
): Promise<void> {
  const dateString = format(date, 'yyyy-MM-dd');
  const now = new Date();
  const workoutTime = userPreferences.workoutTime || '17:30';

  // Skip Sunday (day 0) as a rest day for most splits
  if (dayOfWeek === 0) {
    // Add a rest day or active recovery suggestion
    activities.push({
      id: generateUUID(),
      type: DigestActivityType.WORKOUT,
      title: 'Rest Day',
      description: 'Take a rest day or do light activity like walking or stretching',
      scheduledTime: `${dateString}T${workoutTime}`,
      completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
      isUserOverride: false
    });
    return;
  }

  // Generate workout based on split pattern
  switch (userPreferences.workoutSplit) {
    case 'ppl': // Push Pull Legs
      if (dayOfWeek % 3 === 1) { // Monday, Thursday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Push Day',
          description: 'Chest, shoulders, and triceps workout focusing on pushing movements',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      } else if (dayOfWeek % 3 === 2) { // Tuesday, Friday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Pull Day',
          description: 'Back and biceps workout focusing on pulling movements',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      } else if (dayOfWeek % 3 === 0 && dayOfWeek !== 0) { // Wednesday, Saturday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Legs Day',
          description: 'Leg workout focusing on quads, hamstrings, and calves',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      }
      break;

    case 'upper-lower': // Upper Lower split
      if (dayOfWeek === 1 || dayOfWeek === 3 || dayOfWeek === 5) { // Monday, Wednesday, Friday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Upper Body Workout',
          description: 'Comprehensive upper body training including chest, back, shoulders, and arms',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      } else if (dayOfWeek === 2 || dayOfWeek === 4 || dayOfWeek === 6) { // Tuesday, Thursday, Saturday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Lower Body Workout',
          description: 'Comprehensive lower body training including quads, hamstrings, glutes, and calves',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      }
      break;

    case 'body-part': // Body part split (bro split)
      switch (dayOfWeek) {
        case 1: // Monday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Chest Day',
            description: 'Focus on chest development with bench press, flyes, and dips',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
        case 2: // Tuesday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Back Day',
            description: 'Focus on back development with rows, pull-ups, and lat pulldowns',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
        case 3: // Wednesday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Legs Day',
            description: 'Focus on leg development with squats, lunges, and leg press',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
        case 4: // Thursday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Shoulders Day',
            description: 'Focus on shoulder development with presses, raises, and shrugs',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
        case 5: // Friday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Arms Day',
            description: 'Focus on arm development with bicep curls, tricep extensions, and forearm exercises',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
        case 6: // Saturday
          activities.push({
            id: generateUUID(),
            type: DigestActivityType.WORKOUT,
            title: 'Core & Cardio',
            description: 'Focus on core strength and cardiovascular endurance',
            scheduledTime: `${dateString}T${workoutTime}`,
            completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
            isUserOverride: false
          });
          break;
      }
      break;

    default: // Full body or other
      if (dayOfWeek === 1 || dayOfWeek === 3 || dayOfWeek === 5) { // Monday, Wednesday, Friday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Full Body Workout',
          description: 'Comprehensive full body training with compound movements',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      } else if (dayOfWeek === 2 || dayOfWeek === 6) { // Tuesday, Saturday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Cardio Session',
          description: '30-45 minutes of zone 2 cardio for heart health',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      } else if (dayOfWeek === 4) { // Thursday
        activities.push({
          id: generateUUID(),
          type: DigestActivityType.WORKOUT,
          title: 'Mobility & Recovery',
          description: 'Focus on mobility, flexibility, and active recovery',
          scheduledTime: `${dateString}T${workoutTime}`,
          completed: isSameDay(date, now) && now.getHours() > parseInt(workoutTime.split(':')[0]),
          isUserOverride: false
        });
      }
      break;
  }
}

/**
 * Add meals to activities based on user preferences
 * @param activities Array to add meals to
 * @param date The date to generate meals for
 * @param userPreferences User preferences object
 */
async function addMealsBasedOnPreferences(
  activities: DigestActivity[],
  date: Date,
  userPreferences: any
): Promise<void> {
  const dateString = format(date, 'yyyy-MM-dd');
  const now = new Date();
  const { dietaryRestrictions, mealPreferences } = userPreferences;

  // Helper to check if a meal matches dietary restrictions
  const matchesDietaryRestrictions = (meal: string): boolean => {
    if (dietaryRestrictions.includes('vegetarian') &&
        (meal.includes('chicken') || meal.includes('beef') || meal.includes('fish'))) {
      return false;
    }

    if (dietaryRestrictions.includes('vegan') &&
        (meal.includes('chicken') || meal.includes('beef') || meal.includes('fish') ||
         meal.includes('egg') || meal.includes('yogurt') || meal.includes('cheese'))) {
      return false;
    }

    if (dietaryRestrictions.includes('gluten-free') &&
        (meal.includes('bread') || meal.includes('pasta') || meal.includes('wheat'))) {
      return false;
    }

    if (dietaryRestrictions.includes('dairy-free') &&
        (meal.includes('milk') || meal.includes('yogurt') || meal.includes('cheese'))) {
      return false;
    }

    return true;
  };

  // Breakfast options based on preferences
  const breakfastOptions = [
    'Oatmeal with berries and a side of Greek yogurt',
    'Scrambled eggs with spinach and whole grain toast',
    'Protein smoothie with banana, spinach, and almond milk',
    'Avocado toast with poached eggs',
    'Greek yogurt parfait with granola and mixed berries'
  ].filter(matchesDietaryRestrictions);

  // Lunch options based on preferences
  const lunchOptions = [
    'Grilled chicken salad with mixed vegetables',
    'Quinoa bowl with roasted vegetables and chickpeas',
    'Turkey and avocado wrap with side salad',
    'Lentil soup with a side of whole grain bread',
    'Tuna salad with mixed greens and olive oil dressing'
  ].filter(matchesDietaryRestrictions);

  // Dinner options based on preferences
  const dinnerOptions = [
    'Baked salmon with roasted vegetables and quinoa',
    'Grilled chicken with sweet potato and steamed broccoli',
    'Stir-fried tofu with vegetables and brown rice',
    'Lean beef stir-fry with mixed vegetables',
    'Vegetable curry with brown rice'
  ].filter(matchesDietaryRestrictions);

  // Snack options based on preferences
  const snackOptions = [
    'Apple with almond butter',
    'Greek yogurt with honey',
    'Handful of mixed nuts',
    'Protein bar',
    'Carrot sticks with hummus'
  ].filter(matchesDietaryRestrictions);

  // Add breakfast
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Breakfast',
    description: breakfastOptions[Math.floor(Math.random() * breakfastOptions.length)],
    scheduledTime: `${dateString}T08:00`,
    completed: isSameDay(date, now) && now.getHours() > 8,
    isUserOverride: false
  });

  // Add lunch
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Lunch',
    description: lunchOptions[Math.floor(Math.random() * lunchOptions.length)],
    scheduledTime: `${dateString}T12:30`,
    completed: isSameDay(date, now) && now.getHours() > 12,
    isUserOverride: false
  });

  // Add afternoon snack
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Afternoon Snack',
    description: snackOptions[Math.floor(Math.random() * snackOptions.length)],
    scheduledTime: `${dateString}T15:30`,
    completed: isSameDay(date, now) && now.getHours() > 15,
    isUserOverride: false
  });

  // Add dinner
  activities.push({
    id: generateUUID(),
    type: DigestActivityType.MEAL,
    title: 'Dinner',
    description: dinnerOptions[Math.floor(Math.random() * dinnerOptions.length)],
    scheduledTime: `${dateString}T19:00`,
    completed: isSameDay(date, now) && now.getHours() > 19,
    isUserOverride: false
  });
}

/**
 * Get the user's join date
 * @returns The date when the user joined the app, or null if not available
 */
async function getUserJoinDate(): Promise<Date | null> {
  try {
    // In a real implementation, this would fetch the user's join date from the profile
    // For now, we'll use a hardcoded date (1 month ago)
    const userId = await getUserId();
    if (!userId) return null;

    // For demo purposes, set join date to 1 month ago
    const joinDate = new Date();
    joinDate.setMonth(joinDate.getMonth() - 1);
    joinDate.setHours(0, 0, 0, 0);

    return joinDate;
  } catch (error) {
    console.error('Error getting user join date:', error);
    return null;
  }
}

/**
 * Get actual historical activities for a past date (only completed activities)
 * @param date The date to get activities for
 * @returns An array of actual completed historical activities
 */
async function getActualHistoricalActivities(date: Date): Promise<DigestActivity[]> {
  try {
    // In a real implementation, this would fetch actual historical data from various sources:
    // 1. Workout logs
    // 2. Meal logs
    // 3. Water intake logs
    // 4. Sleep logs
    // 5. Other tracked activities

    // For now, we'll return an empty array to indicate no data was collected
    // This ensures we never show mock data for past dates
    return [];
  } catch (error) {
    console.error('Error getting historical activities:', error);
    return [];
  }
}

/**
 * Apply user overrides to a list of base activities
 * @param baseActivities The original activities
 * @param overrides The user overrides to apply
 * @returns The merged list of activities with overrides applied
 */
function applyOverrides(
  baseActivities: DigestActivity[],
  overrides: DigestActivity[]
): DigestActivity[] {
  if (!overrides || overrides.length === 0) {
    return baseActivities;
  }

  // Create a map of original activity IDs to their overrides
  const overrideMap = new Map<string, DigestActivity>();
  overrides.forEach(override => {
    if (override.originalActivity) {
      overrideMap.set(override.originalActivity.id, override);
    }
  });

  // Apply overrides and add any new activities
  const result: DigestActivity[] = [];

  // First, process base activities, replacing with overrides where applicable
  baseActivities.forEach(activity => {
    const override = overrideMap.get(activity.id);
    if (override) {
      result.push(override);
      overrideMap.delete(activity.id);
    } else {
      result.push(activity);
    }
  });

  // Then add any remaining overrides (these might be completely new activities)
  overrideMap.forEach(override => {
    result.push(override);
  });

  // Sort by scheduled time
  return result.sort((a, b) =>
    new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
  );
}

/**
 * Test utility function to directly try generating a digest with multiple methods.
 * This function bypasses the normal flow and tries all available methods
 * to help diagnose issues with the digest generation.
 */
export async function testDigestGeneration(dateString: string): Promise<{
  apiDigest: DayDigest | null;
  localDigest: DayDigest | null;
  fallbackDigest: DayDigest | null;
  errors: string[];
}> {
  console.log('===== DIGEST GENERATION TEST STARTED =====');
  console.log(`Testing digest generation for date: ${dateString}`);
  
  const results = {
    apiDigest: null as DayDigest | null,
    localDigest: null as DayDigest | null,
    fallbackDigest: null as DayDigest | null,
    errors: [] as string[]
  };
  
  // Step 1: Try the API directly with normal headers
  try {
    console.log('Step 1: Testing API generation with normal headers');
    const userId = await getUserId();
    if (!userId) {
      throw new Error('No user ID found');
    }
    
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
    
    const authHeader = await getDigestAuthHeader();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(authHeader || {})
    };
    
    console.log(`API URL: ${apiUrl}/digest/${dateString}/regenerate`);
    console.log('Request headers:', JSON.stringify(headers));
    
    const response = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        forceRegenerate: true,
        bypassCache: true
      })
    });
    
    console.log(`Response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`API response data:`, JSON.stringify(data));
      
      if (data && data.activities) {
        console.log(`API returned ${data.activities.length} activities`);
        
        // Log each activity
        data.activities.forEach((activity: any, i: number) => {
          console.log(`Activity ${i}: ${activity.title} (${activity.type})`);
        });
        
        results.apiDigest = {
          date: dateString,
          activities: data.activities.map((a: any) => ({
            ...a,
            type: mapActivityType(a.type)
          }))
        };
      } else {
        console.warn('API returned no activities');
        results.errors.push('API returned empty activities array');
      }
    } else {
      console.error(`API error: ${response.status}`);
      try {
        const errorData = await response.json();
        console.error('API error details:', JSON.stringify(errorData));
      } catch (e) {
        console.error('Could not parse error response');
      }
      results.errors.push(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error('Step 1 error:', error);
    results.errors.push(`API generation error: ${(error as Error).message}`);
  }
  
  // Step 2: Try the API with special headers for retry
  try {
    console.log('Step 2: Testing API generation with special retry headers');
    const userId = await getUserId();
    if (!userId) {
      throw new Error('No user ID found');
    }
    
    let apiUrl = await getApiUrl();
    apiUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
    
    const authHeader = await getDigestAuthHeader();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Retry-Attempt': 'true',
      'X-Auth-Protocol-Version': '2',
      ...(authHeader || {})
    };
    
    console.log(`API URL: ${apiUrl}/digest/${dateString}/regenerate`);
    console.log('Request headers with retry:', JSON.stringify(headers));
    
    const response = await fetch(`${apiUrl}/digest/${dateString}/regenerate`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        forceRegenerate: true,
        bypassCache: true
      })
    });
    
    console.log(`Retry response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      if (!results.apiDigest && data && data.activities) {
        console.log(`Retry API returned ${data.activities.length} activities`);
        results.apiDigest = {
          date: dateString,
          activities: data.activities.map((a: any) => ({
            ...a,
            type: mapActivityType(a.type)
          }))
        };
      }
    } else {
      results.errors.push(`Retry API error: ${response.status}`);
    }
  } catch (error) {
    console.error('Step 2 error:', error);
    results.errors.push(`Retry API generation error: ${(error as Error).message}`);
  }
  
  // Step 3: Try local generation directly
  try {
    console.log('Step 3: Testing local generation directly');
    const activities = await generateBasicActivities(new Date(dateString));
    console.log(`Generated ${activities.length} activities locally`);
    
    // Log the activities
    activities.forEach((activity, i) => {
      console.log(`Local activity ${i}: ${activity.title} (${activity.type})`);
    });
    
    results.localDigest = {
      date: dateString,
      activities,
      noDataReason: 'generated-locally' as 'generated-locally'
    };
  } catch (error) {
    console.error('Step 3 error:', error);
    results.errors.push(`Local generation error: ${(error as Error).message}`);
  }
  
  // Step 4: Create minimal fallback activities
  try {
    console.log('Step 4: Creating minimal fallback activities');
    const minimalActivities = [
      {
        id: generateId(),
        type: DigestActivityType.REMINDER,
        title: 'Morning Routine',
        description: 'Start your day with intention and energy.',
        scheduledTime: `${dateString}T08:00:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.MEAL,
        title: 'Healthy Lunch',
        description: 'Focus on protein and vegetables for sustained energy.',
        scheduledTime: `${dateString}T12:30:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.WORKOUT,
        title: 'Daily Exercise',
        description: 'Even 20 minutes of movement improves your health.',
        scheduledTime: `${dateString}T17:00:00`,
        completed: false,
        isUserOverride: false,
      },
      {
        id: generateId(),
        type: DigestActivityType.WATER,
        title: 'Stay Hydrated',
        description: 'Remember to drink water throughout the day.',
        scheduledTime: `${dateString}T14:00:00`,
        completed: false,
        isUserOverride: false,
      }
    ];
    
    console.log(`Created ${minimalActivities.length} minimal fallback activities`);
    
    results.fallbackDigest = {
      date: dateString,
      activities: minimalActivities,
      noDataReason: 'generated-locally' as 'generated-locally'
    };
  } catch (error) {
    console.error('Step 4 error:', error);
    results.errors.push(`Fallback generation error: ${(error as Error).message}`);
  }
  
  console.log('===== DIGEST GENERATION TEST RESULTS =====');
  console.log(`API digest: ${results.apiDigest ? `${results.apiDigest.activities.length} activities` : 'Failed'}`);
  console.log(`Local digest: ${results.localDigest ? `${results.localDigest.activities.length} activities` : 'Failed'}`);
  console.log(`Fallback digest: ${results.fallbackDigest ? `${results.fallbackDigest.activities.length} activities` : 'Failed'}`);
  console.log(`Errors: ${results.errors.length}`);
  if (results.errors.length > 0) {
    console.log('Error details:', results.errors.join('; '));
  }
  console.log('=========================================');
  
  return results;
}


