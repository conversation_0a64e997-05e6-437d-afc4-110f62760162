/**
 * Context Engine V3 - Complete Rewrite
 * 
 * This is a comprehensive rewrite of the context system to address all issues:
 * - Proper data overwriting and preference management
 * - Consistent data processing and prioritization
 * - Robust synchronization between local and server data
 * - Conflict resolution for changing user preferences
 * - Complete user data inclusion for Lotus
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserId } from './profile';
import { getRequest, postRequest, deleteRequest } from './apiRequestManager';

// Enhanced Context Types with better categorization
export enum ContextType {
  // Core User Data (Highest Priority)
  USER_PROFILE = 'user_profile',
  DIETARY_RESTRICTIONS = 'dietary_restrictions',
  INJURIES = 'injuries',
  MEDICAL_CONDITIONS = 'medical_conditions',
  
  // User Preferences (High Priority - Overwritable)
  PREFERENCES = 'preferences',
  GOALS = 'goals',
  SCHEDULE = 'schedule',
  WORKOUT_PREFERENCES = 'workout_preferences',
  NUTRITION_PREFERENCES = 'nutrition_preferences',
  
  // Activity Data (Medium Priority)
  WORKOUT_HISTORY = 'workout_history',
  MEAL_HISTORY = 'meal_history',
  WEIGHT_HISTORY = 'weight_history',
  SLEEP_DATA = 'sleep_data',
  ACTIVITY_LOGS = 'activity_logs',
  
  // Conversational Data (Medium Priority)
  CHAT_HISTORY = 'chat_history',
  CHAT_SUMMARIES = 'chat_summaries',
  USER_FEEDBACK = 'user_feedback',
  
  // Contextual Data (Lower Priority)
  LOCATION_DATA = 'location_data',
  WEATHER_PREFERENCES = 'weather_preferences',
  DEVICE_PREFERENCES = 'device_preferences',
  
  // System Data (Lowest Priority)
  APP_USAGE = 'app_usage',
  FEATURE_FLAGS = 'feature_flags',
  CUSTOM = 'custom'
}

// Context Priority Levels
export enum ContextPriority {
  CRITICAL = 1,    // Safety-critical data (allergies, injuries, medical)
  HIGH = 2,        // Core preferences and goals (overwritable)
  MEDIUM = 3,      // Activity and conversational data
  LOW = 4,         // Contextual and system data
}

// Context Data Structure
export interface ContextEntry {
  id: string;                    // Unique identifier
  userId: string;               // User ID
  type: ContextType;            // Context type
  priority: ContextPriority;    // Priority level
  value: any;                   // The actual data
  timestamp: string;            // When created/updated
  source: string;               // Where it came from
  version: number;              // Version for conflict resolution
  isActive: boolean;            // Whether this entry is active
  expiresAt?: string;           // Optional expiration
  metadata: {
    category?: string;          // Sub-category
    tags?: string[];           // Tags for filtering
    confidence?: number;       // Confidence score (0-1)
    lastUpdated: string;       // Last update timestamp
    updateSource: string;      // What updated this
    conflictResolution?: string; // How conflicts were resolved
    [key: string]: any;        // Additional metadata
  };
}

// Context Query Options
export interface ContextQuery {
  types?: ContextType[];
  priorities?: ContextPriority[];
  categories?: string[];
  tags?: string[];
  activeOnly?: boolean;
  limit?: number;
  sortBy?: 'timestamp' | 'priority' | 'version';
  sortOrder?: 'asc' | 'desc';
}

// Context Engine Result
export interface ContextEngineResult {
  contextText: string;
  totalEntries: number;
  totalLength: number;
  includedTypes: ContextType[];
  criticalDataIncluded: boolean;
  lastUpdated: string;
  version: number;
  errors: string[];
  warnings: string[];
}

// Context Update Options
export interface ContextUpdateOptions {
  overwrite?: boolean;          // Whether to overwrite existing data
  merge?: boolean;              // Whether to merge with existing data
  deactivateOld?: boolean;      // Whether to deactivate old entries
  source?: string;              // Source of the update
  category?: string;            // Category for the update
  tags?: string[];             // Tags for the update
  confidence?: number;         // Confidence in the data
}

class ContextEngineV3 {
  private static instance: ContextEngineV3;
  private readonly STORAGE_KEY = '@lotus_context_v3';
  private readonly SYNC_KEY = '@lotus_context_sync_v3';
  private contextCache: Map<string, ContextEntry[]> = new Map();
  private lastSyncTime: number = 0;
  private syncInProgress: boolean = false;

  private constructor() {}

  static getInstance(): ContextEngineV3 {
    if (!ContextEngineV3.instance) {
      ContextEngineV3.instance = new ContextEngineV3();
    }
    return ContextEngineV3.instance;
  }

  /**
   * Main method to get comprehensive user context for Lotus
   */
  async getUserContextForLLM(forceRefresh: boolean = false): Promise<ContextEngineResult> {
    console.log('[ContextEngineV3] Building comprehensive user context for LLM...');
    
    try {
      // Step 1: Ensure data is synchronized
      await this.synchronizeData(forceRefresh);
      
      // Step 2: Get all active context entries
      const allEntries = await this.getAllContextEntries({ activeOnly: true });
      
      // Step 3: Build prioritized context text
      const result = await this.buildContextText(allEntries);
      
      console.log(`[ContextEngineV3] Built context with ${result.totalEntries} entries, ${result.totalLength} characters`);
      return result;
      
    } catch (error) {
      console.error('[ContextEngineV3] Error building user context:', error);
      
      // Fallback to local data only
      return this.buildFallbackContext();
    }
  }

  /**
   * Add or update context data with proper conflict resolution
   */
  async updateContext(
    type: ContextType,
    value: any,
    options: ContextUpdateOptions = {}
  ): Promise<boolean> {
    console.log(`[ContextEngineV3] Updating context: ${type}`);
    
    try {
      const userId = await getUserId();
      if (!userId) {
        throw new Error('User ID not available');
      }

      const timestamp = new Date().toISOString();
      const entryId = this.generateEntryId(type, timestamp);
      
      // Get existing entries of this type
      const existingEntries = await this.getContextEntries({ types: [type], activeOnly: true });
      
      // Handle different update strategies
      if (options.overwrite) {
        // Deactivate all existing entries of this type
        await this.deactivateEntries(existingEntries.map(e => e.id));
      } else if (options.deactivateOld && options.category) {
        // Deactivate entries in the same category
        const categoryEntries = existingEntries.filter(e => e.metadata.category === options.category);
        await this.deactivateEntries(categoryEntries.map(e => e.id));
      }

      // Create new entry
      const newEntry: ContextEntry = {
        id: entryId,
        userId,
        type,
        priority: this.getContextPriority(type),
        value: options.merge ? this.mergeValues(existingEntries, value) : value,
        timestamp,
        source: options.source || 'user_input',
        version: this.getNextVersion(existingEntries),
        isActive: true,
        metadata: {
          category: options.category,
          tags: options.tags || [],
          confidence: options.confidence || 1.0,
          lastUpdated: timestamp,
          updateSource: options.source || 'user_input',
          conflictResolution: options.overwrite ? 'overwrite' : options.merge ? 'merge' : 'append'
        }
      };

      // Save locally first
      await this.saveEntryLocally(newEntry);
      
      // Sync to server
      await this.syncEntryToServer(newEntry);
      
      // Clear cache to force refresh
      this.clearCache();
      
      console.log(`[ContextEngineV3] Successfully updated context: ${type}`);
      return true;
      
    } catch (error) {
      console.error(`[ContextEngineV3] Error updating context ${type}:`, error);
      return false;
    }
  }

  /**
   * Get context entries with filtering
   */
  async getContextEntries(query: ContextQuery = {}): Promise<ContextEntry[]> {
    const cacheKey = JSON.stringify(query);
    
    // Check cache first
    if (this.contextCache.has(cacheKey)) {
      return this.contextCache.get(cacheKey)!;
    }

    try {
      // Get from local storage
      const localEntries = await this.getLocalEntries();
      
      // Apply filters
      let filteredEntries = this.applyFilters(localEntries, query);
      
      // Sort entries
      filteredEntries = this.sortEntries(filteredEntries, query);
      
      // Apply limit
      if (query.limit) {
        filteredEntries = filteredEntries.slice(0, query.limit);
      }

      // Cache results
      this.contextCache.set(cacheKey, filteredEntries);
      
      return filteredEntries;
      
    } catch (error) {
      console.error('[ContextEngineV3] Error getting context entries:', error);
      return [];
    }
  }

  /**
   * Delete context data with proper cleanup
   */
  async deleteContext(
    type?: ContextType,
    category?: string,
    entryIds?: string[]
  ): Promise<boolean> {
    console.log(`[ContextEngineV3] Deleting context: type=${type}, category=${category}, ids=${entryIds?.length}`);
    
    try {
      let entriesToDelete: ContextEntry[] = [];

      if (entryIds) {
        // Delete specific entries
        const allEntries = await this.getAllContextEntries();
        entriesToDelete = allEntries.filter(e => entryIds.includes(e.id));
      } else if (type && category) {
        // Delete by type and category
        entriesToDelete = await this.getContextEntries({ 
          types: [type], 
          categories: [category], 
          activeOnly: true 
        });
      } else if (type) {
        // Delete all entries of a type
        entriesToDelete = await this.getContextEntries({ 
          types: [type], 
          activeOnly: true 
        });
      } else {
        throw new Error('Must specify either entryIds, type, or type+category');
      }

      // Deactivate entries locally
      await this.deactivateEntries(entriesToDelete.map(e => e.id));
      
      // Sync deletions to server
      await this.syncDeletionsToServer(entriesToDelete.map(e => e.id));
      
      // Clear cache
      this.clearCache();
      
      console.log(`[ContextEngineV3] Successfully deleted ${entriesToDelete.length} context entries`);
      return true;
      
    } catch (error) {
      console.error('[ContextEngineV3] Error deleting context:', error);
      return false;
    }
  }

  /**
   * Synchronize local and server data
   */
  private async synchronizeData(forceSync: boolean = false): Promise<void> {
    const now = Date.now();
    const SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes

    if (!forceSync && this.syncInProgress) {
      console.log('[ContextEngineV3] Sync already in progress, skipping');
      return;
    }

    if (!forceSync && (now - this.lastSyncTime) < SYNC_INTERVAL) {
      console.log('[ContextEngineV3] Recent sync completed, skipping');
      return;
    }

    this.syncInProgress = true;

    try {
      console.log('[ContextEngineV3] Starting data synchronization...');

      // Get local and server data
      const [localEntries, serverEntries] = await Promise.all([
        this.getLocalEntries(),
        this.getServerEntries()
      ]);

      // Resolve conflicts and merge data
      const mergedEntries = this.resolveConflicts(localEntries, serverEntries);

      // Save merged data locally
      await this.saveEntriesLocally(mergedEntries);

      // Update sync timestamp
      this.lastSyncTime = now;
      await AsyncStorage.setItem(this.SYNC_KEY, now.toString());

      console.log(`[ContextEngineV3] Synchronization completed. Merged ${mergedEntries.length} entries`);

    } catch (error) {
      console.error('[ContextEngineV3] Error during synchronization:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Helper Methods

  private generateEntryId(type: ContextType, timestamp: string): string {
    return `${type}_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getContextPriority(type: ContextType): ContextPriority {
    const criticalTypes = [
      ContextType.DIETARY_RESTRICTIONS,
      ContextType.INJURIES,
      ContextType.MEDICAL_CONDITIONS
    ];

    const highTypes = [
      ContextType.USER_PROFILE,
      ContextType.PREFERENCES,
      ContextType.GOALS,
      ContextType.SCHEDULE,
      ContextType.WORKOUT_PREFERENCES,
      ContextType.NUTRITION_PREFERENCES
    ];

    const mediumTypes = [
      ContextType.WORKOUT_HISTORY,
      ContextType.MEAL_HISTORY,
      ContextType.WEIGHT_HISTORY,
      ContextType.CHAT_HISTORY,
      ContextType.CHAT_SUMMARIES,
      ContextType.USER_FEEDBACK
    ];

    if (criticalTypes.includes(type)) return ContextPriority.CRITICAL;
    if (highTypes.includes(type)) return ContextPriority.HIGH;
    if (mediumTypes.includes(type)) return ContextPriority.MEDIUM;
    return ContextPriority.LOW;
  }

  private getNextVersion(existingEntries: ContextEntry[]): number {
    if (existingEntries.length === 0) return 1;
    return Math.max(...existingEntries.map(e => e.version)) + 1;
  }

  private mergeValues(existingEntries: ContextEntry[], newValue: any): any {
    if (existingEntries.length === 0) return newValue;

    // Simple merge strategy - can be enhanced based on data type
    const latestEntry = existingEntries.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )[0];

    if (typeof newValue === 'object' && typeof latestEntry.value === 'object') {
      return { ...latestEntry.value, ...newValue };
    }

    return newValue;
  }

  private async getAllContextEntries(query: ContextQuery = {}): Promise<ContextEntry[]> {
    return this.getContextEntries(query);
  }

  private async getLocalEntries(): Promise<ContextEntry[]> {
    try {
      const data = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!data) return [];

      const entries = JSON.parse(data) as ContextEntry[];
      return entries.filter(e => e.isActive);
    } catch (error) {
      console.error('[ContextEngineV3] Error getting local entries:', error);
      return [];
    }
  }

  private async getServerEntries(): Promise<ContextEntry[]> {
    try {
      const response = await getRequest('/context/v3', undefined, {
        retries: 2,
        retryDelay: 1000,
        fallbackData: { data: [] },
        logTag: '[ContextEngineV3]'
      });

      return response.data || [];
    } catch (error) {
      console.error('[ContextEngineV3] Error getting server entries:', error);
      return [];
    }
  }

  private resolveConflicts(localEntries: ContextEntry[], serverEntries: ContextEntry[]): ContextEntry[] {
    const allEntries = new Map<string, ContextEntry>();

    // Add local entries
    localEntries.forEach(entry => {
      allEntries.set(entry.id, entry);
    });

    // Add server entries, resolving conflicts
    serverEntries.forEach(serverEntry => {
      const localEntry = allEntries.get(serverEntry.id);

      if (!localEntry) {
        // New entry from server
        allEntries.set(serverEntry.id, serverEntry);
      } else {
        // Conflict resolution: use the entry with higher version
        if (serverEntry.version > localEntry.version) {
          allEntries.set(serverEntry.id, {
            ...serverEntry,
            metadata: {
              ...serverEntry.metadata,
              conflictResolution: 'server_version_newer'
            }
          });
        } else if (localEntry.version > serverEntry.version) {
          // Keep local entry but mark conflict resolution
          allEntries.set(localEntry.id, {
            ...localEntry,
            metadata: {
              ...localEntry.metadata,
              conflictResolution: 'local_version_newer'
            }
          });
        } else {
          // Same version, use timestamp
          const serverTime = new Date(serverEntry.timestamp).getTime();
          const localTime = new Date(localEntry.timestamp).getTime();

          if (serverTime > localTime) {
            allEntries.set(serverEntry.id, {
              ...serverEntry,
              metadata: {
                ...serverEntry.metadata,
                conflictResolution: 'server_timestamp_newer'
              }
            });
          }
        }
      }
    });

    return Array.from(allEntries.values());
  }

  private async saveEntryLocally(entry: ContextEntry): Promise<void> {
    const entries = await this.getLocalEntries();
    const existingIndex = entries.findIndex(e => e.id === entry.id);

    if (existingIndex >= 0) {
      entries[existingIndex] = entry;
    } else {
      entries.push(entry);
    }

    await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(entries));
  }

  private async saveEntriesLocally(entries: ContextEntry[]): Promise<void> {
    await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(entries));
  }

  private async syncEntryToServer(entry: ContextEntry): Promise<void> {
    try {
      await postRequest('/context/v3', entry, {
        retries: 2,
        retryDelay: 1000,
        logTag: '[ContextEngineV3]'
      });
    } catch (error) {
      console.error('[ContextEngineV3] Error syncing entry to server:', error);
      // Don't throw - local save succeeded
    }
  }

  private async deactivateEntries(entryIds: string[]): Promise<void> {
    const entries = await this.getLocalEntries();

    entries.forEach(entry => {
      if (entryIds.includes(entry.id)) {
        entry.isActive = false;
        entry.metadata.lastUpdated = new Date().toISOString();
        entry.metadata.updateSource = 'deactivation';
      }
    });

    await this.saveEntriesLocally(entries);
  }

  private async syncDeletionsToServer(entryIds: string[]): Promise<void> {
    try {
      await deleteRequest('/context/v3', { entryIds }, {
        retries: 2,
        retryDelay: 1000,
        logTag: '[ContextEngineV3]'
      });
    } catch (error) {
      console.error('[ContextEngineV3] Error syncing deletions to server:', error);
    }
  }

  private applyFilters(entries: ContextEntry[], query: ContextQuery): ContextEntry[] {
    let filtered = entries;

    if (query.types) {
      filtered = filtered.filter(e => query.types!.includes(e.type));
    }

    if (query.priorities) {
      filtered = filtered.filter(e => query.priorities!.includes(e.priority));
    }

    if (query.categories) {
      filtered = filtered.filter(e =>
        e.metadata.category && query.categories!.includes(e.metadata.category)
      );
    }

    if (query.tags) {
      filtered = filtered.filter(e =>
        e.metadata.tags && e.metadata.tags.some(tag => query.tags!.includes(tag))
      );
    }

    if (query.activeOnly !== false) {
      filtered = filtered.filter(e => e.isActive);
    }

    return filtered;
  }

  private sortEntries(entries: ContextEntry[], query: ContextQuery): ContextEntry[] {
    const sortBy = query.sortBy || 'priority';
    const sortOrder = query.sortOrder || 'asc';

    return entries.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'priority':
          comparison = a.priority - b.priority;
          break;
        case 'timestamp':
          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
          break;
        case 'version':
          comparison = a.version - b.version;
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  private async buildContextText(entries: ContextEntry[]): Promise<ContextEngineResult> {
    const result: ContextEngineResult = {
      contextText: '',
      totalEntries: entries.length,
      totalLength: 0,
      includedTypes: [],
      criticalDataIncluded: false,
      lastUpdated: new Date().toISOString(),
      version: 1,
      errors: [],
      warnings: []
    };

    try {
      // Sort entries by priority and timestamp
      const sortedEntries = entries.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority; // Lower number = higher priority
        }
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });

      // Group entries by type for better organization
      const groupedEntries = this.groupEntriesByType(sortedEntries);

      // Build context sections
      const contextSections: string[] = [];

      // Add critical data first
      const criticalSections = this.buildCriticalDataSections(groupedEntries);
      if (criticalSections.length > 0) {
        contextSections.push('=== CRITICAL USER DATA ===');
        contextSections.push(...criticalSections);
        result.criticalDataIncluded = true;
      }

      // Add user profile and preferences
      const profileSections = this.buildProfileSections(groupedEntries);
      if (profileSections.length > 0) {
        contextSections.push('\n=== USER PROFILE & PREFERENCES ===');
        contextSections.push(...profileSections);
      }

      // Add activity data
      const activitySections = this.buildActivitySections(groupedEntries);
      if (activitySections.length > 0) {
        contextSections.push('\n=== ACTIVITY & HISTORY ===');
        contextSections.push(...activitySections);
      }

      // Add conversational context
      const conversationalSections = this.buildConversationalSections(groupedEntries);
      if (conversationalSections.length > 0) {
        contextSections.push('\n=== CONVERSATIONAL CONTEXT ===');
        contextSections.push(...conversationalSections);
      }

      // Combine all sections
      result.contextText = contextSections.join('\n');
      result.totalLength = result.contextText.length;
      result.includedTypes = Array.from(new Set(entries.map(e => e.type)));

      // Check for length limits and truncate if necessary
      const MAX_CONTEXT_LENGTH = 12000; // Increased limit for comprehensive context
      if (result.totalLength > MAX_CONTEXT_LENGTH) {
        result.contextText = this.truncateContext(result.contextText, MAX_CONTEXT_LENGTH);
        result.totalLength = result.contextText.length;
        result.warnings.push(`Context truncated from ${result.totalLength} to ${MAX_CONTEXT_LENGTH} characters`);
      }

      console.log(`[ContextEngineV3] Built context: ${result.totalEntries} entries, ${result.totalLength} chars, ${result.includedTypes.length} types`);

    } catch (error) {
      console.error('[ContextEngineV3] Error building context text:', error);
      result.errors.push(`Error building context: ${error.message}`);
    }

    return result;
  }

  private groupEntriesByType(entries: ContextEntry[]): Map<ContextType, ContextEntry[]> {
    const grouped = new Map<ContextType, ContextEntry[]>();

    entries.forEach(entry => {
      if (!grouped.has(entry.type)) {
        grouped.set(entry.type, []);
      }
      grouped.get(entry.type)!.push(entry);
    });

    return grouped;
  }

  private buildCriticalDataSections(groupedEntries: Map<ContextType, ContextEntry[]>): string[] {
    const sections: string[] = [];

    // Dietary restrictions
    const dietaryRestrictions = groupedEntries.get(ContextType.DIETARY_RESTRICTIONS);
    if (dietaryRestrictions && dietaryRestrictions.length > 0) {
      sections.push('DIETARY RESTRICTIONS:');
      dietaryRestrictions.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Injuries
    const injuries = groupedEntries.get(ContextType.INJURIES);
    if (injuries && injuries.length > 0) {
      sections.push('INJURIES & LIMITATIONS:');
      injuries.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Medical conditions
    const medical = groupedEntries.get(ContextType.MEDICAL_CONDITIONS);
    if (medical && medical.length > 0) {
      sections.push('MEDICAL CONDITIONS:');
      medical.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    return sections;
  }

  private buildProfileSections(groupedEntries: Map<ContextType, ContextEntry[]>): string[] {
    const sections: string[] = [];

    // User profile
    const profile = groupedEntries.get(ContextType.USER_PROFILE);
    if (profile && profile.length > 0) {
      sections.push('USER PROFILE:');
      profile.forEach(entry => {
        sections.push(`${this.formatEntryValue(entry)}`);
      });
    }

    // General preferences
    const preferences = groupedEntries.get(ContextType.PREFERENCES);
    if (preferences && preferences.length > 0) {
      sections.push('PREFERENCES:');
      preferences.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Goals
    const goals = groupedEntries.get(ContextType.GOALS);
    if (goals && goals.length > 0) {
      sections.push('GOALS:');
      goals.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Schedule
    const schedule = groupedEntries.get(ContextType.SCHEDULE);
    if (schedule && schedule.length > 0) {
      sections.push('SCHEDULE:');
      schedule.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Workout preferences
    const workoutPrefs = groupedEntries.get(ContextType.WORKOUT_PREFERENCES);
    if (workoutPrefs && workoutPrefs.length > 0) {
      sections.push('WORKOUT PREFERENCES:');
      workoutPrefs.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Nutrition preferences
    const nutritionPrefs = groupedEntries.get(ContextType.NUTRITION_PREFERENCES);
    if (nutritionPrefs && nutritionPrefs.length > 0) {
      sections.push('NUTRITION PREFERENCES:');
      nutritionPrefs.forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    return sections;
  }

  private buildActivitySections(groupedEntries: Map<ContextType, ContextEntry[]>): string[] {
    const sections: string[] = [];

    // Recent workouts
    const workouts = groupedEntries.get(ContextType.WORKOUT_HISTORY);
    if (workouts && workouts.length > 0) {
      sections.push('RECENT WORKOUTS:');
      workouts.slice(0, 5).forEach(entry => { // Limit to 5 most recent
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Recent meals
    const meals = groupedEntries.get(ContextType.MEAL_HISTORY);
    if (meals && meals.length > 0) {
      sections.push('RECENT MEALS:');
      meals.slice(0, 5).forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // Weight history
    const weights = groupedEntries.get(ContextType.WEIGHT_HISTORY);
    if (weights && weights.length > 0) {
      sections.push('WEIGHT HISTORY:');
      weights.slice(0, 3).forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    return sections;
  }

  private buildConversationalSections(groupedEntries: Map<ContextType, ContextEntry[]>): string[] {
    const sections: string[] = [];

    // Chat summaries
    const chatSummaries = groupedEntries.get(ContextType.CHAT_SUMMARIES);
    if (chatSummaries && chatSummaries.length > 0) {
      sections.push('RECENT CONVERSATION SUMMARIES:');
      chatSummaries.slice(0, 3).forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    // User feedback
    const feedback = groupedEntries.get(ContextType.USER_FEEDBACK);
    if (feedback && feedback.length > 0) {
      sections.push('USER FEEDBACK:');
      feedback.slice(0, 3).forEach(entry => {
        sections.push(`- ${this.formatEntryValue(entry)}`);
      });
    }

    return sections;
  }

  private formatEntryValue(entry: ContextEntry): string {
    if (typeof entry.value === 'string') {
      return entry.value;
    } else if (typeof entry.value === 'object') {
      // Format objects in a readable way
      if (entry.value.description) {
        return entry.value.description;
      } else if (entry.value.name) {
        return entry.value.name;
      } else {
        return JSON.stringify(entry.value);
      }
    } else {
      return String(entry.value);
    }
  }

  private truncateContext(context: string, maxLength: number): string {
    if (context.length <= maxLength) return context;

    // Try to truncate at a section boundary
    const sections = context.split('\n===');
    let truncated = '';

    for (const section of sections) {
      const sectionWithHeader = truncated ? `\n===${section}` : section;
      if ((truncated + sectionWithHeader).length > maxLength) {
        break;
      }
      truncated += sectionWithHeader;
    }

    if (truncated.length === 0) {
      // If no complete sections fit, truncate at word boundary
      const words = context.split(' ');
      truncated = '';
      for (const word of words) {
        if ((truncated + ' ' + word).length > maxLength) {
          break;
        }
        truncated += (truncated ? ' ' : '') + word;
      }
    }

    return truncated + '\n\n[Context truncated due to length limits]';
  }

  private async buildFallbackContext(): Promise<ContextEngineResult> {
    console.log('[ContextEngineV3] Building fallback context from local data only');

    try {
      const localEntries = await this.getLocalEntries();
      return this.buildContextText(localEntries);
    } catch (error) {
      console.error('[ContextEngineV3] Error building fallback context:', error);

      return {
        contextText: 'USER CONTEXT: Unable to load user context. Using default recommendations.',
        totalEntries: 0,
        totalLength: 0,
        includedTypes: [],
        criticalDataIncluded: false,
        lastUpdated: new Date().toISOString(),
        version: 0,
        errors: [`Fallback context error: ${error.message}`],
        warnings: ['Using minimal context due to errors']
      };
    }
  }

  private clearCache(): void {
    this.contextCache.clear();
  }
}

// Export singleton instance and convenience methods
export const contextEngineV3 = ContextEngineV3.getInstance();

// Main function to get user context for LLM calls
export async function getUserContextForLLM(forceRefresh: boolean = false): Promise<ContextEngineResult> {
  return await contextEngineV3.getUserContextForLLM(forceRefresh);
}

// Convenience methods for updating different types of context
export async function updateUserPreferences(preferences: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.PREFERENCES, preferences, {
    ...options,
    overwrite: options.overwrite !== false, // Default to overwrite for preferences
    source: 'user_preference_update'
  });
}

export async function updateUserProfile(profile: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.USER_PROFILE, profile, {
    ...options,
    merge: options.merge !== false, // Default to merge for profile
    source: 'profile_update'
  });
}

export async function addWorkoutHistory(workout: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.WORKOUT_HISTORY, workout, {
    ...options,
    source: 'workout_completion'
  });
}

export async function addMealHistory(meal: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.MEAL_HISTORY, meal, {
    ...options,
    source: 'meal_logging'
  });
}

export async function updateDietaryRestrictions(restrictions: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.DIETARY_RESTRICTIONS, restrictions, {
    ...options,
    overwrite: options.overwrite !== false, // Default to overwrite for safety
    source: 'dietary_restriction_update'
  });
}

export async function updateInjuries(injuries: any, options: ContextUpdateOptions = {}): Promise<boolean> {
  return await contextEngineV3.updateContext(ContextType.INJURIES, injuries, {
    ...options,
    overwrite: options.overwrite !== false, // Default to overwrite for safety
    source: 'injury_update'
  });
}

export async function deleteUserContext(type?: ContextType, category?: string, entryIds?: string[]): Promise<boolean> {
  return await contextEngineV3.deleteContext(type, category, entryIds);
}

export default contextEngineV3;
