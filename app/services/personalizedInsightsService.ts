/**
 * Personalized Insights Service - Generates data-driven insights and recommendations
 * 
 * Features:
 * - Workout performance analysis
 * - Nutrition pattern insights
 * - Progress trend analysis
 * - Behavioral pattern recognition
 * - Predictive recommendations
 * - Weekly/monthly summaries
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getRequest } from './apiRequestManager';
import { StreakType, getStreakSummary } from './streakService';

export enum InsightType {
  PERFORMANCE_TREND = 'performance_trend',
  NUTRITION_PATTERN = 'nutrition_pattern',
  WORKOUT_OPTIMIZATION = 'workout_optimization',
  RECOVERY_ANALYSIS = 'recovery_analysis',
  GOAL_PROGRESS = 'goal_progress',
  BEHAVIORAL_PATTERN = 'behavioral_pattern',
  PREDICTION = 'prediction',
  MILESTONE_ACHIEVEMENT = 'milestone_achievement'
}

export enum InsightPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface Insight {
  id: string;
  type: InsightType;
  priority: InsightPriority;
  title: string;
  description: string;
  actionable: boolean;
  recommendations: string[];
  data: Record<string, any>;
  confidence: number; // 0-100
  timestamp: string;
  expiresAt?: string;
  category: string;
  tags: string[];
}

export interface InsightSummary {
  totalInsights: number;
  highPriorityInsights: number;
  actionableInsights: number;
  recentInsights: Insight[];
  insightsByCategory: Record<string, number>;
  weeklyTrends: {
    category: string;
    trend: 'improving' | 'declining' | 'stable';
    change: number;
  }[];
}

export interface UserBehaviorPattern {
  workoutTiming: {
    preferredHours: number[];
    consistency: number;
    weekdayVsWeekend: 'weekday' | 'weekend' | 'balanced';
  };
  nutritionHabits: {
    mealTiming: number[];
    macroPreferences: Record<string, number>;
    consistencyScore: number;
  };
  progressPatterns: {
    bestPerformanceDays: string[];
    recoveryNeeds: number;
    plateauRisk: number;
  };
}

const INSIGHTS_CACHE_KEY = '@personalized_insights';
const BEHAVIOR_PATTERNS_KEY = '@behavior_patterns';
const INSIGHTS_CACHE_TTL = 60 * 60 * 1000; // 1 hour

class PersonalizedInsightsService {
  private insightsCache: Insight[] = [];
  private lastCacheUpdate: number = 0;
  private behaviorPatterns: UserBehaviorPattern | null = null;

  /**
   * Generate personalized insights based on user data
   */
  async generateInsights(forceRefresh = false): Promise<Insight[]> {
    const now = Date.now();
    
    // Return cached insights if still valid
    if (!forceRefresh && this.insightsCache.length > 0 && (now - this.lastCacheUpdate) < INSIGHTS_CACHE_TTL) {
      return this.insightsCache;
    }

    console.log('[Insights] Generating personalized insights...');

    try {
      // Gather data from various sources
      const [
        workoutData,
        nutritionData,
        streakData,
        weightData,
        behaviorPatterns
      ] = await Promise.all([
        this.getWorkoutData(),
        this.getNutritionData(),
        getStreakSummary(),
        this.getWeightData(),
        this.analyzeBehaviorPatterns()
      ]);

      // Generate insights from different analyzers
      const insights: Insight[] = [];
      
      insights.push(...await this.analyzeWorkoutPerformance(workoutData));
      insights.push(...await this.analyzeNutritionPatterns(nutritionData));
      insights.push(...await this.analyzeStreakProgress(streakData));
      insights.push(...await this.analyzeWeightTrends(weightData));
      insights.push(...await this.analyzeBehavioralPatterns(behaviorPatterns));
      insights.push(...await this.generatePredictiveInsights(workoutData, nutritionData));

      // Sort by priority and confidence
      insights.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return b.confidence - a.confidence;
      });

      // Cache the results
      this.insightsCache = insights;
      this.lastCacheUpdate = now;
      
      // Store in AsyncStorage
      await AsyncStorage.setItem(INSIGHTS_CACHE_KEY, JSON.stringify({
        insights,
        timestamp: now
      }));

      console.log(`[Insights] Generated ${insights.length} insights`);
      return insights;

    } catch (error) {
      console.error('[Insights] Error generating insights:', error);
      return this.getStoredInsights();
    }
  }

  /**
   * Get insights summary for dashboard
   */
  async getInsightsSummary(): Promise<InsightSummary> {
    const insights = await this.generateInsights();
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentInsights = insights.filter(insight => 
      new Date(insight.timestamp) >= weekAgo
    );

    const insightsByCategory: Record<string, number> = {};
    insights.forEach(insight => {
      insightsByCategory[insight.category] = (insightsByCategory[insight.category] || 0) + 1;
    });

    // Calculate weekly trends (simplified)
    const weeklyTrends = Object.keys(insightsByCategory).map(category => ({
      category,
      trend: 'stable' as const, // Would be calculated from historical data
      change: 0
    }));

    return {
      totalInsights: insights.length,
      highPriorityInsights: insights.filter(i => i.priority === InsightPriority.HIGH || i.priority === InsightPriority.CRITICAL).length,
      actionableInsights: insights.filter(i => i.actionable).length,
      recentInsights: recentInsights.slice(0, 5),
      insightsByCategory,
      weeklyTrends
    };
  }

  /**
   * Analyze workout performance patterns
   */
  private async analyzeWorkoutPerformance(workoutData: any[]): Promise<Insight[]> {
    const insights: Insight[] = [];

    if (workoutData.length < 3) {
      return insights; // Need more data
    }

    // Analyze workout frequency
    const recentWorkouts = workoutData.slice(-14); // Last 14 workouts
    const avgDaysBetween = this.calculateAverageDaysBetween(recentWorkouts);

    if (avgDaysBetween > 3) {
      insights.push({
        id: `workout_frequency_${Date.now()}`,
        type: InsightType.WORKOUT_OPTIMIZATION,
        priority: InsightPriority.MEDIUM,
        title: 'Workout Frequency Could Be Improved',
        description: `You're averaging ${avgDaysBetween.toFixed(1)} days between workouts. More consistent training could accelerate your progress.`,
        actionable: true,
        recommendations: [
          'Try to maintain 3-4 workouts per week',
          'Set specific workout days in your calendar',
          'Consider shorter workouts if time is a constraint'
        ],
        data: { avgDaysBetween, recentWorkouts: recentWorkouts.length },
        confidence: 85,
        timestamp: new Date().toISOString(),
        category: 'Workout Performance',
        tags: ['frequency', 'consistency', 'optimization']
      });
    }

    // Analyze performance trends
    const performanceScores = workoutData.map(w => w.performanceScore || 0);
    const recentPerformance = performanceScores.slice(-5).reduce((a, b) => a + b, 0) / 5;
    const previousPerformance = performanceScores.slice(-10, -5).reduce((a, b) => a + b, 0) / 5;

    if (recentPerformance > previousPerformance * 1.1) {
      insights.push({
        id: `performance_improvement_${Date.now()}`,
        type: InsightType.PERFORMANCE_TREND,
        priority: InsightPriority.HIGH,
        title: 'Performance is Trending Upward! 📈',
        description: `Your recent workouts show a ${((recentPerformance / previousPerformance - 1) * 100).toFixed(1)}% improvement in performance.`,
        actionable: false,
        recommendations: [
          'Keep up the excellent work!',
          'Consider gradually increasing workout intensity',
          'Track this progress to maintain motivation'
        ],
        data: { recentPerformance, previousPerformance, improvement: recentPerformance / previousPerformance },
        confidence: 90,
        timestamp: new Date().toISOString(),
        category: 'Performance',
        tags: ['improvement', 'motivation', 'progress']
      });
    }

    return insights;
  }

  /**
   * Analyze nutrition patterns
   */
  private async analyzeNutritionPatterns(nutritionData: any[]): Promise<Insight[]> {
    const insights: Insight[] = [];

    if (nutritionData.length < 7) {
      return insights; // Need at least a week of data
    }

    // Analyze macro consistency
    const proteinIntakes = nutritionData.map(d => d.protein || 0);
    const proteinConsistency = this.calculateConsistencyScore(proteinIntakes);

    if (proteinConsistency < 0.7) {
      insights.push({
        id: `protein_consistency_${Date.now()}`,
        type: InsightType.NUTRITION_PATTERN,
        priority: InsightPriority.MEDIUM,
        title: 'Protein Intake Varies Significantly',
        description: 'Your protein intake fluctuates quite a bit day-to-day. More consistent protein can help with muscle recovery and growth.',
        actionable: true,
        recommendations: [
          'Aim for similar protein amounts each day',
          'Plan protein sources for each meal',
          'Consider protein supplements if needed'
        ],
        data: { consistencyScore: proteinConsistency, avgProtein: proteinIntakes.reduce((a, b) => a + b, 0) / proteinIntakes.length },
        confidence: 80,
        timestamp: new Date().toISOString(),
        category: 'Nutrition',
        tags: ['protein', 'consistency', 'macros']
      });
    }

    return insights;
  }

  /**
   * Analyze streak progress
   */
  private async analyzeStreakProgress(streakData: any): Promise<Insight[]> {
    const insights: Insight[] = [];

    // Check for streak achievements
    if (streakData.recentAchievements.length > 0) {
      const latestAchievement = streakData.recentAchievements[0];
      insights.push({
        id: `streak_achievement_${Date.now()}`,
        type: InsightType.MILESTONE_ACHIEVEMENT,
        priority: InsightPriority.HIGH,
        title: `🔥 Streak Milestone Achieved!`,
        description: `Congratulations on your ${latestAchievement.title}! Your consistency is paying off.`,
        actionable: false,
        recommendations: [
          'Share your achievement with friends',
          'Set a new streak goal',
          'Celebrate this milestone!'
        ],
        data: latestAchievement,
        confidence: 100,
        timestamp: new Date().toISOString(),
        category: 'Achievements',
        tags: ['streak', 'milestone', 'celebration']
      });
    }

    return insights;
  }

  /**
   * Analyze weight trends
   */
  private async analyzeWeightTrends(weightData: any[]): Promise<Insight[]> {
    const insights: Insight[] = [];

    if (weightData.length < 5) {
      return insights;
    }

    // Calculate trend
    const weights = weightData.map(w => w.weight);
    const trend = this.calculateTrend(weights);

    if (Math.abs(trend) > 0.5) { // Significant trend
      const direction = trend > 0 ? 'increasing' : 'decreasing';
      const isPositive = (trend < 0); // Assuming weight loss is positive

      insights.push({
        id: `weight_trend_${Date.now()}`,
        type: InsightType.PERFORMANCE_TREND,
        priority: InsightPriority.MEDIUM,
        title: `Weight is ${direction}`,
        description: `Your weight has been ${direction} by an average of ${Math.abs(trend).toFixed(1)} lbs per week.`,
        actionable: true,
        recommendations: isPositive ? [
          'Great progress! Keep up the current routine',
          'Consider taking progress photos',
          'Monitor how you feel alongside the numbers'
        ] : [
          'Review your nutrition and exercise plan',
          'Consider consulting with a nutritionist',
          'Focus on sustainable habits'
        ],
        data: { trend, direction, weeklyChange: trend },
        confidence: 75,
        timestamp: new Date().toISOString(),
        category: 'Weight Management',
        tags: ['weight', 'trend', 'progress']
      });
    }

    return insights;
  }

  /**
   * Analyze behavioral patterns
   */
  private async analyzeBehavioralPatterns(patterns: UserBehaviorPattern): Promise<Insight[]> {
    const insights: Insight[] = [];

    // Analyze workout timing consistency
    if (patterns.workoutTiming.consistency < 0.6) {
      insights.push({
        id: `workout_timing_${Date.now()}`,
        type: InsightType.BEHAVIORAL_PATTERN,
        priority: InsightPriority.LOW,
        title: 'Workout Timing Varies Significantly',
        description: 'You work out at different times throughout the day. A consistent schedule might help build stronger habits.',
        actionable: true,
        recommendations: [
          'Try to work out at the same time each day',
          'Choose a time that fits your energy levels',
          'Block workout time in your calendar'
        ],
        data: patterns.workoutTiming,
        confidence: 70,
        timestamp: new Date().toISOString(),
        category: 'Habits',
        tags: ['timing', 'consistency', 'habits']
      });
    }

    return insights;
  }

  /**
   * Generate predictive insights
   */
  private async generatePredictiveInsights(workoutData: any[], nutritionData: any[]): Promise<Insight[]> {
    const insights: Insight[] = [];

    // Predict plateau risk based on performance trends
    if (workoutData.length >= 10) {
      const recentPerformance = workoutData.slice(-5);
      const performanceVariation = this.calculateVariation(recentPerformance.map(w => w.performanceScore || 0));

      if (performanceVariation < 0.1) { // Low variation might indicate plateau
        insights.push({
          id: `plateau_prediction_${Date.now()}`,
          type: InsightType.PREDICTION,
          priority: InsightPriority.MEDIUM,
          title: 'Potential Plateau Detected',
          description: 'Your performance has been consistent but not improving. You might be approaching a plateau.',
          actionable: true,
          recommendations: [
            'Consider changing your workout routine',
            'Increase workout intensity gradually',
            'Try new exercises or training methods',
            'Ensure adequate recovery time'
          ],
          data: { performanceVariation, plateauRisk: 0.7 },
          confidence: 65,
          timestamp: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // Expires in 2 weeks
          category: 'Predictions',
          tags: ['plateau', 'prediction', 'optimization']
        });
      }
    }

    return insights;
  }

  /**
   * Helper methods for data analysis
   */
  private calculateAverageDaysBetween(workouts: any[]): number {
    if (workouts.length < 2) return 0;
    
    const dates = workouts.map(w => new Date(w.date));
    dates.sort((a, b) => a.getTime() - b.getTime());
    
    let totalDays = 0;
    for (let i = 1; i < dates.length; i++) {
      const daysDiff = (dates[i].getTime() - dates[i-1].getTime()) / (1000 * 60 * 60 * 24);
      totalDays += daysDiff;
    }
    
    return totalDays / (dates.length - 1);
  }

  private calculateConsistencyScore(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    // Consistency score: higher is more consistent
    return Math.max(0, 1 - (stdDev / mean));
  }

  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    // Simple linear regression slope
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  private calculateVariation(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    
    return variance / (mean * mean); // Coefficient of variation
  }

  private async analyzeBehaviorPatterns(): Promise<UserBehaviorPattern> {
    // This would analyze user data to identify patterns
    // For now, return a default pattern
    return {
      workoutTiming: {
        preferredHours: [7, 8, 18, 19],
        consistency: 0.7,
        weekdayVsWeekend: 'balanced'
      },
      nutritionHabits: {
        mealTiming: [7, 12, 19],
        macroPreferences: { protein: 0.3, carbs: 0.4, fat: 0.3 },
        consistencyScore: 0.8
      },
      progressPatterns: {
        bestPerformanceDays: ['Monday', 'Wednesday', 'Friday'],
        recoveryNeeds: 48,
        plateauRisk: 0.3
      }
    };
  }

  private async getWorkoutData(): Promise<any[]> {
    // This would fetch actual workout data
    return [];
  }

  private async getNutritionData(): Promise<any[]> {
    // This would fetch actual nutrition data
    return [];
  }

  private async getWeightData(): Promise<any[]> {
    // This would fetch actual weight data
    return [];
  }

  private async getStoredInsights(): Promise<Insight[]> {
    try {
      const stored = await AsyncStorage.getItem(INSIGHTS_CACHE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        return data.insights || [];
      }
    } catch (error) {
      console.error('[Insights] Error loading stored insights:', error);
    }
    return [];
  }
}

// Export singleton instance
export const personalizedInsightsService = new PersonalizedInsightsService();

// Convenience functions
export const generatePersonalizedInsights = (forceRefresh = false) =>
  personalizedInsightsService.generateInsights(forceRefresh);

export const getInsightsSummary = () =>
  personalizedInsightsService.getInsightsSummary();
