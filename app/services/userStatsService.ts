import AsyncStorage from '@react-native-async-storage/async-storage';
import { makeRequestWithRetry } from './apiClient';
import { getRequest } from './apiRequestManager';
import { getUserId } from './chatService';
import { getWeightEntries } from './weightTracking';
import { getLogs } from './conversationService';

// Constants
const USER_STATS_STORAGE_KEY = '@user_stats';

// User stats interface
export interface UserStats {
  userId: string;
  workouts: {
    total: number;
    thisWeek: number;
    thisMonth: number;
    streak: number;
    lastWorkoutDate?: string;
  };
  nutrition: {
    mealsLogged: number;
    avgCalories: number;
    avgProtein: number;
    streakDays: number;
    lastMealDate?: string;
  };
  weight: {
    current?: number;
    initial?: number;
    goal?: number;
    change?: number;
    changePercentage?: number;
    lastUpdated?: string;
  };
  activity: {
    totalDaysActive: number;
    currentStreak: number;
    longestStreak: number;
    lastActiveDate?: string;
  };
  updatedAt: string;
}

// Get user stats
export async function getUserStats(): Promise<UserStats | null> {
  try {
    // First try to get from local storage for immediate UI rendering
    const localStats = await getLocalStats();

    // Try to get from server
    const userId = await getUserId();
    if (!userId) {
      console.log('No user ID available, returning local stats only');
      return localStats;
    }

    try {
      // Use the new API request manager with better error handling
      const now = new Date().toISOString();
      const defaultStats = {
        userId,
        workouts: {
          total: 0,
          thisWeek: 0,
          thisMonth: 0,
          streak: 0,
        },
        nutrition: {
          mealsLogged: 0,
          avgCalories: 0,
          avgProtein: 0,
          streakDays: 0,
        },
        weight: {},
        activity: {
          totalDaysActive: 0,
          currentStreak: 0,
          longestStreak: 0,
        },
        updatedAt: now,
      };

      const stats = await getRequest<UserStats>(
        '/user/stats',
        undefined,
        {
          // If the request fails, use calculated or default stats
          fallbackData: localStats || await calculateUserStats(userId) || defaultStats,
          // Use a longer cache TTL for user stats
          cacheTTL: 15 * 60 * 1000, // 15 minutes
          // Add a delay to prevent rate limiting
          delayMs: 200,
          // Use more retries with longer delays
          retries: 3,
          retryDelay: 1000,
          // Use a custom log tag
          logTag: '[User Stats]',
          // Don't cache errors
          useCache: true
        }
      );

      if (stats) {
        // Save to local storage for offline access
        await saveLocalStats(stats);

        return stats;
      }
    } catch (error) {
      console.error('Error fetching user stats from server:', error);
    }

    // If server request fails, calculate stats locally
    if (!localStats) {
      const calculatedStats = await calculateUserStats(userId);
      if (calculatedStats) {
        await saveLocalStats(calculatedStats);
        return calculatedStats;
      }
    }

    return localStats;
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return null;
  }
}

// Get stats from local storage
async function getLocalStats(): Promise<UserStats | null> {
  try {
    const statsJson = await AsyncStorage.getItem(USER_STATS_STORAGE_KEY);
    if (statsJson) {
      return JSON.parse(statsJson);
    }
    return null;
  } catch (error) {
    console.error('Error getting stats from local storage:', error);
    return null;
  }
}

// Save stats to local storage
async function saveLocalStats(stats: UserStats): Promise<void> {
  try {
    await AsyncStorage.setItem(USER_STATS_STORAGE_KEY, JSON.stringify(stats));
  } catch (error) {
    console.error('Error saving stats to local storage:', error);
  }
}

// Calculate user stats from local data
async function calculateUserStats(userId: string): Promise<UserStats | null> {
  try {
    // Get all logs
    const logs = await getLogs();

    // Get weight entries
    const weightEntries = await getWeightEntries();

    // Current date info
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Filter workout logs
    const workoutLogs = logs.filter(log => log.type === 'workout');
    const workoutDates = workoutLogs.map(log => new Date(log.timestamp).toISOString().split('T')[0]);
    const uniqueWorkoutDates = [...new Set(workoutDates)];

    // Calculate workout stats
    const workoutsThisWeek = workoutLogs.filter(log =>
      new Date(log.timestamp) >= thisWeekStart
    ).length;

    const workoutsThisMonth = workoutLogs.filter(log =>
      new Date(log.timestamp) >= thisMonthStart
    ).length;

    // Calculate workout streak
    let workoutStreak = 0;
    let lastWorkoutDate = workoutLogs.length > 0
      ? new Date(Math.max(...workoutLogs.map(log => new Date(log.timestamp).getTime())))
      : undefined;

    // Filter meal logs
    const mealLogs = logs.filter(log => log.type === 'meal');
    const mealDates = mealLogs.map(log => new Date(log.timestamp).toISOString().split('T')[0]);
    const uniqueMealDates = [...new Set(mealDates)];

    // Calculate nutrition stats
    const totalCalories = mealLogs.reduce((sum, log) => {
      const calories = log.metrics?.meal?.calories || 0;
      return sum + calories;
    }, 0);

    const totalProtein = mealLogs.reduce((sum, log) => {
      const protein = log.metrics?.meal?.protein || 0;
      return sum + protein;
    }, 0);

    const avgCalories = mealLogs.length > 0 ? Math.round(totalCalories / mealLogs.length) : 0;
    const avgProtein = mealLogs.length > 0 ? Math.round(totalProtein / mealLogs.length) : 0;

    // Calculate meal streak
    let mealStreak = 0;
    let lastMealDate = mealLogs.length > 0
      ? new Date(Math.max(...mealLogs.map(log => new Date(log.timestamp).getTime())))
      : undefined;

    // Calculate weight stats
    let currentWeight, initialWeight, goalWeight, weightChange, weightChangePercentage, lastWeightUpdate;

    if (weightEntries.length > 0) {
      // Sort by date
      const sortedEntries = [...weightEntries].sort((a, b) =>
        new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      currentWeight = sortedEntries[0].weight;
      initialWeight = sortedEntries[sortedEntries.length - 1].weight;
      lastWeightUpdate = sortedEntries[0].date;

      if (currentWeight && initialWeight) {
        weightChange = currentWeight - initialWeight;
        weightChangePercentage = (weightChange / initialWeight) * 100;
      }
    }

    // Calculate activity stats
    const allActivityDates = [...uniqueWorkoutDates, ...uniqueMealDates];
    const uniqueActivityDates = [...new Set(allActivityDates)].sort();

    // Calculate current streak
    let currentStreak = 0;
    let longestStreak = 0;
    let lastActiveDate;

    if (uniqueActivityDates.length > 0) {
      lastActiveDate = uniqueActivityDates[uniqueActivityDates.length - 1];

      // Calculate streaks
      // TODO: Implement streak calculation logic
    }

    // Create stats object
    const stats: UserStats = {
      userId,
      workouts: {
        total: workoutLogs.length,
        thisWeek: workoutsThisWeek,
        thisMonth: workoutsThisMonth,
        streak: workoutStreak,
        lastWorkoutDate: lastWorkoutDate?.toISOString(),
      },
      nutrition: {
        mealsLogged: mealLogs.length,
        avgCalories,
        avgProtein,
        streakDays: mealStreak,
        lastMealDate: lastMealDate?.toISOString(),
      },
      weight: {
        current: currentWeight,
        initial: initialWeight,
        goal: goalWeight,
        change: weightChange,
        changePercentage: weightChangePercentage,
        lastUpdated: lastWeightUpdate,
      },
      activity: {
        totalDaysActive: uniqueActivityDates.length,
        currentStreak,
        longestStreak,
        lastActiveDate,
      },
      updatedAt: now.toISOString(),
    };

    return stats;
  } catch (error) {
    console.error('Error calculating user stats:', error);
    return null;
  }
}

// Update user stats
export async function updateUserStats(): Promise<UserStats | null> {
  try {
    const userId = await getUserId();
    if (!userId) {
      console.error('No user ID available');
      return null;
    }

    // Calculate stats locally
    const calculatedStats = await calculateUserStats(userId);
    if (!calculatedStats) {
      return null;
    }

    // Save to local storage
    await saveLocalStats(calculatedStats);

    // Try to sync with server
    try {
      const response = await makeRequestWithRetry({
        url: '/user/stats',
        method: 'PUT',
        data: calculatedStats
      });

      if (response.data) {
        return response.data as UserStats;
      }
    } catch (error) {
      console.error('Error syncing stats with server:', error);
    }

    return calculatedStats;
  } catch (error) {
    console.error('Error updating user stats:', error);
    return null;
  }
}
