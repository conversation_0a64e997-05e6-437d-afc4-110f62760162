import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { WeatherData } from './nutritionService';

// Storage keys
const WEATHER_DATA_KEY = 'weather_data';

// Weather API key from secure environment variables
const API_KEY = process.env.WEATHER_API_KEY || '06576664b5b94743be730036252804';

/**
 * Fetch weather data directly from WeatherAPI.com using coordinates
 */
export async function fetchWeatherByCoordinates(
  latitude: number,
  longitude: number
): Promise<WeatherData | null> {
  try {
    console.log(`[Weather API Direct] Fetching weather for coordinates: ${latitude}, ${longitude}`);

    // Make direct axios request to WeatherAPI.com
    const response = await axios.get(
      `https://api.weatherapi.com/v1/current.json?key=${API_KEY}&q=${latitude},${longitude}&aqi=no`
    );

    if (response.data && response.data.current) {
      const windSpeedKph = response.data.current.wind_kph;
      const windSpeedMph = windSpeedKph ? Math.round(windSpeedKph * 0.621371) : null;

      const weatherData: WeatherData = {
        temperature: response.data.current.temp_c,
        temperatureF: response.data.current.temp_f,
        condition: response.data.current.condition.text,
        humidity: response.data.current.humidity,
        location: response.data.location.name,
        timestamp: new Date().toISOString(),
        feelsLike: response.data.current.feelslike_c,
        feelsLikeF: response.data.current.feelslike_f,
        windSpeed: windSpeedKph,
        windSpeedMph: windSpeedMph,
        windDirection: response.data.current.wind_dir,
        iconUrl: response.data.current.condition.icon,
        isDay: response.data.current.is_day === 1
      };

      console.log(`[Weather API Direct] Weather data fetched: ${weatherData.temperatureF}°F (${weatherData.temperature}°C), ${weatherData.condition} in ${weatherData.location}`);

      // Save to storage for future use
      await AsyncStorage.setItem(WEATHER_DATA_KEY, JSON.stringify(weatherData));

      return weatherData;
    }

    console.error('[Weather API Direct] Invalid response structure:', response.data);
    return null;
  } catch (error) {
    console.error('[Weather API Direct] Error fetching weather data:', error);
    return null;
  }
}

/**
 * Fetch weather data directly from WeatherAPI.com using city name
 */
export async function fetchWeatherByCity(city: string): Promise<WeatherData | null> {
  try {
    console.log(`[Weather API Direct] Fetching weather for city: ${city}`);

    // Make direct axios request to WeatherAPI.com
    const response = await axios.get(
      `https://api.weatherapi.com/v1/current.json?key=${API_KEY}&q=${city}&aqi=no`
    );

    if (response.data && response.data.current) {
      const windSpeedKph = response.data.current.wind_kph;
      const windSpeedMph = windSpeedKph ? Math.round(windSpeedKph * 0.621371) : null;

      const weatherData: WeatherData = {
        temperature: response.data.current.temp_c,
        temperatureF: response.data.current.temp_f,
        condition: response.data.current.condition.text,
        humidity: response.data.current.humidity,
        location: response.data.location.name,
        timestamp: new Date().toISOString(),
        feelsLike: response.data.current.feelslike_c,
        feelsLikeF: response.data.current.feelslike_f,
        windSpeed: windSpeedKph,
        windSpeedMph: windSpeedMph,
        windDirection: response.data.current.wind_dir,
        iconUrl: response.data.current.condition.icon,
        isDay: response.data.current.is_day === 1
      };

      console.log(`[Weather API Direct] Weather data fetched: ${weatherData.temperatureF}°F (${weatherData.temperature}°C), ${weatherData.condition} in ${weatherData.location}`);

      // Save to storage for future use
      await AsyncStorage.setItem(WEATHER_DATA_KEY, JSON.stringify(weatherData));

      return weatherData;
    }

    console.error('[Weather API Direct] Invalid response structure:', response.data);
    return null;
  } catch (error) {
    console.error('[Weather API Direct] Error fetching weather data:', error);
    return null;
  }
}

/**
 * Get unavailable weather data object
 */
export function getUnavailableWeatherData(): WeatherData {
  return {
    temperature: null,
    temperatureF: null,
    condition: 'Weather data unavailable',
    humidity: null,
    location: 'Unknown',
    timestamp: new Date().toISOString(),
    feelsLike: null,
    feelsLikeF: null,
    windSpeed: null,
    windSpeedMph: null,
    windDirection: null,
    iconUrl: null,
    isDay: true,
    unavailable: true
  };
}
