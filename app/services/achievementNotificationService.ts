import { Achievement } from './achievementService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants
const ACHIEVEMENT_NOTIFICATION_QUEUE_KEY = '@achievement_notification_queue';
const ACHIEVEMENT_NOTIFICATION_SEEN_KEY = '@achievement_notification_seen';

// Interface for notification queue item
export interface AchievementNotificationItem {
  achievement: Achievement;
  timestamp: string;
  seen: boolean;
}

// Get the notification queue
export async function getNotificationQueue(): Promise<AchievementNotificationItem[]> {
  try {
    const queueJson = await AsyncStorage.getItem(ACHIEVEMENT_NOTIFICATION_QUEUE_KEY);
    if (queueJson) {
      return JSON.parse(queueJson);
    }
    return [];
  } catch (error) {
    console.error('Error getting achievement notification queue:', error);
    return [];
  }
}

// Add an achievement to the notification queue
export async function addAchievementToNotificationQueue(achievement: Achievement): Promise<boolean> {
  try {
    // Check if this achievement has already been seen
    const seenAchievementsJson = await AsyncStorage.getItem(ACHIEVEMENT_NOTIFICATION_SEEN_KEY);
    const seenAchievements: string[] = seenAchievementsJson ? JSON.parse(seenAchievementsJson) : [];
    
    // If this achievement has already been seen and is unlocked, don't add it to the queue
    if (achievement.status === 'unlocked' && seenAchievements.includes(achievement.id)) {
      console.log(`Achievement ${achievement.id} has already been seen, skipping notification`);
      return false;
    }
    
    // Get the current queue
    const queue = await getNotificationQueue();
    
    // Check if this achievement is already in the queue
    const existingIndex = queue.findIndex(item => item.achievement.id === achievement.id);
    
    if (existingIndex >= 0) {
      // Update the existing item
      queue[existingIndex] = {
        achievement,
        timestamp: new Date().toISOString(),
        seen: false,
      };
    } else {
      // Add the new item
      queue.push({
        achievement,
        timestamp: new Date().toISOString(),
        seen: false,
      });
    }
    
    // Save the updated queue
    await AsyncStorage.setItem(ACHIEVEMENT_NOTIFICATION_QUEUE_KEY, JSON.stringify(queue));
    
    return true;
  } catch (error) {
    console.error('Error adding achievement to notification queue:', error);
    return false;
  }
}

// Mark an achievement as seen
export async function markAchievementAsSeen(achievementId: string): Promise<boolean> {
  try {
    // Get the current queue
    const queue = await getNotificationQueue();
    
    // Find the achievement in the queue
    const existingIndex = queue.findIndex(item => item.achievement.id === achievementId);
    
    if (existingIndex >= 0) {
      // Mark as seen
      queue[existingIndex].seen = true;
      
      // Save the updated queue
      await AsyncStorage.setItem(ACHIEVEMENT_NOTIFICATION_QUEUE_KEY, JSON.stringify(queue));
      
      // Also add to the seen achievements list
      const seenAchievementsJson = await AsyncStorage.getItem(ACHIEVEMENT_NOTIFICATION_SEEN_KEY);
      const seenAchievements: string[] = seenAchievementsJson ? JSON.parse(seenAchievementsJson) : [];
      
      if (!seenAchievements.includes(achievementId)) {
        seenAchievements.push(achievementId);
        await AsyncStorage.setItem(ACHIEVEMENT_NOTIFICATION_SEEN_KEY, JSON.stringify(seenAchievements));
      }
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error marking achievement as seen:', error);
    return false;
  }
}

// Remove an achievement from the notification queue
export async function removeAchievementFromNotificationQueue(achievementId: string): Promise<boolean> {
  try {
    // Get the current queue
    const queue = await getNotificationQueue();
    
    // Filter out the achievement
    const updatedQueue = queue.filter(item => item.achievement.id !== achievementId);
    
    // Save the updated queue
    await AsyncStorage.setItem(ACHIEVEMENT_NOTIFICATION_QUEUE_KEY, JSON.stringify(updatedQueue));
    
    return true;
  } catch (error) {
    console.error('Error removing achievement from notification queue:', error);
    return false;
  }
}

// Get the next achievement notification to show
export async function getNextAchievementNotification(): Promise<AchievementNotificationItem | null> {
  try {
    // Get the current queue
    const queue = await getNotificationQueue();
    
    // Find the first unseen achievement
    const nextNotification = queue.find(item => !item.seen);
    
    return nextNotification || null;
  } catch (error) {
    console.error('Error getting next achievement notification:', error);
    return null;
  }
}

// Clear all notifications
export async function clearAllAchievementNotifications(): Promise<boolean> {
  try {
    await AsyncStorage.setItem(ACHIEVEMENT_NOTIFICATION_QUEUE_KEY, JSON.stringify([]));
    return true;
  } catch (error) {
    console.error('Error clearing achievement notifications:', error);
    return false;
  }
}
