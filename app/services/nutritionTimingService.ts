/**
 * Nutrition Timing Optimization Service - Optimizes meal timing for performance and goals
 * 
 * Features:
 * - Pre/post workout nutrition recommendations
 * - Meal timing optimization based on circadian rhythms
 * - Nutrient timing for specific goals (muscle gain, fat loss, performance)
 * - Hydration timing recommendations
 * - Supplement timing optimization
 * - Intermittent fasting window optimization
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest } from './apiRequestManager';

export enum NutritionGoal {
  MUSCLE_GAIN = 'muscle_gain',
  FAT_LOSS = 'fat_loss',
  PERFORMANCE = 'performance',
  RECOVERY = 'recovery',
  GENERAL_HEALTH = 'general_health'
}

export enum MealType {
  PRE_WORKOUT = 'pre_workout',
  POST_WORKOUT = 'post_workout',
  BREAKFAST = 'breakfast',
  LUNCH = 'lunch',
  DINNER = 'dinner',
  SNACK = 'snack'
}

export interface NutritionTiming {
  mealType: MealType;
  optimalTime: string; // HH:MM format
  timeWindow: {
    earliest: string;
    latest: string;
  };
  macroRatios: {
    protein: number;
    carbs: number;
    fat: number;
  };
  calories: number;
  reasoning: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
}

export interface WorkoutNutrition {
  preWorkout: {
    timing: number; // minutes before workout
    recommendations: NutritionRecommendation[];
    hydration: HydrationRecommendation;
  };
  duringWorkout: {
    recommendations: NutritionRecommendation[];
    hydration: HydrationRecommendation;
  };
  postWorkout: {
    timing: number; // minutes after workout
    recommendations: NutritionRecommendation[];
    hydration: HydrationRecommendation;
    recoveryWindow: number; // minutes for optimal recovery nutrition
  };
}

export interface NutritionRecommendation {
  food: string;
  amount: string;
  macros: {
    protein: number;
    carbs: number;
    fat: number;
    calories: number;
  };
  reasoning: string;
  alternatives?: string[];
}

export interface HydrationRecommendation {
  amount: number; // ml
  timing: string;
  type: 'water' | 'electrolyte' | 'sports_drink';
  reasoning: string;
}

export interface CircadianOptimization {
  chronotype: 'morning' | 'evening' | 'intermediate';
  mealTimings: NutritionTiming[];
  fastingWindow?: {
    start: string;
    end: string;
    duration: number; // hours
  };
  supplementTiming: SupplementTiming[];
}

export interface SupplementTiming {
  supplement: string;
  optimalTime: string;
  reasoning: string;
  interactions?: string[];
}

export interface NutritionTimingProfile {
  userId: string;
  goal: NutritionGoal;
  workoutTimes: string[]; // Typical workout times
  sleepSchedule: {
    bedtime: string;
    wakeTime: string;
  };
  mealPreferences: {
    mealsPerDay: number;
    intermittentFasting: boolean;
    fastingHours?: number;
  };
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'high' | 'very_high';
  bodyComposition: {
    weight: number;
    bodyFat?: number;
    muscleMass?: number;
  };
  metabolicRate: number;
  lastUpdated: string;
}

const TIMING_PROFILE_KEY = '@nutrition_timing_profile';
const RECOMMENDATIONS_CACHE_KEY = '@nutrition_timing_recommendations';

class NutritionTimingService {
  private profile: NutritionTimingProfile | null = null;
  private cachedRecommendations: CircadianOptimization | null = null;

  /**
   * Create or update nutrition timing profile
   */
  async updateProfile(profileData: Partial<NutritionTimingProfile>): Promise<NutritionTimingProfile> {
    const existingProfile = await this.getProfile();
    
    const updatedProfile: NutritionTimingProfile = {
      ...existingProfile,
      ...profileData,
      lastUpdated: new Date().toISOString()
    };

    this.profile = updatedProfile;
    await this.saveProfile();

    // Clear cached recommendations when profile changes
    this.cachedRecommendations = null;

    console.log('[NutritionTiming] Profile updated');
    return updatedProfile;
  }

  /**
   * Get optimized nutrition timing recommendations
   */
  async getOptimizedTiming(): Promise<CircadianOptimization> {
    if (this.cachedRecommendations) {
      return this.cachedRecommendations;
    }

    const profile = await this.getProfile();
    const optimization = await this.generateOptimizedTiming(profile);
    
    this.cachedRecommendations = optimization;
    await this.cacheRecommendations(optimization);

    return optimization;
  }

  /**
   * Get workout-specific nutrition recommendations
   */
  async getWorkoutNutrition(
    workoutTime: string,
    workoutDuration: number,
    workoutIntensity: 'low' | 'moderate' | 'high'
  ): Promise<WorkoutNutrition> {
    const profile = await this.getProfile();
    
    return {
      preWorkout: this.generatePreWorkoutNutrition(profile, workoutTime, workoutIntensity),
      duringWorkout: this.generateDuringWorkoutNutrition(profile, workoutDuration, workoutIntensity),
      postWorkout: this.generatePostWorkoutNutrition(profile, workoutIntensity)
    };
  }

  /**
   * Get meal timing recommendations for a specific day
   */
  async getDailyMealTiming(date: string): Promise<NutritionTiming[]> {
    const optimization = await this.getOptimizedTiming();
    const profile = await this.getProfile();

    // Adjust timings based on workout schedule for the day
    const workoutTime = this.getWorkoutTimeForDate(date, profile.workoutTimes);
    
    if (workoutTime) {
      return this.adjustTimingsForWorkout(optimization.mealTimings, workoutTime);
    }

    return optimization.mealTimings;
  }

  /**
   * Get hydration schedule for the day
   */
  async getHydrationSchedule(): Promise<HydrationRecommendation[]> {
    const profile = await this.getProfile();
    const schedule: HydrationRecommendation[] = [];

    // Wake-up hydration
    schedule.push({
      amount: 500,
      timing: profile.sleepSchedule.wakeTime,
      type: 'water',
      reasoning: 'Rehydrate after overnight fast'
    });

    // Throughout the day
    const wakeHour = parseInt(profile.sleepSchedule.wakeTime.split(':')[0]);
    const bedHour = parseInt(profile.sleepSchedule.bedtime.split(':')[0]);
    
    for (let hour = wakeHour + 2; hour < bedHour - 2; hour += 2) {
      schedule.push({
        amount: 250,
        timing: `${hour.toString().padStart(2, '0')}:00`,
        type: 'water',
        reasoning: 'Maintain hydration throughout the day'
      });
    }

    return schedule;
  }

  /**
   * Private methods for generating recommendations
   */
  private async generateOptimizedTiming(profile: NutritionTimingProfile): Promise<CircadianOptimization> {
    const chronotype = this.determineChronotype(profile.sleepSchedule);
    const mealTimings = this.generateMealTimings(profile, chronotype);
    const fastingWindow = profile.mealPreferences.intermittentFasting ? 
      this.generateFastingWindow(profile) : undefined;
    const supplementTiming = this.generateSupplementTiming(profile, chronotype);

    return {
      chronotype,
      mealTimings,
      fastingWindow,
      supplementTiming
    };
  }

  private determineChronotype(sleepSchedule: NutritionTimingProfile['sleepSchedule']): 'morning' | 'evening' | 'intermediate' {
    const wakeHour = parseInt(sleepSchedule.wakeTime.split(':')[0]);
    const bedHour = parseInt(sleepSchedule.bedtime.split(':')[0]);

    if (wakeHour <= 6 && bedHour <= 22) {
      return 'morning';
    } else if (wakeHour >= 8 && bedHour >= 24) {
      return 'evening';
    } else {
      return 'intermediate';
    }
  }

  private generateMealTimings(profile: NutritionTimingProfile, chronotype: string): NutritionTiming[] {
    const timings: NutritionTiming[] = [];
    const wakeTime = profile.sleepSchedule.wakeTime;
    const bedTime = profile.sleepSchedule.bedtime;
    
    const dailyCalories = profile.metabolicRate;
    const mealsPerDay = profile.mealPreferences.mealsPerDay;

    // Breakfast timing
    const breakfastTime = this.addMinutesToTime(wakeTime, chronotype === 'morning' ? 30 : 60);
    timings.push({
      mealType: MealType.BREAKFAST,
      optimalTime: breakfastTime,
      timeWindow: {
        earliest: this.addMinutesToTime(wakeTime, 15),
        latest: this.addMinutesToTime(wakeTime, 120)
      },
      macroRatios: this.getMacroRatiosForGoal(profile.goal, 'breakfast'),
      calories: Math.round(dailyCalories * 0.25),
      reasoning: 'Kickstart metabolism and provide energy for the day',
      importance: 'high'
    });

    // Lunch timing
    const lunchTime = this.addMinutesToTime(breakfastTime, 4 * 60); // 4 hours after breakfast
    timings.push({
      mealType: MealType.LUNCH,
      optimalTime: lunchTime,
      timeWindow: {
        earliest: this.addMinutesToTime(lunchTime, -60),
        latest: this.addMinutesToTime(lunchTime, 60)
      },
      macroRatios: this.getMacroRatiosForGoal(profile.goal, 'lunch'),
      calories: Math.round(dailyCalories * 0.35),
      reasoning: 'Maintain energy levels and support afternoon activities',
      importance: 'high'
    });

    // Dinner timing
    const dinnerTime = this.addMinutesToTime(bedTime, -3 * 60); // 3 hours before bed
    timings.push({
      mealType: MealType.DINNER,
      optimalTime: dinnerTime,
      timeWindow: {
        earliest: this.addMinutesToTime(dinnerTime, -90),
        latest: this.addMinutesToTime(dinnerTime, 60)
      },
      macroRatios: this.getMacroRatiosForGoal(profile.goal, 'dinner'),
      calories: Math.round(dailyCalories * 0.30),
      reasoning: 'Support recovery and avoid late-night eating',
      importance: 'medium'
    });

    // Add snacks if more than 3 meals per day
    if (mealsPerDay > 3) {
      const remainingCalories = dailyCalories * 0.10;
      const snackCalories = remainingCalories / (mealsPerDay - 3);

      // Morning snack
      if (mealsPerDay >= 4) {
        timings.push({
          mealType: MealType.SNACK,
          optimalTime: this.addMinutesToTime(breakfastTime, 2.5 * 60),
          timeWindow: {
            earliest: this.addMinutesToTime(breakfastTime, 2 * 60),
            latest: this.addMinutesToTime(lunchTime, -30)
          },
          macroRatios: { protein: 0.3, carbs: 0.4, fat: 0.3 },
          calories: Math.round(snackCalories),
          reasoning: 'Maintain energy between breakfast and lunch',
          importance: 'low'
        });
      }

      // Afternoon snack
      if (mealsPerDay >= 5) {
        timings.push({
          mealType: MealType.SNACK,
          optimalTime: this.addMinutesToTime(lunchTime, 2.5 * 60),
          timeWindow: {
            earliest: this.addMinutesToTime(lunchTime, 2 * 60),
            latest: this.addMinutesToTime(dinnerTime, -30)
          },
          macroRatios: { protein: 0.3, carbs: 0.3, fat: 0.4 },
          calories: Math.round(snackCalories),
          reasoning: 'Prevent afternoon energy dip',
          importance: 'low'
        });
      }
    }

    return timings;
  }

  private generateFastingWindow(profile: NutritionTimingProfile): { start: string; end: string; duration: number } {
    const fastingHours = profile.mealPreferences.fastingHours || 16;
    const dinnerTime = this.addMinutesToTime(profile.sleepSchedule.bedtime, -3 * 60);
    const fastingStart = this.addMinutesToTime(dinnerTime, 2 * 60); // 2 hours after dinner
    const fastingEnd = this.addMinutesToTime(fastingStart, fastingHours * 60);

    return {
      start: fastingStart,
      end: fastingEnd,
      duration: fastingHours
    };
  }

  private generateSupplementTiming(profile: NutritionTimingProfile, chronotype: string): SupplementTiming[] {
    const timings: SupplementTiming[] = [];

    // Vitamin D (morning)
    timings.push({
      supplement: 'Vitamin D',
      optimalTime: this.addMinutesToTime(profile.sleepSchedule.wakeTime, 30),
      reasoning: 'Best absorbed with morning meal and supports circadian rhythm'
    });

    // Magnesium (evening)
    timings.push({
      supplement: 'Magnesium',
      optimalTime: this.addMinutesToTime(profile.sleepSchedule.bedtime, -60),
      reasoning: 'Promotes relaxation and better sleep quality'
    });

    // Protein powder (post-workout or morning)
    if (profile.goal === NutritionGoal.MUSCLE_GAIN) {
      timings.push({
        supplement: 'Protein Powder',
        optimalTime: 'Post-workout or morning',
        reasoning: 'Supports muscle protein synthesis when protein needs are highest'
      });
    }

    return timings;
  }

  private generatePreWorkoutNutrition(
    profile: NutritionTimingProfile,
    workoutTime: string,
    intensity: string
  ): WorkoutNutrition['preWorkout'] {
    const timing = intensity === 'high' ? 60 : 30; // minutes before workout
    
    const recommendations: NutritionRecommendation[] = [];
    
    if (intensity === 'high') {
      recommendations.push({
        food: 'Banana with almond butter',
        amount: '1 medium banana + 1 tbsp almond butter',
        macros: { protein: 4, carbs: 30, fat: 8, calories: 190 },
        reasoning: 'Provides quick energy and sustained fuel',
        alternatives: ['Oatmeal with berries', 'Toast with honey']
      });
    } else {
      recommendations.push({
        food: 'Apple slices',
        amount: '1 medium apple',
        macros: { protein: 0, carbs: 25, fat: 0, calories: 95 },
        reasoning: 'Light carbs for energy without digestive stress',
        alternatives: ['Handful of dates', 'Small smoothie']
      });
    }

    return {
      timing,
      recommendations,
      hydration: {
        amount: 500,
        timing: `${timing} minutes before workout`,
        type: 'water',
        reasoning: 'Ensure proper hydration for performance'
      }
    };
  }

  private generateDuringWorkoutNutrition(
    profile: NutritionTimingProfile,
    duration: number,
    intensity: string
  ): WorkoutNutrition['duringWorkout'] {
    const recommendations: NutritionRecommendation[] = [];
    
    // Only recommend during-workout nutrition for longer, intense sessions
    if (duration > 60 && intensity === 'high') {
      recommendations.push({
        food: 'Sports drink',
        amount: '150-200ml every 15-20 minutes',
        macros: { protein: 0, carbs: 15, fat: 0, calories: 60 },
        reasoning: 'Maintain blood sugar and electrolyte balance',
        alternatives: ['Diluted fruit juice', 'Coconut water']
      });
    }

    return {
      recommendations,
      hydration: {
        amount: duration > 60 ? 750 : 500,
        timing: 'Throughout workout',
        type: duration > 60 ? 'electrolyte' : 'water',
        reasoning: 'Replace fluids lost through sweat'
      }
    };
  }

  private generatePostWorkoutNutrition(
    profile: NutritionTimingProfile,
    intensity: string
  ): WorkoutNutrition['postWorkout'] {
    const timing = 30; // minutes after workout
    const recoveryWindow = 120; // 2-hour anabolic window
    
    const recommendations: NutritionRecommendation[] = [];
    
    if (profile.goal === NutritionGoal.MUSCLE_GAIN) {
      recommendations.push({
        food: 'Protein shake with banana',
        amount: '1 scoop whey protein + 1 banana',
        macros: { protein: 25, carbs: 30, fat: 2, calories: 240 },
        reasoning: 'Optimize muscle protein synthesis and glycogen replenishment',
        alternatives: ['Greek yogurt with berries', 'Chocolate milk']
      });
    } else if (profile.goal === NutritionGoal.FAT_LOSS) {
      recommendations.push({
        food: 'Protein shake',
        amount: '1 scoop protein powder in water',
        macros: { protein: 25, carbs: 3, fat: 1, calories: 120 },
        reasoning: 'Support muscle recovery while maintaining caloric deficit',
        alternatives: ['Hard-boiled eggs', 'Cottage cheese']
      });
    }

    return {
      timing,
      recommendations,
      recoveryWindow,
      hydration: {
        amount: 750,
        timing: 'Within 30 minutes post-workout',
        type: 'electrolyte',
        reasoning: 'Rehydrate and replace electrolytes lost during exercise'
      }
    };
  }

  private getMacroRatiosForGoal(goal: NutritionGoal, mealType: string): { protein: number; carbs: number; fat: number } {
    const ratios = {
      [NutritionGoal.MUSCLE_GAIN]: { protein: 0.30, carbs: 0.45, fat: 0.25 },
      [NutritionGoal.FAT_LOSS]: { protein: 0.35, carbs: 0.35, fat: 0.30 },
      [NutritionGoal.PERFORMANCE]: { protein: 0.25, carbs: 0.55, fat: 0.20 },
      [NutritionGoal.RECOVERY]: { protein: 0.30, carbs: 0.40, fat: 0.30 },
      [NutritionGoal.GENERAL_HEALTH]: { protein: 0.25, carbs: 0.45, fat: 0.30 }
    };

    return ratios[goal];
  }

  private addMinutesToTime(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }

  private adjustTimingsForWorkout(timings: NutritionTiming[], workoutTime: string): NutritionTiming[] {
    // Adjust meal timings when there's a workout scheduled
    return timings.map(timing => {
      // Add pre/post workout meals if needed
      // This is a simplified version - would be more complex in practice
      return timing;
    });
  }

  private getWorkoutTimeForDate(date: string, workoutTimes: string[]): string | null {
    // Simple implementation - would check actual workout schedule
    return workoutTimes.length > 0 ? workoutTimes[0] : null;
  }

  private async getProfile(): Promise<NutritionTimingProfile> {
    if (this.profile) {
      return this.profile;
    }

    try {
      const stored = await AsyncStorage.getItem(TIMING_PROFILE_KEY);
      if (stored) {
        this.profile = JSON.parse(stored);
        return this.profile!;
      }
    } catch (error) {
      console.error('[NutritionTiming] Error loading profile:', error);
    }

    // Return default profile
    return {
      userId: 'default',
      goal: NutritionGoal.GENERAL_HEALTH,
      workoutTimes: ['07:00'],
      sleepSchedule: { bedtime: '22:00', wakeTime: '06:00' },
      mealPreferences: { mealsPerDay: 3, intermittentFasting: false },
      activityLevel: 'moderate',
      bodyComposition: { weight: 70 },
      metabolicRate: 2000,
      lastUpdated: new Date().toISOString()
    };
  }

  private async saveProfile(): Promise<void> {
    try {
      if (this.profile) {
        await AsyncStorage.setItem(TIMING_PROFILE_KEY, JSON.stringify(this.profile));
      }
    } catch (error) {
      console.error('[NutritionTiming] Error saving profile:', error);
    }
  }

  private async cacheRecommendations(recommendations: CircadianOptimization): Promise<void> {
    try {
      await AsyncStorage.setItem(RECOMMENDATIONS_CACHE_KEY, JSON.stringify({
        recommendations,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('[NutritionTiming] Error caching recommendations:', error);
    }
  }
}

// Export singleton instance
export const nutritionTimingService = new NutritionTimingService();

// Convenience functions
export const updateNutritionTimingProfile = (profileData: Partial<NutritionTimingProfile>) =>
  nutritionTimingService.updateProfile(profileData);

export const getOptimizedNutritionTiming = () =>
  nutritionTimingService.getOptimizedTiming();

export const getWorkoutNutrition = (workoutTime: string, duration: number, intensity: 'low' | 'moderate' | 'high') =>
  nutritionTimingService.getWorkoutNutrition(workoutTime, duration, intensity);

export const getDailyMealTiming = (date: string) =>
  nutritionTimingService.getDailyMealTiming(date);

export const getHydrationSchedule = () =>
  nutritionTimingService.getHydrationSchedule();
