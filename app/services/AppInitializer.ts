import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStoredTokens } from './auth';
import { getProfileFromStorage } from './profile';
import { getApiUrl, isNetworkAvailable } from './apiClient';
import { getContextData } from './contextService';
import { getLogs } from './conversationService';
import { getWeightEntries } from './weightTracking';
import { getNutritionTargets, getTodayNutrition, getCurrentWeather, getNutritionProfile } from './nutritionService';
import { checkAndResetDailyNutrition } from './nutritionResetService';
import { getUserAchievements } from './achievementService';
import { getUserStats } from './userStatsService';
import { getNextMeal, getNextWorkout } from './recommendationService';

// Define initialization steps
export type InitStep = {
  id: string;
  name: string;
  description: string;
  task: () => Promise<any>;
  required: boolean;
  timeout?: number;
  retries?: number;
  dependencies?: string[];
  priority?: 'critical' | 'high' | 'medium' | 'low';
};

// Define initialization status
export type InitStatus = {
  step: string;
  progress: number;
  message: string;
  error?: string;
  isComplete: boolean;
};

// Define initialization result
export type InitResult = {
  success: boolean;
  data: Record<string, any>;
  errors: Record<string, string>;
  skipped: string[];
};

// Define initialization options
export type InitOptions = {
  onProgress?: (status: InitStatus) => void;
  onComplete?: (result: InitResult) => void;
  onError?: (error: Error, step: string) => void;
  timeout?: number;
  skipIfOffline?: string[];
  forceRefresh?: boolean;
};

// Define critical initialization steps - only these run at startup
const criticalInitSteps: InitStep[] = [
  {
    id: 'network',
    name: 'Network Check',
    description: 'Checking network connectivity',
    task: async () => {
      const available = await isNetworkAvailable();
      return { isNetworkAvailable: available };
    },
    required: true,
    timeout: 3000,
    retries: 1,
    priority: 'critical'
  },
  {
    id: 'api',
    name: 'API Configuration',
    description: 'Configuring API connection',
    task: async () => {
      const apiUrl = await getApiUrl();
      return { apiUrl };
    },
    required: true,
    timeout: 2000,
    dependencies: ['network'],
    priority: 'critical'
  },
  {
    id: 'auth',
    name: 'Authentication',
    description: 'Retrieving authentication tokens',
    task: async () => {
      const tokens = await getStoredTokens();
      return { tokens };
    },
    required: true,
    timeout: 3000,
    priority: 'critical'
  },
  {
    id: 'profile',
    name: 'User Profile',
    description: 'Loading user profile',
    task: async () => {
      const profile = await getProfileFromStorage();
      return { profile };
    },
    required: false,
    timeout: 3000,
    dependencies: ['auth'],
    priority: 'critical'
  }
];

// Define post-initialization steps that can run after UI is shown
const postInitSteps: InitStep[] = [
  {
    id: 'nutrition',
    name: 'Nutrition Data',
    description: 'Loading nutrition data',
    task: async () => {
      try {
        // Import services dynamically to avoid circular dependencies
        const { checkAndResetDailyNutrition } = require('./nutritionResetService');
        const { getNutritionProfile, getNutritionTargets, getTodayNutrition } = require('./nutritionService');
        
        // Reset nutrition data at midnight if needed
        await checkAndResetDailyNutrition();

        // Get nutrition profile and data in parallel
        const [profile, targets, consumed] = await Promise.all([
          getNutritionProfile(),
          getNutritionTargets(),
          getTodayNutrition()
        ]);

        return {
          nutritionProfile: profile,
          nutritionTargets: targets,
          todayNutrition: consumed
        };
      } catch (error) {
        console.warn('Error loading nutrition data:', error);
        // Return default nutrition data instead of failing
        return {
          nutritionProfile: {
            weight: 150,
            activityLevel: 'moderatelyActive',
            goal: 'maintain',
            dietaryPreferences: []
          },
          nutritionTargets: {
            calories: 2000,
            protein: 150,
            carbs: 200,
            fat: 65
          },
          todayNutrition: {
            calories: 0,
            protein: 0,
            carbs: 0,
            fat: 0
          }
        };
      }
    },
    required: false,
    timeout: 10000,
    dependencies: ['auth', 'profile'],
    priority: 'high'
  },
  {
    id: 'logs',
    name: 'Activity Logs',
    description: 'Loading activity history',
    task: async () => {
      try {
        const { getLogs } = require('./conversationService');
        const logs = await getLogs();
        return { logs };
      } catch (error) {
        console.warn('Error loading activity logs, using empty array:', error);
        return { logs: [] };
      }
    },
    required: false,
    timeout: 8000,
    dependencies: ['auth', 'profile'],
    priority: 'medium'
  },
  {
    id: 'context',
    name: 'User Context',
    description: 'Loading user context data',
    task: async () => {
      try {
        const { getContextData } = require('./contextService');
        const contextData = await getContextData();
        return { contextData };
      } catch (error) {
        console.warn('Error loading user context, using empty array:', error);
        return { contextData: [] };
      }
    },
    required: false,
    timeout: 8000,
    dependencies: ['auth', 'profile'],
    priority: 'medium'
  },
  {
    id: 'weight',
    name: 'Weight Data',
    description: 'Loading weight tracking data',
    task: async () => {
      try {
        const { getWeightEntries } = require('./weightTracking');
        const weightEntries = await getWeightEntries();
        return { weightEntries };
      } catch (error) {
        console.warn('Error loading weight data, using empty array:', error);
        return { weightEntries: [] };
      }
    },
    required: false,
    timeout: 6000,
    dependencies: ['auth', 'profile'],
    priority: 'medium'
  },
  {
    id: 'weather',
    name: 'Weather Data',
    description: 'Fetching weather information',
    task: async () => {
      try {
        const { getCurrentWeather } = require('./nutritionService');
        const weather = await getCurrentWeather();
        return { weather };
      } catch (error) {
        console.warn('Error fetching weather data:', error);
        return { weather: null };
      }
    },
    required: false,
    timeout: 6000,
    dependencies: ['network'],
    priority: 'low'
  },
  {
    id: 'achievements',
    name: 'Achievements',
    description: 'Loading achievements',
    task: async () => {
      try {
        const { getUserAchievements } = require('./achievementService');
        const achievements = await getUserAchievements();
        return { achievements };
      } catch (error) {
        console.warn('Error loading achievements, using empty array:', error);
        return { achievements: [] };
      }
    },
    required: false,
    timeout: 6000,
    dependencies: ['auth', 'profile'],
    priority: 'low'
  },
  {
    id: 'stats',
    name: 'User Stats',
    description: 'Loading user statistics',
    task: async () => {
      try {
        const { getUserStats } = require('./userStatsService');
        const stats = await getUserStats();
        return { stats };
      } catch (error) {
        console.warn('Error loading user stats, using default stats:', error);
        return {
          stats: {
            userId: 'default',
            workouts: { total: 0, thisWeek: 0, thisMonth: 0, streak: 0 },
            nutrition: { mealsLogged: 0, avgCalories: 0, avgProtein: 0, streakDays: 0 },
            weight: {},
            activity: { totalDaysActive: 0, currentStreak: 0, longestStreak: 0 },
            updatedAt: new Date().toISOString(),
          }
        };
      }
    },
    required: false,
    timeout: 8000,
    dependencies: ['auth', 'profile'],
    priority: 'medium'
  }
];

// Initialize app with only critical steps
export async function initializeApp(options: InitOptions = {}): Promise<InitResult> {
  const {
    onProgress,
    onComplete,
    onError,
    timeout = 15000, // Reduced from 45000
    skipIfOffline = ['weather'],
    forceRefresh = false
  } = options;

  // Check if we've initialized recently (within last 5 minutes) and not forcing refresh
  if (!forceRefresh) {
    try {
      const lastInit = await AsyncStorage.getItem('app_last_init');
      if (lastInit) {
        const lastInitTime = new Date(lastInit).getTime();
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;
        
        if (now - lastInitTime < fiveMinutes) {
          console.log('App initialized recently, using cached data');
          
          if (onProgress) {
            onProgress({
              step: 'cached',
              progress: 1,
              message: 'Using cached data',
              isComplete: true
            });
          }

          if (onComplete) {
            onComplete({
              success: true,
              data: { cached: true },
              errors: {},
              skipped: []
            });
          }

          return {
            success: true,
            data: { cached: true },
            errors: {},
            skipped: []
          };
        }
      }
    } catch (error) {
      console.warn('Error checking last initialization:', error);
    }
  }

  // Initialize result
  const result: InitResult = {
    success: true,
    data: {},
    errors: {},
    skipped: []
  };

  // Set up global timeout
  const globalTimeoutId = setTimeout(() => {
    console.error('App initialization timed out after', timeout, 'ms');
    if (onError) {
      onError(new Error(`Initialization timed out after ${timeout}ms`), 'global');
    }
  }, timeout);

  try {
    // Calculate total steps for progress reporting
    const totalSteps = criticalInitSteps.length;
    let completedSteps = 0;

    // Execute critical steps in order (these will block UI)
    for (const step of criticalInitSteps) {
      try {
        // Report start of step
        if (onProgress) {
          onProgress({
            step: step.id,
            progress: completedSteps / totalSteps,
            message: step.description,
            isComplete: false
          });
        }

        // Execute step with timeout
        const stepResult = await Promise.race([
          step.task(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error(`Step ${step.id} timed out`)),
            step.timeout || 5000)
          )
        ]);

        // Store result
        result.data = { ...result.data, ...stepResult };
        completedSteps++;

        // Report progress
        if (onProgress) {
          onProgress({
            step: step.id,
            progress: completedSteps / totalSteps,
            message: `${step.name} complete`,
            isComplete: false
          });
        }
      } catch (error: any) {
        console.error(`Error in initialization step ${step.id}:`, error);

        // Store error
        result.errors[step.id] = error.message || 'Unknown error';

        // Report error
        if (onError) {
          onError(error instanceof Error ? error : new Error(error?.message || 'Unknown error'), step.id);
        }

        // Report progress with error
        if (onProgress) {
          onProgress({
            step: step.id,
            progress: completedSteps / totalSteps,
            message: `Error in ${step.name}`,
            error: error.message || 'Unknown error',
            isComplete: false
          });
        }

        // If this step is required, mark initialization as failed
        if (step.required) {
          result.success = false;
          break;
        }

        // Otherwise continue with next step
        completedSteps++;
      }
    }

    // Clear global timeout
    clearTimeout(globalTimeoutId);

    // Report completion of critical initialization
    if (onProgress) {
      onProgress({
        step: 'complete',
        progress: 1,
        message: result.success ? 'Essential data loaded' : 'Initialization completed with errors',
        isComplete: true
      });
    }

    if (onComplete) {
      onComplete(result);
    }

    // Start loading non-critical data in the background
    setTimeout(() => {
      loadPostInitData().catch(err => {
        console.warn('Error loading post-init data:', err);
      });
    }, 500);

    return result;
  } catch (error: any) {
    // Clear global timeout
    clearTimeout(globalTimeoutId);

    console.error('Unexpected error during app initialization:', error);

    // Report error
    if (onError) {
      onError(error instanceof Error ? error : new Error(error?.message || 'Unknown error'), 'global');
    }

    // Report completion with error
    if (onProgress) {
      onProgress({
        step: 'error',
        progress: 1,
        message: 'Initialization failed',
        error: error.message || 'Unknown error',
        isComplete: true
      });
    }

    if (onComplete) {
      onComplete({
        success: false,
        data: {},
        errors: { global: error.message || 'Unknown error' },
        skipped: []
      });
    }

    return {
      success: false,
      data: {},
      errors: { global: error.message || 'Unknown error' },
      skipped: []
    };
  }
}

// Load non-critical data in the background after UI is shown
async function loadPostInitData(): Promise<void> {
  console.log('Starting background data loading...');
  
  try {
    // Group steps by priority
    const highPriority = postInitSteps.filter(step => step.priority === 'high');
    const mediumPriority = postInitSteps.filter(step => step.priority === 'medium');
    const lowPriority = postInitSteps.filter(step => step.priority === 'low');
    
    // Load high priority data first (parallel)
    if (highPriority.length > 0) {
      console.log('Loading high priority data...');
      const highPriorityPromises = highPriority.map(step => executeStep(step));
      await Promise.allSettled(highPriorityPromises);
    }
    
    // Load medium priority data next (parallel)
    if (mediumPriority.length > 0) {
      console.log('Loading medium priority data...');
      const mediumPriorityPromises = mediumPriority.map(step => executeStep(step));
      await Promise.allSettled(mediumPriorityPromises);
    }
    
    // Load low priority data last (parallel)
    if (lowPriority.length > 0) {
      console.log('Loading low priority data...');
      const lowPriorityPromises = lowPriority.map(step => executeStep(step));
      await Promise.allSettled(lowPriorityPromises);
    }
    
    // Set initialization timestamp
    const timestamp = new Date().toISOString();
    await AsyncStorage.setItem('app_last_init', timestamp);
    
    console.log('Background data loading complete');
  } catch (error) {
    console.warn('Error during background data loading:', error);
  }
}

// Helper function to execute a step with timeout
async function executeStep(step: InitStep): Promise<any> {
  try {
    console.log(`Executing background step: ${step.id}`);
    const result = await Promise.race([
      step.task(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Step ${step.id} timed out`)),
        step.timeout || 8000)
      )
    ]);
    
    // Cache the result
    await AsyncStorage.setItem(`app_data_${step.id}`, JSON.stringify({
      data: result,
      timestamp: Date.now()
    }));
    
    console.log(`Background step complete: ${step.id}`);
    return result;
  } catch (error) {
    console.warn(`Error in background step ${step.id}:`, error);
    return null;
  }
}

// Function to get cached data for a specific step
export async function getCachedStepData(stepId: string): Promise<any | null> {
  try {
    const cached = await AsyncStorage.getItem(`app_data_${stepId}`);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      // Check if data is less than 30 minutes old
      if (Date.now() - timestamp < 30 * 60 * 1000) {
        return data;
      }
    }
    return null;
  } catch (error) {
    console.warn(`Error getting cached data for ${stepId}:`, error);
    return null;
  }
}
