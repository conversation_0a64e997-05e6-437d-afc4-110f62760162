import AsyncStorage from '@react-native-async-storage/async-storage';
import { getCachedStepData } from './AppInitializer';

// Key app data that needs preloading
export type PreloadableData = 
  | 'nutrition'
  | 'logs'
  | 'weight'
  | 'weather'
  | 'achievements'
  | 'stats';

/**
 * Preload essential app data for a screen
 * @param dataTypes - Array of data types to preload
 * @returns Object with loaded data
 */
export async function preloadData(dataTypes: PreloadableData[]): Promise<Record<string, any>> {
  console.log('[Preload] Preloading data:', dataTypes);
  
  const results: Record<string, any> = {};
  
  // Process each data type in parallel
  await Promise.all(dataTypes.map(async (dataType) => {
    try {
      // First try to get from cached initialization data
      const cachedData = await getCachedStepData(dataType);
      if (cachedData) {
        results[dataType] = cachedData;
        return;
      }
      
      // If not available in cache, load it dynamically
      switch (dataType) {
        case 'nutrition':
          const { getNutritionTargets, getTodayNutrition } = require('./nutritionService');
          const [targets, consumed] = await Promise.all([
            getNutritionTargets(),
            getTodayNutrition()
          ]);
          results.nutrition = { targets, consumed };
          break;
          
        case 'logs':
          const { getLogs } = require('./conversationService');
          results.logs = await getLogs();
          break;
          
        case 'weight':
          const { getWeightEntries } = require('./weightTracking');
          results.weight = await getWeightEntries();
          break;
          
        case 'weather':
          const { getCurrentWeather } = require('./nutritionService');
          results.weather = await getCurrentWeather();
          break;
          
        case 'achievements':
          const { getUserAchievements } = require('./achievementService');
          results.achievements = await getUserAchievements();
          break;
          
        case 'stats':
          const { getUserStats } = require('./userStatsService');
          results.stats = await getUserStats();
          break;
      }
    } catch (error) {
      console.warn(`[Preload] Error preloading ${dataType}:`, error);
      // Set default empty data for failed loads
      results[dataType] = dataType === 'logs' || dataType === 'weight' || dataType === 'achievements' ? [] : {};
    }
  }));
  
  return results;
}

/**
 * Preload data specifically for a screen
 * @param screenName - Name of the screen to preload data for
 * @returns Object with loaded data needed for that screen
 */
export async function preloadScreenData(screenName: string): Promise<Record<string, any>> {
  // Map screens to required data types
  const screenDataMap: Record<string, PreloadableData[]> = {
    'DigestScreen': ['logs', 'weather'],
    'DietScreen': ['nutrition', 'weather', 'weight'],
    'StrengthScreen': ['logs', 'weather', 'stats'],
    'HistoryScreen': ['logs', 'weight'],
    'DashboardScreen': ['logs', 'nutrition', 'weight', 'stats'],
    'ProfileScreen': ['stats', 'achievements'],
  };
  
  // Get data types for requested screen
  const dataTypes = screenDataMap[screenName] || [];
  
  // If no specific data types defined for this screen, return empty object
  if (dataTypes.length === 0) {
    return {};
  }
  
  // Preload the data
  return preloadData(dataTypes);
}

/**
 * Check if data is already loaded
 * @param dataType - Type of data to check
 * @returns True if data is loaded and cached
 */
export async function isDataLoaded(dataType: PreloadableData): Promise<boolean> {
  const cachedData = await getCachedStepData(dataType);
  return cachedData !== null;
} 