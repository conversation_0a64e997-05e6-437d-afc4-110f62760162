import { ContextType } from './contextService';
import { 
  Log, 
  WeightEntry, 
  WeightStats 
} from './sharedTypes';

// Import services dynamically to avoid circular dependencies
const getConversationService = () => {
  return require('./conversationService');
};

const getContextService = () => {
  return require('./contextService');
};

// Save a new weight entry with improved animation and UX handling
export const saveWeightEntry = async (weight: number, note?: string, date: Date = new Date()): Promise<string | null> => {
  try {
    console.log(`Saving weight entry: ${weight} lbs, date: ${date.toISOString()}`);

    // Validate weight value
    if (isNaN(weight) || weight <= 0 || weight > 1000) {
      throw new Error('Invalid weight value');
    }

    // Ensure weight is a number
    const weightValue = Number(weight);

    // Normalize date to start of day for comparison
    const normalizedDate = new Date(date);
    normalizedDate.setHours(0, 0, 0, 0);
    const dateString = normalizedDate.toISOString().split('T')[0];

    // Create a new entry with a more stable ID format
    // Use date in ID to help prevent duplicates during sync
    const dateForId = date.toISOString().split('T')[0].replace(/-/g, '');
    const entryId = `weight_${dateForId}_${Date.now()}`;
    const timestamp = date.toISOString();
    const contextDate = timestamp;

    console.log(`Creating new weight entry with ID: ${entryId}`);

    const weightEntry: WeightEntry = {
      value: weightValue,
      date: timestamp,
      note: note
    };

    // Ensure all required fields are present and correctly formatted
    const weightLog: Log = {
      id: entryId,
      type: 'weight',
      description: `Weight: ${weightValue} lbs`,
      timestamp,
      contextDate, // Required by backend
      metrics: {
        weight: weightEntry
      }
    };

    // Local save first to ensure data is available immediately
    const conversationService = getConversationService();
    await conversationService.saveLog(weightLog);

    // Save to context database in the background
    try {
      const contextService = getContextService();
      await contextService.addContextEntry({
        type: ContextType.WEIGHT_HISTORY,
        value: weightValue,
        source: 'user_input',
      });
      console.log('Saved weight to context database');
    } catch (error) {
      console.error('Error saving weight to context database:', error);
    }

    // Check and update achievements in the background
    try {
      const { checkAndUpdateAchievements } = require('./achievementService');
      checkAndUpdateAchievements()
        .then(() => console.log('Checked and updated achievements after weight entry'))
        .catch((error: any) => console.error('Error checking achievements after weight entry:', error));
    } catch (achievementError) {
      console.error('Error importing achievement service:', achievementError);
    }

    // Check for existing entries on the same date and update backend in the background
    getWeightEntries().then(async existingEntries => {
      try {
        const entriesOnSameDate = existingEntries.filter(entry => {
          const entryDate = new Date(entry.date);
          entryDate.setHours(0, 0, 0, 0);
          return entryDate.toISOString().split('T')[0] === dateString && entry.value !== weightValue;
        });

        // If there are other entries for the same date, update them in the background
        if (entriesOnSameDate.length > 0) {
          console.log(`Found ${entriesOnSameDate.length} existing entries for date ${dateString}, updating them in the background`);

          const conversationService = getConversationService();
          const logs = await conversationService.getLogs();
          for (const existingEntry of entriesOnSameDate) {
            // Find the log for this entry
            const existingLog = logs.find((log: Log) => {
              if (log.type !== 'weight') return false;
              
              // Check if this log contains the weight entry
              const logWeight = log.metrics?.weight;
              if (!logWeight) return false;
              
              // Compare dates (normalized to day)
              const logDate = new Date(logWeight.date);
              logDate.setHours(0, 0, 0, 0);
              const entryDate = new Date(existingEntry.date);
              entryDate.setHours(0, 0, 0, 0);
              
              return logDate.getTime() === entryDate.getTime() && 
                     logWeight.value === existingEntry.value;
            });
            
            if (existingLog) {
              // Update the log with the new weight value
              const updatedLog = {
                ...existingLog,
                description: `Weight: ${weightValue} lbs (updated)`,
                metrics: {
                  ...existingLog.metrics,
                  weight: {
                    ...existingLog.metrics?.weight,
                    value: weightValue,
                    note: note || existingLog.metrics?.weight?.note || 'Updated automatically'
                  }
                }
              };
              
              await conversationService.saveLog(updatedLog);
            }
          }
        }
      } catch (error) {
        console.error('Error updating existing weight entries:', error);
      }
    });

    return entryId;
  } catch (error) {
    console.error('Error saving weight entry:', error);
    return null;
  }
};

// Get all weight entries
export const getWeightEntries = async (): Promise<WeightEntry[]> => {
  try {
    const conversationService = getConversationService();
    const logs = await conversationService.getLogs();
    
    // Filter for weight logs
    const weightLogs = logs.filter((log: Log) => log.type === 'weight');
    
    // Extract weight entries from logs
    const weightEntries = weightLogs
      .map((log: Log) => {
        if (!log.metrics?.weight) return null;
        
        return {
          value: log.metrics.weight.value,
          date: log.metrics.weight.date || log.timestamp,
          note: log.metrics.weight.note
        };
      })
      .filter(Boolean) as WeightEntry[];
    
    // Sort by date (oldest first for charts)
    return weightEntries
      .sort((a: WeightEntry, b: WeightEntry) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } catch (error) {
    console.error('Error getting weight entries:', error);
    return [];
  }
};

// Calculate weight statistics
export const calculateWeightStats = (entries: WeightEntry[]): WeightStats => {
  if (!entries || entries.length === 0) {
    return {
      current: null,
      average: null,
      min: null,
      max: null,
      change: null,
      trend: null
    };
  }

  // Sort entries by date (newest first)
  const sortedEntries = [...entries].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Get current weight (most recent)
  const current = sortedEntries[0].value;
  
  // Calculate average
  const sum = sortedEntries.reduce((acc, entry) => acc + entry.value, 0);
  const average = sum / sortedEntries.length;
  
  // Find min and max
  const values = sortedEntries.map(entry => entry.value);
  const min = Math.min(...values);
  const max = Math.max(...values);
  
  // Calculate change (between oldest and newest)
  const oldest = sortedEntries[sortedEntries.length - 1].value;
  const change = {
    value: current - oldest,
    percentage: ((current - oldest) / oldest) * 100,
    isPositive: current > oldest
  };
  
  // Determine trend
  let trend: 'up' | 'down' | 'stable' = 'stable';
  if (change.value > 0.5) {
    trend = 'up';
  } else if (change.value < -0.5) {
    trend = 'down';
  }
  
  return {
    current,
    average,
    min,
    max,
    change,
    trend
  };
};

// Delete a weight entry
export const deleteWeightEntry = async (date: string, value: number): Promise<boolean> => {
  try {
    const conversationService = getConversationService();
    const logs = await conversationService.getLogs();
    const nonWeightLogs = logs.filter((log: Log) => log.type !== 'weight');
    await conversationService.saveAllLocalLogs(nonWeightLogs);
    return true;
  } catch (error) {
    console.error('Error deleting weight entry:', error);
    return false;
  }
};
