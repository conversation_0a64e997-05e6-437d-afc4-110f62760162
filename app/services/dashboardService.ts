import { getCur<PERSON>Weat<PERSON>, getMealSuggestions, MealSuggestion } from './nutritionService';
import { getContextData, ContextType } from './contextService';
import { getProfileFromStorage } from './profile';
import { getUserStats } from './userStatsService';
import { api, makeRequestWithRetry } from './apiClient';
import { getAuthHeader } from './auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getRequest, postRequest } from './apiRequestManager';
import { Ionicons } from '@expo/vector-icons';
import type { MainTabParamList } from '../navigation/MainTabNavigator';

// Dashboard data storage key
const DASHBOARD_STORAGE_KEY = '@dashboard_data';
const DASHBOARD_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

// Dashboard data interface
export interface DashboardData {
  workoutSuggestion: {
    title: string;
    description: string;
  } | null;
  mealSuggestion: MealSuggestion | null;
  dailyTip: string | null;
  lastUpdated: string;
}

export interface Highlight {
  id: string;
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle: string;
  screen: keyof MainTabParamList;
}

/**
 * Get dashboard data with recommendations for the day
 */
export async function getDashboardData(forceRefresh: boolean = false): Promise<DashboardData> {
  try {
    // Check if we have cached data that's still valid
    if (!forceRefresh) {
      const cachedData = await getCachedDashboardData();
      if (cachedData) {
        console.log('Using cached dashboard data');
        return cachedData;
      }
    }

    console.log('Fetching fresh dashboard data');
    
    // Get user profile and context data
    const profile = await getProfileFromStorage();
    const contextData = await getContextData();
    const weather = await getCurrentWeather();
    const userStats = await getUserStats();
    
    // Get meal suggestions
    const mealSuggestions = await getMealSuggestions(true);
    const mealSuggestion = mealSuggestions.length > 0 ? mealSuggestions[0] : null;
    
    // Get workout suggestion from API
    let workoutSuggestion = null;
    try {
      workoutSuggestion = await getWorkoutSuggestion(profile, contextData, weather);
    } catch (error) {
      console.error('Error getting workout suggestion:', error);
      // Use fallback workout suggestion
      workoutSuggestion = getFallbackWorkoutSuggestion();
    }
    
    // Get daily tip from API
    let dailyTip = null;
    try {
      dailyTip = await getDailyTip(profile, contextData, userStats);
    } catch (error) {
      console.error('Error getting daily tip:', error);
      // Use fallback tip
      dailyTip = getFallbackDailyTip();
    }
    
    // Create dashboard data
    const dashboardData: DashboardData = {
      workoutSuggestion,
      mealSuggestion,
      dailyTip,
      lastUpdated: new Date().toISOString()
    };
    
    // Cache the data
    await cacheDashboardData(dashboardData);
    
    return dashboardData;
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    
    // Return default data
    return {
      workoutSuggestion: getFallbackWorkoutSuggestion(),
      mealSuggestion: null,
      dailyTip: getFallbackDailyTip(),
      lastUpdated: new Date().toISOString()
    };
  }
}

/**
 * Get workout suggestion from API
 */
async function getWorkoutSuggestion(profile: any, contextData: any, weather: any): Promise<any> {
  // For now, return a fallback suggestion
  // In a real implementation, you would call your API
  return getFallbackWorkoutSuggestion();
}

/**
 * Get daily tip from API
 */
async function getDailyTip(profile: any, contextData: any, userStats: any): Promise<string> {
  // For now, return a fallback tip
  // In a real implementation, you would call your API
  return getFallbackDailyTip();
}

/**
 * Get fallback workout suggestion
 */
function getFallbackWorkoutSuggestion(): any {
  const suggestions = [
    {
      title: "Upper Body Focus",
      description: "Chest, shoulders, and triceps workout with compound movements"
    },
    {
      title: "Lower Body Strength",
      description: "Squats, lunges, and deadlifts for building leg strength"
    },
    {
      title: "Full Body Circuit",
      description: "High-intensity circuit training targeting all major muscle groups"
    },
    {
      title: "Core Stability",
      description: "Planks, Russian twists, and leg raises for a stronger core"
    },
    {
      title: "Active Recovery",
      description: "Light cardio and stretching to promote recovery"
    }
  ];
  
  // Return a random suggestion
  return suggestions[Math.floor(Math.random() * suggestions.length)];
}

/**
 * Get fallback daily tip
 */
function getFallbackDailyTip(): string {
  const tips = [
    "Stay hydrated throughout the day for optimal recovery and performance.",
    "Aim for 7-9 hours of quality sleep to support muscle recovery and growth.",
    "Include protein in every meal to support muscle maintenance and growth.",
    "Take short walking breaks if you sit for long periods during the day.",
    "Practice deep breathing for 5 minutes to reduce stress and improve focus.",
    "Try to eat a variety of colorful vegetables to get a wide range of nutrients.",
    "Consistency is more important than perfection in your fitness journey.",
    "Remember to warm up properly before workouts to prevent injuries.",
    "Track your progress to stay motivated and see how far you've come."
  ];
  
  // Return a random tip
  return tips[Math.floor(Math.random() * tips.length)];
}

/**
 * Cache dashboard data
 */
async function cacheDashboardData(data: DashboardData): Promise<void> {
  try {
    await AsyncStorage.setItem(DASHBOARD_STORAGE_KEY, JSON.stringify({
      data,
      timestamp: Date.now()
    }));
  } catch (error) {
    console.error('Error caching dashboard data:', error);
  }
}

/**
 * Get cached dashboard data
 */
async function getCachedDashboardData(): Promise<DashboardData | null> {
  try {
    const cachedJson = await AsyncStorage.getItem(DASHBOARD_STORAGE_KEY);
    if (!cachedJson) return null;
    
    const cached = JSON.parse(cachedJson);
    const now = Date.now();
    
    // Check if cache is still valid
    if (now - cached.timestamp < DASHBOARD_CACHE_TTL) {
      return cached.data;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached dashboard data:', error);
    return null;
  }
}

export async function getTodaysHighlights(forceRefresh = false): Promise<Highlight[]> {
  // In a real app, this would fetch data from various services
  // For now, we'll return a mix of static and dynamic highlights
  const highlights: Highlight[] = [];

  // Nutrition highlight
  try {
    const { getTodayNutrition, getNutritionTargets } = require('./nutritionService');
    const consumed = await getTodayNutrition();
    const targets = await getNutritionTargets();
    const calorieProgress = targets.calories > 0 ? (consumed.calories / targets.calories) * 100 : 0;
    highlights.push({
      id: 'nutrition',
      icon: 'fast-food-outline',
      title: 'Nutrition',
      subtitle: `${Math.round(calorieProgress)}% of calorie goal`,
      screen: 'Diet',
    });
  } catch (e) {
    console.error("Error getting nutrition highlight:", e);
  }

  // Workout highlight
  try {
    const { getLatestWorkout } = require('./workoutService');
    const latestWorkout = await getLatestWorkout();
    highlights.push({
      id: 'workout',
      icon: 'barbell-outline',
      title: 'Last Workout',
      subtitle: latestWorkout ? latestWorkout.name : 'Log a workout',
      screen: 'Strength',
    });
  } catch (e) {
    console.error("Error getting workout highlight:", e);
  }

  // Digest highlight
  try {
    const { getDayDigest } = require('./digestService');
    const digest = await getDayDigest(new Date());
    const completed = digest.activities.filter((a: any) => a.completed).length;
    const total = digest.activities.length;
    highlights.push({
      id: 'digest',
      icon: 'list-outline',
      title: 'Daily Digest',
      subtitle: `${completed}/${total} activities completed`,
      screen: 'Digest',
    });
  } catch (e) {
    console.error("Error getting digest highlight:", e);
  }

  return highlights;
}
