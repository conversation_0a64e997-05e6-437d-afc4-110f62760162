import { getUserAchievements, clearAchievementsCache, getDefaultAchievements } from './achievementService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Key to track if we've already initialized achievements
const ACHIEVEMENTS_INITIALIZED_KEY = '@achievements_initialized';

/**
 * Initialize the achievements system
 * This should be called when the app starts
 */
export async function initializeAchievements(): Promise<void> {
  try {
    // Check if we've already initialized achievements in this session
    const initialized = await AsyncStorage.getItem(ACHIEVEMENTS_INITIALIZED_KEY);
    if (initialized === 'true') {
      console.log('[AchievementInitializer] Achievements already initialized in this session');
      return;
    }

    console.log('[AchievementInitializer] Initializing achievements system');
    
    // Clear the achievements cache to ensure we get fresh data
    await clearAchievementsCache();
    
    // Fetch achievements from the server
    const achievements = await getUserAchievements(true);
    
    // Log achievements count
    console.log(`[AchievementInitializer] Loaded ${achievements.length} achievements`);
    
    // Check if we have a reasonable number of achievements
    if (achievements.length < 10) {
      console.warn('[AchievementInitializer] Received fewer than 10 achievements, this may indicate an issue');
      
      // Try fetching again with force refresh
      console.log('[AchievementInitializer] Trying again with force refresh');
      const refreshedAchievements = await getUserAchievements(true);
      
      if (refreshedAchievements.length < 10) {
        console.warn('[AchievementInitializer] Still received fewer than 10 achievements, using defaults');
        
        // Use default achievements as fallback
        const defaultAchievements = getDefaultAchievements();
        console.log(`[AchievementInitializer] Using ${defaultAchievements.length} default achievements`);
      }
    }
    
    // Mark achievements as initialized
    await AsyncStorage.setItem(ACHIEVEMENTS_INITIALIZED_KEY, 'true');
    console.log('[AchievementInitializer] Achievements system initialized successfully');
  } catch (error) {
    console.error('[AchievementInitializer] Error initializing achievements:', error);
  }
}

/**
 * Reset the achievements initialization flag
 * This can be used for debugging or when the user logs out
 */
export async function resetAchievementsInitialization(): Promise<void> {
  try {
    await AsyncStorage.removeItem(ACHIEVEMENTS_INITIALIZED_KEY);
    console.log('[AchievementInitializer] Achievements initialization flag reset');
  } catch (error) {
    console.error('[AchievementInitializer] Error resetting achievements initialization flag:', error);
  }
}
