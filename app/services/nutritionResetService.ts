import AsyncStorage from '@react-native-async-storage/async-storage';

// Constants
const LAST_RESET_DATE_KEY = '@nutrition_last_reset_date';
const DAILY_NUTRITION_RESET_LOG_KEY = '@nutrition_reset_log';

/**
 * Checks if the daily nutrition needs to be reset (at midnight)
 * and logs the reset if it happens
 */
export async function checkAndResetDailyNutrition(): Promise<boolean> {
  try {
    // Get the current date (YYYY-MM-DD format)
    const today = new Date().toISOString().split('T')[0];
    
    // Get the last reset date from storage
    const lastResetDate = await AsyncStorage.getItem(LAST_RESET_DATE_KEY);
    
    // If the last reset date is not today, reset the daily nutrition
    if (lastResetDate !== today) {
      console.log(`Resetting daily nutrition. Last reset: ${lastResetDate || 'never'}, Today: ${today}`);
      
      // Log the reset with the previous day's nutrition data
      await logNutritionReset(lastResetDate);
      
      // Update the last reset date
      await AsyncStorage.setItem(LAST_RESET_DATE_KEY, today);
      
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking and resetting daily nutrition:', error);
    return false;
  }
}

/**
 * Logs the nutrition reset with the previous day's nutrition data
 */
async function logNutritionReset(previousDate: string | null): Promise<void> {
  try {
    if (!previousDate) {
      console.log('No previous date, skipping nutrition reset log');
      return;
    }
    
    // Get the previous day's nutrition data
    const { getTodayNutrition } = require('./nutritionService');
    const previousNutrition = await getTodayNutrition();
    
    // Create a log entry
    const logEntry = {
      date: previousDate,
      nutrition: previousNutrition,
      resetTimestamp: new Date().toISOString()
    };
    
    // Get existing logs
    const logsJson = await AsyncStorage.getItem(DAILY_NUTRITION_RESET_LOG_KEY);
    const logs = logsJson ? JSON.parse(logsJson) : [];
    
    // Add the new log entry
    logs.push(logEntry);
    
    // Keep only the last 30 days of logs
    const recentLogs = logs.slice(-30);
    
    // Save the updated logs
    await AsyncStorage.setItem(DAILY_NUTRITION_RESET_LOG_KEY, JSON.stringify(recentLogs));
    
    console.log(`Logged nutrition reset for ${previousDate}:`, previousNutrition);
  } catch (error) {
    console.error('Error logging nutrition reset:', error);
  }
}

/**
 * Gets the nutrition reset logs
 */
export async function getNutritionResetLogs(): Promise<any[]> {
  try {
    const logsJson = await AsyncStorage.getItem(DAILY_NUTRITION_RESET_LOG_KEY);
    return logsJson ? JSON.parse(logsJson) : [];
  } catch (error) {
    console.error('Error getting nutrition reset logs:', error);
    return [];
  }
}
