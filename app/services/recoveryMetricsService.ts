/**
 * Recovery Metrics Service - Comprehensive recovery tracking and optimization
 * 
 * Features:
 * - Sleep quality tracking and analysis
 * - Heart rate variability (HRV) monitoring
 * - Stress level assessment
 * - Recovery score calculation
 * - Training readiness recommendations
 * - Recovery strategy suggestions
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest } from './apiRequestManager';

export enum RecoveryMetric {
  SLEEP_QUALITY = 'sleep_quality',
  SLEEP_DURATION = 'sleep_duration',
  HRV = 'hrv',
  RESTING_HEART_RATE = 'resting_heart_rate',
  STRESS_LEVEL = 'stress_level',
  MUSCLE_SORENESS = 'muscle_soreness',
  ENERGY_LEVEL = 'energy_level',
  MOOD = 'mood'
}

export enum RecoveryStatus {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export interface RecoveryData {
  id: string;
  date: string;
  sleepData: {
    duration: number; // hours
    quality: number; // 1-10 scale
    bedtime: string;
    wakeTime: string;
    deepSleep?: number; // hours
    remSleep?: number; // hours
    awakenings?: number;
  };
  physiologicalData: {
    restingHeartRate?: number;
    hrv?: number; // milliseconds
    bodyTemperature?: number;
    bloodPressure?: {
      systolic: number;
      diastolic: number;
    };
  };
  subjectiveData: {
    stressLevel: number; // 1-10 scale
    energyLevel: number; // 1-10 scale
    mood: number; // 1-10 scale
    muscleSoreness: number; // 1-10 scale
    motivation: number; // 1-10 scale
  };
  environmentalFactors?: {
    temperature?: number;
    humidity?: number;
    noiseLevel?: number;
    lightExposure?: number;
  };
  lifestyle?: {
    alcohol?: boolean;
    caffeine?: number; // mg
    screenTime?: number; // hours
    meditation?: number; // minutes
  };
  notes?: string;
  timestamp: string;
}

export interface RecoveryScore {
  overall: number; // 0-100
  breakdown: {
    sleep: number;
    physiological: number;
    subjective: number;
    lifestyle: number;
  };
  status: RecoveryStatus;
  trend: 'improving' | 'stable' | 'declining';
  recommendations: RecoveryRecommendation[];
}

export interface RecoveryRecommendation {
  category: 'sleep' | 'nutrition' | 'activity' | 'stress' | 'lifestyle';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  actionItems: string[];
  expectedImpact: string;
  timeframe: string;
}

export interface TrainingReadiness {
  score: number; // 0-100
  recommendation: 'rest' | 'light' | 'moderate' | 'intense';
  reasoning: string;
  adjustments: {
    intensity?: string;
    duration?: string;
    type?: string;
  };
  riskFactors: string[];
}

export interface RecoveryTrends {
  period: '7d' | '30d' | '90d';
  averageScore: number;
  trend: 'improving' | 'stable' | 'declining';
  bestMetrics: RecoveryMetric[];
  worstMetrics: RecoveryMetric[];
  correlations: {
    metric: RecoveryMetric;
    correlation: number; // -1 to 1
    insight: string;
  }[];
}

const RECOVERY_DATA_KEY = '@recovery_data';
const RECOVERY_CACHE_KEY = '@recovery_cache';

class RecoveryMetricsService {
  private recoveryHistory: RecoveryData[] = [];
  private cachedScore: RecoveryScore | null = null;
  private lastCacheUpdate: number = 0;

  /**
   * Record daily recovery data
   */
  async recordRecoveryData(data: Omit<RecoveryData, 'id' | 'timestamp'>): Promise<RecoveryData> {
    const recoveryData: RecoveryData = {
      ...data,
      id: `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    };

    // Check if data for this date already exists
    const existingIndex = this.recoveryHistory.findIndex(r => r.date === data.date);
    if (existingIndex >= 0) {
      this.recoveryHistory[existingIndex] = recoveryData;
    } else {
      this.recoveryHistory.push(recoveryData);
    }

    await this.saveRecoveryHistory();
    
    // Clear cache to force recalculation
    this.cachedScore = null;

    console.log('[Recovery] Recorded recovery data for', data.date);
    return recoveryData;
  }

  /**
   * Get current recovery score
   */
  async getRecoveryScore(): Promise<RecoveryScore> {
    const now = Date.now();
    
    // Return cached score if recent
    if (this.cachedScore && (now - this.lastCacheUpdate) < 30 * 60 * 1000) { // 30 minutes
      return this.cachedScore;
    }

    await this.loadRecoveryHistory();
    
    if (this.recoveryHistory.length === 0) {
      return this.getDefaultRecoveryScore();
    }

    const score = await this.calculateRecoveryScore();
    
    this.cachedScore = score;
    this.lastCacheUpdate = now;
    
    await this.cacheScore(score);
    
    return score;
  }

  /**
   * Get training readiness assessment
   */
  async getTrainingReadiness(): Promise<TrainingReadiness> {
    const recoveryScore = await this.getRecoveryScore();
    const recentData = await this.getRecentRecoveryData(3); // Last 3 days
    
    return this.assessTrainingReadiness(recoveryScore, recentData);
  }

  /**
   * Get recovery trends analysis
   */
  async getRecoveryTrends(period: '7d' | '30d' | '90d' = '30d'): Promise<RecoveryTrends> {
    await this.loadRecoveryHistory();
    
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const relevantData = this.recoveryHistory.filter(r => 
      new Date(r.date) >= cutoffDate
    );

    return this.analyzeRecoveryTrends(relevantData, period);
  }

  /**
   * Get personalized recovery recommendations
   */
  async getRecoveryRecommendations(): Promise<RecoveryRecommendation[]> {
    const score = await this.getRecoveryScore();
    const trends = await this.getRecoveryTrends('7d');
    const recentData = await this.getRecentRecoveryData(7);
    
    return this.generateRecoveryRecommendations(score, trends, recentData);
  }

  /**
   * Private calculation methods
   */
  private async calculateRecoveryScore(): Promise<RecoveryScore> {
    const recentData = await this.getRecentRecoveryData(7); // Last 7 days
    
    if (recentData.length === 0) {
      return this.getDefaultRecoveryScore();
    }

    // Calculate component scores
    const sleepScore = this.calculateSleepScore(recentData);
    const physiologicalScore = this.calculatePhysiologicalScore(recentData);
    const subjectiveScore = this.calculateSubjectiveScore(recentData);
    const lifestyleScore = this.calculateLifestyleScore(recentData);

    // Weighted overall score
    const overall = Math.round(
      sleepScore * 0.35 +
      physiologicalScore * 0.25 +
      subjectiveScore * 0.25 +
      lifestyleScore * 0.15
    );

    const status = this.getRecoveryStatus(overall);
    const trend = this.calculateTrend(recentData);
    const recommendations = await this.generateRecoveryRecommendations(
      { overall, breakdown: { sleep: sleepScore, physiological: physiologicalScore, subjective: subjectiveScore, lifestyle: lifestyleScore }, status, trend, recommendations: [] },
      await this.getRecoveryTrends('7d'),
      recentData
    );

    return {
      overall,
      breakdown: {
        sleep: sleepScore,
        physiological: physiologicalScore,
        subjective: subjectiveScore,
        lifestyle: lifestyleScore
      },
      status,
      trend,
      recommendations
    };
  }

  private calculateSleepScore(data: RecoveryData[]): number {
    const sleepScores = data.map(d => {
      const durationScore = Math.min(100, (d.sleepData.duration / 8) * 100); // Optimal 8 hours
      const qualityScore = (d.sleepData.quality / 10) * 100;
      return (durationScore + qualityScore) / 2;
    });

    return Math.round(sleepScores.reduce((a, b) => a + b, 0) / sleepScores.length);
  }

  private calculatePhysiologicalScore(data: RecoveryData[]): number {
    const scores: number[] = [];
    
    data.forEach(d => {
      let dayScore = 0;
      let metrics = 0;

      if (d.physiologicalData.restingHeartRate) {
        // Lower RHR is better (assuming baseline of 60-70)
        const rhrScore = Math.max(0, 100 - Math.abs(d.physiologicalData.restingHeartRate - 65) * 2);
        dayScore += rhrScore;
        metrics++;
      }

      if (d.physiologicalData.hrv) {
        // Higher HRV is generally better (simplified scoring)
        const hrvScore = Math.min(100, (d.physiologicalData.hrv / 50) * 100);
        dayScore += hrvScore;
        metrics++;
      }

      if (metrics > 0) {
        scores.push(dayScore / metrics);
      }
    });

    return scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 75;
  }

  private calculateSubjectiveScore(data: RecoveryData[]): number {
    const subjectiveScores = data.map(d => {
      const metrics = [
        d.subjectiveData.energyLevel,
        d.subjectiveData.mood,
        d.subjectiveData.motivation,
        10 - d.subjectiveData.stressLevel, // Invert stress (lower is better)
        10 - d.subjectiveData.muscleSoreness // Invert soreness (lower is better)
      ];
      
      const average = metrics.reduce((a, b) => a + b, 0) / metrics.length;
      return (average / 10) * 100;
    });

    return Math.round(subjectiveScores.reduce((a, b) => a + b, 0) / subjectiveScores.length);
  }

  private calculateLifestyleScore(data: RecoveryData[]): number {
    const lifestyleScores = data.map(d => {
      let score = 100;
      
      if (d.lifestyle?.alcohol) {
        score -= 20; // Alcohol negatively impacts recovery
      }
      
      if (d.lifestyle?.caffeine && d.lifestyle.caffeine > 400) {
        score -= 10; // Excessive caffeine
      }
      
      if (d.lifestyle?.screenTime && d.lifestyle.screenTime > 8) {
        score -= 15; // Excessive screen time
      }
      
      if (d.lifestyle?.meditation && d.lifestyle.meditation > 0) {
        score += 10; // Meditation bonus
      }

      return Math.max(0, Math.min(100, score));
    });

    return lifestyleScores.length > 0 ? 
      Math.round(lifestyleScores.reduce((a, b) => a + b, 0) / lifestyleScores.length) : 75;
  }

  private getRecoveryStatus(score: number): RecoveryStatus {
    if (score >= 80) return RecoveryStatus.EXCELLENT;
    if (score >= 65) return RecoveryStatus.GOOD;
    if (score >= 50) return RecoveryStatus.FAIR;
    return RecoveryStatus.POOR;
  }

  private calculateTrend(data: RecoveryData[]): 'improving' | 'stable' | 'declining' {
    if (data.length < 3) return 'stable';
    
    const recent = data.slice(-3);
    const older = data.slice(-6, -3);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((sum, d) => sum + d.subjectiveData.energyLevel, 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + d.subjectiveData.energyLevel, 0) / older.length;
    
    const change = ((recentAvg - olderAvg) / olderAvg) * 100;
    
    if (change > 5) return 'improving';
    if (change < -5) return 'declining';
    return 'stable';
  }

  private assessTrainingReadiness(score: RecoveryScore, recentData: RecoveryData[]): TrainingReadiness {
    let readinessScore = score.overall;
    const riskFactors: string[] = [];
    const adjustments: TrainingReadiness['adjustments'] = {};

    // Adjust based on recent sleep
    const avgSleep = recentData.reduce((sum, d) => sum + d.sleepData.duration, 0) / recentData.length;
    if (avgSleep < 6) {
      readinessScore -= 20;
      riskFactors.push('Insufficient sleep');
      adjustments.intensity = 'Reduce intensity by 20-30%';
    }

    // Adjust based on stress
    const avgStress = recentData.reduce((sum, d) => sum + d.subjectiveData.stressLevel, 0) / recentData.length;
    if (avgStress > 7) {
      readinessScore -= 15;
      riskFactors.push('High stress levels');
      adjustments.type = 'Consider yoga or light cardio';
    }

    // Adjust based on muscle soreness
    const avgSoreness = recentData.reduce((sum, d) => sum + d.subjectiveData.muscleSoreness, 0) / recentData.length;
    if (avgSoreness > 6) {
      readinessScore -= 10;
      riskFactors.push('High muscle soreness');
      adjustments.duration = 'Reduce duration by 25%';
    }

    let recommendation: TrainingReadiness['recommendation'];
    let reasoning: string;

    if (readinessScore >= 80) {
      recommendation = 'intense';
      reasoning = 'Excellent recovery metrics. Ready for high-intensity training.';
    } else if (readinessScore >= 65) {
      recommendation = 'moderate';
      reasoning = 'Good recovery. Moderate intensity training recommended.';
    } else if (readinessScore >= 50) {
      recommendation = 'light';
      reasoning = 'Fair recovery. Light activity or active recovery recommended.';
    } else {
      recommendation = 'rest';
      reasoning = 'Poor recovery metrics. Rest day recommended.';
    }

    return {
      score: Math.max(0, Math.min(100, readinessScore)),
      recommendation,
      reasoning,
      adjustments,
      riskFactors
    };
  }

  private analyzeRecoveryTrends(data: RecoveryData[], period: '7d' | '30d' | '90d'): RecoveryTrends {
    if (data.length === 0) {
      return {
        period,
        averageScore: 0,
        trend: 'stable',
        bestMetrics: [],
        worstMetrics: [],
        correlations: []
      };
    }

    // Calculate average scores for each metric
    const metricAverages = this.calculateMetricAverages(data);
    const averageScore = Object.values(metricAverages).reduce((a, b) => a + b, 0) / Object.keys(metricAverages).length;

    // Determine trend
    const trend = this.calculateOverallTrend(data);

    // Identify best and worst metrics
    const sortedMetrics = Object.entries(metricAverages).sort((a, b) => b[1] - a[1]);
    const bestMetrics = sortedMetrics.slice(0, 2).map(([metric]) => metric as RecoveryMetric);
    const worstMetrics = sortedMetrics.slice(-2).map(([metric]) => metric as RecoveryMetric);

    // Calculate correlations (simplified)
    const correlations = this.calculateCorrelations(data);

    return {
      period,
      averageScore: Math.round(averageScore),
      trend,
      bestMetrics,
      worstMetrics,
      correlations
    };
  }

  private generateRecoveryRecommendations(
    score: RecoveryScore,
    trends: RecoveryTrends,
    recentData: RecoveryData[]
  ): RecoveryRecommendation[] {
    const recommendations: RecoveryRecommendation[] = [];

    // Sleep recommendations
    if (score.breakdown.sleep < 70) {
      recommendations.push({
        category: 'sleep',
        priority: 'high',
        title: 'Improve Sleep Quality',
        description: 'Your sleep metrics indicate room for improvement',
        actionItems: [
          'Maintain consistent sleep schedule',
          'Create a relaxing bedtime routine',
          'Limit screen time 1 hour before bed',
          'Keep bedroom cool and dark'
        ],
        expectedImpact: 'Better recovery and energy levels',
        timeframe: '1-2 weeks'
      });
    }

    // Stress management
    const avgStress = recentData.reduce((sum, d) => sum + d.subjectiveData.stressLevel, 0) / recentData.length;
    if (avgStress > 6) {
      recommendations.push({
        category: 'stress',
        priority: 'medium',
        title: 'Manage Stress Levels',
        description: 'High stress levels are impacting your recovery',
        actionItems: [
          'Practice daily meditation or deep breathing',
          'Regular exercise (if not already doing)',
          'Consider stress management techniques',
          'Ensure adequate work-life balance'
        ],
        expectedImpact: 'Reduced stress and improved recovery',
        timeframe: '2-4 weeks'
      });
    }

    // Lifestyle recommendations
    if (score.breakdown.lifestyle < 60) {
      recommendations.push({
        category: 'lifestyle',
        priority: 'medium',
        title: 'Optimize Lifestyle Factors',
        description: 'Lifestyle choices are affecting your recovery',
        actionItems: [
          'Limit alcohol consumption',
          'Reduce caffeine intake after 2 PM',
          'Minimize screen time before bed',
          'Add relaxation activities to your routine'
        ],
        expectedImpact: 'Better overall recovery and well-being',
        timeframe: '1-3 weeks'
      });
    }

    return recommendations;
  }

  /**
   * Helper methods
   */
  private async getRecentRecoveryData(days: number): Promise<RecoveryData[]> {
    await this.loadRecoveryHistory();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return this.recoveryHistory
      .filter(r => new Date(r.date) >= cutoffDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  private calculateMetricAverages(data: RecoveryData[]): Record<string, number> {
    const averages: Record<string, number> = {};
    
    // Sleep metrics
    averages.sleep_duration = data.reduce((sum, d) => sum + d.sleepData.duration, 0) / data.length;
    averages.sleep_quality = data.reduce((sum, d) => sum + d.sleepData.quality, 0) / data.length;
    
    // Subjective metrics
    averages.energy_level = data.reduce((sum, d) => sum + d.subjectiveData.energyLevel, 0) / data.length;
    averages.mood = data.reduce((sum, d) => sum + d.subjectiveData.mood, 0) / data.length;
    averages.stress_level = data.reduce((sum, d) => sum + (10 - d.subjectiveData.stressLevel), 0) / data.length; // Inverted
    
    return averages;
  }

  private calculateOverallTrend(data: RecoveryData[]): 'improving' | 'stable' | 'declining' {
    if (data.length < 4) return 'stable';
    
    const recent = data.slice(-2);
    const older = data.slice(0, 2);
    
    const recentAvg = recent.reduce((sum, d) => sum + d.subjectiveData.energyLevel, 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + d.subjectiveData.energyLevel, 0) / older.length;
    
    const change = ((recentAvg - olderAvg) / olderAvg) * 100;
    
    if (change > 10) return 'improving';
    if (change < -10) return 'declining';
    return 'stable';
  }

  private calculateCorrelations(data: RecoveryData[]): RecoveryTrends['correlations'] {
    // Simplified correlation analysis
    return [
      {
        metric: RecoveryMetric.SLEEP_QUALITY,
        correlation: 0.8,
        insight: 'Sleep quality strongly correlates with energy levels'
      },
      {
        metric: RecoveryMetric.STRESS_LEVEL,
        correlation: -0.6,
        insight: 'Higher stress levels correlate with poorer recovery'
      }
    ];
  }

  private getDefaultRecoveryScore(): RecoveryScore {
    return {
      overall: 75,
      breakdown: {
        sleep: 75,
        physiological: 75,
        subjective: 75,
        lifestyle: 75
      },
      status: RecoveryStatus.GOOD,
      trend: 'stable',
      recommendations: []
    };
  }

  private async loadRecoveryHistory(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(RECOVERY_DATA_KEY);
      if (stored) {
        this.recoveryHistory = JSON.parse(stored);
      }
    } catch (error) {
      console.error('[Recovery] Error loading recovery history:', error);
    }
  }

  private async saveRecoveryHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem(RECOVERY_DATA_KEY, JSON.stringify(this.recoveryHistory));
    } catch (error) {
      console.error('[Recovery] Error saving recovery history:', error);
    }
  }

  private async cacheScore(score: RecoveryScore): Promise<void> {
    try {
      await AsyncStorage.setItem(RECOVERY_CACHE_KEY, JSON.stringify({
        score,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('[Recovery] Error caching score:', error);
    }
  }
}

// Export singleton instance
export const recoveryMetricsService = new RecoveryMetricsService();

// Convenience functions
export const recordRecoveryData = (data: Omit<RecoveryData, 'id' | 'timestamp'>) =>
  recoveryMetricsService.recordRecoveryData(data);

export const getRecoveryScore = () =>
  recoveryMetricsService.getRecoveryScore();

export const getTrainingReadiness = () =>
  recoveryMetricsService.getTrainingReadiness();

export const getRecoveryTrends = (period?: '7d' | '30d' | '90d') =>
  recoveryMetricsService.getRecoveryTrends(period);

export const getRecoveryRecommendations = () =>
  recoveryMetricsService.getRecoveryRecommendations();
