/**
 * Unified Achievement Service - Single source of truth for achievement logic
 * 
 * This service consolidates all achievement-related logic to ensure consistency
 * across the entire application. Fixes issues with progress calculation,
 * streak tracking, and synchronization.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { postRequest, getRequest } from './apiRequestManager';
import { Achievement, AchievementCategory, AchievementStatus } from './sharedTypes';

// Standardized achievement calculation constants
export const ACHIEVEMENT_CONSTANTS = {
  // Progress calculation methods
  PROGRESS_TYPES: {
    CUMULATIVE: 'cumulative',     // Total accumulated value (e.g., total workouts)
    STREAK: 'streak',             // Consecutive days/actions
    THRESHOLD: 'threshold',       // Single value to reach (e.g., max weight lifted)
    PERCENTAGE: 'percentage',     // Percentage of a target
    FREQUENCY: 'frequency'        // Actions per time period
  },
  
  // Streak calculation settings
  STREAK_SETTINGS: {
    GRACE_PERIOD_HOURS: 6,        // Hours past midnight to still count as previous day
    MAX_STREAK_GAP_DAYS: 1,       // Maximum gap to maintain streak
    TIMEZONE_OFFSET_HOURS: 0      // UTC offset for consistent date calculations
  },
  
  // Sync settings
  SYNC_SETTINGS: {
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
    BATCH_SIZE: 10,
    CONFLICT_RESOLUTION: 'server_wins' as 'server_wins' | 'client_wins' | 'merge'
  },
  
  // Cache settings
  CACHE_TTL_MS: 5 * 60 * 1000,    // 5 minutes
  
  // Validation limits
  LIMITS: {
    MAX_PROGRESS_VALUE: 1000000,
    MIN_PROGRESS_VALUE: 0,
    MAX_STREAK_DAYS: 10000
  }
} as const;

export interface AchievementProgress {
  achievementId: string;
  currentValue: number;
  targetValue: number;
  progressPercentage: number;
  lastUpdated: string;
  metadata?: Record<string, any>;
}

export interface StreakData {
  type: string;
  currentStreak: number;
  longestStreak: number;
  lastActivityDate: string;
  streakStartDate: string;
  isActive: boolean;
}

export interface AchievementCalculationResult {
  progress: AchievementProgress;
  statusChanged: boolean;
  newStatus?: AchievementStatus;
  unlockedAt?: string;
  streakData?: StreakData;
}

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  conflicts: Array<{
    achievementId: string;
    localValue: number;
    serverValue: number;
    resolution: 'local' | 'server' | 'merged';
  }>;
}

const STORAGE_KEYS = {
  ACHIEVEMENTS: '@unified_achievements',
  PROGRESS: '@unified_achievement_progress',
  STREAKS: '@unified_streaks',
  SYNC_QUEUE: '@unified_sync_queue',
  LAST_SYNC: '@unified_last_sync'
};

class UnifiedAchievementService {
  private achievements: Map<string, Achievement> = new Map();
  private progress: Map<string, AchievementProgress> = new Map();
  private streaks: Map<string, StreakData> = new Map();
  private syncQueue: Set<string> = new Set();
  private lastCacheUpdate: number = 0;

  /**
   * Calculate achievement progress using standardized logic
   */
  calculateProgress(
    achievement: Achievement,
    currentValue: number,
    metadata?: Record<string, any>
  ): AchievementCalculationResult {
    // Validate inputs
    if (currentValue < ACHIEVEMENT_CONSTANTS.LIMITS.MIN_PROGRESS_VALUE || 
        currentValue > ACHIEVEMENT_CONSTANTS.LIMITS.MAX_PROGRESS_VALUE) {
      throw new Error(`Progress value ${currentValue} is outside valid range`);
    }

    const targetValue = achievement.targetValue || 1;
    let progressPercentage: number;
    let statusChanged = false;
    let newStatus: AchievementStatus | undefined;
    let unlockedAt: string | undefined;

    // Calculate progress based on achievement type
    switch (achievement.progressType) {
      case ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.CUMULATIVE:
        progressPercentage = Math.min(100, (currentValue / targetValue) * 100);
        break;
        
      case ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.THRESHOLD:
        progressPercentage = currentValue >= targetValue ? 100 : (currentValue / targetValue) * 100;
        break;
        
      case ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.PERCENTAGE:
        progressPercentage = Math.min(100, currentValue);
        break;
        
      case ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.STREAK:
        progressPercentage = Math.min(100, (currentValue / targetValue) * 100);
        break;
        
      case ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.FREQUENCY:
        // For frequency-based achievements, current value is the rate achieved
        progressPercentage = Math.min(100, (currentValue / targetValue) * 100);
        break;
        
      default:
        // Default to cumulative calculation
        progressPercentage = Math.min(100, (currentValue / targetValue) * 100);
    }

    // Round to 2 decimal places
    progressPercentage = Math.round(progressPercentage * 100) / 100;

    // Determine status change
    const wasUnlocked = achievement.status === AchievementStatus.UNLOCKED;
    const shouldBeUnlocked = progressPercentage >= 100;

    if (!wasUnlocked && shouldBeUnlocked) {
      statusChanged = true;
      newStatus = AchievementStatus.UNLOCKED;
      unlockedAt = new Date().toISOString();
    }

    const progress: AchievementProgress = {
      achievementId: achievement.id,
      currentValue,
      targetValue,
      progressPercentage,
      lastUpdated: new Date().toISOString(),
      metadata
    };

    // Handle streak-specific logic
    let streakData: StreakData | undefined;
    if (achievement.progressType === ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES.STREAK) {
      streakData = this.calculateStreakData(achievement.id, currentValue, metadata);
    }

    return {
      progress,
      statusChanged,
      newStatus,
      unlockedAt,
      streakData
    };
  }

  /**
   * Calculate streak data with consistent logic
   */
  private calculateStreakData(
    achievementId: string,
    currentStreakValue: number,
    metadata?: Record<string, any>
  ): StreakData {
    const existingStreak = this.streaks.get(achievementId);
    const today = this.getNormalizedDate(new Date());
    
    let streakData: StreakData;

    if (!existingStreak) {
      // First time tracking this streak
      streakData = {
        type: achievementId,
        currentStreak: currentStreakValue,
        longestStreak: currentStreakValue,
        lastActivityDate: today,
        streakStartDate: today,
        isActive: currentStreakValue > 0
      };
    } else {
      const lastActivityDate = new Date(existingStreak.lastActivityDate);
      const todayDate = new Date(today);
      const daysDifference = this.getDaysDifference(lastActivityDate, todayDate);

      if (daysDifference === 0) {
        // Same day update - don't change streak
        streakData = { ...existingStreak };
      } else if (daysDifference === 1) {
        // Consecutive day - extend streak
        streakData = {
          ...existingStreak,
          currentStreak: currentStreakValue,
          longestStreak: Math.max(existingStreak.longestStreak, currentStreakValue),
          lastActivityDate: today,
          isActive: currentStreakValue > 0
        };
      } else if (daysDifference <= ACHIEVEMENT_CONSTANTS.STREAK_SETTINGS.MAX_STREAK_GAP_DAYS + 1) {
        // Small gap - reset streak but keep longest
        streakData = {
          ...existingStreak,
          currentStreak: 1, // Reset to 1 for new activity
          lastActivityDate: today,
          streakStartDate: today,
          isActive: true
        };
      } else {
        // Large gap - reset streak
        streakData = {
          ...existingStreak,
          currentStreak: 1,
          lastActivityDate: today,
          streakStartDate: today,
          isActive: true
        };
      }
    }

    // Validate streak data
    if (streakData.currentStreak > ACHIEVEMENT_CONSTANTS.LIMITS.MAX_STREAK_DAYS) {
      console.warn(`[UnifiedAchievement] Streak value ${streakData.currentStreak} exceeds maximum, capping at ${ACHIEVEMENT_CONSTANTS.LIMITS.MAX_STREAK_DAYS}`);
      streakData.currentStreak = ACHIEVEMENT_CONSTANTS.LIMITS.MAX_STREAK_DAYS;
    }

    this.streaks.set(achievementId, streakData);
    return streakData;
  }

  /**
   * Update achievement progress with validation and conflict resolution
   */
  async updateProgress(
    achievementId: string,
    currentValue: number,
    metadata?: Record<string, any>
  ): Promise<AchievementCalculationResult> {
    await this.loadAchievements();
    
    const achievement = this.achievements.get(achievementId);
    if (!achievement) {
      throw new Error(`Achievement ${achievementId} not found`);
    }

    // Calculate new progress
    const result = this.calculateProgress(achievement, currentValue, metadata);
    
    // Update local storage
    this.progress.set(achievementId, result.progress);
    await this.saveProgress();

    // Update achievement status if changed
    if (result.statusChanged && result.newStatus) {
      achievement.status = result.newStatus;
      achievement.unlockedAt = result.unlockedAt;
      this.achievements.set(achievementId, achievement);
      await this.saveAchievements();
    }

    // Add to sync queue
    this.syncQueue.add(achievementId);
    await this.saveSyncQueue();

    console.log(`[UnifiedAchievement] Updated progress for ${achievementId}: ${result.progress.progressPercentage}%`);
    
    return result;
  }

  /**
   * Sync achievements with server using conflict resolution
   */
  async syncWithServer(): Promise<SyncResult> {
    if (this.syncQueue.size === 0) {
      return { success: true, syncedCount: 0, failedCount: 0, conflicts: [] };
    }

    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      failedCount: 0,
      conflicts: []
    };

    // Process sync queue in batches
    const queueArray = Array.from(this.syncQueue);
    for (let i = 0; i < queueArray.length; i += ACHIEVEMENT_CONSTANTS.SYNC_SETTINGS.BATCH_SIZE) {
      const batch = queueArray.slice(i, i + ACHIEVEMENT_CONSTANTS.SYNC_SETTINGS.BATCH_SIZE);
      
      try {
        const batchResult = await this.syncBatch(batch);
        result.syncedCount += batchResult.syncedCount;
        result.failedCount += batchResult.failedCount;
        result.conflicts.push(...batchResult.conflicts);
        
        // Remove successfully synced items from queue
        batch.forEach(id => {
          if (batchResult.syncedIds?.includes(id)) {
            this.syncQueue.delete(id);
          }
        });
        
      } catch (error) {
        console.error('[UnifiedAchievement] Batch sync failed:', error);
        result.failedCount += batch.length;
        result.success = false;
      }
    }

    await this.saveSyncQueue();
    await this.saveLastSyncTime();

    console.log(`[UnifiedAchievement] Sync completed: ${result.syncedCount} synced, ${result.failedCount} failed, ${result.conflicts.length} conflicts`);
    
    return result;
  }

  /**
   * Get achievement progress with caching
   */
  async getProgress(achievementId: string): Promise<AchievementProgress | null> {
    await this.loadProgress();
    return this.progress.get(achievementId) || null;
  }

  /**
   * Get all achievements with current progress
   */
  async getAllAchievements(): Promise<Array<Achievement & { progress?: AchievementProgress }>> {
    await this.loadAchievements();
    await this.loadProgress();

    return Array.from(this.achievements.values()).map(achievement => ({
      ...achievement,
      progress: this.progress.get(achievement.id)
    }));
  }

  /**
   * Validate achievement data
   */
  validateAchievement(achievement: Partial<Achievement>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!achievement.id || typeof achievement.id !== 'string') {
      errors.push('Achievement ID is required and must be a string');
    }

    if (!achievement.title || typeof achievement.title !== 'string') {
      errors.push('Achievement title is required and must be a string');
    }

    if (achievement.targetValue !== undefined && 
        (typeof achievement.targetValue !== 'number' || achievement.targetValue <= 0)) {
      errors.push('Target value must be a positive number');
    }

    if (achievement.progressType && 
        !Object.values(ACHIEVEMENT_CONSTANTS.PROGRESS_TYPES).includes(achievement.progressType as any)) {
      errors.push('Invalid progress type');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Private helper methods
   */
  private async syncBatch(achievementIds: string[]): Promise<SyncResult & { syncedIds?: string[] }> {
    const progressData = achievementIds.map(id => ({
      achievementId: id,
      progress: this.progress.get(id)
    })).filter(item => item.progress);

    try {
      const response = await postRequest('/achievements/sync', {
        progressData
      }, {
        retries: ACHIEVEMENT_CONSTANTS.SYNC_SETTINGS.MAX_RETRIES,
        retryDelay: ACHIEVEMENT_CONSTANTS.SYNC_SETTINGS.RETRY_DELAY_MS,
        logTag: '[UnifiedAchievement]'
      });

      // Handle server response and conflicts
      const conflicts = response.conflicts || [];
      const syncedIds = response.syncedIds || achievementIds;

      return {
        success: true,
        syncedCount: syncedIds.length,
        failedCount: achievementIds.length - syncedIds.length,
        conflicts,
        syncedIds
      };

    } catch (error) {
      console.error('[UnifiedAchievement] Sync batch error:', error);
      return {
        success: false,
        syncedCount: 0,
        failedCount: achievementIds.length,
        conflicts: []
      };
    }
  }

  private getNormalizedDate(date: Date): string {
    // Normalize to UTC date string for consistent comparison
    const utcDate = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
    return utcDate.toISOString().split('T')[0];
  }

  private getDaysDifference(date1: Date, date2: Date): number {
    const normalizedDate1 = this.getNormalizedDate(date1);
    const normalizedDate2 = this.getNormalizedDate(date2);
    
    const d1 = new Date(normalizedDate1);
    const d2 = new Date(normalizedDate2);
    
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private async loadAchievements(): Promise<void> {
    const now = Date.now();
    if ((now - this.lastCacheUpdate) < ACHIEVEMENT_CONSTANTS.CACHE_TTL_MS && this.achievements.size > 0) {
      return;
    }

    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.ACHIEVEMENTS);
      if (stored) {
        const achievementsArray = JSON.parse(stored) as Achievement[];
        this.achievements.clear();
        achievementsArray.forEach(achievement => {
          this.achievements.set(achievement.id, achievement);
        });
        this.lastCacheUpdate = now;
      }
    } catch (error) {
      console.error('[UnifiedAchievement] Error loading achievements:', error);
    }
  }

  private async saveAchievements(): Promise<void> {
    try {
      const achievementsArray = Array.from(this.achievements.values());
      await AsyncStorage.setItem(STORAGE_KEYS.ACHIEVEMENTS, JSON.stringify(achievementsArray));
    } catch (error) {
      console.error('[UnifiedAchievement] Error saving achievements:', error);
    }
  }

  private async loadProgress(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.PROGRESS);
      if (stored) {
        const progressData = JSON.parse(stored) as Record<string, AchievementProgress>;
        this.progress.clear();
        Object.entries(progressData).forEach(([id, progress]) => {
          this.progress.set(id, progress);
        });
      }
    } catch (error) {
      console.error('[UnifiedAchievement] Error loading progress:', error);
    }
  }

  private async saveProgress(): Promise<void> {
    try {
      const progressData: Record<string, AchievementProgress> = {};
      this.progress.forEach((progress, id) => {
        progressData[id] = progress;
      });
      await AsyncStorage.setItem(STORAGE_KEYS.PROGRESS, JSON.stringify(progressData));
    } catch (error) {
      console.error('[UnifiedAchievement] Error saving progress:', error);
    }
  }

  private async saveSyncQueue(): Promise<void> {
    try {
      const queueArray = Array.from(this.syncQueue);
      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_QUEUE, JSON.stringify(queueArray));
    } catch (error) {
      console.error('[UnifiedAchievement] Error saving sync queue:', error);
    }
  }

  private async saveLastSyncTime(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
    } catch (error) {
      console.error('[UnifiedAchievement] Error saving last sync time:', error);
    }
  }
}

// Export singleton instance
export const unifiedAchievementService = new UnifiedAchievementService();

// Convenience functions
export const updateAchievementProgress = (achievementId: string, currentValue: number, metadata?: Record<string, any>) =>
  unifiedAchievementService.updateProgress(achievementId, currentValue, metadata);

export const getAchievementProgress = (achievementId: string) =>
  unifiedAchievementService.getProgress(achievementId);

export const getAllAchievements = () =>
  unifiedAchievementService.getAllAchievements();

export const syncAchievements = () =>
  unifiedAchievementService.syncWithServer();

export const validateAchievement = (achievement: Partial<Achievement>) =>
  unifiedAchievementService.validateAchievement(achievement);
