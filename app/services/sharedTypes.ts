// Shared types to break circular dependencies
import { Message } from './chatService';

// Re-export Message type
export type { Message };

// Log types
export type LogType = 'workout' | 'meal' | 'conversation' | 'weight';

// Interfaces for workout data
export interface WorkoutExercise {
  name: string;
  sets: Array<{
    reps: number;
    weight?: number;
    duration?: number;
    rest?: number;
    notes?: string;
  }>;
  notes?: string;
}

export interface WorkoutData {
  title: string;
  exercises: WorkoutExercise[];
  duration?: number;
  notes?: string;
  tags?: string[];
  completed?: boolean;
  description?: string;
  type?: string;
  category?: string;
  difficulty?: string;
  completedAt?: string;
  muscleGroups?: string[];
}

// Interfaces for meal data
export interface MealData {
  title: string;
  description: string;
  ingredients?: string[];
  steps?: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  tags?: string[];
}

// Main Log interface
export interface Log {
  id: string;
  type: LogType;
  description: string;
  timestamp: string;
  messages?: Message[]; // Specific to conversation type
  contextDate?: string;
  workoutData?: WorkoutData; // Specific to workout type
  metrics?: { // Can contain meal data or other metrics
    meal?: MealData; // Specific to meal type
    weight?: WeightEntry; // Specific to weight type
    [key: string]: any;
  };
}

// Weight tracking interfaces
export interface WeightEntry {
  value: number;
  date: string; // ISO string
  note?: string;
}

export interface WeightStats {
  current: number | null;
  average: number | null;
  min: number | null;
  max: number | null;
  change: {
    value: number;
    percentage: number;
    isPositive: boolean;
  } | null;
  trend: 'up' | 'down' | 'stable' | null;
}

// Achievement types
export enum AchievementCategory {
  WORKOUT = 'workout',
  NUTRITION = 'nutrition',
  WEIGHT = 'weight',
  STREAK = 'streak',
  GENERAL = 'general'
}

export enum AchievementStatus {
  LOCKED = 'locked',
  IN_PROGRESS = 'in_progress',
  UNLOCKED = 'unlocked'
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  category: AchievementCategory;
  icon: string;
  status: AchievementStatus;
  progress: number; // 0 to 1
  currentValue?: number;
  targetValue?: number;
  unlockedAt?: string;
  createdAt: string;
  updatedAt: string;
  achievementId?: string;
}