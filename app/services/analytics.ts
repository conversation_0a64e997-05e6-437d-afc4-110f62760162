import { useProfile } from '../context/ProfileContext';
import { Log, LogType, getLogs, saveLog } from './conversationService';

// Analytics types
export interface StatCard {
  title: string;
  value: string;
  icon: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export interface InsightCard {
  title: string;
  description: string;
  icon: string;
}

// Generate stats from logs
export const generateStats = async (): Promise<StatCard[]> => {
  try {
    const logs = await getLogs();

    if (logs.length === 0) {
      return [];
    }

    // Count by log type
    const workouts = logs.filter(log => log.type === 'workout');
    const meals = logs.filter(log => log.type === 'meal');
    const conversations = logs.filter(log => log.type === 'conversation');

    // Calculate statistics
    const startDate = new Date(logs.sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    )[0].timestamp);

    const days = Math.max(1, Math.ceil((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24)));

    // Consistency calculation (percentage of days with at least one log)
    const uniqueDays = new Set(logs.map(log =>
      new Date(log.timestamp).toISOString().split('T')[0]
    )).size;

    const consistency = Math.min(100, Math.round((uniqueDays / days) * 100));

    // Get last 30 days logs to calculate trends
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentLogs = logs.filter(log => new Date(log.timestamp) >= thirtyDaysAgo);
    const previousLogs = logs.filter(log => new Date(log.timestamp) < thirtyDaysAgo);

    // Calculate trends
    const calculateTrend = (current: number, previous: number): { value: number, isPositive: boolean } => {
      if (previous === 0) {
         // Avoid division by zero, if current is > 0, trend is positive infinite (cap at 100/200?)
         return { value: current > 0 ? 100 : 0, isPositive: current > 0 };
      }
      const trendValue = Math.round(((current - previous) / previous) * 100);
      return {
        value: Math.abs(trendValue),
        isPositive: trendValue >= 0
      };
    };

    const workoutTrend = calculateTrend(
      recentLogs.filter(log => log.type === 'workout').length,
      previousLogs.filter(log => log.type === 'workout').length
    );

    const mealTrend = calculateTrend(
      recentLogs.filter(log => log.type === 'meal').length,
      previousLogs.filter(log => log.type === 'meal').length
    );

    const conversationTrend = calculateTrend(
      recentLogs.filter(log => log.type === 'conversation').length,
      previousLogs.filter(log => log.type === 'conversation').length
    );
    
    // Trend calculation for consistency needs adjustment if previousLogs is empty
    const recentUniqueDays = new Set(recentLogs.map(log => new Date(log.timestamp).toISOString().split('T')[0])).size;
    const previousUniqueDays = new Set(previousLogs.map(log => new Date(log.timestamp).toISOString().split('T')[0])).size;

    const consistencyTrend = calculateTrend(recentUniqueDays, previousUniqueDays);


    return [
      {
        title: 'Conversations',
        value: conversations.length.toString(),
        icon: 'chatbubbles',
        trend: conversationTrend
      },
      {
        title: 'Health Insights',
        value: workouts.length.toString(),
        icon: 'barbell',
        trend: workoutTrend
      },
      {
        title: 'Consistency',
        value: `${consistency}%`,
        icon: 'calendar',
        trend: consistencyTrend
      }
    ];
  } catch (error) {
    console.error('Error generating stats:', error);
    return [];
  }
};

// Generate insights from logs
export const generateInsights = async (): Promise<InsightCard[]> => {
  try {
    const logs = await getLogs();

    if (logs.length < 5) {  // Need minimum amount of data for insights
      return [];
    }

    const insights: InsightCard[] = [];

    // Find most consistent day
    const dayCount: { [key: string]: number } = {};
    logs.forEach(log => {
      const day = new Date(log.timestamp).toLocaleDateString('en-US', { weekday: 'long' });
      dayCount[day] = (dayCount[day] || 0) + 1;
    });

    const sortedDays = Object.entries(dayCount).sort((a, b) => b[1] - a[1]);
    if (sortedDays.length > 0) {
      const mostConsistentDay = sortedDays[0][0];
      insights.push({
        title: 'Most Active Day',
        description: `You engage with health advice most on ${mostConsistentDay}s`,
        icon: 'calendar'
      });
    }

    // Find time pattern
    const timeRanges: { [key: string]: number } = {
      'Morning (5-11am)': 0,
      'Afternoon (12-5pm)': 0,
      'Evening (6-11pm)': 0,
      'Night (12-4am)': 0
    };

    logs.forEach(log => {
      const hour = new Date(log.timestamp).getHours();
      if (hour >= 5 && hour < 12) {
        timeRanges['Morning (5-11am)']++;
      } else if (hour >= 12 && hour < 18) {
        timeRanges['Afternoon (12-5pm)']++;
      } else if (hour >= 18 && hour < 24) {
        timeRanges['Evening (6-11pm)']++;
      } else {
        timeRanges['Night (12-4am)']++;
      }
    });

    const sortedTimes = Object.entries(timeRanges).sort((a, b) => b[1] - a[1]);
    if (sortedTimes.length > 0 && sortedTimes[0][1] > 0) { // Ensure there's actually a most active time
      const mostActiveTime = sortedTimes[0][0];
      insights.push({
        title: 'Active Time',
        description: `You're most active during ${mostActiveTime}`,
        icon: 'time'
      });
    }

    // Conversation insights
    const conversationLogs = logs.filter(log => log.type === 'conversation');
    if (conversationLogs.length > 0) {
      const totalMessages = conversationLogs.reduce((count, log) =>
        count + (log.messages?.length || 0), 0);

      if (totalMessages > 0) {
        insights.push({
          title: 'Conversation Depth',
          description: `You've exchanged ${totalMessages} messages about your health journey`,
          icon: 'chatbubbles'
        });
      }
    }

    return insights;
  } catch (error) {
    console.error('Error generating insights:', error);
    return [];
  }
};

// Add a sample log for testing (remove in production)
export const addSampleLog = async (): Promise<void> => {
  const workoutSamples = [
    'Bench press: 3x10 at 135lbs',
    ' 5k run in 25 minutes',
    'Squat: 3x8 at 185lbs',
    'Pushups: 4x20',
    'Deadlift: 3x5 at 225lbs'
  ];

  const mealSamples = [
    'Protein smoothie with berries and whey',
    'Grilled chicken salad with olive oil',
    'Salmon with quinoa and vegetables',
    'Oatmeal with peanut butter and banana',
    'Turkey sandwich on whole wheat'
  ];

  // Generate a random log
  const type: LogType = Math.random() > 0.5 ? 'workout' : 'meal';
  const samples = type === 'workout' ? workoutSamples : mealSamples;
  const description = samples[Math.floor(Math.random() * samples.length)];

  // Random timestamp within the last 60 days
  const timestamp = new Date();
  timestamp.setDate(timestamp.getDate() - Math.floor(Math.random() * 60));

  const log: Log = {
    id: `sample_${Date.now()}`,
    type,
    description,
    timestamp: timestamp.toISOString()
  };

  await saveLog(log);
};
