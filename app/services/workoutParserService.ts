import { api } from './apiClient';
import { getUserId } from './chatService';
import { WorkoutExercise } from './conversationService';

/**
 * Parse a workout entry using the LLM
 * @param input - The user's description of the exercise
 * @returns Parsed workout exercise data
 */
export async function parseWorkoutEntry(input: string): Promise<WorkoutExercise | null> {
  try {
    // Get the user ID
    const userId = await getUserId();

    // Create a system prompt that instructs the model to parse the workout entry
    const systemPrompt = `You are a fitness expert assistant that parses workout descriptions.
    When given a workout description, respond ONLY with a JSON object containing the structured exercise data.

    The JSON MUST have this EXACT format with NO DEVIATIONS:
    {
      "exercise": {
        "name": "Exercise name",
        "sets": [
          {
            "reps": 10,
            "weight": 135,
            "duration": 0
          }
        ],
        "muscleGroup": "Primary muscle group"
      }
    }

    IMPORTANT: The "sets" field MUST be an ARRAY of objects, NOT a number. If the user specifies "3 sets", you must create an array with 3 set objects, like this:
    "sets": [
      { "reps": 10, "weight": 135 },
      { "reps": 10, "weight": 135 },
      { "reps": 10, "weight": 135 }
    ]

    CRITICAL REQUIREMENTS:
    1. ONLY return the JSON object, no other text or explanation
    2. Keep the JSON structure EXACTLY as shown above
    3. For bodyweight exercises like pushups, pullups, or situps, set weight to 0
    4. For time-based exercises like planks, set reps to 1 and duration to seconds (e.g., 30, 60)
    5. All numeric values MUST be numbers (not strings)
    6. Identify the primary muscle group (e.g., chest, back, legs, shoulders, arms, core)
    7. If multiple sets with different weights/reps are mentioned, include each as a separate object in the sets array
    8. If no specific number of sets is mentioned, assume 1 set
    9. ALWAYS expand abbreviations to their full exercise names:
       - "BP" → "Bench Press"
       - "DL" → "Deadlift"
       - "OHP" → "Overhead Press"
       - "Lat pulldown" → "Lateral Pulldown"
       - "DB" → "Dumbbell"
       - "BB" → "Barbell"
       - "SQ" → "Squat"
       - "RDL" → "Romanian Deadlift"
       - "Tri" → "Triceps"
       - "Bi" → "Biceps"
       - "Abs" → "Abdominals"
       - "Pec" → "Pectoral"
       - "Delt" → "Deltoid"
    10. Use proper capitalization and formatting for exercise names`;

    // Prepare the messages for the API
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: `Parse this workout entry: ${input}`
      }
    ];

    // Make the API call
    console.log('[parseWorkoutEntry] Making API request');
    const response = await api.post('/chat', {
      messages,
      userId,
      conversationId: `workout_parser_${Date.now()}`,
      temperature: 0.3,
      requestType: 'workout_parser'
    }, {
      timeout: 30000 // 30 second timeout
    });

    if (response.data && response.data.content) {
      const content = response.data.content;
      console.log('Received response from chat API:', content);

      try {
        // Extract JSON from the response
        const jsonRegex = /\{[\s\S]*\}/g;
        const jsonMatch = content.match(jsonRegex);

        if (jsonMatch && jsonMatch.length > 0) {
          const parsedJson = JSON.parse(jsonMatch[0]);

          if (parsedJson.exercise) {
            console.log('Successfully parsed exercise:', parsedJson.exercise.name);

            // Ensure the exercise has the correct structure
            const exercise = parsedJson.exercise;

            // Make sure sets is an array
            if (!Array.isArray(exercise.sets)) {
              // If sets is a number, convert it to an array of set objects
              if (typeof exercise.sets === 'number') {
                const numSets = exercise.sets;
                exercise.sets = [];
                for (let i = 0; i < numSets; i++) {
                  exercise.sets.push({
                    reps: exercise.reps || 10,
                    weight: exercise.weight || 0
                  });
                }
              } else {
                // If sets is not an array or number, create a default array with one set
                exercise.sets = [{
                  reps: exercise.reps || 10,
                  weight: exercise.weight || 0
                }];
              }
            }

            // Log the formatted exercise
            console.log('Formatted exercise:', JSON.stringify(exercise));

            return exercise;
          }
        }

        // If we couldn't extract JSON with the regex, try parsing the entire response
        try {
          const parsedResponse = JSON.parse(content);
          if (parsedResponse.exercise) {
            // Ensure the exercise has the correct structure
            const exercise = parsedResponse.exercise;

            // Make sure sets is an array
            if (!Array.isArray(exercise.sets)) {
              // If sets is a number, convert it to an array of set objects
              if (typeof exercise.sets === 'number') {
                const numSets = exercise.sets;
                exercise.sets = [];
                for (let i = 0; i < numSets; i++) {
                  exercise.sets.push({
                    reps: exercise.reps || 10,
                    weight: exercise.weight || 0
                  });
                }
              } else {
                // If sets is not an array or number, create a default array with one set
                exercise.sets = [{
                  reps: exercise.reps || 10,
                  weight: exercise.weight || 0
                }];
              }
            }

            return exercise;
          }
        } catch (error) {
          console.error('Failed to parse entire response as JSON:', error);
        }

        console.error('No valid exercise data found in response');
        return null;
      } catch (error) {
        console.error('Error parsing LLM response:', error);
        return null;
      }
    } else {
      console.error('Invalid response format from API');
      return null;
    }
  } catch (error) {
    console.error('Error parsing workout entry:', error);
    return null;
  }
}

/**
 * Create a fallback exercise object when LLM parsing fails
 * @param input - The user's description of the exercise
 * @returns A basic exercise object
 */
export function createFallbackExercise(input: string): WorkoutExercise {
  // Extract basic information using regex
  const weightMatch = input.match(/(\d+)\s*(?:lbs?|pounds?|kg)/i);
  const repsMatch = input.match(/(\d+)\s*(?:reps?|repetitions?)/i);
  const setsMatch = input.match(/(\d+)\s*(?:sets?)/i);
  const durationMatch = input.match(/(\d+)\s*(?:sec(?:ond)?s?|min(?:ute)?s?)/i);

  // Extract the exercise name (everything before the first number)
  let exerciseName = input;
  const firstNumberIndex = input.search(/\d/);
  if (firstNumberIndex > 0) {
    exerciseName = input.substring(0, firstNumberIndex).trim();
  }

  // Expand common abbreviations
  exerciseName = expandExerciseAbbreviations(exerciseName);

  // Determine if this is a time-based exercise
  const isTimeBased = input.toLowerCase().includes('plank') ||
                      input.toLowerCase().includes('hold') ||
                      input.toLowerCase().includes('seconds') ||
                      input.toLowerCase().includes('minutes');

  // Create the exercise object
  const exercise: WorkoutExercise = {
    name: exerciseName || 'Custom Exercise',
    sets: [],
    muscleGroup: determineMuscleGroup(input)
  };

  // Create the sets
  const numSets = setsMatch ? parseInt(setsMatch[1]) : 1;

  for (let i = 0; i < numSets; i++) {
    if (isTimeBased) {
      // For time-based exercises
      exercise.sets.push({
        reps: 1,
        weight: 0,
        duration: durationMatch ? parseInt(durationMatch[1]) : 30
      });
    } else {
      // For regular exercises
      exercise.sets.push({
        reps: repsMatch ? parseInt(repsMatch[1]) : 10,
        weight: weightMatch ? parseInt(weightMatch[1]) : 0
      });
    }
  }

  return exercise;
}

/**
 * Expand common exercise abbreviations to their full names
 * @param exerciseName - The abbreviated exercise name
 * @returns The expanded exercise name
 */
function expandExerciseAbbreviations(exerciseName: string): string {
  // Create a map of abbreviations to full names
  const abbreviations: Record<string, string> = {
    'bp': 'Bench Press',
    'dl': 'Deadlift',
    'ohp': 'Overhead Press',
    'lat pulldown': 'Lateral Pulldown',
    'db': 'Dumbbell',
    'bb': 'Barbell',
    'sq': 'Squat',
    'rdl': 'Romanian Deadlift',
    'tri': 'Triceps',
    'bi': 'Biceps',
    'abs': 'Abdominals',
    'pec': 'Pectoral',
    'delt': 'Deltoid',
    'lat': 'Lateral',
    'pullup': 'Pull-up',
    'pushup': 'Push-up',
    'situp': 'Sit-up',
    'pulldown': 'Pull-down',
  };

  // Convert to lowercase for matching
  let lowerName = exerciseName.toLowerCase();

  // Check for exact matches first
  if (abbreviations[lowerName]) {
    return abbreviations[lowerName];
  }

  // Check for word matches within the name
  for (const [abbr, full] of Object.entries(abbreviations)) {
    // Use word boundary to match whole words only
    const regex = new RegExp(`\\b${abbr}\\b`, 'i');
    if (regex.test(lowerName)) {
      lowerName = lowerName.replace(regex, full);
    }
  }

  // Capitalize the first letter of each word
  return lowerName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Determine the muscle group based on the exercise description
 * @param input - The exercise description
 * @returns The primary muscle group
 */
function determineMuscleGroup(input: string): string {
  const lowerInput = input.toLowerCase();

  // Check for common exercises and their muscle groups
  if (lowerInput.includes('bench press') || lowerInput.includes('push-up') ||
      lowerInput.includes('chest fly') || lowerInput.includes('chest press') ||
      lowerInput.includes('pectoral') || lowerInput.includes('pec')) {
    return 'Chest';
  } else if (lowerInput.includes('squat') || lowerInput.includes('lunge') ||
             lowerInput.includes('leg press') || lowerInput.includes('deadlift') ||
             lowerInput.includes('calf') || lowerInput.includes('hamstring')) {
    return 'Legs';
  } else if (lowerInput.includes('pull-up') || lowerInput.includes('row') ||
             lowerInput.includes('lateral pulldown') || lowerInput.includes('back') ||
             lowerInput.includes('lat')) {
    return 'Back';
  } else if (lowerInput.includes('shoulder press') || lowerInput.includes('lateral raise') ||
             lowerInput.includes('overhead press') || lowerInput.includes('deltoid') ||
             lowerInput.includes('delt')) {
    return 'Shoulders';
  } else if (lowerInput.includes('bicep') || lowerInput.includes('curl') ||
             lowerInput.includes('tricep') || lowerInput.includes('extension') ||
             lowerInput.includes('bi') || lowerInput.includes('tri')) {
    return 'Arms';
  } else if (lowerInput.includes('sit-up') || lowerInput.includes('crunch') ||
             lowerInput.includes('plank') || lowerInput.includes('ab') ||
             lowerInput.includes('abdominal')) {
    return 'Core';
  } else {
    return 'Other';
  }
}
