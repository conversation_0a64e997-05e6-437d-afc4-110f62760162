import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { getStoredTokens, signOut, refreshToken } from './auth';
import { Alert } from 'react-native';
import { api } from './apiClient';
import {
  Message,
  LogType,
  WorkoutExercise,
  WorkoutData,
  MealData,
  Log
} from './sharedTypes';

// Import achievement service dynamically to avoid circular dependencies
const getAchievementService = () => {
  return require('./achievementService');
};

// Storage keys
const LOGS_STORAGE_KEY = '@logs';
// Keep workout/meal specific keys if they are managed separately elsewhere later
const WORKOUTS_STORAGE_KEY = '@saved_workouts';
const MEALS_STORAGE_KEY = '@saved_meals';

// Sync-related keys
const LAST_FULL_SYNC_TIME_KEY = 'LAST_FULL_SYNC_TIME';
const LAST_SYNC_ATTEMPT_KEY = 'LAST_SYNC_ATTEMPT';

// API endpoints (API_URL is defined in apiClient now)
// const API_URL = process.env.EXPO_PUBLIC_API_URL;

// Auth header helper - Keep this, but it's less critical if interceptor works
async function getAuthHeader() {
  const tokens = await getStoredTokens();
  if (!tokens) {
    console.warn('No stored tokens found for auth header.');
    return { Authorization: null };
  }
  // Use idToken for API Gateway Cognito Authorizer
  return {
    Authorization: `Bearer ${tokens.idToken}`
  };
}

// API call utility - Keep this, it uses the imported 'api' instance
async function apiCallWithRetry(apiCall: (signal?: AbortSignal) => Promise<any>, maxRetries = 5): Promise<any> {
  // Check if app is signing out (simplified check, assumes a global flag might be set elsewhere if needed)
  // if ((window as any).__AUTH_SIGNING_OUT) {
  //   console.log('Skipping API call because app is signing out');
  //   throw new Error('API call aborted: App is signing out');
  // }

  let lastError;
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    // if ((window as any).__AUTH_SIGNING_OUT) { /* Check again */ }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout

      try {
        const result = await apiCall(controller.signal);
        clearTimeout(timeoutId);
        return result;
      } catch (err) {
        clearTimeout(timeoutId);
        throw err;
      }
    } catch (error: any) {
      lastError = error;
       if (axios.isCancel(error)) {
         console.log(`Request timed out or cancelled (Attempt ${attempt + 1}/${maxRetries})`);
       } else if (error?.response) {
        console.error(`API Error Response (Attempt ${attempt + 1}/${maxRetries}):`, {
          endpoint: error.config?.url || 'unknown',
          status: error.response?.status || 'unknown',
          data: error.response?.data || 'no data'
        });
         // Don't retry on certain client errors unless it's auth-related
         if (error.response.status >= 400 && error.response.status < 500 && error.response.status !== 401) {
             // Potentially break retry loop for non-auth client errors
             // break;
         }
      } else if (error?.request) {
        console.error(`API No Response Error (Attempt ${attempt + 1}/${maxRetries}):`, {
          endpoint: error.config?.url || 'unknown',
          message: error.message || 'unknown',
          code: error.code || 'unknown'
        });
      } else {
        console.error(`API Setup Error (Attempt ${attempt + 1}/${maxRetries}):`, error.message);
      }

      // Don't retry if it's an auth error that already failed refresh
      if (error?.__isAuthError) {
        console.log('Not retrying after auth refresh failure');
        break;
      }

      // Wait before retrying with exponential backoff
      if (attempt < maxRetries - 1) {
        const delay = Math.min(Math.pow(2, attempt) * 500, 5000); // Max 5s delay
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // If we've exhausted all retries, throw the last error
  throw lastError || new Error('API call failed after multiple retries');
}

// Get all logs from storage
export async function getLogs(): Promise<Log[]> {
  try {
    const logsJson = await AsyncStorage.getItem(LOGS_STORAGE_KEY);
    if (logsJson) {
      const logs = JSON.parse(logsJson);
      return Array.isArray(logs) ? logs : [];
    }
    return [];
  } catch (error) {
    console.error('Error getting logs:', error);
    return [];
  }
}

// Save a log to storage
export async function saveLog(log: Log): Promise<void> {
  try {
    // Get existing logs
    const logs = await getLogs();

    // Check if log with same ID already exists
    const existingLogIndex = logs.findIndex(l => l.id === log.id);
    if (existingLogIndex !== -1) {
      // Update existing log
      logs[existingLogIndex] = log;
    } else {
      // Add new log
      logs.push(log);
    }

    // Sort logs by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Save logs
    await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(logs));

    // Update achievements in the background
    try {
      const achievementService = getAchievementService();
      achievementService.checkAndUpdateAchievements().catch((e: any) => 
        console.error('Error updating achievements after log:', e)
      );
    } catch (error) {
      console.error('Error importing achievement service:', error);
    }
  } catch (error) {
    console.error('Error saving log:', error);
    throw error;
  }
}

// Save all logs to storage
export async function saveAllLocalLogs(logs: Log[]): Promise<void> {
  try {
    // Sort logs by timestamp (newest first)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Save logs
    await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(logs));
  } catch (error) {
    console.error('Error saving all logs:', error);
    throw error;
  }
}

// Delete a log from storage
export async function deleteLog(logId: string): Promise<void> {
  try {
    // Get existing logs
    const logs = await getLogs();

    // Filter out the log to delete
    const filteredLogs = logs.filter(log => log.id !== logId);

    // Save filtered logs
    await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(filteredLogs));
  } catch (error) {
    console.error('Error deleting log:', error);
    throw error;
  }
}

// Get logs by type
export async function getLogsByType(type: LogType): Promise<Log[]> {
  try {
    const logs = await getLogs();
    return logs.filter(log => log.type === type);
  } catch (error) {
    console.error(`Error getting logs by type ${type}:`, error);
    return [];
  }
}

// Get logs by date range
export async function getLogsByDateRange(startDate: Date, endDate: Date): Promise<Log[]> {
  try {
    const logs = await getLogs();
    return logs.filter(log => {
      const logDate = new Date(log.timestamp);
      return logDate >= startDate && logDate <= endDate;
    });
  } catch (error) {
    console.error('Error getting logs by date range:', error);
    return [];
  }
}

// Sync logs with backend
export async function syncLogsWithBackend(): Promise<boolean> {
  try {
    // Get auth header
    const authHeader = await getAuthHeader();
    if (!authHeader.Authorization) {
      console.warn('No auth header available for sync');
      return false;
    }

    // Get local logs
    const localLogs = await getLogs();
    if (!localLogs.length) {
      console.log('No local logs to sync');
      return true;
    }

    // Get last sync time
    const lastSyncTime = await AsyncStorage.getItem(LAST_FULL_SYNC_TIME_KEY);
    const syncTime = new Date().toISOString();

    // Update last sync attempt time
    await AsyncStorage.setItem(LAST_SYNC_ATTEMPT_KEY, syncTime);

    // Sync logs
    const response = await apiCallWithRetry(async (signal) => {
      return api.post('/logs/sync', {
        logs: localLogs,
        lastSyncTime: lastSyncTime || null
      }, {
        headers: authHeader,
        signal
      });
    });

    // Update last sync time
    await AsyncStorage.setItem(LAST_FULL_SYNC_TIME_KEY, syncTime);

    // If server returned updated logs, update local storage
    if (response.data && response.data.logs) {
      await saveAllLocalLogs(response.data.logs);
    }

    return true;
  } catch (error) {
    console.error('Error syncing logs with backend:', error);
    return false;
  }
}

// Get conversation logs (specific to conversation type)
export async function getConversationLogs(): Promise<Log[]> {
  try {
    const logs = await getLogs();
    return logs.filter(log => log.type === 'conversation');
  } catch (error) {
    console.error('Error getting conversation logs:', error);
    return [];
  }
}

// Create a new conversation
export async function createConversation(description: string = 'New conversation'): Promise<Log | null> {
  try {
    const conversationId = `conversation_${Date.now()}`;
    const timestamp = new Date().toISOString();
    
    const conversation: Log = {
      id: conversationId,
      type: 'conversation',
      description,
      timestamp,
      messages: []
    };
    
    await saveLog(conversation);
    return conversation;
  } catch (error) {
    console.error('Error creating conversation:', error);
    return null;
  }
}

// Add a message to a conversation
export async function addMessageToConversation(conversationId: string, message: Message): Promise<boolean> {
  try {
    const logs = await getLogs();
    const conversationIndex = logs.findIndex(log => log.id === conversationId);
    
    if (conversationIndex === -1) {
      console.error(`Conversation with ID ${conversationId} not found`);
      return false;
    }
    
    const conversation = logs[conversationIndex];
    const messages = conversation.messages || [];
    
    conversation.messages = [...messages, message];
    
    await saveLog(conversation);
    return true;
  } catch (error) {
    console.error('Error adding message to conversation:', error);
    return false;
  }
}

// Delete a log (wrapper for the existing deleteLog function)
export async function deleteConversationLog(logId: string): Promise<boolean> {
  try {
    await deleteLog(logId);
    return true;
  } catch (error) {
    console.error('Error deleting log:', error);
    return false;
  }
}

// Delete all conversations
export async function deleteAllConversations(): Promise<boolean> {
  try {
    const logs = await getLogs();
    const nonConversationLogs = logs.filter(log => log.type !== 'conversation');
    await saveAllLocalLogs(nonConversationLogs);
    return true;
  } catch (error) {
    console.error('Error deleting all conversations:', error);
    return false;
  }
}

// Sync conversation in background
export async function syncConversationInBackground(conversationId: string): Promise<boolean> {
  try {
    await syncLogsWithBackend();
    return true;
  } catch (error) {
    console.error('Error syncing conversation:', error);
    return false;
  }
}

// Save workout from conversation
export async function saveWorkoutFromConversation(conversationId: string, workout: WorkoutData): Promise<string | null> {
  try {
    const workoutId = `workout_${Date.now()}`;
    const timestamp = new Date().toISOString();
    
    const workoutLog: Log = {
      id: workoutId,
      type: 'workout',
      description: workout.title || 'Workout',
      timestamp,
      workoutData: workout
    };
    
    await saveLog(workoutLog);
    return workoutId;
  } catch (error) {
    console.error('Error saving workout from conversation:', error);
    return null;
  }
}

// Save meal from conversation
export async function saveMealFromConversation(conversationId: string, meal: MealData): Promise<string | null> {
  try {
    const mealId = `meal_${Date.now()}`;
    const timestamp = new Date().toISOString();
    
    const mealLog: Log = {
      id: mealId,
      type: 'meal',
      description: meal.title || 'Meal',
      timestamp,
      metrics: {
        meal
      }
    };
    
    await saveLog(mealLog);
    return mealId;
  } catch (error) {
    console.error('Error saving meal from conversation:', error);
    return null;
  }
}

// Get log by ID
export async function getLogById(logId: string): Promise<Log | null> {
  try {
    const logs = await getLogs();
    const log = logs.find(log => log.id === logId);
    return log || null;
  } catch (error) {
    console.error('Error getting log by ID:', error);
    return null;
  }
}

// Export types for other modules
export type { Log, LogType, WorkoutData, MealData, WorkoutExercise, Message };