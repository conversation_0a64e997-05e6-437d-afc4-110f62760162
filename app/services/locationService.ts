import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Storage keys
const LOCATION_PERMISSION_KEY = 'location_permission_status';
const LAST_KNOWN_LOCATION_KEY = 'last_known_location';
const LOCATION_TIMESTAMP_KEY = 'location_timestamp';
const LOCATION_PERMISSION_ASKED_KEY = 'location_permission_asked';

// Permission status types
export type PermissionStatus = 'granted' | 'denied' | 'undetermined' | 'blocked';

// Types
export interface LocationData {
  latitude: number;
  longitude: number;
  timestamp: string;
}

// Check if location permissions are granted
export async function checkLocationPermission(): Promise<boolean> {
  try {
    const { status } = await Location.getForegroundPermissionsAsync();

    // Store the current status
    await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, status);

    return status === 'granted';
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
}

// Get detailed permission status
export async function getLocationPermissionStatus(): Promise<PermissionStatus> {
  try {
    const { status } = await Location.getForegroundPermissionsAsync();

    // Store the current status
    await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, status);

    return status as PermissionStatus;
  } catch (error) {
    console.error('Error getting location permission status:', error);
    return 'undetermined';
  }
}

// Check if we've already asked for permission
export async function hasAskedForLocationPermission(): Promise<boolean> {
  try {
    const asked = await AsyncStorage.getItem(LOCATION_PERMISSION_ASKED_KEY);
    return asked === 'true';
  } catch (error) {
    console.error('Error checking if location permission was asked:', error);
    return false;
  }
}

// Request location permissions
export async function requestLocationPermission(): Promise<boolean> {
  try {
    console.log('Requesting location permission...');

    // Mark that we've asked for permission
    await AsyncStorage.setItem(LOCATION_PERMISSION_ASKED_KEY, 'true');

    const { status } = await Location.requestForegroundPermissionsAsync();
    const isGranted = status === 'granted';

    // Store permission status
    await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, status);

    return isGranted;
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
}

// Get current location
export async function getCurrentLocation(): Promise<LocationData | null> {
  try {
    // Check if permission is granted
    const hasPermission = await checkLocationPermission();
    if (!hasPermission) {
      console.log('Location permission not granted');
      return null;
    }

    console.log('Getting current location...');

    // Create a promise that will reject after a timeout
    const timeoutPromise = new Promise<null>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Location request timed out'));
      }, 15000); // 15 second timeout
    });

    // Get current location with higher accuracy
    const locationPromise = Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced, // Use balanced accuracy for better reliability
      mayShowUserSettingsDialog: true, // Show settings dialog if needed
    });

    // Race the location request against the timeout
    const location = await Promise.race([locationPromise, timeoutPromise]) as Location.LocationObject;

    console.log('Location obtained:', location.coords);

    const locationData: LocationData = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      timestamp: new Date().toISOString(),
    };

    // Save last known location
    await saveLastKnownLocation(locationData);

    return locationData;
  } catch (error) {
    console.error('Error getting current location:', error);

    // Check if location services are enabled
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        console.log('Location services are disabled on the device');
      }
    } catch (serviceError) {
      console.error('Error checking location services:', serviceError);
    }

    // Try to get last known location as fallback
    const lastLocation = await getLastKnownLocation();
    if (lastLocation) {
      console.log('Using last known location as fallback:', lastLocation);
    } else {
      console.log('No last known location available');
    }
    return lastLocation;
  }
}

// Save last known location
async function saveLastKnownLocation(location: LocationData): Promise<void> {
  try {
    await AsyncStorage.setItem(LAST_KNOWN_LOCATION_KEY, JSON.stringify(location));
    await AsyncStorage.setItem(LOCATION_TIMESTAMP_KEY, new Date().toISOString());
  } catch (error) {
    console.error('Error saving last known location:', error);
  }
}

// Get last known location
export async function getLastKnownLocation(): Promise<LocationData | null> {
  try {
    const locationJson = await AsyncStorage.getItem(LAST_KNOWN_LOCATION_KEY);
    if (!locationJson) {
      return null;
    }

    return JSON.parse(locationJson) as LocationData;
  } catch (error) {
    console.error('Error getting last known location:', error);
    return null;
  }
}

// Check if location is stale (older than 30 minutes)
export async function isLocationStale(): Promise<boolean> {
  try {
    const timestampStr = await AsyncStorage.getItem(LOCATION_TIMESTAMP_KEY);
    if (!timestampStr) {
      return true;
    }

    const timestamp = new Date(timestampStr).getTime();
    const now = new Date().getTime();
    const thirtyMinutesInMs = 30 * 60 * 1000;

    return now - timestamp > thirtyMinutesInMs;
  } catch (error) {
    console.error('Error checking if location is stale:', error);
    return true;
  }
}

// Check if location services are enabled on the device
export async function areLocationServicesEnabled(): Promise<boolean> {
  try {
    return await Location.hasServicesEnabledAsync();
  } catch (error) {
    console.error('Error checking if location services are enabled:', error);
    return false;
  }
}

// Get location with freshness check
export async function getLocationWithFreshnessCheck(forceRefresh = false): Promise<LocationData | null> {
  try {
    // Check if location permission is granted
    const hasPermission = await checkLocationPermission();
    if (!hasPermission) {
      console.log('Location permission not granted, cannot get location');
      return null;
    }

    // Check if location services are enabled
    const servicesEnabled = await areLocationServicesEnabled();
    if (!servicesEnabled) {
      console.log('Location services are disabled on the device');
      return null;
    }

    // If force refresh is requested, skip the cache check
    if (forceRefresh) {
      console.log('Force refreshing location data');
      return getCurrentLocation();
    }

    // Check if we have a recent location
    const isStale = await isLocationStale();
    if (!isStale) {
      const lastLocation = await getLastKnownLocation();
      if (lastLocation) {
        console.log('Using recent cached location:', lastLocation);
        return lastLocation;
      }
    } else {
      console.log('Cached location is stale, getting fresh location');
    }

    // Get fresh location
    return getCurrentLocation();
  } catch (error) {
    console.error('Error getting location with freshness check:', error);
    return null;
  }
}
