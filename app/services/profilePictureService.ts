import { getApiUrl } from './apiClient';
import { getAuthHeader } from './auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ProfilePictureUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload a profile picture to S3 via the backend API
 */
export async function uploadProfilePicture(imageUri: string): Promise<ProfilePictureUploadResult> {
  try {
    console.log('[ProfilePictureService] Starting upload for:', imageUri);

    // Get the API URL and auth headers
    const apiUrl = await getApiUrl();
    const authHeaders = await getAuthHeader();

    // Create form data for the upload
    const formData = new FormData();
    
    // Extract file extension from URI
    const fileExtension = imageUri.split('.').pop()?.toLowerCase() || 'jpg';
    const fileName = `profile_${Date.now()}.${fileExtension}`;
    
    // Add the image file to form data
    formData.append('image', {
      uri: imageUri,
      type: `image/${fileExtension === 'jpg' ? 'jpeg' : fileExtension}`,
      name: fileName,
    } as any);

    console.log('[ProfilePictureService] Uploading to:', `${apiUrl}/profile/picture`);

    // Upload to the backend
    const response = await fetch(`${apiUrl}/profile/picture`, {
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[ProfilePictureService] Upload failed:', response.status, errorText);
      throw new Error(`Upload failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log('[ProfilePictureService] Upload successful:', result);

    // Save the URL to local storage for quick access
    if (result.url) {
      await AsyncStorage.setItem('profilePictureUrl', result.url);
    }

    return {
      success: true,
      url: result.url,
    };

  } catch (error) {
    console.error('[ProfilePictureService] Upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

/**
 * Delete the current profile picture
 */
export async function deleteProfilePicture(): Promise<ProfilePictureUploadResult> {
  try {
    console.log('[ProfilePictureService] Deleting profile picture');

    // Get the API URL and auth headers
    const apiUrl = await getApiUrl();
    const authHeaders = await getAuthHeader();

    // Delete from the backend
    const response = await fetch(`${apiUrl}/profile/picture`, {
      method: 'DELETE',
      headers: authHeaders,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[ProfilePictureService] Delete failed:', response.status, errorText);
      throw new Error(`Delete failed: ${response.status} ${errorText}`);
    }

    console.log('[ProfilePictureService] Delete successful');

    // Remove from local storage
    await AsyncStorage.removeItem('profilePictureUrl');
    await AsyncStorage.removeItem('profilePicture');

    return {
      success: true,
    };

  } catch (error) {
    console.error('[ProfilePictureService] Delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
    };
  }
}

/**
 * Get the current profile picture URL
 */
export async function getProfilePictureUrl(): Promise<string | null> {
  try {
    // First try to get from local storage
    const localUrl = await AsyncStorage.getItem('profilePictureUrl');
    if (localUrl) {
      return localUrl;
    }

    // If not in local storage, try to fetch from backend
    const apiUrl = await getApiUrl();
    const authHeaders = await getAuthHeader();

    const response = await fetch(`${apiUrl}/profile`, {
      method: 'GET',
      headers: authHeaders,
    });

    if (response.ok) {
      const profile = await response.json();
      if (profile.profilePictureUrl) {
        // Save to local storage for future use
        await AsyncStorage.setItem('profilePictureUrl', profile.profilePictureUrl);
        return profile.profilePictureUrl;
      }
    }

    return null;
  } catch (error) {
    console.error('[ProfilePictureService] Error getting profile picture URL:', error);
    return null;
  }
}

/**
 * Check if a profile picture exists
 */
export async function hasProfilePicture(): Promise<boolean> {
  const url = await getProfilePictureUrl();
  return !!url;
}

export default {
  uploadProfilePicture,
  deleteProfilePicture,
  getProfilePictureUrl,
  hasProfilePicture,
};
