import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { api } from './apiClient';
import * as SecureStore from 'expo-secure-store';

export interface UserProfile {
  userId?: string;
  name: string;
  birthday: string;
  weight: string;
  height: string;
  fitnessGoal: string;
  createdAt?: string;
  updatedAt?: string;
}

const API_URL = process.env.EXPO_PUBLIC_API_URL;

// Local storage key
const PROFILE_STORAGE_KEY = '@user_profile';

// Direct API call using fetch instead of axios to bypass any interceptor issues
async function directApiCall(endpoint: string, method: 'GET'|'POST', data?: any, token?: string): Promise<any> {
  console.log(`DIRECT API CALL to ${endpoint} with token: ${token ? token.substring(0,15) + '...' : 'none'}`);

  try {
    const url = `${API_URL}${endpoint}`;
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...(token ? {'Authorization': `Bearer ${token}`} : {})
      },
      body: data ? JSON.stringify(data) : undefined
    };

    console.log(`Sending ${method} request to ${url}`);
    const response = await fetch(url, options);
    console.log(`Direct API response: ${response.status} ${response.statusText}`);

    // Log response headers for debugging
    const headers: Record<string, string> = {};
    response.headers.forEach((value: string, key: string) => {
      headers[key] = value;
    });
    console.log('Response headers:', headers);

    if (response.status === 401) {
      // For 401 errors, try to refresh the token and retry once
      console.log('Received 401 unauthorized, attempting token refresh');
      const tokens = await getTokensFromStorage();
      if (tokens?.refreshToken) {
        const newToken = await refreshTokenDirectly(tokens.refreshToken);
        if (newToken) {
          console.log('Token refreshed, retrying request with new token');
          // Retry with new token
          const retryOptions = {
            ...options,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${newToken}`
            }
          };
          const retryResponse = await fetch(url, retryOptions);
          console.log(`Retry response: ${retryResponse.status} ${retryResponse.statusText}`);

          if (retryResponse.ok) {
            const retryData = await retryResponse.text();
            try {
              return retryData ? JSON.parse(retryData) : null;
            } catch {
              return retryData || null;
            }
          }
        }
      }
    }

    // Normal response handling
    if (!response.ok) {
      console.error(`API call failed with status: ${response.status}`);

      // For 500 errors, return null instead of throwing to prevent app crashes
      if (response.status === 500) {
        console.warn('Server returned 500 error, returning null instead of throwing');
        return null;
      }

      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.text();
    if (!responseData) return null;

    try {
      return JSON.parse(responseData);
    } catch {
      return responseData;
    }
  } catch (error) {
    console.error('Direct API call failed:', error);
    throw error;
  }
}

// Save profile to local storage
export async function saveProfileToStorage(profile: UserProfile): Promise<void> {
  try {
    await AsyncStorage.setItem(PROFILE_STORAGE_KEY, JSON.stringify(profile));
  } catch (error) {
    console.error('Error saving profile to storage:', error);
    throw error;
  }
}

// Get profile from local storage
export async function getProfileFromStorage(): Promise<UserProfile | null> {
  try {
    const profileJson = await AsyncStorage.getItem(PROFILE_STORAGE_KEY);
    return profileJson ? JSON.parse(profileJson) : null;
  } catch (error) {
    console.error('Error getting profile from storage:', error);
    return null;
  }
}

// Helper function to directly get tokens from storage without circular imports
async function getTokensFromStorage(): Promise<{accessToken?: string, refreshToken?: string} | null> {
  try {
    // First check split storage (preferred method)
    const accessToken = await SecureStore.getItemAsync('auth_token_access');
    const refreshToken = await SecureStore.getItemAsync('auth_token_refresh');

    if (accessToken && refreshToken) {
      return { accessToken, refreshToken };
    }

    // Check fallback storage
    const fallbackTokensJson = await AsyncStorage.getItem('auth_tokens_fallback');
    if (fallbackTokensJson) {
      const tokens = JSON.parse(fallbackTokensJson);
      if (tokens.accessToken && tokens.refreshToken) {
        return tokens;
      }
    }

    return null;
  } catch (error) {
    console.error('Error directly retrieving tokens:', error);
    return null;
  }
}

// Direct token refresh that avoids circular imports
async function refreshTokenDirectly(refreshTokenStr: string): Promise<string | null> {
  try {
    console.log('Starting direct token refresh');
    // Use fetch directly to avoid any interceptor issues
    const response = await fetch(`${API_URL}/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'refreshToken',
        refreshToken: refreshTokenStr
      })
    });

    if (!response.ok) {
      console.error('Token refresh failed with status:', response.status);
      return null;
    }

    const data = await response.json();
    console.log('Token refresh response received');

    if (data?.tokens?.accessToken) {
      console.log('Valid tokens received from refresh');
      // Store new tokens in secure storage directly
      await SecureStore.setItemAsync('auth_token_access', data.tokens.accessToken);
      await SecureStore.setItemAsync('auth_token_id', data.tokens.idToken);
      if (data.tokens.refreshToken) {
        await SecureStore.setItemAsync('auth_token_refresh', data.tokens.refreshToken);
      }

      // Also update fallback storage
      await AsyncStorage.setItem('auth_tokens_fallback', JSON.stringify(data.tokens));

      console.log('Tokens refreshed and stored successfully');
      return data.tokens.accessToken;
    }

    return null;
  } catch (error) {
    console.error('Direct token refresh failed:', error);
    return null;
  }
}

// Save profile to server - completely rewritten to use direct API calls
export async function saveProfile(profile: UserProfile, accessToken: string, idToken?: string): Promise<UserProfile> {
  try {
    console.log('Starting profile save process with data:', {
      name: profile.name,
      birthday: profile.birthday,
      fitnessGoal: profile.fitnessGoal,
      weight: profile.weight,
      height: profile.height,
      hasToken: !!accessToken
    });

    // First save locally for immediate feedback
    await saveProfileToStorage(profile);
    console.log('Profile saved to local storage successfully');

    // Get tokens if we don't have idToken yet (API Gateway needs idToken)
    let tokenToUse = idToken;
    if (!tokenToUse) {
      const tokens = await getTokensFromStorage();
      tokenToUse = tokens?.idToken || accessToken; // Fallback to accessToken if necessary
    }

    // Try direct API call to bypass any axios interceptor issues
    try {
      console.log('Calling API to save profile to server');
      const serverProfile = await directApiCall('/profile', 'POST', profile, tokenToUse);

      if (serverProfile && typeof serverProfile === 'object') {
        console.log('Profile saved to server successfully:', {
          userId: serverProfile.userId,
          name: serverProfile.name
        });

        // Validate the returned profile has the basic fields
        if (serverProfile.userId && serverProfile.name) {
          // Update local storage with server data (includes userId, timestamps)
          await saveProfileToStorage(serverProfile);
          return serverProfile;
        } else {
          console.warn('Server returned incomplete profile data:', serverProfile);
        }
      } else {
        console.warn('Server returned invalid profile data:', serverProfile);
      }
    } catch (apiError) {
      console.error('Failed to save profile to server:', apiError);
      throw apiError; // Rethrow to allow caller to handle
    }

    // Return the local profile if server save failed
    return profile;
  } catch (error) {
    console.error('Error in profile save process:', error);
    throw error; // Rethrow to allow caller to handle
  }
}

// Get profile from server - completely rewritten to use direct API calls
export async function getProfile(accessToken: string, idToken?: string): Promise<UserProfile | null> {
  try {
    console.log('getProfile called with token provided:', !!accessToken);

    // Try to get from local storage first for immediate UI rendering
    const localProfile = await getProfileFromStorage();
    console.log('Local profile from storage:', localProfile ? 'Found' : 'Not found');

    if (!accessToken) {
      console.warn('No access token provided for getProfile, returning local profile only');
      return localProfile;
    }

    // Get tokens if we don't have idToken yet (API Gateway needs idToken)
    let tokenToUse = idToken;
    if (!tokenToUse) {
      const tokens = await getTokensFromStorage();
      tokenToUse = tokens?.idToken || accessToken; // Fallback to accessToken if necessary
    }

    // Then try to get fresh data from server
    try {
      console.log('Fetching profile from server');
      const serverProfile = await directApiCall('/profile', 'GET', null, tokenToUse);

      if (serverProfile && typeof serverProfile === 'object') {
        console.log('Profile retrieved from server successfully:', {
          userId: serverProfile.userId,
          name: serverProfile.name
        });

        // Validate the profile structure
        if (serverProfile.userId && serverProfile.name) {
          // Update local storage
          await saveProfileToStorage(serverProfile);
          return serverProfile;
        } else {
          console.warn('Server returned incomplete profile data');
        }
      } else {
        console.log('No profile found on server or invalid response');
      }
    } catch (apiError: any) {
      console.error('Failed to get profile from server:', apiError);

      // If server explicitly returned 404, profile doesn't exist
      if (apiError.status === 404 || (apiError.message && apiError.message.includes('404'))) {
        console.log('Profile not found on server (404)');
        // Clear any existing local profile to ensure onboarding is shown
        try {
          await AsyncStorage.removeItem(PROFILE_STORAGE_KEY);
          console.log('Cleared local profile after 404 from server');
        } catch (clearError) {
          console.error('Error clearing local profile:', clearError);
        }
        return null;
      }
    }

    // Return local profile if server fetch failed
    console.log('Returning local profile as fallback');
    return localProfile;
  } catch (error) {
    console.error('Error in getProfile:', error);
    try {
      const localProfile = await getProfileFromStorage();
      return localProfile;
    } catch (storageError) {
      console.error('Failed to get local profile after server error:', storageError);
      return null;
    }
  }
}

// Check if user has a profile
export async function hasProfile(): Promise<boolean> {
  const profile = await getProfileFromStorage();
  return !!profile && !!profile.name;
}

// Get the user ID from the profile
export async function getUserId(): Promise<string | null> {
  try {
    const profile = await getProfileFromStorage();
    if (profile && profile.userId) {
      return profile.userId;
    }

    // If no userId in profile, try to get from tokens
    const tokens = await getTokensFromStorage();
    if (tokens?.accessToken) {
      try {
        // Try to decode the JWT to get the user ID
        const base64Url = tokens.accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

        // Add padding if needed
        const pad = base64.length % 4;
        const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;

        // Use a React Native compatible base64 decoding approach
        const decodedData = decodeBase64(paddedBase64);
        const payload = JSON.parse(decodedData);

        // Helper function to decode base64 in React Native
        function decodeBase64(str: string): string {
          // This is a simple base64 decoder that works in React Native
          const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
          let output = '';

          str = String(str).replace(/=+$/, '');

          if (str.length % 4 === 1) {
            throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");
          }

          for (
            let bc = 0, bs = 0, buffer, i = 0;
            (buffer = str.charAt(i++));
            ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer, bc++ % 4)
              ? (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6))))
              : 0
          ) {
            buffer = chars.indexOf(buffer);
          }

          return output;
        }
        if (payload.sub) {
          return payload.sub;
        }
      } catch (decodeError) {
        console.error('Error decoding JWT:', decodeError);
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting user ID:', error);
    return null;
  }
}