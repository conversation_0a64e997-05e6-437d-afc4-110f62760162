import AsyncStorage from '@react-native-async-storage/async-storage';
import { api, makeRequestWithRetry } from './apiClient';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

// Cache TTL in milliseconds
const DEFAULT_CACHE_TTL = 10 * 60 * 1000; // 10 minutes - increased from 5
const LONG_CACHE_TTL = 60 * 60 * 1000; // 60 minutes - increased from 30

// Cache prefix for storage keys
const CACHE_PREFIX = 'api_cache:';

// Memory cache to avoid AsyncStorage calls for frequently accessed data
const memoryCache: Record<string, { data: any, expires: number }> = {};

// Request queue to prevent multiple simultaneous requests to the same endpoint
const requestQueue: Record<string, Promise<any>> = {};

// Cache storage
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

/**
 * Generate a cache key from request config
 */
function generateCacheKey(config: AxiosRequestConfig): string {
  const { url, method = 'get', params, data } = config;
  // Simplified key generation to avoid excessive JSON.stringify for large payloads
  const paramsKey = params ? JSON.stringify(params) : '';
  const dataKey = data && typeof data === 'object' ? 
    Object.keys(data).sort().join(',') : 
    (data ? 'has_data' : '');
  
  return `${method.toLowerCase()}:${url}:${paramsKey}:${dataKey}`;
}

/**
 * Save data to cache
 */
async function saveToCache(key: string, data: any, ttl: number): Promise<void> {
  try {
    // Save to memory cache first for faster access
    memoryCache[key] = {
      data,
      expires: Date.now() + ttl
    };
    
    // Then save to persistent storage
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      ttl
    };
    
    // Use a separate thread to save to AsyncStorage to avoid blocking UI
    setTimeout(async () => {
      try {
        await AsyncStorage.setItem(`${CACHE_PREFIX}${key}`, JSON.stringify(entry));
      } catch (error) {
        console.warn('Error saving to API cache in background:', error);
      }
    }, 0);
  } catch (error) {
    console.warn('Error saving to API cache:', error);
  }
}

/**
 * Get data from cache
 */
async function getFromCache(key: string): Promise<any | null> {
  try {
    // Check memory cache first for faster access
    const memoryCacheEntry = memoryCache[key];
    if (memoryCacheEntry && Date.now() < memoryCacheEntry.expires) {
      return memoryCacheEntry.data;
    }
    
    // If not in memory cache or expired, check AsyncStorage
    const entryJson = await AsyncStorage.getItem(`${CACHE_PREFIX}${key}`);
    if (!entryJson) return null;

    const entry: CacheEntry = JSON.parse(entryJson);
    const now = Date.now();

    // Check if cache is still valid
    if (now - entry.timestamp < entry.ttl) {
      // Store in memory cache for future use
      memoryCache[key] = {
        data: entry.data,
        expires: entry.timestamp + entry.ttl
      };
      return entry.data;
    }

    // Cache expired
    return null;
  } catch (error) {
    console.warn('Error reading from API cache:', error);
    return null;
  }
}

/**
 * Clear specific cache entry
 */
export async function clearCacheEntry(config: AxiosRequestConfig): Promise<void> {
  const key = generateCacheKey(config);
  try {
    // Clear from memory cache first
    delete memoryCache[key];
    
    // Then clear from AsyncStorage
    await AsyncStorage.removeItem(`${CACHE_PREFIX}${key}`);
  } catch (error) {
    console.warn('Error clearing API cache entry:', error);
  }
}

/**
 * Clear all API cache
 */
export async function clearAllCache(): Promise<void> {
  try {
    // Clear memory cache
    Object.keys(memoryCache).forEach(key => {
      delete memoryCache[key];
    });
    
    // Clear AsyncStorage cache
    const keys = await AsyncStorage.getAllKeys();
    const cacheKeys = keys.filter(key => key.startsWith(CACHE_PREFIX));
    if (cacheKeys.length > 0) {
      await AsyncStorage.multiRemove(cacheKeys);
    }
  } catch (error) {
    console.error('Error clearing API cache:', error);
  }
}

/**
 * Make an API request with caching, queuing, and improved error handling
 */
export async function makeRequest<T = any>(
  config: AxiosRequestConfig,
  options: {
    useCache?: boolean;
    cacheTTL?: number;
    retries?: number;
    retryDelay?: number;
    fallbackData?: T;
    logTag?: string;
    forceRefresh?: boolean;
    delayMs?: number;
    customBaseURL?: string;
    useAppConfig?: boolean;
  } = {}
): Promise<T> {
  // Get API client configuration if requested
  let apiConfig = { timeout: 20000, retries: 2 }; // Reduced defaults
  if (options.useAppConfig !== false) {
    try {
      const { getApiClientConfig } = require('./apiClient');
      apiConfig = getApiClientConfig();
    } catch (error) {
      console.error(`${options.logTag || '[API]'} Error getting API client configuration:`, error);
    }
  }

  const {
    useCache = true,
    cacheTTL = DEFAULT_CACHE_TTL,
    retries = apiConfig.retries,
    retryDelay = 1000,
    fallbackData,
    logTag = '[API Request]',
    forceRefresh = false,
    delayMs = 0
  } = options;

  // Use API client configuration for timeout if not set in config
  if (!config.timeout && apiConfig.timeout) {
    config.timeout = apiConfig.timeout;
  }

  // Generate cache key
  const cacheKey = generateCacheKey(config);

  // Check if there's already a request in progress for this endpoint
  if (cacheKey in requestQueue) {
    try {
      return await requestQueue[cacheKey];
    } catch (error) {
      // If the queued request fails, we'll try again
      delete requestQueue[cacheKey];
    }
  }

  // Check cache first if enabled and not forcing refresh
  if (useCache && !forceRefresh) {
    const cachedData = await getFromCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  // Add artificial delay if specified
  if (delayMs > 0) {
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }

  // Create the request promise and add it to the queue
  const requestPromise = (async () => {
    try {
      // Handle custom base URL if provided
      const requestConfig = { ...config };
      if (options.customBaseURL !== undefined) {
        // If customBaseURL is an empty string, use the URL as is (for external APIs)
        if (options.customBaseURL === '') {
          // For external APIs, we need to use axios directly
          const response = await axios({
            ...requestConfig,
            url: requestConfig.url,
            method: requestConfig.method || 'GET'
          });

          // Cache the successful response
          if (useCache && response.data) {
            await saveToCache(cacheKey, response.data, cacheTTL);
          }

          return response.data;
        } else {
          // Use the provided custom base URL
          requestConfig.baseURL = options.customBaseURL;
        }
      }

      // Make the request with retries
      const response = await makeRequestWithRetry<T>(requestConfig, {
        maxRetries: retries,
        retryDelay,
        logPrefix: logTag,
        useExponentialBackoff: true,
        jitter: true
      });

      // Cache the successful response
      if (useCache && response.data) {
        await saveToCache(cacheKey, response.data, cacheTTL);
      }

      return response.data;
    } catch (error: any) {
      // Check for network errors
      const isNetworkError = error.message && (
        error.message.includes('Network Error') ||
        error.message.includes('timeout') ||
        error.message.includes('socket hang up')
      );

      // Only log severe errors
      if (isNetworkError || (error.response?.status && error.response.status >= 500)) {
        console.error(`${logTag} Error for ${config.url}: ${error.message}`);
      }

      // If fallback data is provided, use it
      if (fallbackData !== undefined) {
        return fallbackData;
      }

      // Try to get default fallback data
      try {
        // Simplified error fallback handling
        const { getDefaultFallbackData } = require('./apiRequestManager');
        let userId = undefined;
        try {
          const { getUserId } = require('./authService');
          userId = await getUserId();
        } catch (userIdError) {
          // Ignore this error
        }
        
        const fallback = getDefaultFallbackData(config.url || '', userId);
        if (fallback !== undefined) {
          return fallback as T;
        }
      } catch (fallbackError) {
        // Ignore fallback errors
      }

      // Rethrow the original error
      throw error;
    } finally {
      // Clean up the request queue
      delete requestQueue[cacheKey];
    }
  })();

  // Add the request to the queue
  requestQueue[cacheKey] = requestPromise;

  return requestPromise;
}

/**
 * Make a GET request with caching and improved error handling
 */
export async function getRequest<T = any>(
  url: string,
  params?: any,
  options?: {
    useCache?: boolean;
    cacheTTL?: number;
    retries?: number;
    retryDelay?: number;
    fallbackData?: T;
    logTag?: string;
    forceRefresh?: boolean;
    delayMs?: number;
    customBaseURL?: string;
    method?: string;
    useAppConfig?: boolean;
  }
): Promise<T> {
  return makeRequest<T>(
    { url, method: options?.method || 'GET', params },
    { useAppConfig: true, ...options }
  );
}

/**
 * Make a POST request with caching and improved error handling
 */
export async function postRequest<T = any>(
  url: string,
  data?: any,
  options?: {
    useCache?: boolean;
    cacheTTL?: number;
    retries?: number;
    retryDelay?: number;
    fallbackData?: T;
    logTag?: string;
    forceRefresh?: boolean;
    delayMs?: number;
    customBaseURL?: string;
    useAppConfig?: boolean;
  }
): Promise<T> {
  return makeRequest<T>(
    { url, method: 'POST', data },
    { useAppConfig: true, ...options }
  );
}

/**
 * Make a PUT request with caching and improved error handling
 */
export async function putRequest<T = any>(
  url: string,
  data?: any,
  options?: {
    useCache?: boolean;
    cacheTTL?: number;
    retries?: number;
    retryDelay?: number;
    fallbackData?: T;
    logTag?: string;
    forceRefresh?: boolean;
    delayMs?: number;
    customBaseURL?: string;
    useAppConfig?: boolean;
  }
): Promise<T> {
  return makeRequest<T>(
    { url, method: 'PUT', data },
    { useAppConfig: true, ...options }
  );
}

/**
 * Make a DELETE request with caching and improved error handling
 */
export async function deleteRequest<T = any>(
  url: string,
  options?: {
    useCache?: boolean;
    cacheTTL?: number;
    retries?: number;
    retryDelay?: number;
    fallbackData?: T;
    logTag?: string;
    forceRefresh?: boolean;
    delayMs?: number;
    customBaseURL?: string;
    useAppConfig?: boolean;
  }
): Promise<T> {
  return makeRequest<T>(
    { url, method: 'DELETE' },
    { useAppConfig: true, ...options }
  );
}

/**
 * Get default fallback data for common endpoints
 */
export function getDefaultFallbackData(url: string, userId?: string): any {
  const now = new Date().toISOString();

  // User stats fallback
  if (url.includes('/user/stats')) {
    return {
      userId: userId || 'unknown',
      workouts: {
        total: 0,
        thisWeek: 0,
        thisMonth: 0,
        streak: 0,
      },
      nutrition: {
        mealsLogged: 0,
        avgCalories: 0,
        avgProtein: 0,
        streakDays: 0,
      },
      weight: {},
      activity: {
        totalDaysActive: 0,
        currentStreak: 0,
        longestStreak: 0,
      },
      updatedAt: now,
    };
  }

  // Achievements fallback
  if (url.includes('/achievements')) {
    return [
      {
        id: 'first-workout',
        userId: userId || 'unknown',
        title: 'First Workout',
        description: 'Complete your first workout',
        category: 'workout',
        icon: 'fitness-outline',
        status: 'locked',
        progress: 0,
        currentValue: 0,
        targetValue: 1,
        achievementId: 'first-workout',
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'workout-streak-3',
        userId: userId || 'unknown',
        title: 'Workout Streak: 3 Days',
        description: 'Complete workouts for 3 consecutive days',
        category: 'streak',
        icon: 'flame-outline',
        status: 'locked',
        progress: 0,
        currentValue: 0,
        targetValue: 3,
        achievementId: 'workout-streak-3',
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'first-chat',
        userId: userId || 'unknown',
        title: 'First Conversation',
        description: 'Have your first conversation with Lotus',
        category: 'general',
        icon: 'chatbubbles-outline',
        status: 'locked',
        progress: 0,
        currentValue: 0,
        targetValue: 1,
        achievementId: 'first-chat',
        createdAt: now,
        updatedAt: now
      }
    ];
  }

  // Default empty response
  return {};
}
