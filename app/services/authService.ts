import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStoredTokens } from './auth';
import { getProfileFromStorage } from './profile';

/**
 * Get the user ID from various sources
 * 
 * @returns The user ID or null if not found
 */
export async function getUserId(): Promise<string | null> {
  try {
    console.log('[getUserId] Attempting to get user ID from multiple sources');

    // Try to get from direct storage first
    const userId = await AsyncStorage.getItem('user_id');
    if (userId) {
      console.log('[getUserId] Found user ID in direct storage');
      return userId;
    }

    // Try to get from user profile
    try {
      const userProfile = await getProfileFromStorage();
      if (userProfile && userProfile.userId) {
        console.log('[getUserId] Found user ID in profile:', userProfile.userId);
        // Save it for future use
        await AsyncStorage.setItem('user_id', userProfile.userId);
        return userProfile.userId;
      }
    } catch (profileError) {
      console.error('[getUserId] Error getting user ID from profile:', profileError);
    }

    // Try to get from tokens
    try {
      const tokens = await getStoredTokens();
      if (tokens && (tokens.accessToken || tokens.idToken)) {
        const tokenToUse = tokens.idToken || tokens.accessToken;
        
        if (tokenToUse) {
          try {
            // Try to decode the JWT token to get the user ID
            const tokenParts = tokenToUse.split('.');
            if (tokenParts.length >= 2) {
              // Base64 decode the payload
              const base64 = tokenParts[1].replace(/-/g, '+').replace(/_/g, '/');
              
              // Add padding if needed
              const pad = base64.length % 4;
              const paddedBase64 = pad ? base64 + '='.repeat(4 - pad) : base64;
              
              // Decode base64
              function decodeBase64(str: string): string {
                try {
                  return atob(str);
                } catch (e) {
                  // Fallback for React Native
                  return require('buffer').Buffer.from(str, 'base64').toString('binary');
                }
              }
              
              const decodedData = decodeBase64(paddedBase64);
              const payload = JSON.parse(decodedData);
              
              // JWT could use different fields for the user ID
              const extractedId = payload.sub || payload.userId || payload.user_id || payload.cognito_id;
              if (extractedId) {
                console.log('[getUserId] Extracted user ID from token:', extractedId);
                // Save it for future use
                await AsyncStorage.setItem('user_id', extractedId);
                return extractedId;
              }
            }
          } catch (decodeError) {
            console.warn('[getUserId] Error decoding token payload:', decodeError);
          }
        }
      }
    } catch (tokenError) {
      console.error('[getUserId] Error extracting user ID from token:', tokenError);
    }

    // If we still don't have a user ID, generate a temporary one
    const tempUserId = `temp_user_${Date.now()}`;
    console.log('[getUserId] No user ID found, using temporary ID:', tempUserId);
    await AsyncStorage.setItem('user_id', tempUserId);
    return tempUserId;
  } catch (error) {
    console.error('[getUserId] Error getting user ID:', error);
    // Return a fallback ID in case of error
    return `fallback_user_${Date.now()}`;
  }
}

/**
 * Check if the user is authenticated
 * 
 * @returns True if the user is authenticated, false otherwise
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const tokens = await getStoredTokens();
    return !!(tokens && (tokens.accessToken || tokens.idToken));
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
}

/**
 * Get the user profile service
 * This is used by the AppInitializer
 */
export async function getUserProfile() {
  try {
    return await getProfileFromStorage();
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

/**
 * Get the profile service
 * This is used by the AppInitializer
 */
export const profileService = {
  getUserProfile
};
