import AsyncStorage from '@react-native-async-storage/async-storage';

// Interface for user context preferences
export interface ContextPreferences {
  captureInjuries: boolean;
  captureDietaryRestrictions: boolean;
  captureMoodInfo: boolean;
  captureActivityData: boolean;
  captureConversationHistory: boolean;
  allowContextOverride: boolean;  // Important: Allows Lotus to overwrite context
}

// Default preferences - all enabled by default
const DEFAULT_PREFERENCES: ContextPreferences = {
  captureInjuries: true,
  captureDietaryRestrictions: true, 
  captureMoodInfo: true,
  captureActivityData: true,
  captureConversationHistory: true,
  allowContextOverride: true
};

// Keys for storage
const STORAGE_KEY = 'user_context_preferences';
const USER_INJURIES_KEY = 'user_injuries';
const USER_DIETARY_KEY = 'user_dietary_restrictions';
const CONVERSATION_HISTORY_KEY = 'conversation_history_tracking';

/**
 * Get user context preferences
 */
export const getContextPreferences = async (): Promise<ContextPreferences> => {
  try {
    const prefJson = await AsyncStorage.getItem(STORAGE_KEY);
    if (!prefJson) {
      return DEFAULT_PREFERENCES;
    }
    
    // Merge with defaults in case new preferences were added
    const savedPrefs = JSON.parse(prefJson);
    return { ...DEFAULT_PREFERENCES, ...savedPrefs };
  } catch (error) {
    console.error('Error reading context preferences:', error);
    return DEFAULT_PREFERENCES;
  }
};

/**
 * Save user context preferences
 */
export const saveContextPreferences = async (preferences: ContextPreferences): Promise<boolean> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
    return true;
  } catch (error) {
    console.error('Error saving context preferences:', error);
    return false;
  }
};

/**
 * Save user injuries
 */
export const saveUserInjuries = async (injuries: string[]): Promise<boolean> => {
  try {
    await AsyncStorage.setItem(USER_INJURIES_KEY, JSON.stringify(injuries));
    return true;
  } catch (error) {
    console.error('Error saving user injuries:', error);
    return false;
  }
};

/**
 * Get user injuries
 */
export const getUserInjuries = async (): Promise<string[]> => {
  try {
    const injuriesJson = await AsyncStorage.getItem(USER_INJURIES_KEY);
    if (!injuriesJson) {
      return [];
    }
    return JSON.parse(injuriesJson);
  } catch (error) {
    console.error('Error reading user injuries:', error);
    return [];
  }
};

/**
 * Save user dietary restrictions
 */
export const saveUserDietaryRestrictions = async (restrictions: string[]): Promise<boolean> => {
  try {
    await AsyncStorage.setItem(USER_DIETARY_KEY, JSON.stringify(restrictions));
    return true;
  } catch (error) {
    console.error('Error saving dietary restrictions:', error);
    return false;
  }
};

/**
 * Get user dietary restrictions
 */
export const getUserDietaryRestrictions = async (): Promise<string[]> => {
  try {
    const restrictionsJson = await AsyncStorage.getItem(USER_DIETARY_KEY);
    if (!restrictionsJson) {
      return [];
    }
    return JSON.parse(restrictionsJson);
  } catch (error) {
    console.error('Error reading dietary restrictions:', error);
    return [];
  }
};

/**
 * Save conversation history tracking
 */
export const saveConversationHistory = async (history: Record<string, any>): Promise<boolean> => {
  try {
    await AsyncStorage.setItem(CONVERSATION_HISTORY_KEY, JSON.stringify(history));
    return true;
  } catch (error) {
    console.error('Error saving conversation history:', error);
    return false;
  }
};

/**
 * Get conversation history tracking
 */
export const getConversationHistory = async (): Promise<Record<string, any>> => {
  try {
    const historyJson = await AsyncStorage.getItem(CONVERSATION_HISTORY_KEY);
    if (!historyJson) {
      return {};
    }
    return JSON.parse(historyJson);
  } catch (error) {
    console.error('Error reading conversation history:', error);
    return {};
  }
};

/**
 * Clear all context data
 */
export const clearAllContextData = async (): Promise<boolean> => {
  try {
    await AsyncStorage.multiRemove([
      USER_INJURIES_KEY,
      USER_DIETARY_KEY,
      CONVERSATION_HISTORY_KEY
    ]);
    return true;
  } catch (error) {
    console.error('Error clearing context data:', error);
    return false;
  }
};
