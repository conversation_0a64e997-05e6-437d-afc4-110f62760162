/**
 * Social Sharing Service - Handles sharing fitness achievements and progress
 * 
 * Features:
 * - Share workout achievements
 * - Share progress photos with before/after comparisons
 * - Share streak milestones
 * - Share nutrition goals achieved
 * - Generate shareable images with app branding
 * - Multiple platform support (Instagram, Twitter, Facebook, etc.)
 */

import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { captureRef } from 'react-native-view-shot';
import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export enum ShareType {
  WORKOUT_ACHIEVEMENT = 'workout_achievement',
  STREAK_MILESTONE = 'streak_milestone',
  WEIGHT_PROGRESS = 'weight_progress',
  NUTRITION_GOAL = 'nutrition_goal',
  PERSONAL_RECORD = 'personal_record',
  TRANSFORMATION = 'transformation',
  DAILY_SUMMARY = 'daily_summary'
}

export interface ShareableContent {
  type: ShareType;
  title: string;
  description: string;
  data: Record<string, any>;
  imageUri?: string;
  hashtags?: string[];
  mentions?: string[];
}

export interface ShareOptions {
  includeAppBranding: boolean;
  includeStats: boolean;
  includeMotivationalQuote: boolean;
  platform?: 'instagram' | 'twitter' | 'facebook' | 'general';
  customMessage?: string;
}

export interface ShareTemplate {
  type: ShareType;
  backgroundImage?: string;
  layout: 'card' | 'story' | 'post';
  colors: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
  };
  fonts: {
    title: string;
    body: string;
    caption: string;
  };
}

const SHARE_TEMPLATES_KEY = '@share_templates';
const SHARE_HISTORY_KEY = '@share_history';

class SocialSharingService {
  private templates: Map<ShareType, ShareTemplate> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Share content to social platforms
   */
  async shareContent(
    content: ShareableContent, 
    options: ShareOptions = { includeAppBranding: true, includeStats: true, includeMotivationalQuote: false }
  ): Promise<boolean> {
    try {
      // Generate shareable image if needed
      let shareUri = content.imageUri;
      if (!shareUri) {
        shareUri = await this.generateShareableImage(content, options);
      }

      // Prepare share message
      const message = this.generateShareMessage(content, options);

      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert('Sharing not available', 'Sharing is not available on this device');
        return false;
      }

      // Share the content
      await Sharing.shareAsync(shareUri, {
        mimeType: 'image/png',
        dialogTitle: content.title,
        UTI: 'public.png'
      });

      // Record share in history
      await this.recordShare(content, options);

      console.log('[SocialSharing] Content shared successfully');
      return true;

    } catch (error) {
      console.error('[SocialSharing] Error sharing content:', error);
      Alert.alert('Share Failed', 'Unable to share content. Please try again.');
      return false;
    }
  }

  /**
   * Generate shareable image from content
   */
  async generateShareableImage(
    content: ShareableContent, 
    options: ShareOptions
  ): Promise<string> {
    try {
      // This would typically use a canvas library or image generation service
      // For now, we'll create a simple text-based image
      const template = this.getTemplate(content.type);
      const imageData = await this.createImageFromTemplate(content, template, options);
      
      // Save to temporary file
      const fileName = `share_${Date.now()}.png`;
      const fileUri = `${FileSystem.cacheDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(fileUri, imageData, {
        encoding: FileSystem.EncodingType.Base64
      });

      return fileUri;

    } catch (error) {
      console.error('[SocialSharing] Error generating image:', error);
      throw new Error('Failed to generate shareable image');
    }
  }

  /**
   * Capture screenshot of a React component
   */
  async captureComponentAsImage(viewRef: any): Promise<string> {
    try {
      const uri = await captureRef(viewRef, {
        format: 'png',
        quality: 0.8,
        result: 'tmpfile'
      });

      return uri;
    } catch (error) {
      console.error('[SocialSharing] Error capturing component:', error);
      throw new Error('Failed to capture component');
    }
  }

  /**
   * Generate pre-made shareable content for common achievements
   */
  async generateWorkoutAchievement(workoutData: {
    type: string;
    duration: number;
    caloriesBurned?: number;
    personalRecord?: boolean;
    exercises?: number;
  }): Promise<ShareableContent> {
    const title = workoutData.personalRecord ? 
      '🏆 New Personal Record!' : 
      '💪 Workout Complete!';

    const description = `Just crushed a ${workoutData.duration} minute ${workoutData.type} workout${
      workoutData.caloriesBurned ? ` and burned ${workoutData.caloriesBurned} calories` : ''
    }!`;

    return {
      type: ShareType.WORKOUT_ACHIEVEMENT,
      title,
      description,
      data: workoutData,
      hashtags: ['#LotusApp', '#FitnessJourney', '#WorkoutComplete', '#HealthyLifestyle'],
      mentions: []
    };
  }

  /**
   * Generate streak milestone content
   */
  async generateStreakMilestone(streakData: {
    type: string;
    days: number;
    category: string;
  }): Promise<ShareableContent> {
    const title = `🔥 ${streakData.days}-Day ${streakData.category} Streak!`;
    const description = `Consistency pays off! I've maintained my ${streakData.category.toLowerCase()} habit for ${streakData.days} consecutive days.`;

    return {
      type: ShareType.STREAK_MILESTONE,
      title,
      description,
      data: streakData,
      hashtags: ['#LotusApp', '#StreakGoals', '#Consistency', '#HealthyHabits', `#${streakData.days}DayStreak`],
      mentions: []
    };
  }

  /**
   * Generate weight progress content
   */
  async generateWeightProgress(progressData: {
    startWeight: number;
    currentWeight: number;
    goalWeight: number;
    timeframe: string;
    unit: string;
  }): Promise<ShareableContent> {
    const weightChange = progressData.startWeight - progressData.currentWeight;
    const isLoss = weightChange > 0;
    const title = isLoss ? 
      `📉 ${Math.abs(weightChange)} ${progressData.unit} Lost!` : 
      `📈 ${Math.abs(weightChange)} ${progressData.unit} Gained!`;

    const description = `Progress update: ${progressData.startWeight}${progressData.unit} → ${progressData.currentWeight}${progressData.unit} in ${progressData.timeframe}`;

    return {
      type: ShareType.WEIGHT_PROGRESS,
      title,
      description,
      data: progressData,
      hashtags: ['#LotusApp', '#WeightLossJourney', '#ProgressNotPerfection', '#HealthyLifestyle'],
      mentions: []
    };
  }

  /**
   * Get sharing statistics
   */
  async getShareHistory(): Promise<{
    totalShares: number;
    sharesByType: Record<ShareType, number>;
    recentShares: Array<{
      content: ShareableContent;
      options: ShareOptions;
      timestamp: string;
    }>;
  }> {
    try {
      const historyData = await AsyncStorage.getItem(SHARE_HISTORY_KEY);
      if (!historyData) {
        return {
          totalShares: 0,
          sharesByType: {} as Record<ShareType, number>,
          recentShares: []
        };
      }

      const history = JSON.parse(historyData);
      return history;
    } catch (error) {
      console.error('[SocialSharing] Error loading share history:', error);
      return {
        totalShares: 0,
        sharesByType: {} as Record<ShareType, number>,
        recentShares: []
      };
    }
  }

  /**
   * Private helper methods
   */
  private initializeDefaultTemplates(): void {
    // Workout Achievement Template
    this.templates.set(ShareType.WORKOUT_ACHIEVEMENT, {
      type: ShareType.WORKOUT_ACHIEVEMENT,
      layout: 'card',
      colors: {
        primary: '#007AFF',
        secondary: '#34C759',
        text: '#FFFFFF',
        background: '#1C1C1E'
      },
      fonts: {
        title: 'System Bold',
        body: 'System',
        caption: 'System Light'
      }
    });

    // Streak Milestone Template
    this.templates.set(ShareType.STREAK_MILESTONE, {
      type: ShareType.STREAK_MILESTONE,
      layout: 'story',
      colors: {
        primary: '#FF9500',
        secondary: '#FF6B35',
        text: '#FFFFFF',
        background: 'linear-gradient(135deg, #FF9500, #FF6B35)'
      },
      fonts: {
        title: 'System Bold',
        body: 'System',
        caption: 'System Light'
      }
    });

    // Add more templates as needed...
  }

  private getTemplate(type: ShareType): ShareTemplate {
    return this.templates.get(type) || this.templates.get(ShareType.WORKOUT_ACHIEVEMENT)!;
  }

  private async createImageFromTemplate(
    content: ShareableContent,
    template: ShareTemplate,
    options: ShareOptions
  ): Promise<string> {
    // This is a simplified version - in a real implementation,
    // you'd use a proper image generation library
    
    // For now, return a placeholder base64 image
    const placeholderImage = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    return placeholderImage;
  }

  private generateShareMessage(content: ShareableContent, options: ShareOptions): string {
    let message = options.customMessage || content.description;
    
    if (options.includeMotivationalQuote) {
      const quotes = [
        "Progress, not perfection! 💪",
        "Every day is a chance to get stronger! 🔥",
        "Consistency is key! 🗝️",
        "Small steps, big results! 👣",
        "Your only competition is who you were yesterday! 🏆"
      ];
      const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
      message += `\n\n${randomQuote}`;
    }

    if (content.hashtags && content.hashtags.length > 0) {
      message += `\n\n${content.hashtags.join(' ')}`;
    }

    if (options.includeAppBranding) {
      message += '\n\nShared via Lotus Fitness App 🌸';
    }

    return message;
  }

  private async recordShare(content: ShareableContent, options: ShareOptions): Promise<void> {
    try {
      const history = await this.getShareHistory();
      
      const newShare = {
        content,
        options,
        timestamp: new Date().toISOString()
      };

      history.totalShares += 1;
      history.sharesByType[content.type] = (history.sharesByType[content.type] || 0) + 1;
      history.recentShares.unshift(newShare);

      // Keep only last 50 shares
      if (history.recentShares.length > 50) {
        history.recentShares = history.recentShares.slice(0, 50);
      }

      await AsyncStorage.setItem(SHARE_HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('[SocialSharing] Error recording share:', error);
    }
  }
}

// Export singleton instance
export const socialSharingService = new SocialSharingService();

// Convenience functions
export const shareWorkoutAchievement = (workoutData: any, options?: ShareOptions) =>
  socialSharingService.generateWorkoutAchievement(workoutData)
    .then(content => socialSharingService.shareContent(content, options));

export const shareStreakMilestone = (streakData: any, options?: ShareOptions) =>
  socialSharingService.generateStreakMilestone(streakData)
    .then(content => socialSharingService.shareContent(content, options));

export const shareWeightProgress = (progressData: any, options?: ShareOptions) =>
  socialSharingService.generateWeightProgress(progressData)
    .then(content => socialSharingService.shareContent(content, options));

export const captureAndShare = (viewRef: any, content: ShareableContent, options?: ShareOptions) =>
  socialSharingService.captureComponentAsImage(viewRef)
    .then(imageUri => socialSharingService.shareContent({ ...content, imageUri }, options));
